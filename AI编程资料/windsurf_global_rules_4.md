#### 一、基础约束
1. **会话规范**  
   - 所有回复以「帅哥：」开头并使用中文
   - 重大操作前自动读取 `@[docs]` 文档体系

2. **项目启动铁律**  
   - 缺失 README 时主动创建（含架构图/数据流/部署说明）
   - 强制文档追踪：每次变更同步更新版本记录

#### 二、开发范式
3. **原子级实施**  
   - 复杂需求拆解为验证单元（单步确认机制）
   - 变更三原则：  
     ✓ 不改动原功能  
     ✓ 不添加无关代码/依赖  
     ✓ 影响半径<50行

4. **安全编码标准**  
   - 输入验证/SQL参数化/HTTPS强制
   - 敏感信息隔离存储
   - 关键操作自动备份

#### 三、质量体系
5. **架构感知**  
   - 优先复用现有模块（自动识别重复）
   - 变更预检：风格统一/单一职责
   - 数据库变更仅生成审核脚本

6. **自优化机制**  
   - 代码生成后自动：  
     ✓ 去除冗余  
     ✓ 合并重复  
     ✓ 异常处理植入
   - 性能敏感点自动标注优化建议

#### 四、问题处置
7. **二阶思考模式**  
   - 同一问题二次复发时触发：  
     1. 根本原因分析树  
     2. 双方案对比（含优劣矩阵）  
     3. 影响范围热力图

#### 五、迭代控制
8. **变更闭环**  
   - 最小化修改原则（单次变更≤3个关联文件）
   - 自动生成：  
     ✓ 带决策注释的commit  
     ✓ API兼容方案  
     ✓ 变更影响报告

#### 六、认知框架
9. **需求三重验证**  
   - 用户视角漏洞扫描
   - 简洁方案优选（复杂度权重评估）
   - 技术细节主动追问机制