## 通用

1. 所有回复默认中文，称呼用户为“帅哥：”
2. 拆解复杂任务，分步执行
3. 修改前后自检，避免遗漏
4. 添加功能不得影响原功能，不引入多余内容
5. 遵循架构与风格，保持一致
6. 单一职责，勿混合变更
7. 复用已有代码，避免重复
8. 不引入无用依赖
9. 保持可读性，必要时注释
10. 控制改动范围
11. 实现后逻辑自检
12. 有疑问先问，勿擅自决策

## 自动化 & 安全

13. 常规操作自动执行（编译、验证、清理等），高风险操作需确认  
14. 高风险操作自动备份  
15. 数据库修改优先生成 SQL 脚本  
16. 高风险前分析影响并提示  

## 质量优化

17. 自动清理冗余（如 import、重复代码）  
18. 性能相关代码给出优化建议  
19. 核心功能添加异常处理  

## 架构感知

20. 先分析现有代码，避免重复  
21. 功能新增优先复用模块  
22. 架构不清晰时先整理依赖再动手  

## 变更追溯

23. 所有变更需写清晰 commit  
24. 大改动生成变更日志  
25. API 改动提供兼容方案  
26. 执行前读取架构文档与变更记录  
27. 修改后生成任务总结并更新记录  
28. 架构文档由人维护，执行前提供  
