## 基本行为

1. 所有回复默认中文，开头称呼“帅哥：”
2. 拆解复杂任务，分步执行，每步完成后继续
3. 有疑问先问，禁止擅自决策
4. 修改前后自检，确保不遗漏、不破坏原功能

## 项目初始化

5. 初始化前必须阅读 `@[docs]` 目录全部文档并遵循规范  
6. 若无 `README.md`，提醒创建或自动生成，用于记录模块、页面、数据流、依赖与部署方式等  

## 需求处理

7. 充分理解并补全需求，避免遗漏，与用户反复确认  
8. 采用最简洁可行方案，避免过度设计  

## 编码规范

9. 遵循统一目录结构与命名规范，确保代码可读、可维护、可扩展  
10. 避免重复代码，优先复用现有模块  
11. 必要注释说明功能与实现逻辑  
12. 控制改动范围，单一职责修改  
13. 自动清理冗余代码（如 import、重复逻辑）  

## 安全规范

14. 不在客户端存储敏感信息  
15. 验证并过滤用户输入，防止 XSS/SQL 注入  
16. 所有数据传输使用 HTTPS  

## 问题处理

17. 根据反馈定位问题根因，最小改动修复  
18. Bug 经两次调整仍未解决时，启动系统分析：
    - 分析根因，提出假设并验证  
    - 提供两种解决方案，说明优缺点，让用户选择  

## 自动化与效率

19. 常规操作自动执行（编译、构建、清理等），高风险操作需确认  
20. 高风险操作自动备份，数据库改动优先生成 SQL 脚本  
21. 执行前分析改动影响，提供提示  

## 代码质量与性能

22. 性能相关代码（如 SQL、循环）提供优化建议  
23. 核心功能添加异常处理，防止程序崩溃  

## 变更追踪与架构一致性

24. 所有变更需写清晰 commit  
25. 大改动生成变更日志，API 改动提供兼容策略  
26. 每次迭代后更新 `README.md`，记录功能与优化  
27. 执行前读取架构文档与变更记录，确保一致性  
28. 架构文档由人维护，执行前提供给 AI  

## 方法论

29. 系统性思维：任务分解 + 全局分析  
30. 思维树法：提出多解方案 + 比较优劣  
31. 持续迭代：不断完善，覆盖边界与异常情况  
