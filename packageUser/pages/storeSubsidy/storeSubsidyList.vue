<template>
	<view class="store-subsidy-list">
		<!-- 顶部Tab切换 -->
		<view class="tab-container">
			<view class="tab-list">
				<view 
					class="tab-item" 
					:class="{ active: currentTab === 0 }" 
					@click="switchTab(0)"
				>
					可用
				</view>
				<view 
					class="tab-item" 
					:class="{ active: currentTab === 1 }" 
					@click="switchTab(1)"
				>
					已过期
				</view>
				<view 
					class="tab-item" 
					:class="{ active: currentTab === 2 }" 
					@click="switchTab(2)"
				>
					已用尽
				</view>
			</view>
		</view>

		<!-- 列表区域 -->
		<view class="list-container">
			<!-- 空状态 -->
			<view v-if="subsidyList.length === 0 && !loading" class="empty-state">
				<image src="/static/empty.png" class="empty-icon"></image>
				<text class="empty-text">暂无{{getTabName()}}补助金</text>
			</view>

			<!-- 补助金列表 -->
			<view v-else class="subsidy-list">
				<view 
					v-for="(item, index) in subsidyList" 
					:key="index" 
					class="subsidy-card"
					:class="{ 'expired': item.storeSubsidyStatus === '1', 'exhausted': item.storeSubsidyStatus === '2' }"
				>
					<!-- 店铺名称 -->
					<view class="card-header">
						<view class="store-name">{{(item.subsidyStoreName && item.subsidyStoreName.trim()) || '未知店铺'}}</view>
						<view class="status-tag" :class="getStatusClass(item.storeSubsidyStatus)">
							{{getStatusText(item.storeSubsidyStatus)}}
						</view>
					</view>

					<!-- 金额信息 -->
					<view class="amount-info">
						<view v-if="currentTab === 0" class="current-amount">
							<text class="amount-label">剩余可用金额</text>
							<text class="amount-value">¥{{(item.remainingAmount && item.remainingAmount !== 'null') ? item.remainingAmount : '0.00'}}</text>
						</view>
						<view v-else class="original-amount">
							<text class="amount-label">{{currentTab === 1 ? '原始金额' : '已使用金额'}}</text>
							<text class="amount-value">¥{{getAmountText(item)}}</text>
						</view>
					</view>

					<!-- 时间信息 -->
					<view class="time-info">
						<view v-if="currentTab === 0" class="expire-time">
							<text class="time-label">有效期至：</text>
							<text class="time-value" :class="{ 'warning': isNearExpiry(item.expireTime) }">
								{{formatTime(item.expireTime) || '无限期'}}
							</text>
						</view>
						<view v-else class="create-time">
							<text class="time-label">发放时间：</text>
							<text class="time-value">{{formatTime(item.createTime) || '未知时间'}}</text>
						</view>
					</view>

					<!-- 操作按钮 -->
					<view v-if="currentTab === 0" class="action-btn">
						<view class="use-btn" @click="goToStore(item.subsidyStoreId)">
							去店铺使用
						</view>
					</view>
				</view>
			</view>
		</view>

		<!-- 加载更多 -->
		<view v-if="subsidyList.length > 0" class="load-more-container">
			<view v-if="loadingStatus === 'loading'" class="loading-text">加载中...</view>
			<view v-else-if="loadingStatus === 'more'" class="load-more-btn" @click="loadMore">点击加载更多</view>
			<view v-else-if="loadingStatus === 'noMore'" class="no-more-text">没有更多数据了</view>
		</view>
	</view>
</template>

<script>
import { userStore } from '@/store/index.js'

export default {
	name: 'StoreSubsidyList',
	data() {
		return {
			users: null,
			currentTab: 0,
			subsidyList: [],
			loading: false,
			loadingStatus: 'more',
			pageNo: 1,
			pageSize: 10,
			hasMore: true
		}
	},
	created() {
		this.users = userStore()
	},
	onLoad(options) {
		if (options && options.status) {
			this.currentTab = parseInt(options.status)
		}
	},
	onShow() {
		this.loadSubsidyList()
	},
	methods: {
		getTabName() {
			var names = ['可用', '已过期', '已用尽']
			return names[this.currentTab]
		},
		
		getStatusClass(status) {
			var classMap = {
				'0': 'available',
				'1': 'expired', 
				'2': 'exhausted'
			}
			return classMap[status] || ''
		},
		
		getStatusText(status) {
			var textMap = {
				'0': '可用',
				'1': '已过期',
				'2': '已用尽'
			}
			return textMap[status] || '未知'
		},
		
		isNearExpiry(expireTime) {
			if (!expireTime || expireTime === 'null' || expireTime.trim() === '') return false
			try {
				var expire = new Date(expireTime)
				if (isNaN(expire.getTime())) return false
				
				var now = new Date()
				var diffTime = expire.getTime() - now.getTime()
				var diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
				return diffDays <= 7 && diffDays > 0
			} catch (error) {
				console.error('过期时间判断错误：', error)
				return false
			}
		},
		
		formatTime(timeStr) {
			if (!timeStr || timeStr === 'null' || timeStr.trim() === '') return ''
			try {
				var date = new Date(timeStr)
				// 检查日期是否有效
				if (isNaN(date.getTime())) return ''
				
				var month = (date.getMonth() + 1).toString()
				var day = date.getDate().toString()
				if (month.length < 2) month = '0' + month
				if (day.length < 2) day = '0' + day
				return date.getFullYear() + '-' + month + '-' + day
			} catch (error) {
				console.error('时间格式化错误：', error)
				return ''
			}
		},
		
		switchTab(tabIndex) {
			this.currentTab = tabIndex
			this.pageNo = 1
			this.hasMore = true
			this.subsidyList = []
			this.loadSubsidyList()
		},
		
		loadSubsidyList(isLoadMore) {
			var self = this
			if (typeof isLoadMore === 'undefined') isLoadMore = false
			
			if (self.loading) return
			
			if (!self.users || !self.users.userInfo || !self.users.userInfo.id) {
				uni.showToast({
					title: '用户信息未加载',
					icon: 'none'
				})
				return
			}
			
			self.loading = true
			self.loadingStatus = 'loading'
			
			var statusFilter = ''
			if (self.currentTab === 0) {
				statusFilter = '0'  // 可用
			} else if (self.currentTab === 1) {
				statusFilter = '1'  // 已过期
			} else if (self.currentTab === 2) {
				statusFilter = '2'  // 已用尽
			}
			
			var params = {
				pattern: 2,
				payType: '50',  // 使用payType参数，传递店铺补助金发放类型
				memberId: self.users.userInfo.id,
				pageNo: self.pageNo,
				pageSize: self.pageSize
			}
			
			// 添加补助金状态过滤
			if (statusFilter) {
				params.storeSubsidyStatus = statusFilter
			}
			
			uni.http.get(uni.api.findAccountCapitalByMemberId, {
				params: params
			}).then(function(res) {
				var data = res.data
				if (data.success) {
					// 处理分页数据结构：result包含records数组和分页信息
					var result = data.result || {}
					var newList = result.records || []
					
					// 对数据进行安全处理
					var safeList = []
					for (var i = 0; i < newList.length; i++) {
						var item = newList[i]
						safeList.push({
							subsidyStoreName: item.subsidyStoreName || '',
							storeSubsidyStatus: item.storeSubsidyStatus || '0',
							remainingAmount: item.remainingAmount || '0.00',
							amount: item.amount || '0.00',
							expireTime: item.expireTime || '',
							createTime: item.createTime || '',
							subsidyStoreId: item.subsidyStoreId || '',
							payTypeCode: item.payTypeCode || '',
							orderNo: item.orderNo || '',
							remarks: item.remarks || ''
						})
					}
					
					if (isLoadMore) {
						self.subsidyList = self.subsidyList.concat(safeList)
					} else {
						self.subsidyList = safeList
					}
					
					// 根据分页信息判断是否还有更多数据
					self.hasMore = safeList.length >= self.pageSize && (result.current || 1) < (result.pages || 1)
					self.loadingStatus = self.hasMore ? 'more' : 'noMore'
				} else {
					uni.showToast({
						title: data.message || '加载失败',
						icon: 'none'
					})
					self.loadingStatus = 'more'
				}
			}).catch(function(error) {
				console.error('加载补助金列表失败：', error)
				uni.showToast({
					title: '网络错误',
					icon: 'none'
				})
				self.loadingStatus = 'more'
			}).finally(function() {
				self.loading = false
			})
		},
		
		loadMore() {
			if (!this.hasMore || this.loading) return
			this.pageNo++
			this.loadSubsidyList(true)
		},
		
		goToStore(storeId) {
			if (!storeId || storeId === 'null' || storeId.toString().trim() === '') {
				uni.showToast({
					title: '店铺信息异常',
					icon: 'none'
				})
				return
			}
			
			uni.navigateTo({
				url: '/packageGoods/pages/shopIndex/shopIndex?storeId=' + storeId
			}).catch(function(error) {
				console.error('页面跳转失败：', error)
				uni.showToast({
					title: '页面跳转失败',
					icon: 'none'
				})
			})
		},
		
		getAmountText(item) {
			if (this.currentTab === 1) {
				// 已过期：显示原始金额
				return this.parseAmount(item.amount)
			} else if (this.currentTab === 2) {
				// 已用尽：显示已使用金额
				var original = parseFloat(item.amount || 0)
				var remaining = parseFloat(item.remainingAmount || 0)
				return (original - remaining).toFixed(2)
			}
			return '0.00'
		},
		
		parseAmount(amount) {
			if (!amount) return '0.00'
			// 去掉前缀的+或-号，只保留数字
			var cleanAmount = amount.toString().replace(/^[+-]/, '')
			return parseFloat(cleanAmount || 0).toFixed(2)
		}
	}
}
</script>

<style lang="scss" scoped>
.store-subsidy-list {
	background-color: #f5f5f5;
	min-height: 100vh;
}

.tab-container {
	background-color: #fff;
	padding: 0 30rpx;
	border-bottom: 1rpx solid #f0f0f0;
	
	.tab-list {
		display: flex;
		align-items: center;
		
		.tab-item {
			flex: 1;
			height: 88rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			font-size: 28rpx;
			color: #666;
			border-bottom: 4rpx solid transparent;
			transition: all 0.3s;
			
			&.active {
				color: #FF6B35;
				border-bottom-color: #FF6B35;
				font-weight: 500;
			}
		}
	}
}

.list-container {
	padding: 20rpx 30rpx;
}

.empty-state {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 120rpx 0;
	
	.empty-icon {
		width: 120rpx;
		height: 120rpx;
		margin-bottom: 30rpx;
		opacity: 0.3;
	}
	
	.empty-text {
		font-size: 28rpx;
		color: #999;
	}
}

.subsidy-list {
	.subsidy-card {
		background: #fff;
		border-radius: 16rpx;
		padding: 30rpx;
		margin-bottom: 20rpx;
		box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
		
		&.expired {
			opacity: 0.7;
			
			.card-header .store-name {
				color: #999;
			}
		}
		
		&.exhausted {
			opacity: 0.8;
		}
		
		.card-header {
			display: flex;
			align-items: center;
			justify-content: space-between;
			margin-bottom: 20rpx;
			
			.store-name {
				font-size: 32rpx;
				font-weight: 500;
				color: #333;
			}
			
			.status-tag {
				padding: 8rpx 16rpx;
				border-radius: 20rpx;
				font-size: 22rpx;
				
				&.available {
					background: #E8F8F5;
					color: #00C896;
				}
				
				&.expired {
					background: #FEF0F0;
					color: #F56C6C;
				}
				
				&.exhausted {
					background: #F4F4F5;
					color: #909399;
				}
			}
		}
		
		.amount-info {
			margin-bottom: 20rpx;
			
			.current-amount,
			.original-amount {
				display: flex;
				align-items: center;
				justify-content: space-between;
				
				.amount-label {
					font-size: 26rpx;
					color: #666;
				}
				
				.amount-value {
					font-size: 36rpx;
					font-weight: 500;
					color: #FF6B35;
				}
			}
		}
		
		.time-info {
			margin-bottom: 20rpx;
			
			.expire-time,
			.create-time {
				display: flex;
				align-items: center;
				
				.time-label {
					font-size: 24rpx;
					color: #999;
					margin-right: 10rpx;
				}
				
				.time-value {
					font-size: 24rpx;
					color: #666;
					
					&.warning {
						color: #F56C6C;
					}
				}
			}
		}
		
		.action-btn {
			.use-btn {
				width: 100%;
				height: 72rpx;
				background: linear-gradient(135deg, #FF6B35 0%, #FF8A65 100%);
				border-radius: 36rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				font-size: 28rpx;
				color: #fff;
				font-weight: 500;
			}
		}
	}
}

.load-more-container {
	display: flex;
	justify-content: center;
	align-items: center;
	padding: 20rpx 0;
}

.loading-text,
.load-more-btn,
.no-more-text {
	font-size: 28rpx;
	color: #666;
	margin: 0 20rpx;
}

.loading-text {
	font-weight: 500;
}

.load-more-btn {
	background: linear-gradient(135deg, #FF6B35 0%, #FF8A65 100%);
	border-radius: 36rpx;
	padding: 16rpx 32rpx;
	color: #fff;
}
</style> 