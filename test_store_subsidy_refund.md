# 店铺补助金退款功能测试指南

## 功能概述

本次实现了任务5.1：订单取消/售后退款时店铺补助金恢复逻辑。当管理员在后台点击"取消并退款"时，系统会自动恢复该订单使用的店铺补助金。

## 核心实现逻辑

### 1. 数据检索机制
- **通过订单号检索**：使用 `member_account_capital` 表的 `order_no` 字段
- **识别消费记录**：查找 `pay_type='51'`（店铺补助金消费/抵扣）的记录
- **追溯原始记录**：通过备注字段中的"原补助金记录ID"找到原发放记录

### 2. 退款恢复流程
```
1. 查找订单的店铺补助金消费记录（pay_type='51'）
   ↓
2. 从备注中提取原补助金记录ID
   ↓
3. 检查原补助金记录是否未过期且状态允许恢复
   ↓
4. 恢复原记录的 remaining_amount
   ↓
5. 更新原记录状态（已用尽→可用）
   ↓
6. 生成退回流水记录（pay_type='52'）
```

### 3. 关键字段说明
- `pay_type='50'`：店铺补助金发放（收入型）
- `pay_type='51'`：店铺补助金消费/抵扣（支出型）
- `pay_type='52'`：店铺补助金退回/恢复（收入型）
- `store_subsidy_status='0'`：可用
- `store_subsidy_status='1'`：已过期
- `store_subsidy_status='2'`：已用尽

## 测试场景

### 场景1：正常退款恢复
**前置条件：**
1. 用户有店铺补助金（未过期）
2. 下单时使用了店铺补助金
3. 订单状态为待发货或待收货

**测试步骤：**
1. 管理员登录后台
2. 找到使用了店铺补助金的订单
3. 点击"取消并退款"
4. 检查退款结果

**预期结果：**
- 通用余额正确退回
- 店铺补助金恢复到原发放记录的 `remaining_amount`
- 原补助金状态从"已用尽"变为"可用"（如适用）
- 生成 `pay_type='52'` 的退回流水记录

### 场景2：部分补助金已过期
**前置条件：**
1. 用户使用了多笔店铺补助金
2. 其中部分补助金已过期

**预期结果：**
- 未过期的补助金正常恢复
- 已过期的补助金不恢复，记录警告日志

### 场景3：无店铺补助金的订单
**前置条件：**
1. 订单仅使用通用余额和在线支付

**预期结果：**
- 正常退回通用余额
- 不进行店铺补助金处理
- 日志显示"没有使用店铺补助金，无需恢复"

## 数据库验证

### 1. 检查消费记录
```sql
-- 查看订单的店铺补助金消费记录
SELECT * FROM member_account_capital 
WHERE order_no = '订单号' 
  AND pay_type = '51' 
  AND go_and_come = '1';
```

### 2. 检查恢复记录
```sql
-- 查看退款恢复记录
SELECT * FROM member_account_capital 
WHERE order_no = '订单号' 
  AND pay_type = '52' 
  AND go_and_come = '0';
```

### 3. 检查原补助金状态
```sql
-- 查看原补助金记录的恢复情况
SELECT id, amount, remaining_amount, store_subsidy_status, expire_time
FROM member_account_capital 
WHERE pay_type = '50' 
  AND member_list_id = '会员ID'
  AND subsidy_store_id = '店铺ID';
```

## 日志监控

### 关键日志信息
1. **开始处理**：`开始处理订单[xxx]的店铺补助金退款恢复`
2. **无需处理**：`订单[xxx]没有使用店铺补助金，无需恢复`
3. **成功恢复**：`成功恢复店铺补助金：原记录ID=xxx，恢复金额=xxx`
4. **过期警告**：`原补助金记录已过期或状态不允许恢复`
5. **完成处理**：`订单[xxx]店铺补助金退款恢复完成，总恢复金额：xxx`

### 日志级别
- INFO：正常流程信息
- WARN：业务警告（如补助金已过期）
- ERROR：系统错误（如数据异常）

## 异常处理

### 1. 数据异常
- 原补助金记录不存在
- 备注格式不正确
- 金额计算异常

### 2. 业务异常
- 补助金已过期
- 补助金状态不允许恢复
- 会员信息不存在

### 3. 容错机制
- 单条记录处理失败不影响其他记录
- 详细的错误日志记录
- 事务回滚保证数据一致性

## 性能考虑

### 1. 查询优化
- 使用索引字段进行查询（member_list_id, order_no, pay_type）
- 限制查询范围，避免全表扫描

### 2. 批量处理
- 单个订单的多条补助金记录批量处理
- 减少数据库交互次数

### 3. 事务管理
- 使用 `@Transactional` 确保数据一致性
- 合理的事务边界设置

## 注意事项

1. **时间判断**：严格检查补助金过期时间
2. **状态管理**：正确更新补助金状态
3. **金额精度**：使用 BigDecimal 确保金额计算精度
4. **日志记录**：详细记录操作过程便于问题排查
5. **异常处理**：确保部分失败不影响整体流程

## 后续优化建议

1. **并发控制**：考虑添加乐观锁机制
2. **性能监控**：添加处理时间统计
3. **业务规则**：支持更复杂的退款规则配置
4. **用户通知**：退款完成后通知用户补助金恢复情况 