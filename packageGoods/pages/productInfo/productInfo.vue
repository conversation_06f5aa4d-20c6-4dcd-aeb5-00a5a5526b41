<template>
	<view class="productInfo" v-if="infos?.goodinfo">
		<view class="productInfo-operation">
			<view :style="{width:'100%', height:statusBarHeight + 'px'}">

			</view>
			<view class="productInfo-operation-title" :style="{height:navbarHeight + 'px'}">
				<view class="productInfo-operation-title-back" @click="goBackIsTop">
					<image src="@/static/right-arrow-white.png" mode="heightFix"></image>
				</view>
				<view class="productInfo-operation-title-naT">
					{{infos.goodinfo.goodName}}
				</view>
				<view></view>
			</view>
		</view>
		<view class="productInfo-banner" v-if="infos.goodinfo?.mainPicture?.length">
			<z-swiper v-model="infos.goodinfo.mainPicture">
				<z-swiper-item v-for="(item,index) in infos.goodinfo.mainPicture" :key="index">
					<image class="productInfo-banner-image" :src="imgUrl + item" mode="aspectFill"> </image>
				</z-swiper-item>
			</z-swiper>
		</view>
		<view class="productInfo-container">
			<view class="productInfo-container-wrap productInfo-container-info">
				<view class="productInfo-container-info-store">
					<image src="@/static/productInfo/i_2.png" mode=""></image>
					<view class="productInfo-container-info-store-name">
						{{infos.storeInfo.storeName}}
					</view>
				</view>
				<view class="productInfo-container-info-title">
					{{infos.goodinfo.goodDescribe}}
				</view>
				<view class="productInfo-container-info-score">
					<image src="@/static/index/i_1.png" mode=""></image>
					<view>
						{{ infos.goodinfo.smallPrice }}
					</view>
				</view>
				<view class="productInfo-container-info-sh">
					<image src="@/static/productInfo/i_1.png" mode=""></image>
					随时约
				</view>
			</view>

			<!-- 店铺补助金提示 -->
			<view class="productInfo-container-wrap productInfo-container-subsidy" v-if="storeSubsidyInfo.hasSubsidy && users.token">
				<view class="productInfo-container-subsidy-content">
					<view class="productInfo-container-subsidy-text">
						<view class="productInfo-container-subsidy-text-main">
							您在本店有 <text class="amount">¥{{storeSubsidyInfo.totalAmount}}</text> 补助金可用
						</view>
						<view class="productInfo-container-subsidy-text-sub">
							有效期至 {{storeSubsidyInfo.nearestExpireTime}}
						</view>
					</view>
					<view class="productInfo-container-subsidy-btn" @click="navToStoreSubsidyList">
						查看
					</view>
				</view>
			</view>

			<view class="productInfo-container-wrap productInfo-container-storeInfo" v-if="infos.storeInfo">
				<image class="productInfo-container-storeInfo-img" :src="imgUrl + infos.storeInfo.logoAddr" mode="">
				</image>
				<view class="productInfo-container-storeInfo-right">
					<view class="productInfo-container-storeInfo-right-name">
						{{infos.storeInfo.storeName}}
					</view>
					<view class="productInfo-container-storeInfo-right-desc">
						{{infos.storeInfo.introduce}}
					</view>
				</view>
			</view>


			<view class="productInfo-container-wrap">
				<view class="productInfo-container-wrap-title">
					商品详情
				</view>
				<image class="productInfo-container-wrap-img" v-for="(item, index) in infos.goodinfo.detailsGoods"
					:src="imgUrl + item" :key="index" mode="widthFix"></image>

			</view>
		</view>

		<view class="productInfo-bottom">
			<view class="productInfo-bottom-left">
				<view @click="toggleCol(false)">
					<image v-if="infos.isCollect == 1" src="@/static/productInfo/collect.png" mode=""></image>
					<image v-else src="@/static/productInfo/noCollect.png" mode=""></image>
					<view>
						{{infos.isCollect == 1 ? '取消收藏' : '收藏'}}
					</view>
				</view>
				<view @click="openShareDialog">
					<image src="@/static/productInfo/share.png" mode=""></image>
					<view>
						分享
					</view>
				</view>
				<view @click="navToStoreIndex(infos?.storeInfo)">
					<image src="@/static/productInfo/Frame.png" mode=""></image>
					<view>
						进店
					</view>
				</view>
			</view>
			<view class="productInfo-bottom-right">
				<view class="productInfo-bottom-right-lt" @click="showPop(2)" v-if="!getOhterObj.shareDiscountRatio">
					加入购物车
				</view>
				<view class="productInfo-bottom-right-rt" @click="showPop(1)">
					立即兑换
				</view>
			</view>
		</view>

		<shareModal :goodInfo='infos' ref='shareModalRef' v-model="shareDiscountRatio" @savePoster="savePic">
		</shareModal>
		<!-- 海报 -->
		<l-painter path-type="url" file-type="jpg" isCanvasToTempFilePath hidden @success="painterSuccess"
			v-if="posterBg && qrCode" :css="posterStyle.painterWrap">
			<l-painter-view :css="posterStyle.container">
				<l-painter-image :src="posterBg" :css="posterStyle.posterBg"> </l-painter-image>
				<l-painter-view :css="posterStyle.qrCode">
					<l-painter-image :src="qrCode" :css="posterStyle.qrCodeImg"> </l-painter-image>
					<l-painter-view :css="posterStyle.qrCodeUsers">
						<l-painter-image :src="users.userInfo.avatarUrl"
							:css="posterStyle.qrCodeUsersAvatar"></l-painter-image>
						<l-painter-text :text="users.userInfo.nickName" :css="posterStyle.qrCodeUsersNickname">
						</l-painter-text>
					</l-painter-view>
				</l-painter-view>
			</l-painter-view>
		</l-painter>

		<uni-popup ref="specificationPop" type="bottom" :safeArea="false" @change="specificationWrapChange">
			<specificationWrap ref="specificationWrapRef" :hasRequest="false" :info="selectInfo"
				:inputDisabled="Boolean(infos.goodinfo.marketingStorePrefectureGoodId)"></specificationWrap>
			<view class="productInfo-btnOpe">
				<button class="productInfo-btnOpe-btn" style="flex: 1" @click="toOrder()" :loading="btnLoading"
					:disabled="btnLoading" v-if="showBtnList.indexOf(1) != -1">立即兑换</button>
				<button class="productInfo-btnOpe-btn" style="flex: 1" @click="addCart()" :loading="btnLoading"
					:disabled="btnLoading" v-if="showBtnList.indexOf(2) != -1 ">加入购物车</button>
			</view>
		</uni-popup>
	</view>
</template>

<script setup>
	import {
		computed,
		ref,
		watch
	} from 'vue';
	import {
		goBackIsTop,
		shareAbout,
		toUpPage,
		specificationPopPub,
		navToStoreIndex
	} from '@/hooks';
	import {
		parseImgurl
	} from '@/utils'
	import {
		getAllBeShared,
		userStore
	} from '@/store/index.js'

	import shareModal from './components/shareModal/shareModal.vue';
	import {
		onShareAppMessage,
		onUnload
	} from '@dcloudio/uni-app';

	const shareModalRef = ref()
	const shareDiscountRatio = ref(0)
	const {
		selectInfo,
		addCart,
		toOrder,
		showSpecificationWrap,
		specificationWrapChange,
		btnLoading,
		specificationPop,
		specificationWrapRef
	} = specificationPopPub();
	let systemInfo = uni.getSystemInfoSync();
	let options = ref({});
	//商品信息
	let infos = ref({});
	//立即购买和加入购物车按钮的显示  判断数组中有数字1:立即购买,2加入购物车则显示
	let showBtnList = ref([1, 2]);
	
	// 店铺补助金信息
	let storeSubsidyInfo = ref({
		hasSubsidy: false,
		totalAmount: '0.00',
		nearestExpireTime: ''
	});

	//海报相关
	let path = ref();
	let posterBg = ref('');
	const users = userStore();
	const statusBarHeight = systemInfo.statusBarHeight
	const navbarHeight = computed(() => {
		// #ifdef APP-PLUS || H5
		return 44;
		// #endif
		// #ifdef MP
		// 小程序特别处理，让导航栏高度 = 胶囊高度 + 两倍胶囊顶部与状态栏底部的距离之差(相当于同时获得了导航栏底部与胶囊底部的距离)
		// 此方法有缺陷，暂不用(会导致少了几个px)，采用直接固定值的方式
		// return menuButtonInfo.height + (menuButtonInfo.top - this.statusBarHeight) * 2;//导航高度
		let height = systemInfo.platform == 'ios' ? 44 : 48;
		return height;
		// #endif
	})


	//点击立即购买/加入购物车按钮打开弹窗
	function showPop(type = 0) {
		if (type == 0) {
			showBtnList.value = [1, 2];
		} else {
			showBtnList.value = [type];
		}
		showSpecificationWrap({
			...infos.value.goodinfo,
			...infos.value,
			tMemberId: getShareObj.value?.tMemberId,
		});
	}

	const openShareDialog = () => {
		if (users.token) {
			shareModalRef.value.open()
		} else {
			uni.redirectTo({
				url: "/pages/login/login"
			})
		}
	}

	const imgUrl = uni.env.IMAGE_URL;
	//海报canvas样式相关
	const posterStyle = computed(() => {
		return {
			painterWrap: 'width:660rpx',
			container: `width: 660rpx; height: 1002rpx;`,
			posterBg: `width: 660rpx; height: 1002rpx;`,
			qrCode: `position: absolute;right: 262.5rpx;bottom: 24rpx;width:135rpx;height:185rpx;`,
			qrCodeImg: `width: 135rpx;height: 135rpx;border-radius: 8rpx;margin-bottom: 15rpx;`,
			qrCodeUsers: `width: 135rpx;height: 35rpx;background-color: rgba(0, 0, 0, 0.55);border-radius: 15rpx;display: flex;align-items: center;padding: 0 10rpx;font-size: 24rpx;color: #FFFFFF;box-sizing:border-box`,
			qrCodeUsersAvatar: `width: 25rpx;height: 25rpx;margin-right: 4rpx;border-radius: 50%;`,
			qrCodeUsersNickname: `flex:1;line-clamp:1;`,
		};
	});

	const {
		sharePopSelect,
		sharePopChange,
		shareToggle,
		sharePop,
		qrCode,
		getQrCode,
		getShareObj,
		getOhterObj
	} = shareAbout({
		page: '/pages/productInfo/productInfo',
		params: computed(() => {
			return {
				...options.value,
				shareDiscountRatio: shareDiscountRatio.value
			};
		}),
		imageUrl: computed(() => imgUrl + (infos.value?.goodinfo?.mainPicture?.[0] || [])),
		title: computed(() => infos.value?.goodinfo?.goodName || ''),
		// hbCallback: savePic,
	});
	//保存图片
	async function savePic() {
		uni.showLoading({
			mask: true,
		});
		await getQrCode();
	}
	watch(
		() => getOhterObj.value,
		() => {
			if (getOhterObj.value.goodId) {
				options.value = getOhterObj.value;
			}
			refresh();
		}
	);
	onUnload(() => {
		getAllBeShared().setShareDiscountRatio("");
	})
	//收藏和取消收藏
	async function toggleCol() {
		const isCol = infos.value.isCollect == 1 ? false : true
		uni.showLoading({
			mask: true,
		});
		let {
			data
		} = await uni.http.get(isCol ? uni.api.addMemberGoodsCollectionByGoodId : uni.api
			.delMemberGoodsCollectionByGoodId, {
				params: options.value,
			});
		infos.value.isCollect = isCol ? 1 : 0;
		uni.showToast({
			icon: 'none',
			title: data.message,
		});
	}


	async function refresh() {
		try {
			//商品详情
			let {
				data
			} = await uni.http.get(uni.api.findGoodListByGoodId, {
				params: {
					...options.value,
					isTransition: 1,
				},
			});
			//字段解释 -----开始
			// isPrefectureGood 商品属性： 0：普通； 1：专区； 2：中奖拼团； 3：免单； 4：定制； 5：展品； 6：竞价； 7：菜品；8:单品限时抢购；9：店铺专区
			// -----结束
			//主图解析
			if (data.result.goodinfo.mainPicture) {
				data.result.goodinfo.mainPicture = parseImgurl(data.result.goodinfo.mainPicture);
			}
			//分享图
			posterBg.value = data.result.goodinfo?.sharePicture && imgUrl + parseImgurl(data.result.goodinfo
				?.sharePicture)?.[0];
			//为你推荐的详情图解析
			if (data.result.goodinfo.recommendGoods && data.result.goodinfo.recommendGoods.length > 0) {
				data.result.goodinfo.recommendGoods = data.result.goodinfo.recommendGoods.map(item => {
					item.mainPicture = parseImgurl(item.mainPicture);
					return item;
				});
			}
			//详情图解析
			if (data.result.goodinfo.detailsGoods) {
				data.result.goodinfo.detailsGoods = parseImgurl(data.result.goodinfo.detailsGoods);
			}
			//专区赠送字段解析
			if (data.result.goodinfo.give) {
				try {
					data.result.goodinfo.give = JSON.parse(data.result.goodinfo.give);
				} catch (e) {
					console.error('data.result.goodinfo.give解析错误', e);
					//TODO handle the exception
				}
			}
			selectInfo.value = {
				...options.value,
				...data.result.goodinfo,
			}; //弹窗中的信息
			infos.value = {
				...options.value,
				...data.result,
			};
			uni.setNavigationBarTitle({
				title: data.result.goodinfo.goodName,
			});

			// 获取店铺补助金信息
			await getStoreSubsidyInfo();

		} catch (e) {
			console.error(e)
			uni.hideLoading();
			uni.hideToast();
			uni.showModal({
				title: '温馨提示',
				content: e?.data?.message || '网络状况不稳定，请稍后再试',
				showCancel: false,
				confirmText: '知道了',
				success(res) {
					toUpPage();
				},
			});
			//TODO handle the exception
		}

		//当前商品的优惠券信息
		// let {
		// 	data: discountData
		// } = await uni.http.post(uni.api.findMarketingDiscountByGoodId, {
		// 	...options.value,
		// 	pageNo: 1,
		// 	pageSize: 100
		// })
		// couponInfo.value = discountData.result.records;
	}

	//画报生成成功回调
	function painterSuccess(e) {
		path.value = e;
		uni.saveImageToPhotosAlbum({
			filePath: path.value,
			success: function() {
				setTimeout(() => {
					uni.showToast({
						title: '保存成功~',
						icon: 'none',
					});
				}, 350);
			},
			complete() {
				uni.hideLoading();
				shareModalRef.value.close();
			},
		});
	}

	// 获取店铺补助金信息
	async function getStoreSubsidyInfo() {
		if (!users.token || !infos.value?.storeInfo?.id) {
			storeSubsidyInfo.value = {
				hasSubsidy: false,
				totalAmount: '0.00',
				nearestExpireTime: ''
			};
			return;
		}

		try {
			// 调用后端接口获取用户在当前店铺的补助金信息
			let { data } = await uni.http.get(uni.api.getMemberStoreSubsidyInfo, {
				params: {
					storeId: infos.value.storeInfo.id
				}
			});

			if (data.success && data.result) {
				storeSubsidyInfo.value = {
					hasSubsidy: data.result.totalAmount > 0,
					totalAmount: parseFloat(data.result.totalAmount || 0).toFixed(2),
					nearestExpireTime: data.result.nearestExpireTime || ''
				};
			} else {
				storeSubsidyInfo.value = {
					hasSubsidy: false,
					totalAmount: '0.00',
					nearestExpireTime: ''
				};
			}
		} catch (error) {
			console.log('获取店铺补助金信息失败：', error);
			storeSubsidyInfo.value = {
				hasSubsidy: false,
				totalAmount: '0.00',
				nearestExpireTime: ''
			};
		}
	}

	// 跳转到店铺补助金列表页面
	function navToStoreSubsidyList() {
		if (!users.token) {
			uni.redirectTo({
				url: "/pages/login/login"
			});
			return;
		}
		
		uni.navigateTo({
			url: `/packageUser/pages/storeSubsidy/storeSubsidyList?storeId=${infos.value?.storeInfo?.id || ''}`
		});
	}
</script>

<style lang="scss">
	page {
		background-color: #F5F5F5;
	}

	.productInfo {
		padding-bottom: 160rpx;

		&-btnOpe {
			width: 100%;
			padding: 30rpx 100rpx;
			display: flex;
			align-items: center;
			background-color: white;

			&-btn {

				height: 80rpx;
				background: #22a3ff;
				border-radius: 100rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				color: white;
				font-size: 28rpx;
			}
		}

		&-operation {
			width: 750rpx;
			height: 310rpx;
			background: linear-gradient(180deg, rgba(0, 0, 0, 0.45), rgba(0, 0, 0, 0.00) 100%);
			position: fixed;
			top: 0;
			left: 0;
			z-index: 15;

			&-title {
				padding: 0 30rpx;
				display: flex;
				align-items: center;
				justify-content: space-between;

				&-naT {
					flex: 1;
					margin: 0 14rpx;
					font-size: 32rpx;
					color: white;
					line-height: 38rpx;
					// padding-left: 260rpx;
				}

				&-back {
					width: 60rpx;
					height: 60rpx;
					background: rgba(255, 255, 255, .5);
					border-radius: 50%;
					display: flex;
					align-items: center;
					justify-content: center;

					>image {
						transform: rotateZ(180deg);

						height: 33rpx;
					}
				}
			}
		}

		&-banner {
			&-image {
				width: 750rpx;
				height: 750rpx;
			}
		}

		&-bottom {
			position: fixed;
			left: 0;
			bottom: 0;
			width: 750rpx;
			z-index: 15;
			background-color: white;
			padding: 30rpx;
			display: flex;
			align-items: center;
			justify-content: space-between;

			&-right {
				display: flex;
				align-items: center;
				justify-content: flex-end;
				border-radius: 100rpx;
				width: 360rpx;
				height: 80rpx;
				overflow: hidden;

				>view {
					flex: 1;
					display: flex;
					align-items: center;
					justify-content: center;
					height: 100%;
					color: #FFFFFF;
					font-size: 28rpx;
				}

				&-lt {
					background-color: #92D1FF;
				}

				&-rt {
					background-color: #22A3FF;
				}

			}

			&-left {
				display: flex;
				align-items: center;

				>view {
					display: flex;
					flex-direction: column;
					align-items: center;
					justify-content: center;
					color: #999999;
					font-size: 26rpx;
					margin-right: 38rpx;

					>image {
						width: 48rpx;
						height: 48rpx;
						margin-bottom: 12rpx;
					}
				}
			}
		}

		&-container {
			padding: 16rpx;

			&-storeInfo {
				display: flex;
				align-items: center;
				justify-content: space-between;

				&-img {
					width: 105rpx;
					height: 105rpx;
					margin-right: 20rpx;
					border-radius: 16rpx;
				}

				&-right {
					flex: 1;
					display: flex;
					flex-direction: column;
					justify-content: space-between;
					min-height: 80rpx;

					&-name {
						color: #333333;
						font-size: 28rpx;
					}

					&-desc {
						font-size: 24rpx;
						color: #999999;
						line-height: 28rpx;
					}

					&-name,
					&-desc {
						width: 500rpx;
						white-space: nowrap;
						/* 防止文本换行 */
						overflow: hidden;
						/* 隐藏超出的部分 */
						text-overflow: ellipsis;
						/* 使用省略号表示超出部分 */
					}
				}
			}

			&-wrap {
				padding: 30rpx;
				background-color: white;
				border-radius: 16rpx;
				margin-bottom: 24rpx;

				&-title {
					font-size: 32rpx;
					color: #000000;
					margin-bottom: 26rpx;
				}

				&-img {
					width: 100%;
				}
			}

			&-info {
				&-store {
					display: flex;
					align-items: center;
					color: #666666;
					font-size: 26rpx;
					margin-bottom: 20rpx;

					>image {
						width: 42rpx;
						height: 45rpx;
						margin-right: 15rpx;
					}
				}

				&-title {
					color: #333333;
					font-size: 32rpx;
					line-height: 38rpx;
					margin-bottom: 20rpx;
				}

				&-score {
					display: flex;
					align-items: center;
					font-size: 40rpx;
					color: #E6A600;
					line-height: 48rpx;
					margin-bottom: 30rpx;

					>image {
						width: 40rpx;
						height: 40rpx;
						margin-right: 15rpx;
					}
				}

				&-sh {
					display: flex;
					align-items: center;
					color: #999999;
					font-size: 22rpx;

					>image {
						width: 30rpx;
						height: 30rpx;
						margin-right: 10rpx;
					}
				}
			}

			&-subsidy {
				padding: 20rpx;
				background-color: white;
				border-radius: 16rpx;
				margin-bottom: 24rpx;

				&-content {
					display: flex;
					align-items: center;
					justify-content: space-between;

					&-text {
						flex: 1;
						display: flex;
						flex-direction: column;

						&-main {
							font-size: 28rpx;
							color: #333333;
							margin-bottom: 10rpx;
							
							.amount {
								color: #FF6B35;
								font-weight: 600;
							}
						}

						&-sub {
							font-size: 24rpx;
							color: #999999;
						}
					}

					&-btn {
						background-color: #22A3FF;
						border-radius: 100rpx;
						padding: 10rpx 20rpx;
						font-size: 28rpx;
						color: white;
					}
				}
			}
		}
	}
</style>