<template>
	<view class="shopIndex">
		<view style="position: sticky;z-index: 15;top: 0;">
			<view class="shopIndex-top">
				<view v-for="(item,index) in tabList" :key="index" @click="tabSelectedIndex = index"
					:class="{selcted:tabSelectedIndex === index}">
					{{item}}
					<view class="undl">

					</view>
				</view>
				<view class="shopIndex-top-atten" @click="attentionStore">
					{{ infos.isAttention == 1 ? '取消关注' : '关注'}}
				</view>
			</view>

			<view class="shopIndex-top" v-if="tabSelectedIndex === 2">
				<view v-for="(item,index) in tabListSecond" :key="index" @click="tabSecondSelectedIndexChange(index)"
					:class="{selcted:tabSecondSelectedIndex === index}" style="display: flex;
				align-items: center;">
					{{item}}
					<div style="margin-left: 4rpx;display: flex;flex-direction: column;align-items: center;justify-content: center;"
						v-if="index === 2">
						<triangle :color="sortType === 'up' && tabSecondSelectedIndex === 2 ? '#22A3FF' : '#999999'">
						</triangle>
						<div style="transform: rotateZ(180deg);margin-top: 4rpx;">
							<triangle
								:color="sortType === 'down' && tabSecondSelectedIndex === 2 ? '#22A3FF' : '#999999'">
							</triangle>
						</div>
					</div>

				</view>
			</view>
		</view>

		<view class="shopIndex-cnt" v-if="tabSelectedIndex === 0">
			<!-- 优化后的店铺信息卡片 -->
			<view class="store-info-card">
				<!-- 店铺头部信息 -->
				<view class="store-header">
					<view class="store-logo" @click="navigateToStoreCredentials" :data-enterprise="String(infos.storeStraight) === '1' ? '点击查看店铺证件' : ''">
						<image :src="imgUrl + infos.logoAddr" mode="aspectFill"></image>
						<!-- 企业认证标识 -->
						<view v-if="String(infos.storeStraight) === '1'" class="enterprise-badge">
							<text>企业</text>
						</view>
					</view>
					
					<view class="store-basic-info">
						<view class="store-name-row">
							<text class="store-name">{{ infos.storeName }}</text>
							<view class="store-level" v-if="infos.storeLevel">
								<text class="level-text">{{ getLevelText(infos.storeLevel) }}</text>
							</view>
						</view>
						

						
						<view class="store-intro">
							{{ infos.introduce }}
						</view>
					</view>
				</view>
				
				<!-- 店铺数据统计 -->
				<view class="store-stats">
					<view class="stat-item">
						<text class="stat-value">{{ infos.totalPerformance || 0 }}</text>
						<text class="stat-label">创业积分</text>
					</view>
					<view class="stat-divider"></view>
					<view class="stat-item">
						<text class="stat-value">{{ infos.goodsCount || 0 }}</text>
						<text class="stat-label">商品数量</text>
					</view>
					<view class="stat-divider"></view>
					<view class="stat-item">
						<text class="stat-value">{{ infos.salesVolume || 0 }}</text>
						<text class="stat-label">商品销量</text>
					</view>
				</view>
			</view>
			
			<!-- 店铺宣传图片区域 -->
			<view class="shop-propaganda-images">
				<!-- 如果有宣传图片，则展示宣传图片 -->
				<template v-if="propagandaImages.length > 0">
					<image
						v-for="(item, index) in propagandaImages"
						:key="index"
						class="propaganda-image"
						:src="imgUrl + item"
						mode="widthFix">
					</image>
				</template>
				<!-- 如果没有宣传图片，则展示店铺主图 -->
				<image
					v-else
					class="propaganda-image"
					:src="imgUrl + infos.storePicture"
					mode="widthFix">
				</image>
			</view>
		</view>
		<!-- 新的分类页面布局 - 完全按照美团优选效果 -->
		<view class="category-container" v-if="tabSelectedIndex === 1">
			<!-- 左侧一级分类侧边栏 -->
			<view class="category-sidebar">
				<view 
					v-for="(item, index) in typeAllList" 
					:key="index"
					class="category-sidebar-item"
					:class="{ 'category-sidebar-item-active': productTypeSelectedIndex === index }"
					@click="productTypeSelectedIndex = index">
					<text class="category-sidebar-text">{{ item.name }}</text>
				</view>
			</view>
			
			<!-- 右侧商品展示区域 -->
			<view class="category-content">
				<!-- 二级分类横向滚动 -->
				<scroll-view 
					v-if="typeAllList?.[productTypeSelectedIndex]?.chlidGoodType?.length"
					scroll-x="true" 
					class="category-sub-tabs"
					:show-scrollbar="false">
					<view class="category-sub-tabs-container">
						<!-- 显示前4个分类 -->
						<view 
							v-for="(item, index) in typeAllList[productTypeSelectedIndex].chlidGoodType.slice(0, 4)"
							:key="index"
							class="category-sub-tab"
							:class="{ 'category-sub-tab-active': productBottomLeftSelectedIndex === index }"
							@click="productBottomLeftSelectedIndex = index">
							{{ item.name }}
						</view>
						
						<!-- 当分类超过4个时显示"更多"按钮 -->
						<view 
							v-if="typeAllList[productTypeSelectedIndex].chlidGoodType.length > 4"
							class="category-sub-tab category-more-btn"
							@click="showMoreCategories">
							更多
							<text class="more-arrow">▼</text>
						</view>
					</view>
				</scroll-view>
				
				<!-- 商品纵向列表 -->
				<scroll-view scroll-y="true" class="category-goods-list">
					<!-- 调试信息 -->
					<view v-if="commodityList.length === 0" class="debug-info">
						<text>商品列表为空，数据长度: {{ commodityList.length }}</text>
					</view>
					
					<view 
						v-for="(item, index) in commodityList" 
						:key="index"
						class="category-goods-item"
						@click="navToGoodDetail(item)">
						<!-- 商品图片 -->
						<image 
							class="goods-image" 
							:src="imgUrl + parseImgurl(item.mainPicture)?.[0]" 
							mode="aspectFill">
						</image>
						
						<!-- 商品信息 -->
						<view class="goods-info">
							<view class="goods-title">{{ item.goodName }}</view>
							<view class="goods-subtitle" v-if="item.subtitle || item.description">{{ item.subtitle || item.description }}</view>
							<view class="goods-sales-stock">
								<text class="goods-sales">已售 {{ item.salesVolume || item.sales || 0 }}</text>
								<text class="goods-stock" :class="{ 'low-stock': (item.stock || 0) < 10, 'no-stock': (item.stock || 0) === 0 }">
									库存 {{ item.stock || 0 }}
								</text>
							</view>
							
							<!-- 价格和购物车 -->
							<view class="goods-bottom">
								<view class="goods-price">
									<image src="@/static/index/i_1.png" class="price-icon"></image>
									<text class="price-text">{{ item.price || item.smallPrice || item.salePrice || '0' }}</text>
								</view>
								
								<!-- 购物车按钮 -->
								<view class="goods-cart-wrapper">
									<!-- 库存为0时显示缺货 -->
									<view 
										v-if="(item.stock || 0) <= 0"
										class="goods-cart out-of-stock">
										<text>缺货</text>
									</view>
									
									<!-- 有规格的商品显示选规格按钮 -->
									<view 
										v-else-if="item.isSpecification === '1'"
										class="goods-cart spec-select"
										@click.stop="handleAddToCart(item)">
										<text>选规格</text>
									</view>
									
									<!-- 无规格商品的购物车控制 -->
									<block v-else>
										<!-- 直接显示加减控制 -->
										<view class="goods-cart-control">
											<!-- 减少按钮 -->
											<view 
												class="cart-btn minus"
												:class="{ 'disabled': !getCartCount(item.id) }"
												@click.stop="handleUpdateCart(item, -1)">
												<text>-</text>
											</view>
											
											<!-- 数量显示 -->
											<text class="cart-count">{{ getCartCount(item.id) }}</text>
											
											<!-- 添加按钮 -->
											<view 
												class="cart-btn plus"
												:class="{ 'disabled': getCartCount(item.id) >= (item.stock || 0) }"
												@click.stop="getCartCount(item.id) < (item.stock || 0) ? handleUpdateCart(item, 1) : showStockTip()">
												<text>+</text>
											</view>
										</view>
									</block>
								</view>
							</view>
						</view>
					</view>
				</scroll-view>
			</view>
		</view>
		<view class="shopIndex-cnt shopIndex-pds" v-if="tabSelectedIndex === 2">
			<view v-for="(item,index) in bbList" :key="index">
				<productDetail :info='item' width="340rpx" height="460rpx" sizeType='normal'></productDetail>
			</view>
		</view>
		<view class="shopIndex-cnt shopIndex-new" v-if="tabSelectedIndex === 3">
			<view class="shopIndex-new-date">
				01月01日
			</view>
			<view class="shopIndex-new-wrap">
				<image class="shopIndex-new-wrap-img"
					src="https://cdn.zebraui.com/zebra-ui/images/swipe-demo/swipe1.jpg" mode="widthFix"></image>
				<view class="shopIndex-new-wrap-name">
					这里是产品名称这里是商品名称这里是产品名称这里是商
				</view>
				<view class="shopIndex-new-wrap-lv">
					<view>
						<image src="@/static/index/i_1.png" mode=""></image>
						<view>
							1234
						</view>
					</view>
					<view>
						已售 1234
					</view>
				</view>
			</view>
		</view>

		<!-- 购物车浮动按钮 - 使用现有组件 -->
		<cartAction v-if="tabSelectedIndex === 1"></cartAction>

		<!-- 快捷结算栏 - 只在分类页面显示 -->
		<quickCheckoutBar 
			v-if="tabSelectedIndex === 1"
			:show="showQuickCheckout"
			:totalCount="quickCheckoutCount"
			:totalAmount="quickCheckoutTotal"
			:currentPageItems="currentPageCartItems"
			@checkout="handleQuickCheckout">
		</quickCheckoutBar>

		<!-- 添加成功提示 -->
		<view 
			v-if="showAddSuccess"
			class="add-success-toast">
			<view class="add-success-content">
				<image src="/static/cart/cart.png" class="add-success-icon"></image>
				<text class="add-success-text">已添加到购物车</text>
			</view>
		</view>

		<!-- 规格选择弹窗 -->
		<uni-popup ref="specificationPop" type="bottom" @change="specificationWrapChange">
			<specificationWrap 
				ref="specificationWrapRef" 
				:info="selectInfo" 
				:hasRequest="false">
			</specificationWrap>
			<view class="spec-popup-buttons">
				<view class="spec-popup-button spec-popup-button-cart" @click="handleSpecAddToCart">
					<text>加入购物车</text>
				</view>
				<view class="spec-popup-button spec-popup-button-buy" @click="toOrder()">
					<text>立即购买</text>
				</view>
			</view>
		</uni-popup>

		<!-- 更多分类选择弹窗 -->
		<uni-popup ref="moreCategoriesPop" type="top" @change="moreCategoriesPopChange">
			<view class="more-categories-popup">
				<view class="more-categories-header">
					<text class="more-categories-title">选择分类</text>
					<text class="more-categories-close" @click="closeMoreCategories">✕</text>
				</view>
				<view class="more-categories-content">
					<view 
						v-for="(item, index) in typeAllList?.[productTypeSelectedIndex]?.chlidGoodType || []"
						:key="index"
						class="more-category-item"
						:class="{ 'more-category-item-active': productBottomLeftSelectedIndex === index }"
						@click="selectMoreCategory(index)">
						{{ item.name }}
					</view>
				</view>
			</view>
		</uni-popup>

	</view>
</template>

<script setup>
	import {
		computed,
		nextTick,
		ref,
		watch,
		onMounted
	} from 'vue';
	import {
		onShow,
		onLoad,
		onReachBottom
	} from '@dcloudio/uni-app';
	import productDetail from '@/components/productDetail/productDetail.vue';
	import cartAction from '@/components/cartAction/cartAction.vue';
	import triangle from '@/components/triangle/triangle.vue';
	import specificationWrap from '@/components/specificationWrap/specificationWrap.vue';
	import quickCheckoutBar from '@/components/quickCheckoutBar/quickCheckoutBar.vue';
	import {
		listGet,
		navToGoodDetail,
		saleChannelDealAbout,
		specificationPopPub
	} from '@/hooks'
	import {
		parseImgurl
	} from '@/utils'
	import {
		cartCountResult,
		cartCountHooks
	} from '@/hooks/getCartCount.js'
	const imgUrl = uni.env.IMAGE_URL
	const tabList = [
		'首页',
		'分类',
		'宝贝',
		'新品'
	]
	const tabListSecond = [
		'综合',
		'销量',
		'助力值',
	]
	const tabSelectedIndex = ref(0)
	const productTypeSelectedIndex = ref(0)
	const productBottomLeftSelectedIndex = ref(0)
	const tabSecondSelectedIndex = ref(0)
	const sortType = ref('up')
	let options = ref({});
	let typeAllList = ref([])
	let infos = ref({})
	
	// 购物车相关状态
	const showAddSuccess = ref(false)
	
	// 使用现有的购物车数量 hooks
	const { cartCountRefresh } = cartCountHooks()

	// 规格选择弹窗相关
	const {
		selectInfo,
		addCart,
		toOrder,
		showSpecificationWrap,
		specificationWrapChange,
		btnLoading,
		specificationPop,
		specificationWrapRef,
	} = specificationPopPub()

	// 更多分类弹窗相关
	const moreCategoriesPop = ref(null)

	// 添加购物车数量管理
	const cartCountMap = ref({}) // 存储商品ID和对应的购物车数量
	const cartItemIdMap = ref({}) // 存储商品ID和对应的购物车项ID的映射

	// 控制是否显示加减框
	const showQuantityControl = ref({})

	// 快捷结算相关状态 - 改用响应式对象
	const currentPageCartItems = ref({}) // 当前页面添加的购物车商品 {goodId: {quantity, price, cartItemId}}
	
	// 快捷结算计算属性
	const showQuickCheckout = computed(() => {
		const itemCount = Object.keys(currentPageCartItems.value).length
		console.log('showQuickCheckout计算:', itemCount, currentPageCartItems.value)
		console.log('当前tab:', tabSelectedIndex.value)
		return itemCount > 0
	})
	const quickCheckoutCount = computed(() => {
		const count = Object.values(currentPageCartItems.value)
			.reduce((total, item) => total + item.quantity, 0)
		console.log('quickCheckoutCount计算:', count)
		return count
	})
	const quickCheckoutTotal = computed(() => {
		const total = Object.values(currentPageCartItems.value)
			.reduce((total, item) => total + (item.quantity * parseFloat(item.price || 0)), 0)
		console.log('quickCheckoutTotal计算:', total)
		return total
	})

	// 获取商品在购物车中的数量
	const getCartCount = (goodId) => {
		return cartCountMap.value[goodId] || 0
	}

	// 获取商品对应的购物车项ID
	const getCartItemId = (goodId) => {
		return cartItemIdMap.value[goodId]
	}

	// 处理首次添加
	const handleFirstAdd = async (item) => {
		try {
			// 先添加到购物车
			await handleUpdateCart(item, 1)
			// 显示加减控制
			showQuantityControl.value[item.id] = true
		} catch (error) {
			console.error('首次添加失败:', error)
		}
	}

	// 更新购物车数量 - 优化版本
	const handleUpdateCart = async (item, delta) => {
		try {
			const currentCount = getCartCount(item.id)
			const cartItemId = getCartItemId(item.id)
			
			// 如果是减少且当前数量为0，直接返回
			if (delta < 0 && currentCount <= 0) return
			
			const newCount = Math.max(0, currentCount + delta)
			
			// 显示加载提示
			uni.showLoading({ 
				title: delta > 0 ? '添加中...' : '更新中...',
				mask: true
			})
			
			if (delta > 0 && !cartItemId) {
				// 首次添加到购物车
				const { data: addResult } = await uni.http.post(uni.api.addGoodToShoppingCart, {
					goodId: item.id,
					specification: '无',
					isPlatform: 0,
					quantity: 1 // 每次添加1个
				})
				
				if (!addResult?.success) {
					throw new Error(addResult?.message || '添加失败')
				}
				
				// 保存返回的购物车项ID
				cartItemIdMap.value[item.id] = addResult.result
				cartCountMap.value[item.id] = 1
				
				// 更新当前页面购物车商品记录
				updateCurrentPageCartItem(item, 1, addResult.result)
				
			} else if (cartItemId) {
				// 已有购物车项，直接更新数量
				await uni.http.post(uni.api.updateCarGood, {
					id: cartItemId,
					quantity: newCount // 直接传入目标数量
				})
				
				// 更新本地数量，如果为0则清除ID映射
				if (newCount === 0) {
					delete cartItemIdMap.value[item.id]
					delete cartCountMap.value[item.id]
					// 从当前页面记录中移除
					delete currentPageCartItems.value[item.id]
				} else {
					cartCountMap.value[item.id] = newCount
					// 更新当前页面购物车商品记录
					updateCurrentPageCartItem(item, newCount, cartItemId)
				}
			}
			
			// 刷新全局购物车数量
			await cartCountRefresh()
			
			// 显示成功提示
			if (delta > 0) {
				showAddSuccess.value = true
				setTimeout(() => {
					showAddSuccess.value = false
				}, 2000)
			}
			
		} catch (error) {
			console.error('更新购物车失败:', error)
			uni.showToast({
				title: error.message || '操作失败',
				icon: 'none'
			})
			
			// 发生错误时，重新同步购物车状态
			await syncCartState()
			
		} finally {
			uni.hideLoading()
		}
	}

	// 更新当前页面购物车商品记录
	const updateCurrentPageCartItem = (item, quantity, cartItemId) => {
		console.log('updateCurrentPageCartItem调用:', {
			goodId: item.id,
			quantity,
			cartItemId,
			price: item.price || item.smallPrice || item.salePrice || 0
		})
		
		if (quantity > 0) {
			currentPageCartItems.value[item.id] = {
				quantity: quantity,
				price: item.price || item.smallPrice || item.salePrice || 0,
				cartItemId: cartItemId,
				goodName: item.goodName,
				mainPicture: item.mainPicture
			}
		} else {
			delete currentPageCartItems.value[item.id]
		}
		
		console.log('更新后的currentPageCartItems:', currentPageCartItems.value)
		console.log('showQuickCheckout应该是:', Object.keys(currentPageCartItems.value).length > 0)
	}

	// 同步购物车状态
	const syncCartState = async () => {
		try {
			const { data: cartData } = await uni.http.get(uni.api.getCarGoodByMemberId)
			if (cartData?.result?.storeGoods) {
				const newCartCountMap = {}
				const newCartItemIdMap = {}
				
				cartData.result.storeGoods.forEach(store => {
					if (store.myStoreGoods) {
						store.myStoreGoods.forEach(cartItem => {
							if (cartItem.goodStoreListId) {
								const goodId = cartItem.goodStoreListId
								newCartCountMap[goodId] = (newCartCountMap[goodId] || 0) + cartItem.quantity
								// 保存购物车项ID映射（取最新的一个）
								newCartItemIdMap[goodId] = cartItem.id
							}
						})
					}
				})
				
				cartCountMap.value = newCartCountMap
				cartItemIdMap.value = newCartItemIdMap
			}
		} catch (error) {
			console.error('同步购物车状态失败:', error)
		}
	}

	// 生命周期钩子
	onMounted(() => {
		cartCountRefresh();
		syncCartState();
	});

	onShow(() => {
		cartCountRefresh();
		syncCartState();
	});

	// 处理店铺宣传图片
	const propagandaImages = computed(() => {
		if (infos.value?.storePropagandaImages) {
			return infos.value.storePropagandaImages.split(',');
		}
		return [];
	})

	async function refresh() {
		try {
			uni.showLoading({
				mask: true
			})
			
			// 1. 获取店铺基本信息
			const { data } = await uni.http.post(uni.api.storeManageIndex, options.value)
			if (!data || !data.result) {
				throw new Error('获取店铺信息失败')
			}
			
			infos.value = data.result
			uni.setNavigationBarTitle({
				title: data.result.storeName
			})

			// 2. 获取商品分类
			const { data: allList } = await uni.http.get(uni.api.findMoreGoodType, {
				params: {
					goodTypeId: options.value.sysUserId,
					isPlatform: 0,
					type: 0
				}
			})
			
			if (!allList || !allList.result) {
				throw new Error('获取商品分类失败')
			}
			
			typeAllList.value = allList.result
			
			// 3. 确保有分类数据后再加载商品
			await nextTick()
			if (typeAllList.value && typeAllList.value.length > 0) {
				reloadCommodityList()
				
				// 强制刷新商品数据
				setTimeout(() => {
					console.log('强制刷新商品数据')
					getCommodity()
				}, 500)
			}

			// 4. 获取宝贝数据
			getBbData()
			
		} catch (error) {
			console.error('refresh error:', error)
			uni.showToast({
				title: error.message || '加载失败',
				icon: 'none'
			})
		} finally {
			uni.hideLoading()
		}
	}

	// 优化请求参数计算
	const reqOptions = computed(() => {
		try {
			// 获取当前选中的分类
			const currentCategory = typeAllList.value?.[productTypeSelectedIndex.value]
			if (!currentCategory) {
				console.warn('未找到当前选中的分类')
				return {
					isPlatform: 0,
					pattern: 0,
					isWholesale: '0',
					isSelectedProducts: '0'
				}
			}
			
			// 确定使用哪个分类ID
			let goodTypeId
			if (currentCategory.chlidGoodType?.length > 0) {
				// 有二级分类，使用二级分类ID
				goodTypeId = currentCategory.chlidGoodType[productBottomLeftSelectedIndex.value]?.id
			}
			
			// 如果没有二级分类ID，使用一级分类ID
			if (!goodTypeId) {
				goodTypeId = currentCategory.id
			}
			
			return {
				isPlatform: 0,
				pattern: 0,
				isWholesale: '0',
				isSelectedProducts: '0',
				goodTypeId: String(goodTypeId) // 确保 ID 为字符串类型
			}
		} catch (error) {
			console.error('计算请求参数错误:', error)
			return {
				isPlatform: 0,
				pattern: 0,
				isWholesale: '0',
				isSelectedProducts: '0'
			}
		}
	})

	watch(() => ({
		productTypeSelectedIndex: productTypeSelectedIndex.value,
		productBottomLeftSelectedIndex: productBottomLeftSelectedIndex.value
	}), () => {
		reloadCommodityList()
	})



	const tabSecondSelectedIndexChange = (index) => {
		if (tabSecondSelectedIndex.value === 2 && tabSecondSelectedIndex.value === index) {
			sortType.value = sortType.value === 'up' ? 'down' : 'up'
		} else {
			tabSecondSelectedIndex.value = index
		}
	}

	// 先定义commodityList，确保在watch监听器之前初始化
	const {
		mode,
		list: commodityList = ref([]),
		refresh: getCommodity
	} = listGet({
		options: reqOptions,
		apiUrl: uni.api.findGoodListByGoodType,
		isPullDownRefresh: false,
		isWatchOptions: true,
		isReqTypeReq: false,
		openLoadingShow: false,
		isReachBottom: false
	})

	// 监听商品列表变化，获取购物车数量 - 在commodityList定义之后
	watch(() => commodityList?.value, async (newVal) => {
		if (!newVal || !Array.isArray(newVal) || newVal.length === 0) return;
		
		// 同步购物车状态
		await syncCartState();
	}, { immediate: true, deep: true });

	const {
		list: bbList,
		refresh: getBbData
	} = listGet({
		options: computed(() => {
			let res = {
				pattern: tabSecondSelectedIndex.value,
				// sysUserId: infos.value?.sysUserId,
				isPlatform: 0
			}
			if (tabSecondSelectedIndex.value === 2) {
				if (sortType.value === 'up') {
					res.pattern = 4
				} else {
					res.pattern = 3
				}
			}
			return res
		}),
		apiUrl: uni.api.searchGoodList,
		isReachBottom: false,
		isPullDownRefresh: false,
		isWatchOptions: false,
		isReqTypeReq: false,
		openLoadingShow: false
	})

	watch(() => ({
		tabSecondSelectedIndex: tabSecondSelectedIndex.value,
		sortType: sortType.value
	}), () => {
		getBbData()
	})

	// 重新获取商品列表
	async function reloadCommodityList() {
		try {
			console.log('开始重新加载商品列表')
			console.log('typeAllList:', typeAllList.value)
			console.log('当前一级分类索引:', productTypeSelectedIndex.value)
			console.log('当前二级分类索引:', productBottomLeftSelectedIndex.value)
			
			if (!typeAllList.value || !typeAllList.value.length) {
				console.warn('分类列表为空，无法加载商品')
				return
			}
			
			const currentCategory = typeAllList.value[productTypeSelectedIndex.value]
			if (!currentCategory) {
				console.warn('当前选中的一级分类不存在')
				return
			}
			
			// 检查是否有二级分类
			const hasSubCategory = currentCategory.chlidGoodType?.length > 0
			console.log('是否有二级分类:', hasSubCategory)
			
			if (hasSubCategory) {
				// 有二级分类，检查二级分类是否存在
				const subCategory = currentCategory.chlidGoodType[productBottomLeftSelectedIndex.value]
				if (!subCategory) {
					console.warn('当前选中的二级分类不存在')
					return
				}
				console.log('使用二级分类ID获取商品:', subCategory.id)
			} else {
				console.log('使用一级分类ID获取商品:', currentCategory.id)
			}
			
			// 调用获取商品列表接口
			await getCommodity()
			console.log('商品列表加载完成')
			
		} catch (error) {
			console.error('重新加载商品列表失败:', error)
			uni.showToast({
				title: '加载商品失败',
				icon: 'none'
			})
		}
	}

	// 关注店铺
	const attentionStore = async () => {
		uni.showLoading({
			mask: true
		})
		let apiUrl = uni.api.addMemberAttentionStore
		if (infos.value.isAttention == 1) {
			apiUrl = uni.api.delMemberAttentionStore
		}
		await uni.http.post(apiUrl, {
			ids: infos.value.matId
		})
		uni.showToast({
			title: infos.value.isAttention == 1 ? '取消关注成功~' : '关注成功~',
			icon: 'none'
		})
		setTimeout(() => {
			refresh()
		}, 250)

	}

	saleChannelDealAbout(computed(() => {
		return infos.value?.sysUserId || ''
	}), 2)


	onLoad((o) => {
		try {
			console.log('页面加载参数:', o)
			
			// 验证必要参数
			if (!o || !o.sysUserId) {
				throw new Error('缺少必要参数：店铺ID')
			}
			
			// 初始化页面参数
			options.value = {
				...o,
				sysUserId: String(o.sysUserId) // 确保 sysUserId 为字符串类型
			}
			
			console.log('初始化页面参数:', options.value)
			
			// 重置页面状态
			productTypeSelectedIndex.value = 0
			productBottomLeftSelectedIndex.value = 0
			tabSelectedIndex.value = 0
			
			// 加载页面数据
			refresh()
			
			// 获取购物车数量
			cartCountRefresh()
			
		} catch (error) {
			console.error('页面加载错误:', error)
			uni.showToast({
				title: error.message || '页面加载失败',
				icon: 'none',
				duration: 2000
			})
			
			// 延迟返回上一页
			setTimeout(() => {
				uni.navigateBack({
					delta: 1
				})
			}, 2000)
		}
	})

	onReachBottom(() => {
		console.log(tabSelectedIndex.value)
		if (tabSelectedIndex.value === 1) {
			getCommodity(2)
		} else if (tabSelectedIndex.value === 2) {
			getBbData(2)
		}
	})

	// 导航到店铺证件信息页面
	const navigateToStoreCredentials = () => {
		// 只有当店铺主体类型是企业（storeStraight=1）时才跳转
		// 处理API返回的可能是字符串"1"或数字1的情况
		console.log('店铺类型值:', infos.value.storeStraight, '类型:', typeof infos.value.storeStraight);
		// 将storeStraight转换为字符串进行比较，更安全
		if (String(infos.value.storeStraight) === "1") {
			console.log('判断为企业店铺，准备跳转到证件页面');
			// 将店铺信息传递给证件信息页面
			const storeInfoStr = encodeURIComponent(JSON.stringify(infos.value));
			uni.navigateTo({
				url: `/packageTest/storeCredentials/storeCredentials?storeInfo=${storeInfoStr}`
			});
		} else {
			console.log('非企业店铺，不跳转');
		}
	}

	// 购物车相关方法
	const handleAddToCart = async (item) => {
		try {
			// 检查商品是否有规格
			if (item.isSpecification === '1') {
				// 有规格的商品，显示规格选择弹窗
				showSpecificationWrap(item)
				return
			}
			
			// 无规格商品，直接添加到购物车
			await handleUpdateCart(item, 1)
			
		} catch (error) {
			console.error('添加购物车失败:', error)
			uni.showToast({
				title: '添加失败',
				icon: 'none'
			})
		}
	}

	// 规格选择后添加到购物车
	const handleSpecAddToCart = async () => {
		try {
			await addCart({}, async (result) => {
				// 添加成功后的回调
				showAddSuccess.value = true
				setTimeout(() => {
					showAddSuccess.value = false
				}, 2000)
				
				// 刷新购物车数量
				await cartCountRefresh()
				
				// 更新当前页面购物车商品记录（规格商品）
				if (result && selectInfo.value) {
					const specRef = specificationWrapRef.value
					if (specRef) {
						updateCurrentPageCartItem(
							selectInfo.value, 
							specRef.quality || 1, 
							result.cartItemId || result.id
						)
					}
				}
			})
		} catch (error) {
			console.error('添加购物车失败:', error)
		}
	}

	// 更多分类弹窗方法
	const showMoreCategories = () => {
		moreCategoriesPop.value?.open()
	}

	const closeMoreCategories = () => {
		moreCategoriesPop.value?.close()
	}

	const selectMoreCategory = (index) => {
		productBottomLeftSelectedIndex.value = index
		closeMoreCategories()
	}

	const moreCategoriesPopChange = (e) => {
		// 弹窗状态变化处理
	}

	// 获取店铺等级文本
	const getLevelText = (level) => {
		const levelMap = {
			'1': 'LV1',
			'2': 'LV2', 
			'3': 'LV3',
			'4': 'LV4',
			'5': 'LV5'
		}
		return levelMap[level] || `LV${level}`
	}

	// 获取店铺等级名称
	const getLevelName = (level) => {
		const levelNameMap = {
			'1': '新手商家',
			'2': '成长商家',
			'3': '优质商家',
			'4': '金牌商家',
			'5': '钻石商家'
		}
		return levelNameMap[level] || '普通商家'
	}

	// 获取店铺等级描述
	const getLevelDescription = (level) => {
		const levelDescMap = {
			'1': '刚起步的新商家，充满潜力',
			'2': '正在成长中的商家，值得关注',
			'3': '服务优质的可靠商家',
			'4': '经验丰富的金牌商家',
			'5': '顶级品质的钻石商家'
		}
		return levelDescMap[level] || '努力经营中的商家'
	}

	// 显示库存不足提示
	const showStockTip = () => {
		uni.showToast({
			title: '库存不足',
			icon: 'none',
			duration: 1500
		})
	}

	// 处理快捷结算
	const handleQuickCheckout = (selectedItems) => {
		try {
			// 将选中的商品信息存储到本地，供购物车页面使用
			const checkoutData = {
				selectedItems: selectedItems,
				fromQuickCheckout: true,
				timestamp: Date.now()
			}
			
			uni.setStorageSync('quickCheckoutData', checkoutData)
			
			// 跳转到购物车页面
			uni.navigateTo({
				url: '/pages/cart/cart?fromQuickCheckout=1'
			})
			
		} catch (error) {
			console.error('快捷结算跳转失败:', error)
			uni.showToast({
				title: '跳转失败',
				icon: 'none'
			})
		}
	}

	// 页面切换时清空当前页面购物车记录
	watch(() => tabSelectedIndex.value, (newVal, oldVal) => {
		if (oldVal === 1 && newVal !== 1) {
			// 离开分类页面时清空记录
			currentPageCartItems.value = {}
			console.log('页面切换，清空currentPageCartItems')
		}
	})
</script>

<style lang="scss">
	/* 优化后的店铺信息卡片样式 */
	.store-info-card {
		background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
		border-radius: 24rpx;
		padding: 32rpx;
		margin-bottom: 24rpx;
		box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.06);

		.store-header {
			display: flex;
			align-items: flex-start;
			margin-bottom: 32rpx;

			.store-logo {
				width: 120rpx;
				height: 120rpx;
				border-radius: 20rpx;
				overflow: hidden;
				margin-right: 24rpx;
				position: relative;
				flex-shrink: 0;
				border: 3rpx solid #f0f0f0;

				&[data-enterprise]:not([data-enterprise=""]) {
					transition: transform 0.2s;

					&:active {
						transform: scale(0.95);
					}
				}

				image {
					width: 100%;
					height: 100%;
				}

				.enterprise-badge {
					position: absolute;
					top: -6rpx;
					right: -6rpx;
					background: linear-gradient(45deg, #FF6B35, #FF8A50);
					border-radius: 12rpx;
					padding: 4rpx 8rpx;
					box-shadow: 0 4rpx 8rpx rgba(255, 107, 53, 0.3);

					text {
						font-size: 18rpx;
						color: #ffffff;
						font-weight: 600;
					}
				}
			}

			.store-basic-info {
				flex: 1;
				min-height: 120rpx;
				display: flex;
				flex-direction: column;
				justify-content: space-between;

				.store-name-row {
					display: flex;
					align-items: center;
					margin-bottom: 16rpx;

					.store-name {
						font-size: 36rpx;
						color: #333333;
						font-weight: 600;
						margin-right: 16rpx;
						flex: 1;
					}

					.store-level {
						background: linear-gradient(45deg, #FFD700, #FFA500);
						border-radius: 16rpx;
						padding: 6rpx 16rpx;
						box-shadow: 0 2rpx 8rpx rgba(255, 215, 0, 0.3);

						.level-text {
							font-size: 22rpx;
							color: #ffffff;
							font-weight: 600;
						}
					}
				}



				.store-intro {
					font-size: 26rpx;
					color: #666666;
					line-height: 1.5;
					display: -webkit-box;
					-webkit-box-orient: vertical;
					-webkit-line-clamp: 3;
					overflow: hidden;
				}
			}
		}

		.store-stats {
			display: flex;
			align-items: center;
			justify-content: space-around;
			background: rgba(255, 255, 255, 0.8);
			border-radius: 16rpx;
			padding: 24rpx 0;

			.stat-item {
				display: flex;
				flex-direction: column;
				align-items: center;
				flex: 1;

				.stat-value {
					font-size: 32rpx;
					color: #FF6B35;
					font-weight: 600;
					margin-bottom: 8rpx;
				}

				.stat-label {
					font-size: 24rpx;
					color: #999999;
				}
			}

			.stat-divider {
				width: 2rpx;
				height: 40rpx;
				background-color: #e8e8e8;
			}
		}
	}

	/* 店铺宣传图片样式 */
	.shop-propaganda-images {
		width: 100%;

		.propaganda-image {
			width: 100%;
			border-radius: 16rpx;
			margin-bottom: 20rpx;
			background-color: #f5f5f5; /* 图片加载前的背景色 */
		}
	}

	/* 新的分类页面样式 - 完全按照美团优选效果 */
	.category-container {
		display: flex;
		height: calc(100vh - 200rpx); /* 减去顶部导航高度 */
		background-color: #f5f5f5;
		padding-bottom: 160rpx; /* 为快捷结算栏留出空间 */
	}

	.category-sidebar {
		width: 180rpx;
		background-color: #f0f0f0;

		.category-sidebar-item {
			height: 88rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			position: relative;
			background-color: #f0f0f0;

							&.category-sidebar-item-active {
					background-color: #ffffff;
					
					&::before {
						content: '';
						position: absolute;
						left: 0;
						top: 0;
						bottom: 0;
						width: 4rpx;
						background-color: #FF6B35;
					}

					.category-sidebar-text {
						color: #333333;
						font-weight: 600;
					}
				}

				.category-sidebar-text {
					font-size: 26rpx;
					color: #666666;
					text-align: center;
					line-height: 1.2;
					padding: 0 16rpx;
					font-weight: 500;
				}
		}
	}

	.category-content {
		flex: 1;
		display: flex;
		flex-direction: column;
		background-color: #ffffff;
	}

	.category-sub-tabs {
		background-color: #ffffff;
		border-bottom: 2rpx solid #f0f0f0;
		padding: 16rpx 0;

		.category-sub-tabs-container {
			display: flex;
			padding: 0 24rpx;
			align-items: center;

			.category-sub-tab {
				flex-shrink: 0;
				padding: 10rpx 20rpx;
				margin-right: 16rpx;
				background-color: #f8f8f8;
				border-radius: 24rpx;
				font-size: 24rpx;
				color: #333333;
				white-space: nowrap;
				font-weight: 400;

				&.category-sub-tab-active {
					background-color: #FF6B35;
					color: #ffffff;
					font-weight: 500;
				}

				&.category-more-btn {
					background-color: #e8e8e8;
					color: #666666;
					display: flex;
					align-items: center;

					.more-arrow {
						font-size: 20rpx;
						margin-left: 8rpx;
					}
				}
			}
		}
	}

	.category-goods-list {
		flex: 1;
		padding: 20rpx;
		display: flex;
		flex-direction: column;
		overflow-y: auto;

		.debug-info {
			padding: 40rpx;
			text-align: center;
			color: #999999;
			font-size: 28rpx;
		}

		.category-goods-item {
			background-color: #ffffff;
			border-radius: 16rpx;
			overflow: hidden;
			box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
			margin-bottom: 20rpx;
			display: flex;
			align-items: flex-start;
			padding: 24rpx;

			.goods-image {
				width: 140rpx;
				height: 140rpx;
				border-radius: 12rpx;
				margin-right: 24rpx;
				flex-shrink: 0;
			}

			.goods-info {
				flex: 1;
				display: flex;
				flex-direction: column;
				justify-content: space-between;
				min-height: 140rpx;

				.goods-title {
					font-size: 32rpx;
					color: #333333;
					margin-bottom: 12rpx;
					font-weight: 500;
					line-height: 1.4;
					word-wrap: break-word;
					word-break: break-all;
				}

				.goods-subtitle {
					font-size: 24rpx;
					color: #999999;
					margin-bottom: 8rpx;
					line-height: 1.3;
				}

				.goods-sales-stock {
					display: flex;
					align-items: center;
					justify-content: space-between;
					margin-bottom: 12rpx;
					padding: 8rpx 12rpx;
					background: rgba(0, 0, 0, 0.02);
					border-radius: 8rpx;

					.goods-sales {
						font-size: 22rpx;
						color: #666666;
						display: flex;
						align-items: center;
						
						&::before {
							content: "📊";
							margin-right: 4rpx;
							font-size: 18rpx;
						}
					}

					.goods-stock {
						font-size: 22rpx;
						font-weight: 600;
						padding: 2rpx 8rpx;
						border-radius: 6rpx;
						display: flex;
						align-items: center;
						background: rgba(34, 163, 255, 0.1);
						color: #22A3FF;
						
						&::before {
							content: "📦";
							margin-right: 4rpx;
							font-size: 18rpx;
						}

						&.low-stock {
							background: rgba(255, 143, 0, 0.1);
							color: #FF8F00;
							
							&::before {
								content: "⚠️";
							}
						}

						&.no-stock {
							background: rgba(255, 71, 87, 0.1);
							color: #FF4757;
							
							&::before {
								content: "❌";
							}
						}
					}
				}

				.goods-bottom {
					display: flex;
					align-items: center;
					justify-content: space-between;
					margin-top: 16rpx;

					.goods-price {
						display: flex;
						align-items: center;

						.price-icon {
							width: 28rpx;
							height: 28rpx;
							margin-right: 8rpx;
						}

						.price-text {
							font-size: 36rpx;
							color: #FF6B35;
							font-weight: 600;
						}
					}

					.goods-cart-wrapper {
						display: flex;
						align-items: center;
						margin-left: 16rpx;
					}

					.goods-cart-control {
						display: flex;
						align-items: center;
						height: 56rpx;
						background: #F5F5F5;
						border-radius: 28rpx;
						padding: 0 8rpx;
						
						.cart-btn {
							width: 48rpx;
							height: 48rpx;
							border-radius: 24rpx;
							display: flex;
							align-items: center;
							justify-content: center;
							transition: all 0.2s ease;
							
							&.minus {
								background: #FFFFFF;
								
								&.disabled {
									opacity: 0.5;
									pointer-events: none;
								}
								
								text {
									color: #666666;
									font-size: 28rpx;
									font-weight: 600;
								}
							}
							
							&.plus {
								background: linear-gradient(135deg, #FF6B35 0%, #FF8F6B 100%);
								box-shadow: 0 4rpx 8rpx rgba(255, 107, 53, 0.2);
								
								text {
									color: #FFFFFF;
									font-size: 28rpx;
									font-weight: 600;
								}
							}
							
							&:active {
								transform: scale(0.95);
								opacity: 0.9;
							}
						}
						
						.cart-count {
							min-width: 48rpx;
							text-align: center;
							font-size: 28rpx;
							color: #333333;
							font-weight: 500;
						}
					}
				}
			}
		}
	}

	/* 添加成功提示样式 */
	.add-success-toast {
		position: fixed;
		top: 50%;
		left: 50%;
		transform: translate(-50%, -50%);
		z-index: 1000;
		animation: fadeInOut 2s ease-in-out;

		.add-success-content {
			background: rgba(0, 0, 0, 0.8);
			backdrop-filter: blur(10rpx);
			padding: 32rpx 48rpx;
			border-radius: 20rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			flex-direction: column;
			box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.2);

			.add-success-icon {
				width: 64rpx;
				height: 64rpx;
				margin-bottom: 16rpx;
				animation: bounceIn 0.5s ease-out;
			}

			.add-success-text {
				font-size: 28rpx;
				color: #FFFFFF;
				text-align: center;
				font-weight: 500;
			}
		}
	}

	@keyframes fadeInOut {
		0% {
			opacity: 0;
			transform: translate(-50%, -50%) scale(0.8);
		}
		20% {
			opacity: 1;
			transform: translate(-50%, -50%) scale(1.05);
		}
		30% {
			transform: translate(-50%, -50%) scale(1);
		}
		80% {
			opacity: 1;
			transform: translate(-50%, -50%) scale(1);
		}
		100% {
			opacity: 0;
			transform: translate(-50%, -50%) scale(0.8);
		}
	}

	@keyframes bounceIn {
		0% {
			opacity: 0;
			transform: scale(0.3);
		}
		50% {
			opacity: 0.9;
			transform: scale(1.1);
		}
		80% {
			opacity: 1;
			transform: scale(0.9);
		}
		100% {
			opacity: 1;
			transform: scale(1);
		}
	}

	/* 规格选择弹窗按钮样式 */
	.spec-popup-buttons {
		display: flex;
		padding: 30rpx;
		background-color: #ffffff;
		border-top: 2rpx solid #f0f0f0;

		.spec-popup-button {
			flex: 1;
			height: 88rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			border-radius: 44rpx;
			font-size: 32rpx;
			font-weight: 500;

			&.spec-popup-button-cart {
				background-color: #FFF2E6;
				color: #FF6B35;
				margin-right: 20rpx;
			}

			&.spec-popup-button-buy {
				background-color: #22A3FF;
				color: #ffffff;
			}
		}
	}

	/* 更多分类弹窗样式 */
	.more-categories-popup {
		background-color: #ffffff;
		border-radius: 0 0 24rpx 24rpx;
		box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.1);
		max-height: 60vh;
		overflow: hidden;

		.more-categories-header {
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding: 32rpx 32rpx 24rpx;
			border-bottom: 2rpx solid #f0f0f0;

			.more-categories-title {
				font-size: 32rpx;
				color: #333333;
				font-weight: 600;
			}

			.more-categories-close {
				font-size: 32rpx;
				color: #999999;
				padding: 8rpx;
			}
		}

		.more-categories-content {
			padding: 24rpx 32rpx 32rpx;
			display: flex;
			flex-wrap: wrap;
			gap: 16rpx;
			max-height: 50vh;
			overflow-y: auto;

			.more-category-item {
				padding: 12rpx 24rpx;
				background-color: #f8f8f8;
				border-radius: 24rpx;
				font-size: 26rpx;
				color: #333333;
				font-weight: 400;

				&.more-category-item-active {
					background-color: #FF6B35;
					color: #ffffff;
					font-weight: 500;
				}
			}
		}
	}

	page {
		background-color: #F5F5F5;
	}

	.shopIndex {
		.selcted {
			color: #000000 !important;

			.undl {
				background-color: #22A3FF !important;
			}
		}

		.productTypeSelected {
			color: #000000 !important;
		}

		.productBottomLeftSelected {
			background-color: #22A3FF !important;
			color: white !important;
			border-bottom: none !important;
		}

		&-new {

			&-date {
				font-size: 32rpx;
				color: #333333;
				margin-bottom: 30rpx;
			}

			&-wrap {
				padding: 24rpx;
				background-color: white;
				border-radius: 32rpx;

				&-img {
					width: 100%;
					border-radius: 32rpx 32rpx 0px 0px;
					margin-bottom: 24rpx;
				}

				&-name {
					font-size: 28rpx;
					color: #333333;
					line-height: 38rpx;
					margin-bottom: 18rpx;
				}

				&-lv {
					display: flex;
					align-items: center;
					justify-content: space-between;

					>view:nth-child(1) {
						display: flex;
						align-items: center;
						color: #E6A600;
						font-size: 28rpx;

						>image {
							width: 30rpx;
							height: 30rpx;
							margin-right: 15rpx;
						}
					}

					>view:nth-child(2) {
						color: #999999;
						font-size: 28rpx;
					}
				}
			}

		}

		&-pds {
			display: grid;
			/* 三列，均分容器宽度 */
			grid-template-columns: repeat(2, 1fr);
			gap: 20rpx;

			>view {
				display: flex;
				justify-content: center;
				background-color: white;
				border-radius: 20rpx;
			}
		}

					&-cnt {
			padding: 30rpx;

			&-productType {
				white-space: nowrap;
				width: 100%;
				margin-bottom: 20rpx;
				background-color: white;


				&-container {
					height: 100rpx;
					display: flex;
					align-items: center;
					padding: 0 30rpx;

					>view {
						color: #999999;
						font-size: 28rpx;
						margin-right: 54rpx;
					}
				}
			}

			&-storeInfo {
				background: #ffffff;
				border-radius: 16rpx;
				padding: 20rpx;
				display: flex;
				align-items: center;
				margin-bottom: 20rpx;

				&-img {
					width: 220rpx;
					height: 290rpx;
					margin-right: 20rpx;
					border-radius: 16rpx;
					overflow: hidden;
					border: 2rpx solid #f5f5f5;
					display: flex;
					align-items: center;
					justify-content: center;
					position: relative;

					// 企业店铺点击效果
					&[data-enterprise]:not([data-enterprise=""]) {
						transition: transform 0.2s;

						&:active {
							transform: scale(0.98);
							opacity: 0.9;
						}
					}

					// 企业店铺可点击查看资质
					&::after {
						content: attr(data-enterprise);
						position: absolute;
						bottom: 0;
						left: 0;
						right: 0;
						background-color: rgba(0, 0, 0, 0.5);
						color: #fff;
						font-size: 22rpx;
						text-align: center;
						padding: 6rpx 0;
						transform: translateY(100%);
						transition: transform 0.3s;
					}

					&[data-enterprise]:not([data-enterprise=""]):active::after {
						transform: translateY(0);
					}

					// 当是企业店铺时，显示提示标记
					&[data-enterprise]:not([data-enterprise=""])::before {
						content: '企业';
						position: absolute;
						top: 10rpx;
						right: 10rpx;
						padding: 4rpx 10rpx;
						background-color: #22A3FF;
						color: #FFFFFF;
						font-size: 20rpx;
						border-radius: 20rpx;
						z-index: 1;
						box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.2);
					}

					>image {
						width: 100%;
						height: 100%;
					}
				}

				&-right {
					flex: 1;
					min-height: 290rpx;
					display: flex;
					flex-direction: column;
					justify-content: space-between;

					&-fir {
						color: #333333;
						font-size: 32rpx;
						margin-bottom: 10rpx;
						padding-top: 5rpx;
					}

					&-sec {
						color: #999999;
						font-size: 26rpx;
						margin-bottom: 15rpx;
					}

					&-thi {
						color: #333333;
						font-size: 24rpx;
					}
				}
			}
		}

		&-top {
			height: 100rpx;
			background-color: white;
			display: flex;
			align-items: center;
			justify-content: space-around;




			>view {
				font-size: 32rpx;
				color: #999999;

				.undl {
					margin: 0 auto;
					margin-top: 20rpx;
					width: 40rpx;
					height: 8rpx;
					background: transparent;
				}
			}

			&-atten {
				// width: 102rpx;
				padding: 0 30rpx;
				height: 50rpx;
				line-height: 50rpx;
				background: #22a3ff;
				border-radius: 100rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				color: white !important;
				font-size: 24rpx !important;
				position: relative;
				top: -18rpx;
			}
		}
	}

	.goods-cart-wrapper {
		display: flex;
		align-items: center;
		margin-left: 16rpx;
		
		.goods-cart {
			&.spec-select {
				background: linear-gradient(135deg, #FF6B35 0%, #FF8F6B 100%);
				box-shadow: 0 4rpx 8rpx rgba(255, 107, 53, 0.2);
				padding: 0 24rpx;
				height: 56rpx;
				border-radius: 28rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				transition: all 0.2s ease;
				
				text {
					color: #FFFFFF;
					font-size: 24rpx;
					font-weight: 500;
				}
				
				&:active {
					transform: scale(0.95);
					opacity: 0.9;
				}
			}

			&.out-of-stock {
				background: linear-gradient(135deg, #F5F5F5 0%, #E8E8E8 100%);
				border: 1rpx solid #D1D1D1;
				padding: 0 24rpx;
				height: 56rpx;
				border-radius: 28rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				pointer-events: none;
				position: relative;
				overflow: hidden;
				
				&::before {
					content: "";
					position: absolute;
					top: 50%;
					left: 50%;
					width: 2rpx;
					height: 80%;
					background: #999999;
					transform: translate(-50%, -50%) rotate(45deg);
					opacity: 0.3;
				}
				
				text {
					color: #999999;
					font-size: 24rpx;
					font-weight: 500;
					position: relative;
					z-index: 1;
					
					&::before {
						content: "⛔";
						margin-right: 6rpx;
						font-size: 20rpx;
					}
				}
			}
		}
		
		.goods-cart-control {
			display: flex;
			align-items: center;
			height: 56rpx;
			background: #F5F5F5;
			border-radius: 28rpx;
			padding: 0 8rpx;
			
			.cart-btn {
				width: 48rpx;
				height: 48rpx;
				border-radius: 24rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				transition: all 0.2s ease;
				
				&.minus {
					background: #FFFFFF;
					
					&.disabled {
						opacity: 0.5;
						pointer-events: none;
					}
					
					text {
						color: #666666;
						font-size: 28rpx;
						font-weight: 600;
					}
				}
				
				&.plus {
					background: linear-gradient(135deg, #FF6B35 0%, #FF8F6B 100%);
					box-shadow: 0 4rpx 8rpx rgba(255, 107, 53, 0.2);
					
					text {
						color: #FFFFFF;
						font-size: 28rpx;
						font-weight: 600;
					}
				}
				
				&:active {
					transform: scale(0.95);
					opacity: 0.9;
				}
			}
			
			.cart-count {
				min-width: 48rpx;
				text-align: center;
				font-size: 28rpx;
				color: #333333;
				font-weight: 500;
			}
		}
	}

	@keyframes slideIn {
		from {
			opacity: 0;
			transform: scale(0.8);
		}
		to {
			opacity: 1;
			transform: scale(1);
		}
	}
</style>