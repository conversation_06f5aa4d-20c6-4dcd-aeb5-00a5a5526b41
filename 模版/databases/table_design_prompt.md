我有一个[应用类型]系统需求，请帮我设计最优数据库表结构。

【任务说明】
分析我提供的需求（图片/文本），设计符合以下要求的数据库：
1. 提取核心业务实体与关系
2. 设计符合规范的表结构
3. 生成完整SQL

**必须遵循的数据库设计原则：**

## 1. 基础字段规范
所有表必须包含以下字段：
```sql
`id` varchar(32) NOT NULL COMMENT '主键ID',
`del_flag` varchar(1) DEFAULT '0' COMMENT '删除状态 0=未删除 1=已删除',
`create_by` varchar(32) DEFAULT NULL COMMENT '创建人',
`create_time` datetime DEFAULT NULL COMMENT '创建时间',
`update_by` varchar(32) DEFAULT NULL COMMENT '更新人',
`update_time` datetime DEFAULT NULL COMMENT '更新时间'
```

## 2. 字段命名规范
- 使用小写字母和下划线（`snake_case`）。
- 字段名应简洁、明确，避免使用缩写。
- 主键字段统一命名为 `id`。
- 外键字段命名为 `[关联表名]_id`，例如 `user_id`。

## 3. 字段类型规范
- **主键**：`varchar(32)`，使用 UUID 或雪花算法生成。
- **字符串**：根据实际长度选择 `varchar(n)`，避免使用 `text`。
- **数字**：
  - 整数：`int` 或 `bigint`。
  - 小数：`decimal(m,n)`，`m` 为总位数，`n` 为小数位数。
- **时间**：
  - 日期：`date`。
  - 时间戳：`datetime` 或 `timestamp`。
- **布尔值**：`tinyint(1)`，`0` 表示 `false`，`1` 表示 `true`。

## 4. 枚举字段规范
- 枚举字段使用 `varchar` 类型。
- 字段注释必须说明枚举值含义，并注明对应的字典编码，同时必须生成字典配置 SQL
- 示例：
  ```sql
  `status` varchar(1) DEFAULT NULL COMMENT '状态：0=未启用；1=已启用。字典：sys_status'
  ```
- 字典配置 SQL 示例：
  ```sql
  -- 字典配置 SQL 示例
  -- 说明：id 使用雪花算法生成或去除 `-` 的 UUID 字符串
  
  -- 生成去除 `-` 的 UUID
  SET @dict_id = REPLACE(UUID(), '-', '');
  
  -- 添加字典类型
  INSERT INTO `sys_dict` (`id`, `dict_code`, `dict_name`, `description`, `del_flag`, `create_by`, `create_time`, `update_by`, `update_time`) 
  VALUES (@dict_id, 'sys_status', '状态字典', '系统状态字典', '0', 'admin', NOW(), 'admin', NOW());
  
  -- 生成去除 `-` 的 UUID
  SET @item_id_1 = REPLACE(UUID(), '-', '');
  
  -- 添加字典项
  INSERT INTO `sys_dict_item` (`id`, `dict_id`, `item_text`, `item_value`, `item_color`, `description`, `sort_order`, `status`, `create_by`, `create_time`, `update_by`, `update_time`) 
  VALUES (@item_id_1, @dict_id, '未启用', '0', null, '未启用状态', 1, 1, 'admin', NOW(), 'admin', NOW());
  
  -- 生成去除 `-` 的 UUID
  SET @item_id_2 = REPLACE(UUID(), '-', '');
  
  -- 添加字典项
  INSERT INTO `sys_dict_item` (`id`, `dict_id`, `item_text`, `item_value`, `item_color`, `description`, `sort_order`, `status`, `create_by`, `create_time`, `update_by`, `update_time`) 
  VALUES (@item_id_2, @dict_id, '已启用', '1', null, '已启用状态', 2, 1, 'admin', NOW(), 'admin', NOW());
  ```

## 5. 索引规范
- **主键索引**：每个表必须有主键索引。
- **唯一索引**：对唯一性字段（如 `dict_code`）创建唯一索引。
- **外键索引**：对关联字段（如 `dict_id`）创建索引。
- **复合索引**：根据查询需求创建，避免过多索引影响性能。

## 6. 表注释规范
- 每个表必须有注释，说明表的用途。
- 示例：
  ```sql
  COMMENT='字典配置主表';
  ```

## 7. 其他规范
- **字符集**：统一使用 `utf8mb4`，支持表情符号。
- **引擎**：推荐使用 `InnoDB`，支持事务和外键。
- **外键约束**：建议在应用层实现，避免数据库性能问题。
- **字段默认值**：根据业务需求设置合理的默认值，如 `NULL` 或 `0`。

## 示例表结构
```sql
CREATE TABLE `example_table` (
  `id` varchar(32) NOT NULL COMMENT '主键ID',
  `name` varchar(100) NOT NULL COMMENT '名称',
  `status` varchar(1) DEFAULT NULL COMMENT '状态：0=未启用；1=已启用。字典：sys_status',
  `del_flag` varchar(1) DEFAULT '0' COMMENT '删除状态 0=未删除 1=已删除',
  `create_by` varchar(32) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(32) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_example_name` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='示例表';
```