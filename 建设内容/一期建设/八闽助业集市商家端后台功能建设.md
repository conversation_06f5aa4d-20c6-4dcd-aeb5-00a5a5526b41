# 八闽助业集市商家端后台功能建设

| 需求描述 | 需求分类 | 需求状态 | 需求详细描述（可附文档） |
| --- | --- | --- | --- |
| 系统登录功能 | 管理后台 | 已完成 | 实现商家端管理系统登录功能，包括账号密码登录、验证码校验、登录状态保持等功能。提供忘记密码、账号锁定处理等安全措施。 |
| 店铺商品-商品分类功能 | 管理后台 | 已完成 | 实现商品分类查看功能，以树形结构展示平台商品分类体系，支持商家在新增和编辑商品时选择所属分类。商家无法自行创建或修改分类，分类由平台统一管理。 |
| 店铺商品-商品列表功能 | 管理后台 | 已完成 | 实现商品列表管理页面，支持查询、新增、编辑、上下架商品等功能。展示商品分类、主图、编号、名称、价格、状态等信息，支持批量操作、商品排序等功能。 |
| 店铺商品-商品草稿箱功能 | 管理后台 | 已完成 | 实现商品草稿箱管理页面，存储和管理尚未提交审核的商品信息。支持查询、继续编辑、删除草稿、提交审核等操作，帮助商家保存未完成的商品编辑工作。 |
| 店铺商品-商品回收站功能 | 管理后台 | 已完成 | 实现商品回收站管理页面，存储和管理已删除的商品信息。支持查询、恢复商品、彻底删除等操作，提供批量恢复和批量彻底删除功能。 |
| 店铺商品-商品审核记录功能 | 管理后台 | 已完成 | 实现商品审核记录管理页面，查看和管理商品的审核历史。展示商品审核状态、审核意见、提交时间、审核时间等信息，支持查看详情和重新提交功能。 |
| 店铺订单-全部订单功能 | 管理后台 | 已完成 | 实现全部订单管理页面，支持查询、处理店铺的所有订单。展示订单编号、类型、买家、商品、状态、金额等信息，支持查看买家信息、商品信息、订单详情、发货、查看物流等操作。 |
| 店铺订单-待发货订单功能 | 管理后台 | 已完成 | 实现待发货订单管理页面，专门用于管理和处理需要商家发货的订单。支持查询、发货、批量发货等操作，提高订单处理效率。 |
| 店铺订单-待收货订单功能 | 管理后台 | 已完成 | 实现待收货订单管理页面，用于管理和查看已发货但买家尚未确认收货的订单。支持查询、查看物流、提醒收货等操作，帮助商家跟踪订单物流状态。 |
| 店铺订单-交易成功订单功能 | 管理后台 | 已完成 | 实现交易成功订单管理页面，用于管理和查看买家已确认收货的订单。支持查询、查看评价、回复评价、处理售后申请等操作，帮助商家了解交易完成情况。 |
| 店铺资金-余额明细功能 | 管理后台 | 已完成 | 实现店铺余额明细管理页面，用于查询和管理店铺账户余额及交易明细。展示可用余额、冻结余额、总余额等信息，支持交易明细查询和提现申请功能。 |
| 店铺资金-资金流水功能 | 管理后台 | 已完成 | 实现店铺资金流水管理页面，用于查询和管理店铺的所有资金交易记录。展示交易流水号、交易类型、金额、状态等信息，支持查看交易详情和导出资金流水功能。 |
| 商品多规格管理功能 | 管理后台 | 已完成 | 在商品新增和编辑页面，支持添加最多两个规格维度（如颜色、尺寸等），为每个规格组合设置销售价、成本价、会员价和库存，上传规格图片等。 |
| 商品图片管理功能 | 管理后台 | 已完成 | 支持上传商品主图（最多5张）、商品详情图（最多20张）和规格图片，提供图片预览、删除、排序等功能，确保商品展示效果。 |
| 订单发货管理功能 | 管理后台 | 已完成 | 提供订单发货操作界面，支持添加包裹、选择发货商品、填写物流信息、确认发货地址和退货地址等功能，支持分批发货和批量发货。 |
| 订单核销码管理功能 | 管理后台 | 已完成 | 为自提订单提供核销码查看功能，显示核销二维码和核销状态（未核销、已核销、已失效），支持核销状态查询。 |
| 订单评价管理功能 | 管理后台 | 已完成 | 提供订单评价查看和回复功能，展示买家评价内容、评分、评价时间等信息，支持商家回复评价，提升店铺服务水平。 |
| 售后服务管理功能 | 管理后台 | 已完成 | 提供售后申请处理功能，支持查看买家的售后申请信息（申请类型、原因、凭证等），处理退款、换货、维修等售后请求。 |
| 提现申请功能 | 管理后台 | 已完成 | 提供店铺余额提现申请功能，支持输入提现金额、选择提现方式、确认银行卡信息等操作，展示提现申请状态和处理结果。 |
| 账号安全管理功能 | 管理后台 | 已完成 | 提供账号安全管理功能，包括密码修改、账号锁定解除、安全设置等，确保商家账号的安全使用。 |
