{
	"pages": [ //pages数组中第一项表示应用启动页，参考：https://uniapp.dcloud.io/collocation/pages
		{
			"path": "pages/index/index",
			"style": {
				"navigationBarTitleText": "助力平台",
				"navigationStyle": "custom",
				"enablePullDownRefresh": true
			}
		},
		{
			"path": "pages/heartPool/heartPoolNew",
			"style": {
				"navigationBarTitleText": "店铺分类",
				"enablePullDownRefresh": true
			}
		},

		{
			"path": "pages/helpPanel/helpPanelNew",
			"style": {
				"navigationBarTitleText": "品牌推荐",
				"enablePullDownRefresh": true
			}
		},

		{
			"path": "pages/user/user",
			"style": {
				"navigationBarTitleText": "个人",
				"navigationStyle": "custom",
				"enablePullDownRefresh": true
			}
		},
		{
			"path": "pages/cart/cart",
			"style": {
				"navigationBarTitleText": "助力车",
				"navigationBarBackgroundColor": "#ffffff"

			}
		},
		{
			"path": "pages/revenueDetail/revenueDetail",
			"style": {
				"navigationBarTitleText": "收益明细",
				"navigationBarBackgroundColor": "#ffffff"
			}
		},
		{
			"path": "pages/heartFeedbackDetail/heartFeedbackDetail",
			"style": {
				"navigationBarTitleText": "助力值明细详情",
				"navigationBarBackgroundColor": "#ffffff"
			}
		},
		{
			"path": "pages/withDraw/withDraw",
			"style": {
				"navigationBarTitleText": "结算",
				"navigationBarBackgroundColor": "#ffffff"
			}
		},
		{
			"path": "pages/selectBankCard/selectBankCard",
			"style": {
				"navigationBarTitleText": "选择银行卡",
				"navigationBarBackgroundColor": "#ffffff"
			}
		},
		{
			"path": "pages/withDrawLs/withDrawLs",
			"style": {
				"navigationBarTitleText": "提现明细",
				"navigationBarBackgroundColor": "#ffffff"
			}
		},
		{
			"path": "pages/pendingHeartValueDetail/pendingHeartValueDetail",
			"style": {
				"navigationBarTitleText": "待结算助力值明细",
				"navigationBarBackgroundColor": "#ffffff"
			}
		},
		{
			"path": "pages/unavailableHeartValueDetail/unavailableHeartValueDetail",
			"style": {
				"navigationBarTitleText": "不可用助力值明细",
				"navigationBarBackgroundColor": "#ffffff"
			}
		},
		{
			"path": "pages/confirmOrder/confirmOrder",
			"style": {
				"navigationBarTitleText": "确认订单"
			}
		},

		{
			"path": "pages/storeCollection/storeCollection",
			"style": {
				"navigationBarTitleText": "店铺收藏",
				"navigationBarBackgroundColor": "#ffffff"
			}
		},
		{
			"path": "pages/myAddress/myAddress",
			"style": {
				"navigationBarTitleText": "收货地址",
				"navigationBarBackgroundColor": "#ffffff"
			}
		},
		{
			"path": "pages/opAddress/opAddress",
			"style": {
				"navigationBarTitleText": "新增地址",
				"navigationBarBackgroundColor": "#ffffff"
			}
		},

 {
			"path": "pages/commissionDetails/commissionDetails",
			"style": {
				"navigationBarTitleText": "佣金明细",
				"navigationBarBackgroundColor": "#ffffff"
			}
		},
		{
			"path": "pages/productionAbout/productionAbout",
			"style": {
				"navigationBarTitleText": "助梦家权益"
			}
		}
	],
	"tabBar": {
		"color": "#7A7E83",
		"selectedColor": "#000000",
		"borderStyle": "white",
		"backgroundColor": "#ffffff",
		"iconWidth": "30px",
		"list": [{
				"pagePath": "pages/index/index",
				"iconPath": "static/tabbar/index_btn.png",
				"selectedIconPath": "static/tabbar/index_btn_sel.png",
				"text": "首页"
			},
			{
				"pagePath": "pages/heartPool/heartPoolNew",
				"iconPath": "static/tabbar/heartPool_btn.png",
				"selectedIconPath": "static/tabbar/heartPool_btn_sel.png",
				"text": "店铺"
			},
			{
				"pagePath": "pages/user/user",
				"iconPath": "static/tabbar/user_btn.png",
				"selectedIconPath": "static/tabbar/user_btn_sel.png",
				"text": "个人"
			}
		]
	},

	"subPackages": [
		{
			"root": "packageTest",
			"pages": [
				{
					"path": "browseHistory/browseHistory",
					"style": {
						"navigationBarTitleText": "浏览记录",
						"navigationBarBackgroundColor": "#ffffff"
					}
				},
				{
					"path": "storeCredentials/storeCredentials",
					"style": {
						"navigationBarTitleText": "店铺证件信息",
						"navigationBarBackgroundColor": "#ffffff"
					}
				}
			]
		},
		{
			"root": "packageRecharge",
			"pages": [
				{
					"path": "pages/recharge/recharge",
					"style": {
						"navigationBarTitleText": "助力",
						"navigationBarBackgroundColor": "#ffffff"
					}
				}
			]
		},
		{
			"root": "packageUser",
			"pages": [
				{
					"path": "pages/userProfile/userProfile",
					"style": {
						"navigationBarTitleText": "个人资料",
						"navigationBarBackgroundColor": "#ffffff"
					}
				},
				{
					"path": "pages/editUserProfile/editUserProfile",
					"style": {
						"navigationBarTitleText": "修改资料",
						"navigationBarBackgroundColor": "#ffffff"
					}
				},
				{
					"path": "pages/setting/setting",
					"style": {
						"navigationBarTitleText": "设置",
						"navigationBarBackgroundColor": "#ffffff"
					}
				},
				{
					"path": "pages/heartMeter/heartMeter",
					"style": {
						"navigationBarTitleText": "助力值",
						"navigationBarBackgroundColor": "#ffffff"
					}
				},
				{
					"path": "pages/heartFeedback/heartFeedback",
					"style": {
						"navigationBarTitleText": "助力值明细",
						"navigationBarBackgroundColor": "#ffffff"
					}
				},
				{
					"path": "pages/myTeam/myTeam",
					"style": {
						"navigationBarTitleText": "我的团队",
						"navigationBarBackgroundColor": "#ffffff"
					}
				},
				{
					"path": "pages/myBankCard/myBankCard",
					"style": {
						"navigationBarTitleText": "银行卡管理",
						"navigationBarBackgroundColor": "#ffffff"
					}
				},
				{
					"path": "pages/opBankCard/opBankCard",
					"style": {
						"navigationBarTitleText": "添加银行卡",
						"navigationBarBackgroundColor": "#ffffff"
					}
				},
				{
					"path": "pages/storeSubsidy/storeSubsidyList",
					"style": {
						"navigationBarTitleText": "我的店铺补助金",
						"navigationBarBackgroundColor": "#ffffff"
					}
				},
				{
					"path": "pages/myPoster/myPoster",
					"style": {
						"navigationBarTitleText": "邀请好友",
						"navigationBarBackgroundColor": "#ffffff"
					}
				}
			]
		},
		{
			"root": "packageGoods",
			"pages": [
				{
					"path": "pages/productInfo/productInfo",
					"style": {
						"navigationBarTitleText": "商品详情",
						"navigationStyle": "custom"
					}
				},
				{
					"path": "pages/productCollection/productCollection",
					"style": {
						"navigationBarTitleText": "产品收藏",
						"navigationBarBackgroundColor": "#ffffff"
					}
				},
				{
					"path": "pages/shopIndex/shopIndex",
					"style": {
						"navigationBarTitleText": "店铺首页",
						"navigationBarBackgroundColor": "#ffffff"
					}
				}
			]
		},
		{
			"root": "packageOrder",
			"pages": [
				{
					"path": "pages/myPackage/myPackage",
					"style": {
						"navigationBarTitleText": "我的包裹",
						"navigationBarBackgroundColor": "#ffffff",
						"navigationStyle": "custom"
					}
				},
				{
					"path": "pages/orderDetail/orderDetail",
					"style": {
						"navigationBarTitleText": "包裹详情",
						"navigationBarBackgroundColor": "#ffffff"
					}
				},
				{
					"path": "pages/logistics/logistics",
					"style": {
						"navigationBarTitleText": "物流详情",
						"navigationBarBackgroundColor": "#ffffff"
					}
				},
				{
					"path": "pages/applyForAfterSale/applyForAfterSale",
					"style": {
						"navigationBarTitleText": "申请售后",
						"navigationBarBackgroundColor": "#ffffff"
					}
				},
				{
					"path": "pages/afterSaleDetail/afterSaleDetail",
					"style": {
						"navigationBarTitleText": "售后详情",
						"navigationBarBackgroundColor": "#ffffff"
					}
				},
				{
					"path": "pages/afterSaleOrderDetail/afterSaleOrderDetail",
					"style": {
						"navigationBarTitleText": "售后",
						"navigationBarBackgroundColor": "#ffffff"
					}
				}
			]
		},
		{
			"root": "packageSearch",
			"pages": [
				{
					"path": "pages/login/login",
					"style": {
						"navigationBarTitleText": "登录",
						"navigationStyle": "custom",
						"navigationBarBackgroundColor": "#ffffff"
					}
				},
				{
					"path": "pages/phoneLogin/phoneLogin",
					"style": {
						"navigationBarTitleText": "验证码登录",
						"navigationBarBackgroundColor": "#ffffff"
					}
				},
				{
					"path": "pages/search/search",
					"style": {
						"navigationBarTitleText": "搜索"
					}
				},
				{
					"path": "pages/agreement/agreement",
					"style": {
						"navigationBarTitleText": "协议",
						"navigationBarBackgroundColor": "#ffffff"
					}
				}
			]
		}
	],

	"globalStyle": {
		"navigationBarTextStyle": "black",
		"navigationBarTitleText": "助力平台",
		"navigationBarBackgroundColor": "#f5f5f5",
		"backgroundColor": "#f5f5f5",
		"backgroundColorTop": "#f5f5f5",
		"backgroundColorBottom": "#f5f5f5"
	},
	"preloadRule": {
		"pages/user/user": {
			"network": "all",
			"packages": ["packageRecharge"]
		}
	},
	"uniIdRouter": {}
}
