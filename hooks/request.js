import Request from '@/js_sdk/luch-request/luch-request/index.js'
import pinia from '@/store/store';
import {
	userStore,
	locationInfo,
	getAllBeShared,
	setSaleChannelStoreInfo,
	recordFullPagePath
} from '@/store/index.js'
import {
	toLoginAndBackPage
} from './index.js';
const http = new Request();
const store = userStore(pinia)
const recordFullPagePathStore = recordFullPagePath(pinia)
const locationStore = locationInfo(pinia)
const getAllBeSharedStore = getAllBeShared(pinia)
const setSaleChannelStoreInfoStore = setSaleChannelStoreInfo(pinia)
http.setConfig(config => {
	config.baseURL = uni.env.REQUEST_URL
	config.header = {
		'content-type': 'application/x-www-form-urlencoded'
	}
	return config
})
//请求前拦截器
http.interceptors.request.use(config => {
	config.header = {
		// #ifdef MP-WEIXIN
		'softModel': 0,
		// #endif
		// #ifdef H5
		'softModel': 3,
		// #endif
		sysUserId: getAllBeSharedStore.info?.sysUserId || setSaleChannelStoreInfoStore.sysUserId ||
			setSaleChannelStoreInfoStore.intoStoreForSysUserId, //优先绑定分享,其次是接口获得的销售渠道,再者是进店获得的销售渠道
		tMemberId: getAllBeSharedStore.info?.tMemberId || '', //接收的分享人id
		longitude: locationStore.info.longitude, //精度
		latitude: locationStore.info.latitude, //纬度
		shareDiscountRatio: getAllBeSharedStore.shareDiscountRatio || '',
		"X-AUTH-TOKEN": uni.getStorageSync('token') || '',
		...config.header,
	}
	// #ifdef APP-PLUS
	// const platform = uni.$u.os();
	// if (platform == 'ios') {
	// 	header['softModel'] = 2
	// } else {
	// 	header['softModel'] = 1
	// }
	// #endif
	return config
}, config => {
	return Promise.reject(config)
})
//请求后拦截器
http.interceptors.response.use(response => {
	if (response.data?.code == 666) {
		// 先停止所有可能正在进行的UI操作
		try {
			// 停止下拉刷新动画
			uni.stopPullDownRefresh();
			// 隐藏加载提示
			uni.hideLoading();
		} catch (e) {
			console.error('停止UI操作失败', e);
		}

		// 清除用户信息和token
		store.clearUserInfo();
		store.clearToken();

		// 显示登录过期提示
		uni.showToast({
			icon: 'none',
			title: '登录已过期，请重新登录',
			duration: 1500
		});

		// 延迟跳转到登录页面，确保UI操作已完成
		setTimeout(() => {
			// 使用新的分包路径
			let pages = getCurrentPages();
			let currentRoute = pages[pages.length - 1].route; //页面路径
			let currentFullPath = pages[pages.length - 1]['$page']['fullPath']; //页面路径(带参数)
			// 使用已定义的recordFullPagePathStore
			//如果当前页面不在登录页 则记录当前页面信息后跳转登录页
			if (currentRoute.indexOf('login') == -1) {
				recordFullPagePathStore.setPageFullPath(currentFullPath);
				uni.reLaunch({
					url: '/packageSearch/pages/login/login',
				});
			}
		}, 1500);

		return Promise.reject(response);
	} else if (response.data?.code == 500) {
		// 显示业务异常消息
		uni.showToast({
			icon: 'none',
			title: response.data?.message || '操作失败'
		})
		return Promise.reject(response.data) // 返回业务异常数据，而不是整个response
	} else {
		return response
	}

}, response => {
	// 网络异常或其他非业务异常
	if (response.statusCode != 200) {
		uni.showToast({
			icon: 'none',
			title: '网络异常，请稍后再试'
		})
	}
	return Promise.reject(response)
})
uni.http = http