-- ========================================
-- 店铺补助金字段分离 - 数据库更新脚本
-- 执行日期: 2024年
-- 说明: 为PayOrderCarLog和OrderStoreList表添加店铺补助金字段
-- ========================================

-- 1. 修改店铺补助金状态字段默认值为空
-- 只有当交易类型为店铺补助金相关时才有意义
ALTER TABLE `member_account_capital` 
MODIFY COLUMN `store_subsidy_status` varchar(10) DEFAULT NULL COMMENT '店铺补助金状态：0-可用，1-已过期，2-已用尽（仅对pay_type=50,51,52有效）';

-- 2. 清理历史数据中非店铺补助金记录的状态字段
UPDATE `member_account_capital` 
SET `store_subsidy_status` = NULL 
WHERE `pay_type` NOT IN ('50', '51', '52'); 

-- 3. 为PayOrderCarLog表添加店铺补助金支付金额字段（如果不存在）
ALTER TABLE `pay_order_car_log` 
ADD COLUMN IF NOT EXISTS `store_subsidy_amount` decimal(10,2) DEFAULT 0.00 COMMENT '店铺补助金支付金额';

-- 4. 为OrderStoreList表添加店铺补助金支付金额字段（如果不存在）
ALTER TABLE `order_store_list` 
ADD COLUMN IF NOT EXISTS `store_subsidy_amount` decimal(10,2) DEFAULT 0.00 COMMENT '店铺补助金支付金额';

-- 5. 更新PayOrderCarLog表的balance字段注释，明确其含义
ALTER TABLE `pay_order_car_log` 
MODIFY COLUMN `balance` decimal(10,2) DEFAULT 0.00 COMMENT '通用余额支付金额（不包含店铺补助金）';

-- 6. 更新OrderStoreList表的balance字段注释，明确其含义
ALTER TABLE `order_store_list` 
MODIFY COLUMN `balance` decimal(10,2) DEFAULT 0.00 COMMENT '通用余额支付金额（不包含店铺补助金）';

-- ========================================
-- 验证脚本执行结果
-- ========================================

-- 检查PayOrderCarLog表结构
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'heartful-mall' 
    AND TABLE_NAME = 'pay_order_car_log' 
    AND COLUMN_NAME IN ('balance', 'store_subsidy_amount')
ORDER BY ORDINAL_POSITION;

-- 检查OrderStoreList表结构
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'heartful-mall' 
    AND TABLE_NAME = 'order_store_list' 
    AND COLUMN_NAME IN ('balance', 'store_subsidy_amount')
ORDER BY ORDINAL_POSITION;

-- 检查member_account_capital表的store_subsidy_status字段清理情况
SELECT 
    pay_type,
    COUNT(*) as record_count,
    COUNT(CASE WHEN store_subsidy_status IS NOT NULL THEN 1 END) as has_status_count,
    COUNT(CASE WHEN store_subsidy_status IS NULL THEN 1 END) as null_status_count
FROM member_account_capital 
GROUP BY pay_type
ORDER BY pay_type;

-- ========================================
-- 执行完成提示
-- ========================================
SELECT '店铺补助金字段分离更新完成！' as message; 