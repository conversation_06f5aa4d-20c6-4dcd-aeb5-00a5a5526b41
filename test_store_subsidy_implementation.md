# 店铺补助金字段分离实现测试指南

## 实现概述

本次实现将 `PayOrderCarLog` 和 `OrderStoreList` 表中的 `balance` 字段进行了分离：
- `balance` 字段：仅存储**通用余额支付金额**
- 新增 `storeSubsidyAmount` 字段：存储**店铺补助金支付金额**

## 数据库变更

### 1. 表结构变更
```sql
-- 为PayOrderCarLog表添加店铺补助金支付金额字段
ALTER TABLE `pay_order_car_log` 
ADD COLUMN IF NOT EXISTS `store_subsidy_amount` decimal(10,2) DEFAULT 0.00 COMMENT '店铺补助金支付金额';

-- 为OrderStoreList表添加店铺补助金支付金额字段
ALTER TABLE `order_store_list` 
ADD COLUMN IF NOT EXISTS `store_subsidy_amount` decimal(10,2) DEFAULT 0.00 COMMENT '店铺补助金支付金额';

-- 更新字段注释
ALTER TABLE `pay_order_car_log` 
MODIFY COLUMN `balance` decimal(10,2) DEFAULT 0.00 COMMENT '通用余额支付金额（不包含店铺补助金）';

ALTER TABLE `order_store_list` 
MODIFY COLUMN `balance` decimal(10,2) DEFAULT 0.00 COMMENT '通用余额支付金额（不包含店铺补助金）';
```

### 2. 历史数据处理
```sql
-- 清理历史数据中非店铺补助金记录的状态字段
UPDATE `member_account_capital` 
SET `store_subsidy_status` = NULL 
WHERE `pay_type` NOT IN ('50', '51', '52');
```

## 代码变更

### 1. 实体类更新
- `PayOrderCarLog.java`: 添加 `storeSubsidyAmount` 字段和相应注释
- `OrderStoreList.java`: 添加 `storeSubsidyAmount` 字段和相应注释，更新 `balance` 字段注释

### 2. 支付逻辑更新
- `FrontPayController.orderPaySuccess()`: 在店铺补助金扣款时记录总使用金额
- `OrderStoreListServiceImpl.paySuccessOrder()`: 按比例分配通用余额和店铺补助金支付金额

## 测试步骤

### 1. 数据库测试
```sql
-- 检查表结构是否正确添加了字段
DESCRIBE pay_order_car_log;
DESCRIBE order_store_list;

-- 检查字段注释是否正确
SHOW FULL COLUMNS FROM pay_order_car_log WHERE Field IN ('balance', 'store_subsidy_amount');
SHOW FULL COLUMNS FROM order_store_list WHERE Field IN ('balance', 'store_subsidy_amount');
```

### 2. 功能测试

#### 测试场景1：纯通用余额支付
1. 用户账户有足够的通用余额，没有店铺补助金
2. 下单并支付
3. 验证：
   - `PayOrderCarLog.balance` = 支付的余额金额
   - `PayOrderCarLog.storeSubsidyAmount` = 0
   - `OrderStoreList.balance` = 按比例分配的通用余额金额
   - `OrderStoreList.storeSubsidyAmount` = 0

#### 测试场景2：纯店铺补助金支付
1. 用户账户有足够的店铺补助金，通用余额为0
2. 下单并支付
3. 验证：
   - `PayOrderCarLog.balance` = 0
   - `PayOrderCarLog.storeSubsidyAmount` = 支付的店铺补助金金额
   - `OrderStoreList.balance` = 0
   - `OrderStoreList.storeSubsidyAmount` = 按比例分配的店铺补助金金额

#### 测试场景3：混合支付（通用余额 + 店铺补助金）
1. 用户账户有部分通用余额和部分店铺补助金
2. 下单并支付
3. 验证：
   - `PayOrderCarLog.balance` = 通用余额支付金额
   - `PayOrderCarLog.storeSubsidyAmount` = 店铺补助金支付金额
   - `OrderStoreList.balance` = 按比例分配的通用余额金额
   - `OrderStoreList.storeSubsidyAmount` = 按比例分配的店铺补助金金额
   - 两者之和应等于订单实付金额

#### 测试场景4：多订单支付
1. 购物车中有多个不同店铺的商品
2. 使用混合支付方式
3. 验证：
   - 每个订单的支付金额分解正确
   - 所有订单的支付金额之和等于总支付金额
   - 比例分配计算准确

### 3. 数据一致性验证
```sql
-- 验证支付日志和订单的金额一致性
SELECT 
    p.id as pay_log_id,
    p.balance as pay_balance,
    p.store_subsidy_amount as pay_store_subsidy,
    (p.balance + p.store_subsidy_amount) as pay_total,
    o.id as order_id,
    o.balance as order_balance,
    o.store_subsidy_amount as order_store_subsidy,
    (o.balance + o.store_subsidy_amount) as order_total,
    o.actual_payment
FROM pay_order_car_log p
JOIN order_store_list o ON p.id = o.serial_number
WHERE p.create_time >= CURDATE()
ORDER BY p.create_time DESC
LIMIT 10;
```

### 4. 退款测试
1. 对使用混合支付的订单进行退款
2. 验证：
   - 通用余额正确退回到用户账户
   - 店铺补助金正确退回到对应店铺的补助金账户
   - 退款金额分解正确

## 预期效果

### 1. 数据清晰度提升
- 可以清楚区分通用余额支付和店铺补助金支付
- 便于财务对账和数据分析

### 2. 退款处理准确
- 退款时能够准确区分退回到通用账户还是店铺补助金账户
- 避免退款错误

### 3. 报表统计精确
- 店铺收入统计更加准确
- 补助金使用情况统计更加清晰

## 注意事项

1. **历史数据兼容性**: 新字段默认值为0，不影响历史数据
2. **事务一致性**: 所有相关操作都在事务中进行，确保数据一致性
3. **日志记录**: 添加了详细的日志记录，便于问题排查
4. **错误处理**: 增加了异常处理和事务回滚机制

## 监控建议

1. 监控支付成功后的日志，确认字段分离逻辑正常工作
2. 定期检查数据一致性，确保支付金额分解正确
3. 关注退款流程，确保退款金额正确分配
4. 监控店铺补助金余额变化，确保扣款逻辑正确 