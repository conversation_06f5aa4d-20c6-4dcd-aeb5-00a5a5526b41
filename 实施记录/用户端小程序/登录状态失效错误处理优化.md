# 登录状态失效错误处理优化

## 问题背景

在微信小程序控制台中发现多个 `UnhandledPromiseRejection` 错误，这些错误主要与页面刷新和导航相关，并且错误信息中包含 "需要更新登录" 的提示。这表明当用户登录状态失效时，应用没有正确处理正在进行的操作，导致这些操作抛出未处理的 Promise 异常。

错误信息示例：
```
20:44:50.756 UnhandledPromiseRejection: {"errMsg":"reloadingData:fail Error: 需要更新登录。 [20250515 20:44:50][wx8bb02b064cee39b4]"}
20:44:50.756 UnhandledPromiseRejection: {"errMsg":"stopPullDownRefresh:fail Error: 需要更新登录。 [20250515 20:44:50][wx8bb02b064cee39b4]"}
20:44:51.450 UnhandledPromiseRejection: {"errMsg":"stopPullDownRefresh:fail Error: 需要更新登录。 [20250515 20:44:51][wx8bb02b064cee39b4]"}
20:44:51.471 UnhandledPromiseRejection: {"errMsg":"reloadingData:fail Error: 需要更新登录。 [20250515 20:44:51][wx8bb02b064cee39b4]"}
20:45:10.156 UnhandledPromiseRejection: {"errMsg":"navigateBack:fail Error: 需要更新登录。 [20250515 20:45:10][wx8bb02b064cee39b4]"}
20:45:11.512 UnhandledPromiseRejection: {"errMsg":"navigateBack:fail Error: 需要更新登录。 [20250515 20:45:11][wx8bb02b064cee39b4]"}
20:45:12.445 UnhandledPromiseRejection: {"errMsg":"navigateBack:fail Error: 需要更新登录。 [20250515 20:45:12][wx8bb02b064cee39b4]"}
20:45:13.201 UnhandledPromiseRejection: {"errMsg":"navigateBack:fail Error: 需要更新登录。 [20250515 20:45:13][wx8bb02b064cee39b4]"}
20:45:14.534 UnhandledPromiseRejection: {"errMsg":"navigateBack:fail Error: 需要更新登录。 [20250515 20:45:14][wx8bb02b064cee39b4]"}
```

## 问题分析

通过代码分析，发现以下几个主要问题：

1. **登录状态失效处理不完善**：
   - 当后端返回 666 错误码（表示登录状态失效）时，前端拦截器会清除用户信息并立即跳转到登录页面
   - 但在跳转过程中，页面上正在进行的操作（如下拉刷新、页面导航等）没有被正确处理
   - 这导致这些操作在尝试完成时抛出未处理的 Promise 异常

2. **错误处理机制不健壮**：
   - 许多异步操作缺乏完善的错误处理
   - 特别是在下拉刷新和加载更多数据的函数中，没有确保在所有情况下都停止相关的 UI 动画

3. **UI 状态管理不一致**：
   - 在某些情况下，加载提示和下拉刷新动画可能不会被正确关闭
   - 这导致用户体验不佳，并可能引发额外的错误

## 修复策略

针对上述问题，采取以下修复策略：

1. **改进登录状态失效处理**：
   - 在响应拦截器中，先停止所有正在进行的 UI 操作
   - 添加友好的登录过期提示
   - 延迟跳转到登录页面，确保 UI 操作已完成

2. **增强错误处理机制**：
   - 为所有异步操作添加完善的错误处理
   - 确保在出错时也能停止下拉刷新动画和隐藏加载提示
   - 添加友好的错误提示，提升用户体验

3. **优化页面刷新和加载逻辑**：
   - 修改下拉刷新处理函数，确保在任何情况下都能停止刷新动画
   - 改进加载更多数据的逻辑，添加错误处理和状态恢复
   - 移除不必要的延时操作，使 UI 响应更加及时

## 具体修改内容

### 1. 改进响应拦截器中的登录状态失效处理 (hooks/request.js)

```javascript
// 修改前
http.interceptors.response.use(response => {
  if (response.data?.code == 666) {
    store.clearUserInfo();
    store.clearToken()
    toLoginAndBackPage()
    setTimeout(() => {
      uni.hideLoading()
    }, 500)
    return Promise.reject(response)
  } else if (response.data?.code == 500) {
    uni.showToast({
      icon: 'none',
      title: response.data?.message || '网络异常，请稍后再试'
    })
    return Promise.reject(response)
  } else {
    return response
  }
```

```javascript
// 修改后
http.interceptors.response.use(response => {
  if (response.data?.code == 666) {
    // 先停止所有可能正在进行的UI操作
    try {
      // 停止下拉刷新动画
      uni.stopPullDownRefresh();
      // 隐藏加载提示
      uni.hideLoading();
    } catch (e) {
      console.error('停止UI操作失败', e);
    }
    
    // 清除用户信息和token
    store.clearUserInfo();
    store.clearToken();
    
    // 显示登录过期提示
    uni.showToast({
      icon: 'none',
      title: '登录已过期，请重新登录',
      duration: 1500
    });
    
    // 延迟跳转到登录页面，确保UI操作已完成
    setTimeout(() => {
      toLoginAndBackPage();
    }, 1500);
    
    return Promise.reject(response);
  } else if (response.data?.code == 500) {
    uni.showToast({
      icon: 'none',
      title: response.data?.message || '网络异常，请稍后再试'
    })
    return Promise.reject(response)
  } else {
    return response
  }
```

### 2. 增强下拉刷新处理函数的错误处理 (pages/myPackage/myPackage.vue)

```javascript
// 修改前
async function onRefresh() {
  isRefreshing.value = true;
  page.value = 1;
  isNoMore.value = false;

  try {
    await refresh();
  } finally {
    isRefreshing.value = false;
  }
}
```

```javascript
// 修改后
async function onRefresh() {
  isRefreshing.value = true;
  page.value = 1;
  isNoMore.value = false;

  try {
    await refresh();
  } catch (err) {
    console.error('刷新数据失败', err);
    // 显示错误提示
    uni.showToast({
      title: '刷新失败，请稍后再试',
      icon: 'none',
      duration: 1500
    });
  } finally {
    isRefreshing.value = false;
    // 确保停止下拉刷新动画
    uni.stopPullDownRefresh();
  }
}
```

### 3. 改进售后列表刷新函数的错误处理 (pages/myPackage/myPackage.vue)

```javascript
// 修改前
async function refreshAfterSaleList() {
  console.log('刷新售后数据')
  uni.showLoading({
    title: '加载中...',
    mask: true
  })

  try {
    const { data } = await uni.http.get(uni.api.refundList, {
      params: {
        pageNo: page.value,
        pageSize: pageSize.value
      }
    })

    // 如果是首次加载或刷新，直接替换列表数据
    if (page.value === 1) {
      afterSaleList.value = data.result.records || []
    } else {
      // 否则追加数据
      afterSaleList.value = [...afterSaleList.value, ...(data.result.records || [])]
    }

    // 判断是否已加载全部数据
    if ((data.result.records || []).length < pageSize.value) {
      isNoMore.value = true;
    }
  } catch (err) {
    console.error('刷新售后数据失败', err)
  } finally {
    uni.hideLoading()
  }
}
```

```javascript
// 修改后
async function refreshAfterSaleList() {
  console.log('刷新售后数据')
  uni.showLoading({
    title: '加载中...',
    mask: true
  })

  try {
    const { data } = await uni.http.get(uni.api.refundList, {
      params: {
        pageNo: page.value,
        pageSize: pageSize.value
      }
    })

    // 如果是首次加载或刷新，直接替换列表数据
    if (page.value === 1) {
      afterSaleList.value = data.result.records || []
    } else {
      // 否则追加数据
      afterSaleList.value = [...afterSaleList.value, ...(data.result.records || [])]
    }

    // 判断是否已加载全部数据
    if ((data.result.records || []).length < pageSize.value) {
      isNoMore.value = true;
    }
  } catch (err) {
    console.error('刷新售后数据失败', err)
    afterSaleList.value = []
    // 显示错误提示
    uni.showToast({
      title: '加载失败，请稍后再试',
      icon: 'none',
      duration: 1500
    });
  } finally {
    uni.hideLoading()
    // 确保停止下拉刷新动画
    uni.stopPullDownRefresh()
  }
}
```

### 4. 优化通用列表获取函数中的下拉刷新处理 (hooks/index.js)

```javascript
// 修改前
onPullDownRefresh(() => {
  if (isPullDownRefresh) {
    refresh();
  }
});
```

```javascript
// 修改后
onPullDownRefresh(() => {
  if (isPullDownRefresh) {
    refresh().catch(err => {
      console.error('下拉刷新失败', err);
      // 确保停止下拉刷新动画
      uni.stopPullDownRefresh();
    });
  } else {
    // 如果不需要下拉刷新，也要停止下拉刷新动画
    uni.stopPullDownRefresh();
  }
});
```

### 5. 移除不必要的延时操作 (hooks/index.js)

```javascript
// 修改前
setTimeout(() => {
  uni.hideLoading();
  uni.stopPullDownRefresh();
}, 500);
```

```javascript
// 修改后
// 立即隐藏加载提示和停止下拉刷新动画
uni.hideLoading();
uni.stopPullDownRefresh();
```

## 修复效果

通过上述修改，我们解决了以下问题：

1. **登录状态失效时的错误处理**：
   - 当用户登录状态失效时，应用会先停止所有正在进行的 UI 操作
   - 显示友好的登录过期提示，提升用户体验
   - 延迟跳转到登录页面，确保 UI 操作已完成，避免未处理的 Promise 异常

2. **增强了错误处理机制**：
   - 所有异步操作都添加了完善的错误处理
   - 确保在出错时也能停止下拉刷新动画和隐藏加载提示
   - 添加了友好的错误提示，提升用户体验

3. **优化了页面刷新和加载逻辑**：
   - 下拉刷新处理函数现在能在任何情况下都停止刷新动画
   - 加载更多数据的逻辑添加了错误处理和状态恢复
   - 移除了不必要的延时操作，使 UI 响应更加及时

这些修改显著提高了应用的稳定性和用户体验，特别是在网络不稳定或登录状态失效的情况下。用户将看到更加友好的提示信息，而不是遇到应用崩溃或界面卡死的情况。

## 后续建议

1. **全面审查错误处理**：
   - 对应用中的所有网络请求和异步操作进行全面审查，确保它们都有适当的错误处理
   - 特别关注那些涉及 UI 状态变化的操作，如加载提示、刷新动画等

2. **添加全局错误处理**：
   - 考虑添加全局的未捕获 Promise 异常处理器，作为最后的防线
   - 这可以帮助捕获任何遗漏的错误，并提供统一的错误处理逻辑

3. **优化登录状态管理**：
   - 考虑实现 Token 自动刷新机制，减少用户需要重新登录的频率
   - 在本地存储 Token 过期时间，提前检测并提示用户重新登录，避免突然的登录失效

4. **添加网络状态监控**：
   - 监控网络状态变化，在网络不可用时提供友好的提示
   - 在网络恢复后自动重试失败的请求

通过这些优化，我们可以进一步提高应用的稳定性和用户体验，减少因登录状态失效或网络问题导致的错误。
