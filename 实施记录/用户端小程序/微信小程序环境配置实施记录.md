# 微信小程序环境配置实施记录

## 目录

1. [项目背景](#项目背景)
2. [配置文件改造](#配置文件改造)
   - [改造目的](#改造目的)
   - [改造内容](#改造内容)
   - [完整代码](#完整代码)
3. [环境检测机制](#环境检测机制)
   - [uni.getAccountInfoSync() API 详解](#unigetaccountinfosync-api-详解)
   - [envVersion 属性值及含义](#envversion-属性值及含义)
   - [环境检测实现原理](#环境检测实现原理)
4. [最佳实践建议](#最佳实践建议)
5. [总结](#总结)

## 项目背景

在微信小程序开发过程中，我们通常需要针对不同的运行环境（开发环境、测试环境、生产环境）配置不同的服务器地址、API接口等参数。为了避免每次发布时手动修改配置，我们实现了一套基于微信小程序运行模式的自动环境切换机制。

本文档记录了我们对项目中 `hooks/config.js` 配置文件的改造过程，以及基于 `uni.getAccountInfoSync()` API 实现的环境自动切换机制。

## 配置文件改造

### 改造目的

1. 实现微信小程序环境的自动切换，无需手动修改配置
2. 根据小程序的运行模式（开发版、体验版、正式版）自动选择对应的环境配置
3. 简化开发流程，减少人为错误
4. 确保开发和测试使用测试环境，正式发布使用生产环境

### 改造内容

我们对 `hooks/config.js` 文件进行了以下改造：

1. 定义环境类型常量
2. 配置生产环境和测试环境的参数
3. 使用 `uni.getAccountInfoSync()` 获取小程序运行环境信息
4. 根据环境版本自动选择对应配置
5. 导出环境类型常量，方便在其他地方使用

### 完整代码

以下是改造后的 `hooks/config.js` 完整代码：

```javascript
/**
 * 环境配置文件
 * 根据微信小程序的运行模式自动选择对应环境
 * - 开发版和体验版使用测试环境配置
 * - 正式版使用生产环境配置
 */

// 环境类型常量
const ENV_TYPE = {
    PRODUCTION: 0, // 生产环境
    TEST: 1        // 测试环境
}

// 配置列表
const CONFIG_LIST = [
    // 生产环境配置
    {
        ENV_NAME: '生产环境',
        APP_NAME: '八闽助业集市', // 小程序名称
        APP_COMPANY: '公司名称', // 公司名称
        WX_MINI_APP_ID: 'wx68b8413ff8cd3349', // 微信小程序APPID
        WX_MINI_ORIGINAL_ID: 'gh_0ca485c04f2f', // 微信小程序原始ID
        OFFICIAL_WEBSITE_ADDRESS: 'https://app.gongkeshop.com', // 官网地址
        WX_MINI_SHARE_TYPE: 0, // 小程序的类型（包括分享） 微信小程序版本类型，可取值： 0-正式版； 1-测试版； 2-体验版。 默认值为0。
        IMAGE_URL: 'https://api.gongkeshop.com/heartful-mall-api/sys/common/view/', // 生产环境图片地址
        REQUEST_URL: 'https://api.gongkeshop.com/heartful-mall-api/', // 生产环境请求地址
        CLEAR_SHARE_CACHE_TIME: 30 // 单位分钟 进入后台总时长超过多久则清除请求头部的分享相关信息
    },
    // 测试环境配置
    {
        ENV_NAME: '测试环境',
        APP_NAME: '八闽助业集市', // 小程序名称
        APP_COMPANY: '公司名称', // 公司名称
        WX_MINI_APP_ID: 'wx68b8413ff8cd3349', // 微信小程序APPID
        WX_MINI_ORIGINAL_ID: 'gh_0ca485c04f2f', // 微信小程序原始ID
        OFFICIAL_WEBSITE_ADDRESS: 'http://app.gongkeshop.com', // 官网地址
        WX_MINI_SHARE_TYPE: 0, // 小程序的类型（包括分享） 微信小程序版本类型，可取值： 0-正式版； 1-测试版； 2-体验版。 默认值为0。
        IMAGE_URL: 'https://www.zehuaiit.top/heartful-mall-api/sys/common/view/', // 测试环境图片地址
        REQUEST_URL: 'https://www.zehuaiit.top/heartful-mall-api/', // 测试环境请求地址
        CLEAR_SHARE_CACHE_TIME: 30 // 单位分钟 进入后台总时长超过多久则清除请求头部的分享相关信息
    }
]

// 根据微信小程序运行环境自动选择配置
let CURRENT_ENV;

// #ifdef MP-WEIXIN
// 获取微信小程序运行环境
const accountInfo = uni.getAccountInfoSync();
const envVersion = accountInfo.miniProgram.envVersion;

// 根据环境版本选择配置
// develop: 开发版
// trial: 体验版
// release: 正式版
if (envVersion === 'release') {
    // 正式版使用生产环境配置
    CURRENT_ENV = ENV_TYPE.PRODUCTION;
    console.log('当前为小程序正式版，使用生产环境配置');
} else {
    // 开发版和体验版使用测试环境配置
    CURRENT_ENV = ENV_TYPE.TEST;
    console.log(`当前为小程序${envVersion === 'develop' ? '开发版' : '体验版'}，使用测试环境配置`);
}
// #endif

// 如果不是微信小程序或无法获取环境信息，默认使用生产环境
if (typeof CURRENT_ENV === 'undefined') {
    CURRENT_ENV = ENV_TYPE.PRODUCTION;
    console.log('无法确定运行环境，默认使用生产环境配置');
}

// 设置全局环境配置
uni.env = CONFIG_LIST[CURRENT_ENV];

// 输出当前环境信息到控制台，方便调试
console.log(`当前环境: ${uni.env.ENV_NAME}`);
console.log(`API地址: ${uni.env.REQUEST_URL}`);

// 导出环境类型常量，方便在其他地方使用
export const ENV = ENV_TYPE;
```

## 环境检测机制

### uni.getAccountInfoSync() API 详解

`uni.getAccountInfoSync()` 是 uni-app 框架对微信小程序原生 API `wx.getAccountInfoSync()` 的封装，用于获取当前小程序的账号信息。

#### API 来源与文档依据

- 微信小程序官方文档：[wx.getAccountInfoSync()](https://developers.weixin.qq.com/miniprogram/dev/api/open-api/account-info/wx.getAccountInfoSync.html)
- uni-app 文档：[uni.getAccountInfoSync()](https://uniapp.dcloud.net.cn/api/plugins/getAccountInfoSync.html)

#### 返回值结构

`uni.getAccountInfoSync()` 返回一个对象，包含以下主要信息：

```javascript
{
  miniProgram: {
    appId: "wx68b8413ff8cd3349",       // 小程序 appId
    envVersion: "develop",             // 小程序版本 develop/trial/release
    version: "1.0.0"                   // 线上小程序版本号
  },
  plugin: {                            // 插件信息（如果是插件）
    appId: "",                         // 插件 appId
    version: ""                        // 插件版本号
  }
}
```

#### 工作原理

`uni.getAccountInfoSync()` 通过调用微信小程序底层的能力，直接获取当前运行环境的账号信息。这个方法是同步的，意味着它会立即返回结果，不需要使用回调函数或 Promise。

在我们的配置中，主要使用了 `accountInfo.miniProgram.envVersion` 属性来判断当前小程序的运行环境。

### envVersion 属性值及含义

`accountInfo.miniProgram.envVersion` 属性有以下几种可能的值：

| 值 | 含义 | 说明 |
|---|---|---|
| `"develop"` | 开发版 | 在开发者工具中运行或通过开发者工具预览的版本 |
| `"trial"` | 体验版 | 通过"小程序管理后台"发布的体验版本，仅对特定体验者开放 |
| `"release"` | 正式版 | 通过审核并发布的正式版本，所有用户可访问 |
| `undefined` | 未定义 | 无法获取环境信息时的值 |

### 环境检测实现原理

我们的环境检测机制基于以下原则：

1. 使用条件编译 `// #ifdef MP-WEIXIN` 确保代码只在微信小程序环境中执行
2. 通过 `uni.getAccountInfoSync()` 获取小程序的运行环境信息
3. 根据 `envVersion` 的值判断当前运行环境：
   - 当 `envVersion === 'release'` 时，使用生产环境配置
   - 当 `envVersion === 'develop'` 或 `envVersion === 'trial'` 时，使用测试环境配置
4. 如果无法获取环境信息，默认使用生产环境配置

实现代码示例：

```javascript
// #ifdef MP-WEIXIN
// 获取微信小程序运行环境
const accountInfo = uni.getAccountInfoSync();
const envVersion = accountInfo.miniProgram.envVersion;

// 根据环境版本选择配置
if (envVersion === 'release') {
    // 正式版使用生产环境配置
    CURRENT_ENV = ENV_TYPE.PRODUCTION;
} else {
    // 开发版和体验版使用测试环境配置
    CURRENT_ENV = ENV_TYPE.TEST;
}
// #endif
```

## 最佳实践建议

基于我们的实施经验，提出以下最佳实践建议：

### 1. 使用条件编译

始终使用条件编译确保特定平台的代码只在对应平台执行：

```javascript
// #ifdef MP-WEIXIN
// 微信小程序特有代码
// #endif
```

### 2. 提供默认配置

为无法确定环境的情况提供默认配置，通常默认为生产环境：

```javascript
if (typeof CURRENT_ENV === 'undefined') {
    CURRENT_ENV = ENV_TYPE.PRODUCTION;
}
```

### 3. 添加调试日志

在配置文件中添加适当的调试日志，方便开发过程中排查问题：

```javascript
console.log(`当前环境: ${uni.env.ENV_NAME}`);
console.log(`API地址: ${uni.env.REQUEST_URL}`);
```

### 4. 环境配置分离

将不同环境的配置参数明确分离，便于维护和更新：

```javascript
const CONFIG_LIST = [
    // 生产环境配置
    {
        ENV_NAME: '生产环境',
        REQUEST_URL: 'https://api.example.com/'
    },
    // 测试环境配置
    {
        ENV_NAME: '测试环境',
        REQUEST_URL: 'https://test-api.example.com/'
    }
]
```

### 5. 避免硬编码

避免在业务代码中硬编码环境相关的参数，统一使用配置文件中的值：

```javascript
// 错误示例
const url = 'https://api.example.com/users';

// 正确示例
const url = `${uni.env.REQUEST_URL}users`;
```

## 总结

通过对 `hooks/config.js` 文件的改造，我们实现了微信小程序环境的自动切换机制。该机制基于 `uni.getAccountInfoSync()` API，能够根据小程序的运行模式（开发版、体验版、正式版）自动选择对应的环境配置。

这种实现方式具有以下优势：

1. **自动化**：无需手动修改配置，减少人为错误
2. **一致性**：确保开发和测试使用测试环境，正式发布使用生产环境
3. **可维护性**：配置集中管理，易于更新和维护
4. **开发友好**：提供清晰的环境信息和调试日志

通过这种环境配置机制，我们显著提高了开发效率，减少了环境配置错误导致的问题，为项目的稳定运行提供了保障。
