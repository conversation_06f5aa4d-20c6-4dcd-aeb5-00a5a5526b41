# 商品详情分包实施计划

## 一、分包目标

将商品详情相关页面从主包移至独立分包，减小主包体积，优化小程序加载性能。

## 二、分包范围

根据分包优化实施计划和代码分析，本次分包将包含以下页面：

1. **商品详情页**：`pages/productInfo/productInfo.vue`
2. **商品收藏页**：`pages/productCollection/productCollection.vue`
3. **店铺首页**：`pages/shopIndex/shopIndex.vue`
4. **店铺证件信息页**：已在之前的分包中实施（`packageTest/storeCredentials/storeCredentials.vue`）

## 三、依赖分析

### 1. 商品详情页（productInfo.vue）

**组件依赖**：
- `shareModal`：商品详情页内部组件 `pages/productInfo/components/shareModal/shareModal.vue`
- `l-painter`：海报生成组件
- `uni-popup`：弹出层组件
- `specificationWrap`：商品规格选择组件 `components/specificationWrap/specificationWrap.vue`
- `z-swiper` 和 `z-swiper-item`：轮播图组件

**工具依赖**：
- `hooks`：`goBackIsTop`, `shareAbout`, `toUpPage`, `specificationPopPub`, `navToStoreIndex`
- `utils`：`parseImgurl`
- `store`：`getAllBeShared`, `userStore`

### 2. 商品收藏页（productCollection.vue）

**组件依赖**：
- `uni-popup`：弹出层组件
- `uni-popup-dialog`：对话框组件
- `productDetail`：商品详情组件 `components/productDetail/productDetail.vue`

**工具依赖**：
- `hooks`：`listGet`
- `utils`：相关工具函数

### 3. 店铺首页（shopIndex.vue）

**组件依赖**：
- 商品列表组件
- 店铺信息组件

**工具依赖**：
- `hooks`：相关导航和数据处理函数
- `utils`：相关工具函数

## 四、实施步骤

### 步骤1：创建分包目录结构

1. 创建商品详情分包目录 `packageGoods`
2. 在分包目录下创建对应的页面目录结构：
   ```
   packageGoods/
   ├── pages/
   │   ├── productInfo/
   │   │   ├── components/
   │   │   │   └── shareModal/
   │   │   │       └── shareModal.vue
   │   │   └── productInfo.vue
   │   ├── productCollection/
   │   │   └── productCollection.vue
   │   └── shopIndex/
   │       └── shopIndex.vue
   ```

### 步骤2：修改 pages.json 配置

在 `pages.json` 中添加分包配置：

```json
{
  "subPackages": [
    {
      "root": "packageGoods",
      "pages": [
        "pages/productInfo/productInfo",
        "pages/productCollection/productCollection",
        "pages/shopIndex/shopIndex"
      ]
    }
  ]
}
```

### 步骤3：迁移页面文件

1. 迁移商品详情页及其组件：
   - 复制 `pages/productInfo/productInfo.vue` 到 `packageGoods/pages/productInfo/productInfo.vue`
   - 复制 `pages/productInfo/components/shareModal/shareModal.vue` 到 `packageGoods/pages/productInfo/components/shareModal/shareModal.vue`

2. 迁移商品收藏页：
   - 复制 `pages/productCollection/productCollection.vue` 到 `packageGoods/pages/productCollection/productCollection.vue`

3. 迁移店铺首页：
   - 复制 `pages/shopIndex/shopIndex.vue` 到 `packageGoods/pages/shopIndex/shopIndex.vue`

### 步骤4：更新页面引用路径

1. 修改所有引用这些页面的地方，使用新的路径：
   - 将 `/pages/productInfo/productInfo` 修改为 `/packageGoods/pages/productInfo/productInfo`
   - 将 `/pages/productCollection/productCollection` 修改为 `/packageGoods/pages/productCollection/productCollection`
   - 将 `/pages/shopIndex/shopIndex` 修改为 `/packageGoods/pages/shopIndex/shopIndex`

2. 重点检查以下文件中的路径引用：
   - `hooks/index.js` 中的 `navToGoodDetail` 和 `navToStoreIndex` 函数
   - 首页、分类页等跳转到商品详情的地方
   - 个人中心页面跳转到商品收藏的地方

### 步骤5：测试验证

1. 编译项目，检查是否有编译错误
2. 测试从首页、分类页到商品详情页的跳转
3. 测试商品详情页内的功能：
   - 轮播图显示
   - 商品信息展示
   - 规格选择
   - 加入购物车
   - 立即购买
   - 分享功能
4. 测试从个人中心到商品收藏页的跳转
5. 测试从商品详情页到店铺首页的跳转

### 步骤6：删除原页面

确认分包功能正常后，从 `pages.json` 中移除原页面配置，并删除原页面文件。

## 五、风险控制

1. **备份原始文件**：
   - 在删除原文件前确保分包页面正常工作

2. **渐进式实施**：
   - 先完成分包配置和页面迁移
   - 测试分包功能正常后再删除原页面

3. **路径检查**：
   - 使用全局搜索确保所有引用路径都已更新

4. **回滚准备**：
   - 准备回滚脚本或步骤，以便在出现问题时快速恢复

## 六、预期成果

1. **主包体积减小**：
   - 预计减少约200-300KB

2. **加载性能提升**：
   - 首次启动时间缩短
   - 按需加载商品详情页面

3. **结构优化**：
   - 更清晰的项目结构
   - 更好的代码组织

## 七、实施计划

| 阶段 | 任务 | 预计耗时 |
|------|------|----------|
| 准备 | 分析依赖关系，制定实施计划 | 0.5小时 |
| 实施 | 创建分包目录，修改配置，迁移页面 | 1小时 |
| 测试 | 功能测试，路径验证 | 1小时 |
| 优化 | 解决问题，完善分包 | 0.5小时 |
| 总计 | | 3小时 |
