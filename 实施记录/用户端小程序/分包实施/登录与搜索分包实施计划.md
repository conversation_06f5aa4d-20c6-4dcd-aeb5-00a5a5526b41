# 登录与搜索分包实施计划

## 一、分包目标

将登录和搜索相关页面从主包移至独立分包，进一步减小主包体积，优化小程序加载性能。

## 二、分包范围

根据分包优化实施计划和代码分析，本次分包将包含以下页面：

1. **登录页面**：`pages/login/login.vue`
2. **验证码登录页面**：`pages/phoneLogin/phoneLogin.vue`
3. **搜索页面**：`pages/search/search.vue`
4. **协议页面**：`pages/agreement/agreement.vue`（与登录相关的协议页面）

## 三、依赖分析

### 1. 登录页面（login.vue）

**组件依赖**：
- 可能使用了一些基础UI组件，如按钮、输入框等

**工具依赖**：
- `hooks`：可能包含登录相关的钩子函数
- `utils`：可能包含表单验证等工具函数
- `store`：用户状态管理

### 2. 验证码登录页面（phoneLogin.vue）

**组件依赖**：
- 可能使用了一些基础UI组件，如按钮、输入框、倒计时等

**工具依赖**：
- `hooks`：可能包含登录相关的钩子函数
- `utils`：可能包含表单验证、手机号验证等工具函数
- `store`：用户状态管理

### 3. 搜索页面（search.vue）

**组件依赖**：
- `productDetail`：商品详情组件（已在公共组件目录中）

**工具依赖**：
- `hooks`：`navTo`, `navToGoodDetail` 等导航函数
- `utils`：可能包含搜索历史记录管理等工具函数

### 4. 协议页面（agreement.vue）

**组件依赖**：
- 可能较少，主要是文本展示

**工具依赖**：
- 可能较少，主要是基础功能

## 四、实施步骤

### 步骤1：创建分包目录结构

1. 创建登录与搜索分包目录 `packageSearch`
2. 在分包目录下创建对应的页面目录结构：
   ```
   packageSearch/
   ├── pages/
   │   ├── login/
   │   │   └── login.vue
   │   ├── phoneLogin/
   │   │   └── phoneLogin.vue
   │   ├── search/
   │   │   └── search.vue
   │   └── agreement/
   │       └── agreement.vue
   ```

### 步骤2：修改 pages.json 配置

在 `pages.json` 中添加分包配置：

```json
{
  "subPackages": [
    {
      "root": "packageSearch",
      "pages": [
        "pages/login/login",
        "pages/phoneLogin/phoneLogin",
        "pages/search/search",
        "pages/agreement/agreement"
      ]
    }
  ]
}
```

同时，从 `pages.json` 的 `pages` 数组中移除这些页面的原始配置。

### 步骤3：迁移页面文件

1. 迁移登录页面：
   - 复制 `pages/login/login.vue` 到 `packageSearch/pages/login/login.vue`

2. 迁移验证码登录页面：
   - 复制 `pages/phoneLogin/phoneLogin.vue` 到 `packageSearch/pages/phoneLogin/phoneLogin.vue`

3. 迁移搜索页面：
   - 复制 `pages/search/search.vue` 到 `packageSearch/pages/search/search.vue`

4. 迁移协议页面：
   - 复制 `pages/agreement/agreement.vue` 到 `packageSearch/pages/agreement/agreement.vue`

### 步骤4：更新页面引用路径

1. 修改所有引用这些页面的地方，使用新的分包路径：
   - 将 `/pages/login/login` 修改为 `/packageSearch/pages/login/login`
   - 将 `/pages/phoneLogin/phoneLogin` 修改为 `/packageSearch/pages/phoneLogin/phoneLogin`
   - 将 `/pages/search/search` 修改为 `/packageSearch/pages/search/search`
   - 将 `/pages/agreement/agreement` 修改为 `/packageSearch/pages/agreement/agreement`

2. 重点检查以下文件中的路径引用：
   - 各个页面中的登录跳转逻辑
   - 首页、分类页等跳转到搜索页面的地方
   - 登录页面跳转到验证码登录页面的地方
   - 登录页面跳转到协议页面的地方

### 步骤5：测试验证

1. 编译项目，检查是否有编译错误
2. 测试登录流程：
   - 从未登录状态进入需要登录的页面，检查是否正确跳转到登录页
   - 测试账号密码登录
   - 测试验证码登录
   - 测试查看协议
3. 测试搜索功能：
   - 从首页、分类页进入搜索页面
   - 测试搜索功能
   - 测试搜索历史记录功能

### 步骤6：删除原页面

确认分包功能正常后，从 `pages.json` 中移除原页面配置，并删除原页面文件。

## 五、风险控制

1. **登录功能的特殊性**：
   - 登录是小程序的核心功能，需要特别谨慎处理
   - 确保所有需要登录验证的页面都能正确跳转到登录页面
   - 测试登录成功后的状态保持和页面跳转

2. **搜索功能的广泛使用**：
   - 搜索功能可能在多个页面被调用，需要全面检查
   - 确保搜索结果的正确展示

3. **备份原始文件**：
   - 在删除原文件前确保分包页面正常工作

4. **渐进式实施**：
   - 先完成分包配置和页面迁移
   - 测试分包功能正常后再删除原页面

5. **路径检查**：
   - 使用全局搜索确保所有引用路径都已更新

6. **回滚准备**：
   - 准备回滚脚本或步骤，以便在出现问题时快速恢复

## 六、预期成果

1. **主包体积减小**：
   - 预计减少约100-150KB

2. **加载性能提升**：
   - 首次启动时间缩短
   - 按需加载登录和搜索页面

3. **结构优化**：
   - 更清晰的项目结构
   - 更好的代码组织

## 七、实施计划

| 阶段 | 任务 | 预计耗时 |
|------|------|----------|
| 准备 | 分析依赖关系，制定实施计划 | 0.5小时 |
| 实施 | 创建分包目录，修改配置，迁移页面 | 1小时 |
| 测试 | 功能测试，路径验证 | 1小时 |
| 优化 | 解决问题，完善分包 | 0.5小时 |
| 总计 | | 3小时 |
