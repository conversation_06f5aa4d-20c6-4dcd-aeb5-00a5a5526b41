# 充值页面分包实施记录

## 实施目标

将充值页面（recharge.vue）从主包移动到子包中，减轻微信小程序主包体积。

## 实施步骤

1. 创建充值子包目录 `packageRecharge/pages/recharge`
2. 将充值页面文件 `recharge.vue` 从主包 `pages/recharge/` 移动到子包 `packageRecharge/pages/recharge/`
3. 更新 pages.json 配置，将充值页面从主包 pages 数组移动到 subPackages 数组中
4. 更新所有引用充值页面的路径，从 `/pages/recharge/recharge` 改为 `/packageRecharge/pages/recharge/recharge`
5. 删除原主包中的充值页面
6. 添加预加载配置，在用户访问个人中心页面时预加载充值子包

## 更新的文件

1. 新增文件：
   - `/packageRecharge/pages/recharge/recharge.vue`

2. 删除文件：
   - `/pages/recharge/recharge.vue`

3. 修改文件：
   - `pages.json`: 更新页面配置和添加预加载规则
   - `pages/index/index.vue`: 更新充值页面路径
   - `pages/user/user.vue`: 更新充值页面路径
   - `pages/productionAbout/productionAbout.vue`: 更新充值页面路径
   - `components/payAboutModal/payAboutModal.vue`: 更新充值页面路径
   - `packageUser/pages/heartMeter/heartMeter.vue`: 更新充值页面路径

## 注意事项

1. 预加载配置已添加，在用户访问个人中心页面时会预加载充值子包，提高用户体验
2. 充值是高频使用的功能，但独立成子包可以有效减轻主包体积
3. 子包加载可能增加首次访问充值页面的等待时间，但预加载策略可以缓解这一问题

## 后续优化建议

1. 监控充值页面访问性能，评估预加载策略效果
2. 考虑将其他相关支付页面（如 cashier）也移入同一子包
3. 针对高频功能，可以考虑使用骨架屏优化加载体验 