# 订单与售后分包实施计划

## 一、分包目标

将订单与售后相关页面从主包移至独立分包，减小主包体积，优化小程序加载性能。

## 二、分包范围

根据分包优化实施计划和代码分析，本次分包将包含以下页面：

1. **我的包裹页**：`pages/myPackage/myPackage.vue`
2. **订单详情页**：`pages/orderDetail/orderDetail.vue`
3. **物流详情页**：`pages/logistics/logistics.vue`
4. **售后申请页**：`pages/applyForAfterSale/applyForAfterSale.vue`
5. **售后详情页**：`pages/afterSaleDetail/afterSaleDetail.vue`
6. **售后订单详情页**：`pages/afterSaleOrderDetail/afterSaleOrderDetail.vue`

## 三、依赖分析

### 1. 我的包裹页（myPackage.vue）

**组件依赖**：
- `uni-nav-bar`：导航栏组件
- `uv-tabs`：标签页组件
- `uni-popup`：弹出层组件
- `uni-popup-dialog`：对话框组件
- `empty`：空状态组件
- `exchange-goods-popup`：商品选择弹窗组件 `components/exchangeGoodsPopup/exchangeGoodsPopup.vue`

**工具依赖**：
- `hooks`：`listGet`, `navTo`, `redTo`, `navToStoreIndex`, `toUpPage`
- `utils`：`clone`, `getSafeBottom`, `parseObjToPath`, `parseImgurl`
- `cashier`：支付相关函数

### 2. 订单详情页（orderDetail.vue）

**组件依赖**：
- `uni-countdown`：倒计时组件
- `uni-steps`：步骤条组件

**工具依赖**：
- `hooks`：`navTo`, `redTo`, `toUpPage`
- `utils`：`getAllTime`, `getSafeBottom`, `parseObjToPath`, `parseImgurl`

### 3. 物流详情页（logistics.vue）

**组件依赖**：
- `uni-steps`：步骤条组件

**工具依赖**：
- `utils`：`parseImgurl`

### 4. 售后申请页（applyForAfterSale.vue）

**组件依赖**：
- `uni-file-picker`：文件选择器组件
- `uni-popup`：弹出层组件
- `numberInput`：数字输入组件
- `discountCouponWrap`：优惠券组件

**工具依赖**：
- `hooks`：`navTo`, `redTo`, `specificationPopPub`, `toUpPage`, `uploadImg`
- `utils`：`clone`, `getSafeBottom`, `parseImgurl`
- `store`：`locationInfo`

### 5. 售后详情页（afterSaleDetail.vue）

**组件依赖**：
- `submitSendInfo`：提交物流信息组件 `pages/afterSaleDetail/components/submitSendInfo/submitSendInfo.vue`

**工具依赖**：
- `hooks`：`navTo`
- `utils`：`clone`, `getAllTime`, `getSafeBottom`, `parseImgurl`

### 6. 售后订单详情页（afterSaleOrderDetail.vue）

**组件依赖**：
- 与售后详情页类似

**工具依赖**：
- 与售后详情页类似

## 四、实施步骤

### 步骤1：创建分包目录结构

1. 创建订单分包目录 `packageOrder`
2. 在分包目录下创建对应的页面目录结构：
   ```
   packageOrder/
   ├── pages/
   │   ├── myPackage/
   │   │   └── myPackage.vue
   │   ├── orderDetail/
   │   │   └── orderDetail.vue
   │   ├── logistics/
   │   │   └── logistics.vue
   │   ├── applyForAfterSale/
   │   │   └── applyForAfterSale.vue
   │   ├── afterSaleDetail/
   │   │   ├── components/
   │   │   │   └── submitSendInfo/
   │   │   │       └── submitSendInfo.vue
   │   │   └── afterSaleDetail.vue
   │   └── afterSaleOrderDetail/
   │       └── afterSaleOrderDetail.vue
   ```

### 步骤2：修改 pages.json 配置

在 `pages.json` 中添加分包配置：

```json
{
  "subPackages": [
    {
      "root": "packageOrder",
      "pages": [
        "pages/myPackage/myPackage",
        "pages/orderDetail/orderDetail",
        "pages/logistics/logistics",
        "pages/applyForAfterSale/applyForAfterSale",
        "pages/afterSaleDetail/afterSaleDetail",
        "pages/afterSaleOrderDetail/afterSaleOrderDetail"
      ]
    }
  ]
}
```

### 步骤3：迁移页面文件

1. 迁移我的包裹页：
   - 复制 `pages/myPackage/myPackage.vue` 到 `packageOrder/pages/myPackage/myPackage.vue`

2. 迁移订单详情页：
   - 复制 `pages/orderDetail/orderDetail.vue` 到 `packageOrder/pages/orderDetail/orderDetail.vue`

3. 迁移物流详情页：
   - 复制 `pages/logistics/logistics.vue` 到 `packageOrder/pages/logistics/logistics.vue`

4. 迁移售后申请页：
   - 复制 `pages/applyForAfterSale/applyForAfterSale.vue` 到 `packageOrder/pages/applyForAfterSale/applyForAfterSale.vue`

5. 迁移售后详情页及其组件：
   - 复制 `pages/afterSaleDetail/afterSaleDetail.vue` 到 `packageOrder/pages/afterSaleDetail/afterSaleDetail.vue`
   - 复制 `pages/afterSaleDetail/components/submitSendInfo/submitSendInfo.vue` 到 `packageOrder/pages/afterSaleDetail/components/submitSendInfo/submitSendInfo.vue`

6. 迁移售后订单详情页：
   - 复制 `pages/afterSaleOrderDetail/afterSaleOrderDetail.vue` 到 `packageOrder/pages/afterSaleOrderDetail/afterSaleOrderDetail.vue`

### 步骤4：更新页面引用路径

1. 修改所有引用这些页面的地方，使用新的分包路径：
   - 将 `/pages/myPackage/myPackage` 修改为 `/packageOrder/pages/myPackage/myPackage`
   - 将 `/pages/orderDetail/orderDetail` 修改为 `/packageOrder/pages/orderDetail/orderDetail`
   - 将 `/pages/logistics/logistics` 修改为 `/packageOrder/pages/logistics/logistics`
   - 将 `/pages/applyForAfterSale/applyForAfterSale` 修改为 `/packageOrder/pages/applyForAfterSale/applyForAfterSale`
   - 将 `/pages/afterSaleDetail/afterSaleDetail` 修改为 `/packageOrder/pages/afterSaleDetail/afterSaleDetail`
   - 将 `/pages/afterSaleOrderDetail/afterSaleOrderDetail` 修改为 `/packageOrder/pages/afterSaleOrderDetail/afterSaleOrderDetail`

2. 重点检查以下文件中的路径引用：
   - 个人中心页面中的订单入口
   - 商品详情页中的下单流程
   - 订单详情页中的物流跳转
   - 我的包裹页中的售后申请跳转

### 步骤5：测试验证

1. 编译项目，检查是否有编译错误
2. 测试从个人中心到我的包裹页的跳转
3. 测试订单详情页内的功能：
   - 订单状态显示
   - 物流信息查看
   - 确认收货
   - 取消订单
4. 测试售后流程：
   - 申请售后
   - 查看售后详情
   - 提交物流信息

### 步骤6：删除原页面

确认分包功能正常后，从 `pages.json` 中移除原页面配置，并删除原页面文件。

## 五、风险控制

1. **备份原始文件**：
   - 在删除原文件前确保分包页面正常工作

2. **渐进式实施**：
   - 先完成分包配置和页面迁移
   - 测试分包功能正常后再删除原页面

3. **路径检查**：
   - 使用全局搜索确保所有引用路径都已更新

4. **回滚准备**：
   - 准备回滚脚本或步骤，以便在出现问题时快速恢复

## 六、预期成果

1. **主包体积减小**：
   - 预计减少约200-300KB

2. **加载性能提升**：
   - 首次启动时间缩短
   - 按需加载订单和售后页面

3. **结构优化**：
   - 更清晰的项目结构
   - 更好的代码组织

## 七、实施计划

| 阶段 | 任务 | 预计耗时 |
|------|------|----------|
| 准备 | 分析依赖关系，制定实施计划 | 0.5小时 |
| 实施 | 创建分包目录，修改配置，迁移页面 | 1.5小时 |
| 测试 | 功能测试，路径验证 | 1.5小时 |
| 优化 | 解决问题，完善分包 | 0.5小时 |
| 总计 | | 4小时 |
