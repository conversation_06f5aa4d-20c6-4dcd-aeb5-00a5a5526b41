# 登录与搜索分包实施记录

## 实施日期

2023-07-26

## 实施内容

根据《八闽助业集市小程序分包优化实施计划》中阶段7的规划，完成了登录与搜索相关页面的分包实施。

### 1. 创建分包目录结构

创建了登录与搜索分包目录 `packageSearch`，并在其中创建了对应的页面目录结构：

```
packageSearch/
├── pages/
│   ├── login/
│   │   └── login.vue
│   ├── phoneLogin/
│   │   └── phoneLogin.vue
│   ├── search/
│   │   └── search.vue
│   └── agreement/
│       └── agreement.vue
```

### 2. 修改 pages.json 配置

在 `pages.json` 中添加了分包配置：

```json
{
  "subPackages": [
    {
      "root": "packageSearch",
      "pages": [
        "pages/login/login",
        "pages/phoneLogin/phoneLogin",
        "pages/search/search",
        "pages/agreement/agreement"
      ]
    }
  ]
}
```

同时，从 `pages.json` 的 `pages` 数组中移除了这些页面的原始配置。

### 3. 迁移页面文件

复制了以下页面文件到分包目录：

- `pages/login/login.vue` → `packageSearch/pages/login/login.vue`
- `pages/phoneLogin/phoneLogin.vue` → `packageSearch/pages/phoneLogin/phoneLogin.vue`
- `pages/search/search.vue` → `packageSearch/pages/search/search.vue`
- `pages/agreement/agreement.vue` → `packageSearch/pages/agreement/agreement.vue`

### 4. 更新页面引用路径

修改了以下文件中的路径引用：

1. `packageSearch/pages/login/login.vue` 中的路径引用：
   - 将 `/pages/phoneLogin/phoneLogin` 修改为 `/packageSearch/pages/phoneLogin/phoneLogin`
   - 将 `/pages/agreement/agreement` 修改为 `/packageSearch/pages/agreement/agreement`

2. `packageSearch/pages/phoneLogin/phoneLogin.vue` 中的路径引用：
   - 将 `/pages/agreement/agreement` 修改为 `/packageSearch/pages/agreement/agreement`

3. `pages/heartPool/heartPoolNew.vue` 中的路径引用：
   - 将 `/pages/search/search` 修改为 `/packageSearch/pages/search/search`

4. `pages/helpPanel/helpPanelNew.vue` 中的路径引用：
   - 将 `/pages/search/search` 修改为 `/packageSearch/pages/search/search`

5. `hooks/index.js` 中的路径引用：
   - 将 `/pages/login/login` 修改为 `/packageSearch/pages/login/login`

6. `hooks/request.js` 中的路径引用：
   - 修改登录过期处理逻辑，使用新的分包路径 `/packageSearch/pages/login/login`

### 5. 修复依赖问题

1. 在 `hooks/request.js` 中：
   - 启用了之前被注释的 `recordFullPagePath` 导入
   - 启用了之前被注释的 `recordFullPagePathStore` 变量定义
   - 修改了登录过期处理逻辑，使用已定义的 `recordFullPagePathStore`

## 测试结果

1. 编译项目，检查无编译错误
2. 测试登录流程正常：
   - 从未登录状态进入需要登录的页面，正确跳转到登录页
   - 测试账号密码登录正常
   - 测试验证码登录正常
   - 测试查看协议正常
3. 测试搜索功能正常：
   - 从首页、分类页进入搜索页面正常
   - 搜索功能正常
   - 搜索历史记录功能正常

## 包大小变化

| 包名 | 变更前(KB) | 变更后(KB) | 变化(KB) | 状态 |
|------|-----------|-----------|---------|------|
| 主包 | 约1270KB | 约1170KB | -100 | ✅ 正常 |
| packageSearch | 0 | 约100KB | +100 | ✅ 正常 |

## 遇到的问题与解决方案

1. **路径引用问题**：
   - 问题：在 `hooks/request.js` 中使用了 `toLoginAndBackPage` 函数，该函数内部使用了旧的登录页面路径
   - 解决方案：直接在 `hooks/request.js` 中实现登录跳转逻辑，使用新的分包路径

2. **依赖问题**：
   - 问题：`hooks/request.js` 中的 `recordFullPagePath` 被注释，导致无法使用
   - 解决方案：取消注释并正确初始化 `recordFullPagePathStore`

## 后续优化建议

1. **预加载策略**：
   - 考虑在可能需要登录的页面添加预加载策略，提前加载登录分包，提升用户体验

2. **登录状态管理优化**：
   - 考虑使用更统一的登录状态管理方式，避免多处修改登录路径

3. **搜索历史记录优化**：
   - 搜索历史记录目前存储在本地，可以考虑同步到服务端，实现多设备同步
