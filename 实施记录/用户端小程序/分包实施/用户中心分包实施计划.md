# 用户中心分包实施计划

## 实施目标

将用户中心相关页面从主包拆分为独立分包，减小主包体积，优化小程序加载性能。

## 分包页面清单

以下页面将迁移到用户中心分包：

1. **个人资料** (`pages/userProfile/userProfile.vue`)
2. **修改资料** (`pages/editUserProfile/editUserProfile.vue`)
3. **设置** (`pages/setting/setting.vue`)
4. **我的团队** (`pages/myTeam/myTeam.vue`)
5. **助力值** (`pages/heartMeter/heartMeter.vue`)
6. **助力值明细** (`pages/heartFeedback/heartFeedback.vue`)
7. **银行卡管理** (`pages/myBankCard/myBankCard.vue`)
8. **添加银行卡** (`pages/opBankCard/opBankCard.vue`)
9. **邀请好友** (`pages/myPoster/myPoster.vue`)

## 实施步骤

### 1. 创建分包目录结构

```
packageUser/
├── pages/
│   ├── userProfile/
│   │   └── userProfile.vue
│   ├── editUserProfile/
│   │   └── editUserProfile.vue
│   ├── setting/
│   │   └── setting.vue
│   ├── myTeam/
│   │   └── myTeam.vue
│   ├── heartMeter/
│   │   └── heartMeter.vue
│   ├── heartFeedback/
│   │   └── heartFeedback.vue
│   ├── myBankCard/
│   │   └── myBankCard.vue
│   ├── opBankCard/
│   │   └── opBankCard.vue
│   └── myPoster/
│       └── myPoster.vue
```

### 2. 配置分包信息

在 `pages.json` 中添加分包配置：

```json
{
  "subPackages": [
    {
      "root": "packageUser",
      "pages": [
        "pages/userProfile/userProfile",
        "pages/editUserProfile/editUserProfile",
        "pages/setting/setting",
        "pages/myTeam/myTeam",
        "pages/heartMeter/heartMeter",
        "pages/heartFeedback/heartFeedback",
        "pages/myBankCard/myBankCard",
        "pages/opBankCard/opBankCard",
        "pages/myPoster/myPoster"
      ]
    }
  ]
}
```

### 3. 迁移页面文件

按照以下顺序迁移页面文件：

1. 先迁移依赖较少的页面：
   - 个人资料页面
   - 设置页面
   - 助力值页面

2. 再迁移依赖较多的页面：
   - 修改资料页面
   - 我的团队页面
   - 助力值明细页面
   - 银行卡管理页面
   - 添加银行卡页面
   - 邀请好友页面

### 4. 更新页面引用路径

1. 更新个人中心页面中的路径引用：
   - 将 `/pages/xxx/xxx` 修改为 `/packageUser/pages/xxx/xxx`

2. 更新分包内页面之间的跳转路径：
   - 如个人资料页面到修改资料页面的跳转

3. 更新其他页面中可能引用这些页面的路径

### 5. 测试验证

1. 测试从个人中心页面进入各分包页面
2. 测试分包内页面之间的跳转
3. 测试分包页面的功能完整性
4. 测试返回逻辑

## 实施细节

### 1. 个人资料页面迁移

1. 复制 `pages/userProfile/userProfile.vue` 到 `packageUser/pages/userProfile/userProfile.vue`
2. 更新页面中的跳转路径：
   ```js
   // 修改前
   navTo(`/pages/editUserProfile/editUserProfile`)
   
   // 修改后
   navTo(`/packageUser/pages/editUserProfile/editUserProfile`)
   ```

### 2. 修改资料页面迁移

1. 复制 `pages/editUserProfile/editUserProfile.vue` 到 `packageUser/pages/editUserProfile/editUserProfile.vue`
2. 确保 w-picker 组件引用正确

### 3. 设置页面迁移

1. 复制 `pages/setting/setting.vue` 到 `packageUser/pages/setting/setting.vue`
2. 更新页面中的跳转路径：
   ```js
   // 修改前
   navTo('/pages/agreement/agreement?path=userServiceAgreement')
   
   // 修改后
   navTo('/pages/agreement/agreement?path=userServiceAgreement')
   // 注意：agreement页面保留在主包中，路径不变
   ```

### 4. 我的团队页面迁移

1. 复制 `pages/myTeam/myTeam.vue` 到 `packageUser/pages/myTeam/myTeam.vue`
2. 更新页面中的跳转路径：
   ```js
   // 修改前
   navTo('/pages/revenueDetail/revenueDetail')
   
   // 修改后
   navTo('/packageUser/pages/revenueDetail/revenueDetail')
   // 如果revenueDetail也在分包中
   ```

### 5. 助力值页面迁移

1. 复制 `pages/heartMeter/heartMeter.vue` 到 `packageUser/pages/heartMeter/heartMeter.vue`
2. 更新页面中的跳转路径：
   ```js
   // 修改前
   navTo('/pages/heartFeedback/heartFeedback')
   
   // 修改后
   navTo('/packageUser/pages/heartFeedback/heartFeedback')
   ```

### 6. 助力值明细页面迁移

1. 复制 `pages/heartFeedback/heartFeedback.vue` 到 `packageUser/pages/heartFeedback/heartFeedback.vue`
2. 更新页面中的跳转路径：
   ```js
   // 修改前
   navTo(`/pages/heartFeedbackDetail/heartFeedbackDetail?info=${JSON.stringify(item)}`)
   
   // 修改后
   navTo(`/packageUser/pages/heartFeedbackDetail/heartFeedbackDetail?info=${JSON.stringify(item)}`)
   // 如果heartFeedbackDetail也在分包中
   ```

### 7. 银行卡管理页面迁移

1. 复制 `pages/myBankCard/myBankCard.vue` 到 `packageUser/pages/myBankCard/myBankCard.vue`
2. 更新页面中的跳转路径：
   ```js
   // 修改前
   navTo('/pages/opBankCard/opBankCard')
   
   // 修改后
   navTo('/packageUser/pages/opBankCard/opBankCard')
   ```

### 8. 添加银行卡页面迁移

1. 复制 `pages/opBankCard/opBankCard.vue` 到 `packageUser/pages/opBankCard/opBankCard.vue`
2. 确保 w-picker 组件引用正确

### 9. 邀请好友页面迁移

1. 复制 `pages/myPoster/myPoster.vue` 到 `packageUser/pages/myPoster/myPoster.vue`
2. 确保 l-painter 相关组件引用正确

### 10. 更新个人中心页面引用

修改 `pages/user/user.vue` 中的所有跳转路径：

```js
// 修改前
navToBeforeLogin('/pages/userProfile/userProfile')
navToBeforeLogin('/pages/heartMeter/heartMeter')
navToBeforeLogin('/pages/myTeam/myTeam')
navToBeforeLogin('/pages/myPoster/myPoster')
navToBeforeLogin('/pages/myBankCard/myBankCard')
navToBeforeLogin('/pages/setting/setting')

// 修改后
navToBeforeLogin('/packageUser/pages/userProfile/userProfile')
navToBeforeLogin('/packageUser/pages/heartMeter/heartMeter')
navToBeforeLogin('/packageUser/pages/myTeam/myTeam')
navToBeforeLogin('/packageUser/pages/myPoster/myPoster')
navToBeforeLogin('/packageUser/pages/myBankCard/myBankCard')
navToBeforeLogin('/packageUser/pages/setting/setting')
```

## 风险控制

1. **备份原始文件**：
   - 在删除原文件前确保分包页面正常工作

2. **渐进式实施**：
   - 一次迁移一个页面，每次迁移后进行测试

3. **路径检查**：
   - 使用全局搜索确保所有引用路径都已更新

4. **回滚准备**：
   - 准备回滚脚本或步骤，以便在出现问题时快速恢复

## 验证测试

1. **功能测试**：
   - 测试每个分包页面的功能是否正常
   - 测试页面间跳转是否正确

2. **性能测试**：
   - 测量主包大小变化
   - 测量分包加载时间

3. **兼容性测试**：
   - 在不同设备上测试分包加载和功能

## 预期成果

1. **主包体积减小**：
   - 预计减少约200KB

2. **加载性能提升**：
   - 首次启动时间缩短
   - 按需加载用户中心页面

3. **结构优化**：
   - 更清晰的项目结构
   - 更好的代码组织
