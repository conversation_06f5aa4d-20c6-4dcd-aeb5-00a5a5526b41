# 删除旧品牌页面计划

## 背景

项目中存在两个品牌页面：
- 旧版品牌页面：`pages/helpPanel/helpPanel.vue` 及其组件 `pages/helpPanel/components/hotWrap/hotWrap.vue`
- 新版品牌页面：`pages/helpPanel/helpPanelNew.vue`

经过分析，旧版品牌页面已经不再被使用，可以安全删除以减小项目体积。

## 分析结果

1. **页面引用分析**：
   - 首页的"查看更多"按钮已经更新为指向新的品牌页面 `pages/helpPanel/helpPanelNew.vue`
   - 在 `pages/heartPool/heartPool.vue` 中有一些被注释掉的代码，原本是跳转到旧品牌页面的
   - tabbar配置中没有使用旧品牌页面

2. **组件使用分析**：
   - `hotWrap` 组件仅在旧的品牌页面中使用，没有其他页面引用
   - 新的品牌页面使用了不同的组件结构和样式

3. **文档记录**：
   - 有文档记录了从旧品牌页面到新品牌页面的迁移过程
   - 文档 `docs/UI优化记录/品牌页面接入首页跳转.md` 记录了将首页"查看更多"按钮从旧页面更新到新页面的过程

## 删除计划

### 1. 备份文件

在删除前，先备份以下文件：
- `pages/helpPanel/helpPanel.vue`
- `pages/helpPanel/components/hotWrap/hotWrap.vue`

### 2. 删除文件

删除以下文件：
- `pages/helpPanel/helpPanel.vue`
- `pages/helpPanel/components/hotWrap/hotWrap.vue`
- 如果 `pages/helpPanel/components/hotWrap` 目录为空，也可以删除该目录
- 如果 `pages/helpPanel/components` 目录为空，也可以删除该目录

### 3. 清理配置

在 `pages.json` 中删除旧品牌页面的配置：
```json
{
  "path": "pages/helpPanel/helpPanel",
  "style": {
    "navigationBarTitleText": "品牌馆"
  }
}
```

### 4. 测试验证

删除后，需要进行以下测试：
- 编译项目，确保没有编译错误
- 测试首页"查看更多"按钮，确保能正确跳转到新品牌页面
- 测试新品牌页面的所有功能，确保正常运行

### 5. 更新文档

在 `docs/UI优化记录` 目录下创建文档，记录删除旧品牌页面的过程和结果。

## 预期收益

1. **减小项目体积**：
   - 删除不再使用的页面和组件，减小项目体积
   - 提高小程序加载速度

2. **简化项目结构**：
   - 减少冗余代码，使项目结构更加清晰
   - 降低维护成本

3. **避免混淆**：
   - 避免开发人员在两个品牌页面之间混淆
   - 确保所有功能都集中在新品牌页面上
