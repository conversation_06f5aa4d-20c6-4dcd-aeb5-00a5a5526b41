# 包裹页面数量显示样式优化

## 问题描述

在包裹页面中，数量显示的样式存在间距问题，导致"数量:"标签与数值之间没有合理的布局结构，这可能是导致申请换货按钮位置异常的原因之一。

## 修改内容

1. 在商品数量显示区域添加了包装容器，将"数量:"标签与数值放在同一个容器中
2. 为数量显示区域添加了合理的样式结构，确保布局更加稳定
3. 在CSS中添加了对应的样式定义，确保数量显示区域的对齐和间距合理

### 代码修改

**修改前：**
```html
<view class="myPackage-container-wrap-info-good-right-other">
  <text class="amount-label">数量:</text>{{itm.amount}}
  <view class="myPackage-container-wrap-info-good-right-other-right">
    <image src="@/static/index/i_1.png" mode=""></image>
    {{itm.price}}
  </view>
</view>
```

**修改后：**
```html
<view class="myPackage-container-wrap-info-good-right-other">
  <view class="myPackage-container-wrap-info-good-right-other-left">
    <text class="amount-label">数量:</text>{{itm.amount}}
  </view>
  <view class="myPackage-container-wrap-info-good-right-other-right">
    <image src="@/static/index/i_1.png" mode=""></image>
    {{itm.price}}
  </view>
</view>
```

**CSS样式添加：**
```css
&-left {
  display: flex;
  align-items: center;
}
```

## 修改原理

1. 通过添加包装容器，使"数量:"标签与数值形成一个整体，避免布局混乱
2. 使用flex布局确保元素对齐和间距合理
3. 保持整体布局结构不变，只优化内部元素的组织方式

## 效果对比

**修改前：**
- "数量:"标签与数值之间没有明确的布局结构
- 可能导致申请换货按钮位置异常

**修改后：**
- "数量:"标签与数值形成一个整体，布局更加稳定
- 申请换货按钮位置应该更加稳定，不会被数量显示区域影响

## 后续建议

1. 可以考虑为所有类似的标签-值对添加统一的样式结构，确保整个应用的一致性
2. 监控申请换货按钮的位置是否正常，如果仍有问题，可能需要进一步调整
3. 考虑在其他页面中也采用类似的布局结构，保持整个应用的一致性
