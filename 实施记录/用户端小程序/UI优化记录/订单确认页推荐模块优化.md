# 订单确认页推荐模块优化

## 问题描述

订单确认页面中的推荐模块存在数据绑定问题。由于订单页面会根据不同店铺拆分为多个订单，每个订单都应该显示与该订单相关的推荐人信息和佣金，而不是使用全局的推荐信息。

## 修改内容

1. 将推荐人信息卡片从全局显示改为每个订单单独显示
2. 修改数据绑定源：
   - 推荐预计可得佣金：从 `info.totalCommissionAmount` 改为 `item.commissionAmount`
   - 推荐人信息：从 `info.promoterInfo` 改为 `item.promoterInfo`

注意：底部的推荐人优惠总额保持原有逻辑不变，继续使用 `info.totalShareDiscountAmount`，因为后端接口已经做了汇总处理。

## 修改原理

根据业务需求，订单确认页面会根据不同店铺拆分为多个订单，每个订单都有自己的推荐人信息和佣金数据。通过修改数据绑定源，确保每个订单卡片显示的推荐信息与该订单对应的店铺和商品相匹配，提高数据的准确性。

## 修改文件

- `pages/confirmOrder/confirmOrder.vue`

## 具体修改

1. 推荐人信息卡片条件渲染和数据绑定：
```vue
<!-- 修改前 -->
<view class="confirmOrder-reference" v-if="info.totalCommissionAmount > 0">
  <!-- 推荐人信息 -->
  {{info.promoterInfo?.nickname}} {{info.promoterInfo?.phone}}
  <!-- 推荐预计可得 -->
  {{info.totalCommissionAmount}}
</view>

<!-- 修改后 -->
<view class="confirmOrder-reference" v-if="item.commissionAmount > 0">
  <!-- 推荐人信息 -->
  {{item.promoterInfo?.nickname}} {{item.promoterInfo?.phone}}
  <!-- 推荐预计可得 -->
  {{item.commissionAmount}}
</view>
```

2. 底部推荐人优惠显示（保持原有逻辑不变）：
```vue
<view v-if="info.totalShareDiscountAmount > 0">
  推荐人优惠 <text style="color: #22A3FF;margin-left: 10rpx;">{{info.totalShareDiscountAmount}}</text>
</view>
```

## 后续建议

1. 考虑对推荐模块的样式进行优化，使其更加美观和易于识别
2. 确保在不同机型上的显示效果一致
3. 考虑添加适当的数据加载状态和错误处理
