# 热门推荐商品列表优化

## 问题描述
热门推荐商品列表需要优化为默认一屏显示三个商品，并支持横向滚动，同时不影响页面整体样式。

## 修改内容
1. 修改了热门推荐部分的样式，使其能够在一屏内显示三个商品
2. 优化了横向滚动的实现方式
3. 调整了商品卡片的宽度和图片尺寸，使其更加协调
4. 保持了原有的商品信息展示方式和整体页面风格

## 修改原理
1. 使用 `white-space: nowrap` 和 `display: inline-block` 实现横向排列和滚动
2. 调整商品卡片宽度为 220rpx，确保一屏能够显示三个商品
3. 相应调整商品图片尺寸，保持美观比例
4. 保留原有的商品名称和价格样式，确保信息展示清晰

## 效果对比
**修改前**：热门推荐商品列表可能无法在一屏内完整显示三个商品，滚动效果不够流畅。

**修改后**：热门推荐商品列表默认一屏显示三个商品，支持流畅的横向滚动，整体页面样式保持不变。

## 后续建议
1. 可以考虑为滚动添加指示器，提示用户有更多商品可以滚动查看
2. 可以考虑添加点击商品卡片的跳转功能，提升用户体验
3. 如需进一步优化，可以考虑添加商品卡片的轻微阴影效果，增强立体感
