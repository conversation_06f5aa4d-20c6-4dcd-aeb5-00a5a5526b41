# 售后页面优化

## 问题描述

1. 旧版售后列表页面（pages/afterSaleList/afterSaleList.vue）已被标记为"不再使用"，但其路由配置仍存在于pages.json中
2. 在包裹页面（myPackage.vue）的售后页签中，所有售后订单都显示"退款金额"，但对于换货类型的售后订单应该显示"换货数量"
3. 在售后详情页面（afterSaleDetail.vue）中，对于换货类型的售后订单不需要显示"换货原因"字段

## 修改内容

### 1. 删除pages.json中的旧版售后列表页面路由配置

在pages.json文件中删除以下配置：

```json
{
  "path": "pages/afterSaleList/afterSaleList",
  "style": {
    "navigationBarTitleText": "换货/售后",
    "navigationBarBackgroundColor": "#ffffff"
  }
}
```

### 2. 修改售后页签中的商品信息显示

在`pages/myPackage/myPackage.vue`文件中，修改售后页签中的商品信息显示逻辑：

```javascript
// 修改前
<view class="myPackage-container-wrap-info-good-right-other">
  <view class="refund-label">退款金额:</view>
  <view class="myPackage-container-wrap-info-good-right-other-right">
    <image src="@/static/index/i_1.png" mode=""></image>
    {{item.refundPrice}}
  </view>
</view>

// 修改后
<view class="myPackage-container-wrap-info-good-right-other">
  <view class="refund-label" v-if="item.refundType == 2">换货数量:</view>
  <view class="refund-label" v-else>退款金额:</view>
  <view class="myPackage-container-wrap-info-good-right-other-right" v-if="item.refundType == 2">
    {{item.refundAmount}}
  </view>
  <view class="myPackage-container-wrap-info-good-right-other-right" v-else>
    <image src="@/static/index/i_1.png" mode=""></image>
    {{item.refundPrice}}
  </view>
</view>
```

### 3. 修改售后详情页面，移除"换货原因"字段

在`pages/afterSaleDetail/afterSaleDetail.vue`文件中，修改换货原因字段的显示逻辑：

```javascript
// 修改前
<view class="afterSaleDetailNew-wrap-lineTwo">
  <view>
    {{info.refundType == 2 ? '换货': '退款'}}原因
  </view>
  <view class="afterSaleDetailNew-wrap-lineTwo-right">
    {{info.refundReasonLabel}}
  </view>
</view>

// 修改后
<view class="afterSaleDetailNew-wrap-lineTwo" v-if="info.refundType != 2">
  <view>
    退款原因
  </view>
  <view class="afterSaleDetailNew-wrap-lineTwo-right">
    {{info.refundReasonLabel}}
  </view>
</view>
```

## 修改原则

1. 删除不再使用的页面路由配置，减少代码冗余
2. 根据售后类型（退款或换货）显示不同的信息，提高用户体验
3. 移除对用户不必要的信息，简化界面

## 效果对比

### 修改前
- 旧版售后列表页面的路由配置仍存在于pages.json中
- 所有售后订单都显示"退款金额"，即使是换货类型的订单
- 售后详情页面中，换货类型的订单也显示"换货原因"字段

### 修改后
- 移除了旧版售后列表页面的路由配置
- 根据售后类型显示不同的信息：退款类型显示"退款金额"，换货类型显示"换货数量"
- 售后详情页面中，只有退款类型的订单才显示"退款原因"字段

## 后续建议

1. 继续优化售后页面的用户体验，如添加更明显的状态标识
2. 考虑为换货和退款流程提供更详细的进度展示
3. 在分包实施过程中，确保售后相关页面都放在同一个分包中
