# 助力值明细页面UI优化修正

## 修正内容

根据用户反馈，对助力值明细页面的UI优化进行了以下修正：

### 金额显示修正

- **移除重复的正负号**：
  - 原实现中为金额添加了 `+` 和 `-` 前缀，但数据本身已经包含了正负号
  - 修正后直接显示原始金额数据，避免重复显示正负号

### 修改前代码

```html
<view class="transaction-amount" :class="item.goAndCome == '0' ? 'income' : 'expense'">
  {{item.goAndCome == '0' ? '+' : '-'}} {{item.amount}}
</view>
```

### 修改后代码

```html
<view class="transaction-amount" :class="item.goAndCome == '0' ? 'income' : 'expense'">
  {{item.amount}}
</view>
```

## 修正原理

1. **避免信息冗余**：
   - 数据源中的金额值已经包含了正负号，无需在前端再次添加
   - 重复的正负号会导致显示为 `+ +100.00` 或 `- -29.00` 的形式，造成视觉混乱

2. **保持数据一致性**：
   - 直接使用后端提供的原始数据，确保数据显示的一致性
   - 通过颜色区分（红色和绿色）已经足够清晰地表示收入和支出

## 效果对比

**修改前**：
- 收入显示为：`+ +100.00`（重复的正号）
- 支出显示为：`- -29.00`（重复的负号）

**修改后**：
- 收入显示为：`+100.00`（单个正号）
- 支出显示为：`-29.00`（单个负号）

## 后续建议

1. **数据格式统一**：
   - 建议与后端开发人员沟通，统一金额数据的格式标准
   - 可以考虑由后端提供原始数值，前端根据业务逻辑添加正负号

2. **用户体验优化**：
   - 考虑添加千位分隔符，提高大额数字的可读性
   - 可以考虑为不同类型的交易添加图标，进一步增强视觉识别性
