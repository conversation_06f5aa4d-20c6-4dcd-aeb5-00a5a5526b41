# 分类页面图片显示优化

## 问题描述

在分类页面（`pages/heartPool/heartPool.vue`）中，左侧的店铺 logo 图片显示不全，导致页面美观度受到影响。具体表现为图片只显示左半部分，右半部分被裁剪。

问题原因：
1. 客户上传的店铺 logo 尺寸不固定
2. 图片使用了 `widthFix` 模式，该模式会保持图片原始宽高比，但当图片比例与容器不匹配时会导致显示问题
3. 图片容器有固定尺寸（宽度 256rpx，高度 331rpx），但图片样式只设置了宽度

## 修改内容

修改了 `pages/heartPool/components/peopleDetail/peopleDetail.vue` 文件中的图片显示模式和样式：

1. 将图片的 `mode` 属性从 `widthFix` 改为 `aspectFill`：
   ```html
   <!-- 修改前 -->
   <image :src="imgUrl + storeInfo.logoAddr" mode="widthFix"></image>
   
   <!-- 修改后 -->
   <image :src="imgUrl + storeInfo.logoAddr" mode="aspectFill"></image>
   ```

2. 为图片添加 `height: 100%` 样式，确保图片完全填满容器：
   ```css
   /* 修改前 */
   >image {
       width: 100%;
   }
   
   /* 修改后 */
   >image {
       width: 100%;
       height: 100%;
   }
   ```

## 修改原理

- `aspectFill` 模式会保持图片的原始宽高比，同时确保图片填满整个容器。短边会完全显示，长边可能会被裁剪
- 这种模式特别适合处理不同尺寸的图片，能够保证视觉上的统一性
- 对于店铺 logo 来说，通常中心部分是最重要的，这种裁剪方式能保留 logo 的核心部分

## 效果对比

### 修改前
- 图片显示不全，只显示左半部分
- 视觉效果不佳，影响用户体验

### 修改后
- 图片完整填满容器，保持原始比例
- 所有店铺 logo 都能以统一的方式展示，提升页面美观度

## 后续建议

1. 如果条件允许，可以在上传环节增加图片裁剪功能，让用户自行决定 logo 的显示区域
2. 考虑为店铺 logo 提供尺寸建议，引导用户上传更适合展示的图片
3. 定期检查图片显示效果，确保不同设备上的展示一致性
