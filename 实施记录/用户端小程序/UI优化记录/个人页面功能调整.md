# 个人页面功能调整

## 需求背景
1. 隐藏浏览记录功能
2. 将银行卡管理功能从"其他功能"板块移到"营销中心"板块

## 实施内容

### 1. 隐藏浏览记录功能
- 将浏览记录功能模块注释掉，保留代码但不显示在界面上
- 添加注释说明该功能已隐藏

### 2. 银行卡管理功能迁移
- 从"其他功能"板块中移除银行卡管理功能
- 将银行卡管理功能添加到"营销中心"板块中
- 保持原有的图标和点击事件不变

## 实施代码

### 隐藏浏览记录功能
```html
<!-- 浏览记录功能已隐藏
<view @click="navToBeforeLogin('/pages/browseHistory/browseHistory')">
    <image src="@/static/user/tab_2.png" mode=""></image>
    <view>
        浏览记录
    </view>
</view>
-->
```

### 银行卡管理功能迁移
1. 添加到营销中心：
```html
<view class="user-ct-marketing-detail">
    <view @click="navToBeforeLogin('/pages/myTeam/myTeam')">
        <image src="@/static/user/tab_8.png" mode=""></image>
        <view>
            我的团队
        </view>
    </view>
    <view @click="navToBeforeLogin('/pages/myPoster/myPoster')">
        <image src="@/static/user/tab_4.png" mode=""></image>
        <view>
            邀请好友
        </view>
    </view>
    <view @click="navToBeforeLogin('/pages/myBankCard/myBankCard')">
        <image src="@/static/user/tab_6.png" mode=""></image>
        <view>
            银行卡管理
        </view>
    </view>
</view>
```

2. 从其他功能中移除：
```html
<!-- 银行卡管理功能已移至营销中心 -->
```

## 实施原理

1. **功能隐藏**：
   - 通过注释代码的方式隐藏浏览记录功能，而非删除代码
   - 这种方式便于日后需要时快速恢复功能

2. **功能迁移**：
   - 将银行卡管理功能移至营销中心，使相关功能更加集中
   - 保持原有的图标和交互方式，减少用户学习成本
   - 在原位置添加注释，便于开发人员理解代码变更

## 后续建议

1. **用户引导**：
   - 可考虑在银行卡管理功能迁移初期添加引导提示，帮助用户快速找到功能的新位置

2. **功能整合**：
   - 可考虑进一步整合营销中心的功能，使其更加完整和系统化
   - 例如，可以考虑添加收益统计、推广数据等功能
