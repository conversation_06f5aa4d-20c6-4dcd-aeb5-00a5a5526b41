# 售后页面跳转优化

## 问题描述

在申请换货成功后，系统跳转到了一个已被标记为"不再使用"的旧版售后列表页面（pages/afterSaleList/afterSaleList.vue），而不是正确跳转到包裹页面（myPackage.vue）的售后页签。

## 修改内容

### 1. 修改申请换货成功后的跳转逻辑

在 `pages/applyForAfterSale/applyForAfterSale.vue` 文件中，修改提交成功后的跳转逻辑：

```javascript
// 修改前
setTimeout(() => {
  uni.$emit('refreshMyOrder')
  if (isEditAfter.value) {
    uni.$emit('afterSaleOrderDetailInfo')
    toUpPage()
  } else {
    uni.redirectTo({
      url: '/pages/afterSaleList/afterSaleList'
    })
  }
}, 500)

// 修改后
setTimeout(() => {
  uni.$emit('refreshMyOrder')
  if (isEditAfter.value) {
    uni.$emit('afterSaleOrderDetailInfo')
    toUpPage()
  } else {
    // 修改跳转逻辑，直接跳转到包裹页面的售后页签
    uni.redirectTo({
      url: '/pages/myPackage/myPackage?tabIndex=4'
    })
  }
}, 500)
```

### 2. 修改包裹页面中的售后列表跳转逻辑

在 `pages/myPackage/myPackage.vue` 文件中，修改 `toTkDetail` 函数：

```javascript
// 修改前
function toTkDetail() {
  closeRefundSuccessPop()
  navTo('/pages/afterSaleList/afterSaleList')
}

// 修改后
function toTkDetail() {
  closeRefundSuccessPop()
  // 直接切换到当前页面的售后页签
  changeTabSelectIndex({index: 4})
}
```

### 3. 删除不再使用的旧版售后列表页面

删除 `pages/afterSaleList/afterSaleList.vue` 文件，因为该页面已被标记为"不再使用"，并且其功能已完全集成到包裹页面的售后页签中。

## 修改原则

1. 统一用户体验，确保所有售后相关操作都在包裹页面的售后页签中完成
2. 移除冗余页面，减少代码重复和维护成本
3. 确保在删除旧页面前，所有引用该页面的代码都已更新

## 效果对比

### 修改前
- 申请换货成功后跳转到独立的售后列表页面
- 用户需要手动返回到包裹页面查看其他订单
- 存在两个功能重复的页面（旧版售后列表和包裹页面的售后页签）

### 修改后
- 申请换货成功后直接跳转到包裹页面的售后页签
- 用户可以在同一页面内轻松切换查看不同类型的订单
- 移除了冗余页面，简化了代码结构

## 后续建议

1. 继续检查其他可能引用旧版售后列表页面的代码，确保全部更新
2. 考虑进一步优化包裹页面的售后页签，提升用户体验
3. 在分包实施过程中，确保售后相关页面都放在同一个分包中，减少跨包引用
