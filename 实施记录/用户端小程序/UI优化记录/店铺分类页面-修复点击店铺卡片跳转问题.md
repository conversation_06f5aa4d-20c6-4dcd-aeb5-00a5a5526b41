# 店铺分类页面 - 修复点击店铺卡片跳转问题

## 问题描述

在店铺分类页面（heartPoolNew.vue）中，点击店铺卡片跳转到店铺详情页时，storeManageId 参数传递为空导致页面报错。

## 修改内容

1. 在 formatShopList 计算属性中，保留原始数据的完整结构，只添加UI显示需要的字段：

```javascript
return list.value.map(shop => {
  // 保留原始数据结构，只添加UI显示需要的字段
  return {
    ...shop, // 保留原始数据的所有字段
    // 为UI显示增加的字段
    logo: imgUrl + shop.logoAddr,
    name: shop.storeName,
    level: shop.storeLevel || 0,
    // 其他UI字段...
  };
});
```

2. 在 onCardClick 函数中，直接将整个 shop 对象传递给 navToStoreIndex 函数，与原页面中的「立即助力」按钮保持一致：

```javascript
const onCardClick = (shop) => {
  // 直接将整个shop对象传递给navToStoreIndex函数，与原页面保持一致
  navToStoreIndex(shop);
};
```

## 修改原理

1. 保留原始数据的完整结构，确保不会丢失关键字段
2. 直接将整个对象传递给 navToStoreIndex 函数，与原页面中的「立即助力」按钮完全保持一致
3. navToStoreIndex 函数内部会从传入的对象中提取 storeManageId、id 或 storeId 字段，以及 sysUserId 字段

## 效果对比

修改前：点击店铺卡片时，可能因为 storeManageId 参数为空导致页面跳转失败或报错

修改后：点击店铺卡片时，与原页面中的「立即助力」按钮使用相同的跳转逻辑，确保页面正常跳转

## 后续建议

1. 建议在其他类似的店铺卡片组件中也采用相同的数据传递方式，保持代码一致性
2. 建议在数据格式化时尽量保留原始数据结构，避免丢失关键字段
