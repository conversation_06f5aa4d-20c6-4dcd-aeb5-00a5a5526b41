# 删除旧店铺分类页面计划

## 背景

项目中存在两个店铺分类页面：
- 旧版店铺分类页面：`pages/heartPool/heartPool.vue`
- 新版店铺分类页面：`pages/heartPool/heartPoolNew.vue`

经过分析，旧版店铺分类页面已经不再被主动使用，可以考虑删除以减小项目体积。但需要注意的是，旧页面中使用的 `peopleDetail` 组件仍被其他页面引用。

## 分析结果

1. **页面引用分析**：
   - 旧的店铺分类页面 `pages/heartPool/heartPool.vue` 仍然在 `pages.json` 中配置，但在 tabbar 中已被注释掉
   - 当前 tabbar 使用的是新的店铺分类页面 `pages/heartPool/heartPoolNew.vue`
   - 没有发现其他页面直接跳转到旧的店铺分类页面

2. **组件使用分析**：
   - `pages/heartPool/components/peopleDetail/peopleDetail.vue` 组件不仅在旧的店铺分类页面中使用，还在 `pages/storeCollection/storeCollection.vue`（店铺收藏页面）中使用
   - 这意味着即使删除旧的店铺分类页面，也不能删除 peopleDetail 组件

3. **文档记录**：
   - 有文档记录了店铺分类页面的优化和接口对接过程
   - 文档 `docs/UI优化记录/分类页面图片显示优化.md` 记录了对 peopleDetail 组件的优化

## 删除计划

### 1. 备份文件

在删除前，先备份以下文件：
- `pages/heartPool/heartPool.vue`

### 2. 移动组件

由于 `peopleDetail` 组件仍被其他页面使用，需要将其移动到公共组件目录：

1. 创建公共组件目录（如果不存在）：`components/peopleDetail`
2. 复制 `pages/heartPool/components/peopleDetail/peopleDetail.vue` 到 `components/peopleDetail/peopleDetail.vue`
3. 修改 `pages/storeCollection/storeCollection.vue` 中的引用路径：
   ```javascript
   // 修改前
   import peopleDetail from '@/pages/heartPool/components/peopleDetail/peopleDetail.vue';
   
   // 修改后
   import peopleDetail from '@/components/peopleDetail/peopleDetail.vue';
   ```

### 3. 删除文件

删除以下文件：
- `pages/heartPool/heartPool.vue`

### 4. 清理配置

在 `pages.json` 中删除旧店铺分类页面的配置：
```json
{
  "path": "pages/heartPool/heartPool",
  "style": {
    "navigationBarTitleText": "分类",
    "enablePullDownRefresh": true
  }
}
```

### 5. 测试验证

删除后，需要进行以下测试：
- 编译项目，确保没有编译错误
- 测试店铺收藏页面，确保 peopleDetail 组件正常显示
- 测试新店铺分类页面的所有功能，确保正常运行

### 6. 更新文档

在 `docs/UI优化记录` 目录下创建文档，记录删除旧店铺分类页面的过程和结果。

## 风险评估

1. **组件依赖风险**：
   - 由于 peopleDetail 组件被其他页面使用，移动组件可能导致引用路径问题
   - 需要确保所有使用该组件的页面都更新了引用路径

2. **功能缺失风险**：
   - 旧页面可能包含新页面未实现的功能
   - 需要确保新页面已完全实现旧页面的所有功能

3. **样式兼容性风险**：
   - 移动 peopleDetail 组件可能导致样式问题
   - 需要确保组件在新位置仍能正常显示

## 预期收益

1. **减小项目体积**：
   - 删除不再使用的页面，减小项目体积
   - 提高小程序加载速度

2. **简化项目结构**：
   - 减少冗余代码，使项目结构更加清晰
   - 降低维护成本

3. **避免混淆**：
   - 避免开发人员在两个店铺分类页面之间混淆
   - 确保所有功能都集中在新店铺分类页面上
