# 售后页面业务逻辑优化

## 问题描述

1. 在售后页面中，对于已被商家拒绝的售后单（status == 5），不应显示"撤销申请"按钮，但当前逻辑允许除了已完成、已关闭状态外的所有售后单都可以撤销申请
2. 在售后详情页面(afterSaleDetail.vue)中存在以下问题：
   - 对于拒绝状态(status == 5)的售后单，不应显示"撤销申请"按钮
   - "修改申请"按钮当前在待商家处理状态(status == 0)、拒绝状态(status == 5)和退款中状态(status == 3)时都显示，但实际上只应在待商家处理状态(status == 0)时显示
   - 对于换货类型(refundType == 2)被拒绝的售后单，顶部标题显示"店铺拒绝退款"，应改为"店铺拒绝换货"
   - 在拒绝状态的信息展示中，缺少"拒绝时间"的标题，使时间显示不够清晰

## 修改内容

### 1. 修改售后页面中的"撤销申请"按钮逻辑

在`pages/myPackage/myPackage.vue`文件中，修改"撤销申请"按钮的显示条件：

```javascript
// 修改前
<view class="operation-btn cancel-btn" @click.stop="dealAfterSaleOrder(item)"
    v-if="item.status != 4 && item.status != 6 && item.status != 7 && item.status != 8">
    撤销申请
</view>

// 修改后
<view class="operation-btn cancel-btn" @click.stop="dealAfterSaleOrder(item)"
    v-if="item.status == 0">
    撤销申请
</view>
```

### 2. 修改售后详情页面中的按钮逻辑

在`pages/afterSaleDetail/afterSaleDetail.vue`文件中，修改"修改申请"和"撤销申请"按钮的显示条件：

```javascript
// 修改前
<view @click="toAfterSale" v-if="info.status == 3 || info.status == 5 || info.status == 0">
    修改申请
</view>
<view @click.stop="dealOrder"
    v-if="info.status != 4 && info.status != 6 && info.status != 7 && info.status != 8">
    撤销申请
</view>

// 修改后
<view @click="toAfterSale" v-if="info.status == 0">
    修改申请
</view>
<view @click.stop="dealOrder" v-if="info.status == 0">
    撤销申请
</view>
```

### 3. 修改售后详情页面中的拒绝状态标题和内容

在`pages/afterSaleDetail/afterSaleDetail.vue`文件中，修改拒绝状态的标题和内容显示：

```javascript
// 修改前
infoRes.title = '店铺拒绝退款'
infoRes.time = info.value.updateTime
infoRes.content =
    `${info.value.refundExplain ? ('拒绝说明:' + info.value.refundExplain + '\n\n') : ''}拒绝原因:${info.value.refusedExplain}`

// 修改后
infoRes.title = info.value.refundType == 2 ? '店铺拒绝换货' : '店铺拒绝退款'
infoRes.time = info.value.updateTime
infoRes.content =
    `${info.value.refundExplain ? ('拒绝说明:' + info.value.refundExplain + '\n\n') : ''}拒绝原因:${info.value.refusedExplain}\n\n拒绝时间:${info.value.updateTime}`
```

## 修改原则

1. 按照业务逻辑，只有待商家处理状态(status == 0)的售后单才能撤销申请和修改申请
2. 根据售后类型(退款或换货)显示对应的拒绝信息
3. 增加"拒绝时间"标题，使信息展示更加清晰

## 效果对比

### 修改前
- 已被商家拒绝的售后单也显示"撤销申请"按钮
- 售后详情页面中，拒绝状态和退款中状态的售后单也显示"修改申请"按钮
- 换货类型被拒绝的售后单顶部标题显示"店铺拒绝退款"
- 拒绝状态的信息展示中没有"拒绝时间"的标题

### 修改后
- 只有待商家处理状态的售后单才显示"撤销申请"按钮
- 售后详情页面中，只有待商家处理状态的售后单才显示"修改申请"和"撤销申请"按钮
- 换货类型被拒绝的售后单顶部标题显示"店铺拒绝换货"
- 拒绝状态的信息展示中增加了"拒绝时间"的标题，使信息展示更加清晰

## 后续建议

1. 考虑为不同状态的售后单添加更明显的视觉区分，如颜色标识
2. 优化售后流程的用户引导，提高用户体验
3. 考虑增加售后状态变更的通知功能，让用户及时了解售后进度
