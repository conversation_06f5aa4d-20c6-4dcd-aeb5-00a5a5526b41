# 修复 helpPanelNew 页面重复请求问题

## 问题描述

在 `pages/helpPanel/helpPanelNew.vue` 和 `pages/helpPanel/helpPanel.vue` 页面中，发现存在重复调用 API 的问题，导致网络请求冗余。

## 问题原因分析

经过代码分析，发现以下几个问题：

1. `listGet` 函数默认会监听 `options` 变化并自动发起请求，但页面中没有设置 `isWatchOptions: false`
2. 在 `onLoad` 函数中，先调用 `refreshPage()`，然后又调用 `refresh()`，可能导致重复请求
3. 在 `pages/helpPanel/helpPanelNew.vue` 中使用了 `navTo` 函数，但没有导入该函数

## 修改内容

### 1. 修复 `navTo` 函数未导入问题

在 `pages/helpPanel/helpPanelNew.vue` 中添加 `navTo` 函数的导入：

```javascript
import { adNavTo, listGet, navToGoodDetail, navToStoreIndex, navTo } from '@/hooks';
```

### 2. 禁用 `options` 变化自动请求

在 `listGet` 函数调用中添加 `isWatchOptions: false` 参数：

```javascript
const {
  list,
  refresh
} = listGet({
  apiUrl: uni.api.findStoreManageList,
  isReqTypeReq: false,
  isWatchOptions: false, // 不监听options变化自动请求
  options: computed(() => ({
    storeType: storeType.value,
    pattern: 0
  })),
});
```

### 3. 优化异步执行顺序

将 `setTimeout` 替换为 `Promise.resolve().then`，确保异步执行顺序：

```javascript
// 加载页面数据
refreshPage();
// 单独调用刷新店铺列表，使用Promise确保顺序执行
Promise.resolve().then(() => {
  refresh();
});
```

## 修改原则

1. 保持原有业务逻辑不变
2. 减少不必要的网络请求
3. 确保代码执行顺序的可预测性

## 效果对比

修改前：页面加载时会发起多次重复的网络请求
修改后：页面加载时只会发起必要的网络请求，避免了重复调用

## 后续建议

1. 在使用 `listGet` 函数时，根据实际需求设置 `isWatchOptions` 参数
2. 在页面加载时，避免重复调用数据刷新函数
3. 使用 Promise 或其他异步控制方式，确保异步操作的执行顺序
