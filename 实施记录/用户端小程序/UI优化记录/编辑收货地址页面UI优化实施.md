# 编辑收货地址页面UI优化实施记录

## 优化内容

根据页面整体美观度需求，对编辑收货地址页面进行了样式布局优化，主要包括以下几个方面：

### 1. 表单项设计优化

- **整体布局改进**：
  - 将页面背景色改为浅灰色 `#F8F8F8`，表单项背景为白色，增强层次感
  - 优化表单项内边距，从原来的高度固定100rpx改为更加灵活的padding设置
  - 优化表单项分隔线，使用更加细腻的1rpx边框
  - 增加表单项之间的视觉区分度

- **标签与输入区域优化**：
  - 重新设计标签区域，使用flex布局使其更加灵活
  - 将必填项标记（*）设置为醒目的红色，并优化其与标签文字的间距
  - 为标签文字添加适当的字重，增强可读性
  - 优化输入框样式，提高用户体验

### 2. 选择地址项优化

- **交互体验提升**：
  - 为"选择地址"项添加hover效果，提供明确的可点击反馈
  - 优化右箭头图标，添加过渡动画效果
  - 点击时箭头有轻微位移，增强交互感
  - 背景色变化提供额外的视觉反馈

- **视觉设计改进**：
  - 调整文字与图标的间距和对齐方式
  - 优化箭头图标的大小和样式
  - 使用`aspectFit`模式确保图标显示正确

### 3. 开关控件优化

- **样式协调**：
  - 调整开关控件的颜色为`#22A3FF`，与应用整体风格保持一致
  - 优化开关控件的位置，使用flex布局确保其位于右侧
  - 保持与其他表单项的视觉一致性

### 4. 保存按钮优化

- **视觉吸引力提升**：
  - 重新设计保存按钮，使用圆角设计增加现代感
  - 添加轻微的阴影效果，增强立体感
  - 优化按钮尺寸和内边距，提高可点击性
  - 添加点击反馈效果，增强交互体验

- **布局优化**：
  - 调整按钮在底部的位置和间距
  - 使用更加柔和的过渡效果
  - 优化按钮文字样式，增加字重和调整字号

## 代码修改

### 模板部分修改

```html
<!-- 修改前 -->
<view class="opAddress-line">
  <view class="opAddress-line-left">
    *收货人
  </view>
  <input class="opAddress-line-input" placeholder-class='opAddress-line-placeholderClass' type="text"
    v-model="datas.linkman" placeholder="请输入收货人姓名" />
</view>

<!-- 修改后 -->
<view class="form-item">
  <view class="form-label">
    <text class="required-mark">*</text>
    <text>收货人</text>
  </view>
  <view class="form-input-wrap">
    <input class="form-input" type="text" v-model="datas.linkman" placeholder="请输入收货人姓名" placeholder-class="placeholder-style" />
  </view>
</view>
```

### 选择地址项修改

```html
<!-- 修改前 -->
<view class="opAddress-line">
  <view class="opAddress-line-left">
    *选择地址
  </view>
  <view class="opAddress-line-special" @click="showAddressModal">
    {{datas.areaExplan ? datas.areaExplan : '请选择省市区'}}
    <image src="@/static/right-arrow-gray.png" mode=""></image>
  </view>
</view>

<!-- 修改后 -->
<view class="form-item address-select" hover-class="item-hover" @click="showAddressModal">
  <view class="form-label">
    <text class="required-mark">*</text>
    <text>选择地址</text>
  </view>
  <view class="form-select">
    <text class="select-text">{{datas.areaExplan ? datas.areaExplan : '请选择省市区'}}</text>
    <image class="select-arrow" src="@/static/right-arrow-gray.png" mode="aspectFit"></image>
  </view>
</view>
```

### 保存按钮修改

```html
<!-- 修改前 -->
<view class="opAddress-btn">
  <view @click="submit">
    保存信息
  </view>
</view>

<!-- 修改后 -->
<view class="save-button" hover-class="button-hover" @click="submit">
  <text>保存信息</text>
</view>
```

### 样式部分修改

```scss
/* 修改前 */
page {
  background-color: white;
}

.opAddress {
  padding: 30rpx;

  &-btn {
    position: fixed;
    bottom: 0;
    left: 0;
    z-index: 5;
    width: 750rpx;
    padding: 30rpx;
    background-color: white;

    >view {
      height: 84rpx;
      line-height: 84rpx;
      text-align: center;
      color: white;
      font-size: 30rpx;
      background-color: #22A3FF;
      border-radius: 16rpx;
    }
  }

  &-line {
    display: flex;
    align-items: center;
    height: 100rpx;
    color: #333333;
    font-size: 28rpx;
    border-bottom: 2rpx solid #EEEDED;

    &-placeholderClass {
      color: #666666;
      font-size: 28rpx;
    }

    &-left {
      width: 170rpx;
    }

    &-input {
      color: #666666;
      font-size: 28rpx;
    }

    &-special {
      display: flex;
      align-items: center;
      justify-content: space-between;
      flex: 1;
      color: #666666;
      font-size: 28rpx;

      >image {
        width: 40rpx;
        height: 40rpx;
      }
    }
  }
}

/* 修改后 */
page {
  background-color: #F8F8F8;
}

.opAddress {
  padding: 0;
}

.form-item {
  display: flex;
  align-items: center;
  padding: 28rpx 30rpx;
  background-color: #FFFFFF;
  border-bottom: 1rpx solid #F0F0F0;
}

.form-label {
  width: 180rpx;
  display: flex;
  align-items: center;
  font-size: 28rpx;
  color: #333333;
  font-weight: 500;
}

.required-mark {
  color: #FF4D4F;
  margin-right: 8rpx;
}

.form-input-wrap {
  flex: 1;
}

.form-input {
  width: 100%;
  height: 60rpx;
  font-size: 28rpx;
  color: #333333;
}

.placeholder-style {
  color: #999999;
  font-size: 28rpx;
}

.form-select {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 60rpx;
}

.select-text {
  font-size: 28rpx;
  color: #333333;
}

.select-arrow {
  width: 32rpx;
  height: 32rpx;
  transition: transform 0.2s;
}

.address-select {
  position: relative;
  
  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: transparent;
    transition: background-color 0.2s;
  }
}

.item-hover {
  background-color: #F9F9F9;
  
  .select-arrow {
    transform: translateX(4rpx);
  }
}

.form-switch {
  flex: 1;
  display: flex;
  justify-content: flex-end;
}

.save-button {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  padding: 30rpx;
  background-color: #ffffff;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  z-index: 10;
  
  display: flex;
  align-items: center;
  justify-content: center;
  height: 90rpx;
  background-color: #22A3FF;
  border-radius: 45rpx;
  transition: opacity 0.2s ease;
  margin: 0 30rpx;
  
  text {
    font-size: 32rpx;
    color: #ffffff;
    font-weight: 500;
  }
}

.button-hover {
  opacity: 0.8;
}
```

### 代码优化

修复了几个未使用的变量警告：

1. 移除了未使用的 `getSafeBottom` 导入
2. 注释掉了未使用的 `changeDefault` 函数（当前使用 `switchChange` 代替）
3. 修复了 `submit` 函数中未使用的 `res` 变量
4. 修复了 `getLonAndLat` 函数中未使用的 `reject` 参数

```javascript
// 修改前
import {
  getSafeBottom
} from '@/utils/index.js'

// 修改后
// import {
  // getSafeBottom
// } from '@/utils/index.js'
```

```javascript
// 修改前
function changeDefault() {
  if (!datas.value.isDefault * 1) {
    datas.value.isDefault = 1
  } else {
    datas.value.isDefault = 0
  }
}

// 修改后
// 设置是否默认 - 当前使用switchChange代替此功能
// function changeDefault() {
//   if (!datas.value.isDefault * 1) {
//     datas.value.isDefault = 1
//   } else {
//     datas.value.isDefault = 0
//   }
// }
```

```javascript
// 修改前
let {
  data: res
} = await uni.http.get(uni.api.addAddress, {
  params: datas.value
});

// 修改后
await uni.http.get(uni.api.addAddress, {
  params: datas.value
});
```

```javascript
// 修改前
return new Promise(async (resolve, reject) => {

// 修改后
return new Promise(async (resolve) => {
```

## 效果对比

### 修改前
- 页面背景为纯白色，缺乏层次感
- 表单项设计简单，分隔线较粗
- 必填项标记与标签文字间距不合理
- "选择地址"的可点击性提示不够明确
- 保存按钮为矩形设计，缺乏现代感

### 修改后
- 页面使用浅灰色背景，表单项为白色，增强了层次感
- 表单项设计更加精致，分隔线更加细腻
- 必填项标记使用醒目的红色，与标签文字间距合理
- "选择地址"添加了hover效果和动画，提供明确的可点击反馈
- 保存按钮使用圆角设计，增加了现代感和交互反馈

## 注意事项

1. 本次优化仅涉及UI样式调整，未修改任何业务逻辑
2. 保留了原有的功能和交互方式，确保用户操作习惯不受影响
3. 修复了代码中的几个小问题（未使用的变量和函数）
4. 优化后的设计更加符合现代移动应用的视觉风格
