# 包裹页面申请换货按钮位置优化

## 问题描述

在包裹页面中，申请换货按钮原本位于每个商品信息区域内部，这导致了以下问题：
1. 当有多个商品时，每个商品都会显示一个申请换货按钮，造成视觉混乱
2. 按钮位置不够突出，用户可能难以发现
3. 申请换货是针对整个订单的操作，而不是针对单个商品的操作，放在商品信息区域内部不符合逻辑

## 修改内容

1. 将申请换货按钮从商品信息区域内部移动到右上角（交易状态下方）
2. 添加了新的样式类 `myPackage-container-wrap-info-top-exchange-btn`，使按钮在视觉上与交易状态相关联
3. 调整了按钮的样式，使其更加突出和易于点击
4. 保持原有的点击事件和业务逻辑不变，只是将商品索引固定为第一个商品（idx=0）

### 代码修改

**HTML修改：**
1. 在商品信息区域顶部添加申请换货按钮：
```html
<view class="myPackage-container-wrap-info-top-right">
  {{item.isSender == '1' && item.STATUS == 1 ? '部分发货' : getStatusDetail(item).name}}
</view>
<view class="myPackage-container-wrap-info-top-exchange-btn"
  @click.stop="toAfterSaleSingle(item,0)"
  v-if="getStatusDetail(item).type == 7 && !item.pickUpQrCode && item.goods && item.goods.length > 0">
  申请换货
</view>
```

**CSS修改：**
```css
&-exchange-btn {
  position: absolute;
  right: 0;
  top: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  color: #1A69D1;
  height: 60rpx;
  border: 2rpx solid #1A69D1;
  border-radius: 100rpx;
  padding: 0 20rpx;
  z-index: 10;
}
```

## 修改原理

1. 将申请换货按钮移动到更合理的位置，使其与订单状态相关联
2. 通过绝对定位确保按钮位置固定，不会受到其他元素的影响
3. 使用与原按钮相同的样式和颜色，保持视觉一致性
4. 通过z-index确保按钮不会被其他元素遮挡

## 效果对比

**修改前：**
- 每个商品都有一个申请换货按钮，位于商品信息区域内部
- 按钮位置不够突出，用户可能难以发现
- 多个按钮造成视觉混乱

**修改后：**
- 申请换货按钮位于右上角，与交易状态相关联
- 按钮位置更加突出，用户更容易发现
- 整个订单只有一个申请换货按钮，视觉更加清晰

## 后续建议

1. 考虑为所有操作按钮添加统一的样式和位置规范，提高用户体验的一致性
2. 可以考虑在点击申请换货按钮时，增加一个商品选择的步骤，让用户可以选择要换货的商品
3. 监控用户对新按钮位置的反馈，必要时进行进一步调整
