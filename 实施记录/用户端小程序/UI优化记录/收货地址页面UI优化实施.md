# 收货地址页面UI优化实施记录

## 优化内容

根据页面整体美观度需求，对收货地址页面进行了样式布局优化，主要包括以下几个方面：

### 1. 地址卡片设计优化

- **增强卡片立体感**：
  - 添加了卡片阴影效果 `box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05)`
  - 优化了卡片圆角和内边距，使设计更加现代化
  - 增加了卡片之间的间距，减少视觉拥挤感
  - 添加了点击反馈效果，提升交互体验

- **内部布局优化**：
  - 重新设计了内容排版，使信息层次更加清晰
  - 调整了元素间距，创造更有节奏的视觉层次
  - 优化了文字大小和颜色，提高可读性

### 2. 信息层次优化

- **主次信息区分**：
  - 地址信息：增大字体至30rpx，加粗处理，作为主要信息
  - 联系人信息：调整字体颜色为#666666，作为次要信息
  - "默认"标签：优化样式，使用蓝色背景和边框，更加突出

- **操作区域优化**：
  - 优化了编辑和删除按钮的视觉效果
  - 增大了图标尺寸，提高可识别性
  - 优化了分隔线设计，提升整体质感

### 3. 新增地址按钮优化

- 重新设计了底部的"新增地址"按钮
- 使用圆角设计，增加现代感
- 添加了点击反馈效果
- 优化了按钮文字样式，使其更加醒目

## 代码修改

### 模板部分修改

```html
<!-- 修改前 -->
<view class="myAddress-line" v-for="(item,index) in list" :key="index" @click="selectAddresss(item)">
  <view class="myAddress-line-detail">
    {{ item.areaExplan }}{{ item.areaAddress }}
  </view>
  <view class="myAddress-line-concat">
    {{ item.linkman }} {{ item.phone }}
    <view class="myAddress-line-concat-default" v-if="item.isDefault == 1">
      默认
    </view>
  </view>
  <view class="myAddress-line-operation">
    <view class="myAddress-line-operation-view"
      @click.stop="navTo(`/pages/opAddress/opAddress${parseObjToPath(item)}`)">
      <image src="@/static/myAddress/edit.png" mode=""></image>
      <view>
        编辑
      </view>
    </view>
    <view class="myAddress-line-operation-l">
    </view>
    <view class="myAddress-line-operation-view" @click.stop="showDeleteModal(item)">
      <image src="@/static/myAddress/delete.png" mode=""></image>
      <view>
        删除
      </view>
    </view>
  </view>
</view>

<!-- 修改后 -->
<view class="address-item" hover-class="item-hover" v-for="(item,index) in list" :key="index" @click="selectAddresss(item)">
  <view class="address-content">
    <view class="address-info">
      <view class="address-detail">{{ item.areaExplan }}{{ item.areaAddress }}</view>
      <view class="address-contact">
        <text class="contact-name">{{ item.linkman }}</text>
        <text class="contact-phone">{{ item.phone }}</text>
        <view class="default-tag" v-if="item.isDefault == 1">默认</view>
      </view>
    </view>
    
    <view class="address-actions">
      <view class="action-btn edit" @click.stop="navTo(`/pages/opAddress/opAddress${parseObjToPath(item)}`)">
        <image src="@/static/myAddress/edit.png" mode="aspectFit"></image>
        <text>编辑</text>
      </view>
      <view class="action-divider"></view>
      <view class="action-btn delete" @click.stop="showDeleteModal(item)">
        <image src="@/static/myAddress/delete.png" mode="aspectFit"></image>
        <text>删除</text>
      </view>
    </view>
  </view>
</view>
```

### 样式部分修改

```scss
/* 修改前 */
.myAddress-line {
  background: #ffffff;
  border-radius: 16rpx;
  overflow: hidden;
  padding: 24rpx;
  margin-bottom: 20rpx;
  
  &-operation {
    display: flex;
    align-items: center;
    padding-top: 14rpx;
    border-top: 2rpx solid #F5F5F5;
    
    &-l {
      width: 2rpx;
      height: 40rpx;
      background-color: #F5F5F5;
    }
    
    &-view {
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 28rpx;
      color: #666666;
      flex: 1;
      
      >image {
        width: 24rpx;
        height: 24rpx;
        margin-right: 10rpx;
      }
    }
  }
  
  &-detail {
    color: #333333;
    font-size: 26rpx;
    line-height: 40rpx;
    margin-bottom: 14rpx;
  }
  
  &-concat {
    display: flex;
    align-items: center;
    color: #999999;
    font-size: 28rpx;
    margin-bottom: 20rpx;
    
    &-default {
      width: 105rpx;
      height: 35rpx;
      line-height: 35rpx;
      background: #22a3ff;
      border: 2rpx solid #45bbaf;
      border-radius: 8rpx;
      text-align: center;
      color: white;
      font-size: 26rpx;
      margin-left: 20rpx;
    }
  }
}

/* 修改后 */
.address-item {
  margin-bottom: 24rpx;
  border-radius: 16rpx;
  background-color: #ffffff;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  overflow: hidden;
  transition: transform 0.2s ease;
}

.item-hover {
  transform: scale(0.98);
  opacity: 0.9;
}

.address-content {
  padding: 30rpx;
}

.address-info {
  margin-bottom: 24rpx;
}

.address-detail {
  font-size: 30rpx;
  color: #333333;
  font-weight: 500;
  line-height: 1.5;
  margin-bottom: 16rpx;
}

.address-contact {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}

.contact-name {
  font-size: 28rpx;
  color: #666666;
  margin-right: 16rpx;
}

.contact-phone {
  font-size: 28rpx;
  color: #666666;
}

.default-tag {
  margin-left: 16rpx;
  padding: 4rpx 12rpx;
  background-color: #E6F7FF;
  border: 1rpx solid #22A3FF;
  border-radius: 8rpx;
  font-size: 24rpx;
  color: #22A3FF;
}

.address-actions {
  display: flex;
  align-items: center;
  padding-top: 24rpx;
  border-top: 1rpx solid #F5F5F5;
}

.action-btn {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 12rpx 0;
  
  image {
    width: 32rpx;
    height: 32rpx;
    margin-right: 8rpx;
  }
  
  text {
    font-size: 28rpx;
    color: #666666;
  }
}

.action-btn.edit {
  color: #22A3FF;
}

.action-btn.delete {
  color: #FF4D4F;
}

.action-divider {
  width: 1rpx;
  height: 40rpx;
  background-color: #F0F0F0;
}
```

### 新增地址按钮优化

```scss
/* 修改前 */
.myAddress-btn {
  position: fixed;
  bottom: 0;
  left: 0;
  z-index: 5;
  width: 750rpx;
  padding: 30rpx;
  background-color: white;
  
  >view {
    height: 84rpx;
    line-height: 84rpx;
    text-align: center;
    color: white;
    font-size: 30rpx;
    background-color: #22A3FF;
    border-radius: 16rpx;
  }
}

/* 修改后 */
.add-address-btn {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  padding: 30rpx;
  background-color: #ffffff;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  z-index: 10;
  
  display: flex;
  align-items: center;
  justify-content: center;
  height: 90rpx;
  background-color: #22A3FF;
  border-radius: 45rpx;
  transition: opacity 0.2s ease;
  margin: 0 30rpx;
  
  text {
    font-size: 32rpx;
    color: #ffffff;
    font-weight: 500;
  }
}

.btn-hover {
  opacity: 0.8;
}
```

### 代码优化

修复了一个未使用的变量警告：

```javascript
// 修改前
async function deleteAdd() {
  uni.showLoading({
    mask: true
  })
  let {
    data
  } = await uni.http.get(uni.api.delAddress, {
    params: {
      ...deleteItem,
    }
  });
  refresh()
  uni.showToast({
    title: '操作成功~',
    icon: 'none'
  })
}

// 修改后
async function deleteAdd() {
  uni.showLoading({
    mask: true
  })
  await uni.http.get(uni.api.delAddress, {
    params: {
      ...deleteItem,
    }
  });
  refresh()
  uni.showToast({
    title: '操作成功~',
    icon: 'none'
  })
}
```

## 效果对比

### 修改前
- 地址卡片设计简单，缺乏层次感
- 信息排版较为平淡，主次不分明
- "默认"标签样式较为简单
- 编辑和删除按钮视觉效果一般
- 新增地址按钮为矩形设计

### 修改后
- 地址卡片增加了阴影效果，提升了立体感
- 信息排版更加清晰，主次分明
- "默认"标签使用了更加协调的颜色和样式
- 编辑和删除按钮视觉效果更加突出
- 新增地址按钮使用了圆角设计，更加现代化
- 添加了交互反馈效果，提升了用户体验

## 注意事项

1. 本次优化仅涉及UI样式调整，未修改任何业务逻辑
2. 保留了原有的功能和交互方式，确保用户操作习惯不受影响
3. 修复了代码中的一个小问题（未使用的变量）
4. 优化后的设计更加符合现代移动应用的视觉风格
