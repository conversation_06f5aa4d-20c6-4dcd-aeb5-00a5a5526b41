# 申请换货页面业务逻辑优化

## 问题描述

根据接口说明文档，申请换货页面存在以下业务逻辑问题：

1. 店铺订单换货时不应传递 `refundReason` 对象，但当前代码中无论是店铺订单还是平台订单都传递了此对象
2. 页面允许用户选择换货商品规格，但根据接口说明，不传 `exchangeGoodSpecificationId` 和 `exchangeGoodSpecification` 时，系统会自动使用当前订单商品规格
3. 换货凭证和换货说明未设置为必填项

## 修改内容

### 1. 店铺订单换货不传递 refundReason 对象

修改提交逻辑，针对店铺订单和平台订单区分处理：

```javascript
// 换货
// 店铺订单换货不需要传递refundReason对象
const isPlatform = info.value.isPlatform === '1';

// 不传exchangeGoodSpecificationId和exchangeGoodSpecification，系统会自动使用当前订单商品规格
const baseObj = {
  orderGoodRecordId: i.id,
  refundAmount: i.refundAmount || 1,
  remarks: i.remarks || '',
  refundCertificate: i.refundCertificate || '',
  taoOrderId: i.taoOrderId || '' //判断是否是1688商品，目前仅作上传图片用
};

// 只有平台订单才需要传递refundReason
if (isPlatform && i.refundReasonResult) {
  let refundReasonParse = {
    ...i.refundReasonResult,
    id: i.refundReasonResult.id || i.refundReasonResult.value || '',
  }
  baseObj.refundReason = refundReasonParse;
}

return baseObj;
```

### 2. 移除换货商品选择功能

移除了换货商品选择部分，改为显示提示信息：

```vue
<view class="applyForAfterSale-wrap">
  <view class="applyForAfterSale-wrap-title">
    <view class="applyForAfterSale-wrap-title-spec">

    </view>
    <view>
      换货商品
    </view>
  </view>
  <view class="applyForAfterSale-wrap-line">
    <view>
      将使用原商品规格
    </view>
    <view class="applyForAfterSale-wrap-line-info">
      系统将自动使用当前订单商品规格
    </view>
  </view>
</view>
```

同时移除了选择规格弹窗：

```vue
<!-- 移除选择规格弹窗，默认使用原商品规格 -->
```

### 3. 将换货凭证和换货说明设为必填

添加必填标记和提示信息：

```vue
<view>
  换货凭证 <text class="required-mark">*</text>
</view>
</view>
<view>
  <uni-file-picker fileMediatype="image" :image-styles="imageStyles" limit="5"
    @select="fileSelect($event)" @delete='fileDelete($event)' />
</view>
<view class="applyForAfterSale-wrap-tip">
  请上传换货凭证，以便商家更好地处理您的换货申请
</view>
```

```vue
<view>
  换货说明 <text class="required-mark">*</text>
</view>
</view>
<view>
  <textarea class="applyForAfterSale-wrap-textarea" maxlength='150' placeholder="请填写申请说明，以便店铺客服更好的处理售后问题"
    v-model="list[0].remarks"></textarea>
</view>
<view class="applyForAfterSale-wrap-tip">
  请详细描述换货原因，以便商家更好地处理您的换货申请
</view>
```

添加必填样式：

```scss
.required-mark {
  color: #ff4d4f;
  margin-left: 4rpx;
}

&-wrap-tip {
  font-size: 24rpx;
  color: #999;
  margin-top: 10rpx;
  padding: 0 20rpx;
}

&-wrap-line-info {
  font-size: 24rpx;
  color: #999;
}
```

### 4. 修改提交验证逻辑

添加换货凭证和换货说明的必填验证：

```javascript
// 检查换货凭证和换货说明是否已填写
if (!list.value[0].refundCertificate || list.value[0].refundCertificate.length === 0) {
  uni.showToast({
    title: '请上传换货凭证',
    icon: 'none'
  })
  return;
}

if (!list.value[0].remarks) {
  uni.showToast({
    title: '请填写换货说明',
    icon: 'none'
  })
  return;
}
```

### 5. 根据订单类型显示换货原因

平台订单需要选择换货原因，店铺订单不需要：

```vue
<!-- 平台订单才需要选择换货原因 -->
<view class="applyForAfterSale-wrap" v-if="info.isPlatform === '1'">
  <view class="applyForAfterSale-wrap-title">
    <view class="applyForAfterSale-wrap-title-spec">

    </view>
    <view>
      换货原因 <text class="required-mark">*</text>
    </view>
  </view>
  <view class="applyForAfterSale-wrap-line" v-for="(item,index) in reasonList" :key='index'
    @click="selectedReason(index)">
    <view>
      {{item.text || item.name}}
    </view>
    <view class="applyForAfterSale-wrap-line-circle">
      <view v-if="selectedReasonIndex === index"></view>
    </view>
  </view>
</view>
```

## 修改原则

1. 严格遵循接口说明文档的要求
2. 简化用户操作流程，移除不必要的选择步骤
3. 明确标识必填项，提高用户体验
4. 区分平台订单和店铺订单的不同处理逻辑

## 效果对比

### 修改前

1. 所有订单类型都需要选择换货原因
2. 需要选择换货商品规格
3. 换货凭证和换货说明不是必填项
4. 店铺订单换货也传递 refundReason 对象

### 修改后

1. 只有平台订单需要选择换货原因
2. 移除换货商品规格选择，默认使用原商品规格
3. 换货凭证和换货说明设为必填项
4. 店铺订单换货不传递 refundReason 对象

## 后续建议

1. 考虑进一步优化用户体验，如添加更详细的操作指引
2. 可以考虑在换货说明中添加常用换货原因的快速选择
3. 对于店铺订单，可以考虑添加更多店铺相关信息展示
