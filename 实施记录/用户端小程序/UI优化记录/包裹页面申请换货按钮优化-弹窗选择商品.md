# 包裹页面申请换货按钮优化-弹窗选择商品（更新版）

## 问题描述

在包裹页面中，「申请换货」按钮原本位于每个商品项中，当订单包含多个商品时，用户只能对单个商品进行换货申请。这种设计存在以下问题：

1. 每个商品项都有一个「申请换货」按钮，导致界面视觉混乱
2. 当用户需要对多个商品进行换货时，需要多次操作，用户体验不佳
3. 按钮位置不够突出，用户可能难以发现

## 修改内容

1. 将商品项中的「申请换货」按钮移除
2. 在订单底部操作栏添加统一的「申请换货」按钮
3. 创建商品选择弹窗组件，当用户点击「申请换货」按钮时弹出
4. 用户在弹窗中选择需要换货的商品，然后点击确定进入换货申请页面

### 代码修改

1. 创建商品选择弹窗组件 `components/exchangeGoodsPopup/exchangeGoodsPopup.vue`
2. 在 `pages/myPackage/myPackage.vue` 中：
   - 移除商品项中的「申请换货」按钮
   - 在订单底部操作栏添加「申请换货」按钮
   - 导入并使用商品选择弹窗组件
   - 添加相关的处理函数

#### 商品选择弹窗组件

```vue
<template>
  <uni-popup ref="popup" type="center">
    <view class="exchange-goods-popup">
      <view class="exchange-goods-popup-header">
        <text class="exchange-goods-popup-title">选择发货商品</text>
        <view class="exchange-goods-popup-close" @click="close">
          <text class="icon-close">×</text>
        </view>
      </view>

      <scroll-view scroll-y class="exchange-goods-popup-content">
        <view
          v-for="(item, index) in goodsList"
          :key="index"
          class="exchange-goods-popup-item"
          :class="{ 'selected': selectedIndex === index }"
          @click="selectItem(index)"
        >
          <!-- 商品信息展示 -->
        </view>
      </scroll-view>

      <view class="exchange-goods-popup-footer">
        <button class="exchange-goods-popup-confirm" @click="confirm">确定</button>
      </view>
    </view>
  </uni-popup>
</template>
```

#### 订单底部操作栏添加「申请换货」按钮

```vue
<view class="operation-btn exchange-btn" @click.stop="showExchangeGoodsPopup(item)"
  v-if="getStatusDetail(item).type == 7 && !item.pickUpQrCode && item.goods && item.goods.length > 0">
  申请换货
</view>
```

#### 添加商品选择弹窗相关函数

```javascript
// 显示换货商品选择弹窗
function showExchangeGoodsPopup(item) {
  if (!item.goods || item.goods.length === 0) {
    uni.showToast({
      title: '暂无可申请换货的商品',
      icon: 'none'
    });
    return;
  }

  // 过滤掉已经申请过售后的商品
  const availableGoods = item.goods.filter(good => (good.ongoingRefundCount || 0) < good.amount);

  if (availableGoods.length === 0) {
    uni.showToast({
      title: '暂无可申请换货的商品',
      icon: 'none'
    });
    return;
  }

  currentOrder.value = item;
  currentOrderGoods.value = availableGoods;

  nextTick(() => {
    exchangeGoodsPopup.value.open();
  });
}

// 处理商品选择确认
function handleExchangeGoodsConfirm(selectedGoods, selectedIndex) {
  navTo('/pages/applyForAfterSale/applyForAfterSale', () => {
    setTimeout(() => {
      let obj = {
        selectedId: [selectedGoods.id],
        info: currentOrder.value,
        tkType: 3
      };
      uni.$emit('applyForAfterSaleInfo', obj);
    }, 300);
  });
}
```

## 修改原理

1. 将「申请换货」按钮从商品项移至订单底部操作栏，使界面更加整洁
2. 通过弹窗让用户选择需要换货的商品，解决了多商品订单中只能对单个商品申请换货的问题
3. 保持原有的业务逻辑不变，只是增加了一个商品选择的中间步骤
4. 弹窗中展示商品的详细信息，包括图片、名称、规格等，帮助用户更好地识别商品

## 效果对比

**修改前：**
- 每个商品项都有一个「申请换货」按钮
- 用户只能对单个商品进行换货申请
- 界面视觉混乱，按钮不够突出

**修改后：**
- 订单底部操作栏有一个统一的「申请换货」按钮
- 用户可以在弹窗中选择需要换货的商品
- 界面更加整洁，按钮更加突出
- 用户体验更好，操作更加直观

## 更新内容（2023-07-10）

根据用户反馈，对商品选择弹窗进行了以下优化：

1. 将弹窗从中间弹出改为从底部弹出，符合移动端交互习惯
2. 移除了商品项中的买家账号和配送方式信息，使界面更加简洁
3. 增加了商品数量的显示，方便用户了解商品数量
4. 优化了弹窗的样式，包括圆角、按钮样式等，提升视觉体验

## 更新内容（2023-07-11）

根据用户反馈，对商品选择弹窗进行了进一步优化：

1. 修复了弹窗打开后上方页签部分的高亮效果问题，调整了弹窗的背景色和遮罩透明度
2. 优化了商品选择控件的显示效果，使其更加细腻，包括：
   - 调整了选择按钮的大小和样式
   - 未选中状态显示为白色圆圈，选中状态显示为蓝色圆圈
   - 增加了选择按钮的边框和间距
3. 修复了弹窗底部的突兀效果，添加了更自然的底部安全区域
4. 优化了弹窗头部的样式，添加了顶部指示器，使用户更容易理解可以下拉关闭
5. 调整了商品项的样式，使其更加美观：
   - 增加了轻微的阴影效果
   - 调整了内边距和外边距
   - 优化了选中状态的视觉效果

## 更新内容（2023-07-12）

根据用户反馈，对商品选择弹窗进行了进一步优化：

1. 修复了弹窗打开时顶部订单页签选择部分的高亮效果问题
   - 增加了弹窗打开/关闭事件处理
   - 通过自定义事件通知页面隐藏/显示页签高亮效果
   - 添加了全局样式控制页签的显示层级
2. 优化了商品选择控件的样式间距，使其更加协调
   - 调整了选择按钮的大小和边框
   - 增加了选择按钮的外边距
   - 添加了flex-shrink属性防止按钮被挤压
   - 调整了选中状态的图标大小

## 更新内容（2023-07-13）

根据用户反馈，对商品选择弹窗进行了进一步优化：

1. 修复了商品选中效果超出弹窗边框的问题
   - 为内容区域添加了overflow: hidden属性
   - 为商品项添加了position: relative和overflow: hidden属性
   - 调整了选择按钮的外边距，添加了右侧边距
2. 添加了取消选中功能
   - 修改了选择商品的逻辑，点击已选中的商品可以取消选中
   - 优化了用户交互体验，使操作更加灵活

## 更新内容（2023-07-14）

根据用户反馈，对商品选择弹窗进行了进一步优化：

1. 修复了商品选中边框超出弹窗边框的问题
   - 为商品项添加了box-sizing: border-box和width: 100%属性
   - 为内容区域增加了左右内边距，从20rpx调整为30rpx
   - 为商品内容区域添加了max-width限制，防止内容溢出
   - 确保所有元素都使用border-box盒模型，避免边框导致宽度计算问题

### 代码修改（2023-07-10）

```vue
<!-- 修改前 -->
<uni-popup ref="popup" type="center">
  <!-- 弹窗内容 -->
</uni-popup>

<!-- 修改后 -->
<uni-popup ref="popup" type="bottom" :safeArea="true">
  <!-- 弹窗内容 -->
</uni-popup>
```

```vue
<!-- 修改前：商品项信息 -->
<view class="exchange-goods-popup-item-name">{{ item.goodName }}</view>
<view class="exchange-goods-popup-item-spec">规格: {{ item.specification || item.goodSpecification || '无' }}</view>
<view class="exchange-goods-popup-item-delivery">配送方式: {{ item.distribution || '快递' }}</view>
<view class="exchange-goods-popup-item-account">买家账号: {{ orderInfo.buyerAccount || '' }}</view>

<!-- 修改后：商品项信息 -->
<view class="exchange-goods-popup-item-name">{{ item.goodName }}</view>
<view class="exchange-goods-popup-item-spec">规格: {{ item.specification || item.goodSpecification || '无' }}</view>
<view class="exchange-goods-popup-item-amount">数量: {{ item.amount }}</view>
```

### 代码修改（2023-07-11）

```vue
<!-- 修改前 -->
<uni-popup ref="popup" type="bottom" :safeArea="true">
  <!-- 弹窗内容 -->
</uni-popup>

<!-- 修改后 -->
<uni-popup ref="popup" type="bottom" :safeArea="true" :maskClick="true" backgroundColor="rgba(0, 0, 0, 0.6)">
  <!-- 弹窗内容 -->
</uni-popup>
```

```vue
<!-- 修改前：选择按钮 -->
<view class="exchange-goods-popup-item-select">
  <view class="select-icon" v-if="selectedIndex === index">✓</view>
</view>

<!-- 修改后：选择按钮 -->
<view class="exchange-goods-popup-item-select" :class="{ 'selected': selectedIndex === index }">
  <view class="select-icon" v-if="selectedIndex === index">✓</view>
</view>
```

```scss
/* 修改前：选择按钮样式 */
&-select {
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  background-color: #1989fa;
  display: flex;
  align-items: center;
  justify-content: center;

  .select-icon {
    color: #fff;
    font-size: 24rpx;
  }
}

/* 修改后（2023-07-11）：选择按钮样式 */
&-select {
  width: 44rpx;
  height: 44rpx;
  border-radius: 50%;
  border: 2rpx solid #DDDDDD;
  background-color: #FFFFFF;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 20rpx;

  &.selected {
    border-color: #1A69D1;
    background-color: #1A69D1;
  }

  .select-icon {
    color: #fff;
    font-size: 24rpx;
  }
}

/* 修改后（2023-07-12）：选择按钮样式 */
&-select {
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  border: 1rpx solid #DDDDDD;
  background-color: #FFFFFF;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 30rpx;
  flex-shrink: 0;

  &.selected {
    border-color: #1A69D1;
    background-color: #1A69D1;
  }

  .select-icon {
    color: #fff;
    font-size: 20rpx;
  }
}
```

```vue
<!-- 修改前：弹窗底部 -->
<view class="exchange-goods-popup-footer">
  <button class="exchange-goods-popup-confirm" @click="confirm">确定</button>
</view>

<!-- 修改后：弹窗底部 -->
<view class="exchange-goods-popup-footer">
  <button class="exchange-goods-popup-confirm" @click="confirm">确定</button>
</view>
<!-- 底部安全区域 -->
<view class="exchange-goods-popup-safe-area"></view>
```

### 代码修改（2023-07-12）

```vue
<!-- 修改前：弹窗组件 -->
<uni-popup ref="popup" type="bottom" :safeArea="true" :maskClick="true" backgroundColor="rgba(0, 0, 0, 0.6)">
  <!-- 弹窗内容 -->
</uni-popup>

<!-- 修改后：弹窗组件添加事件监听 -->
<uni-popup ref="popup" type="bottom" :safeArea="true" :maskClick="true" backgroundColor="rgba(0, 0, 0, 0.7)" @change="handlePopupChange">
  <!-- 弹窗内容 -->
</uni-popup>
```

```js
// 修改前：无事件处理

// 修改后：添加弹窗状态变化处理
// 处理弹窗状态变化
function handlePopupChange(e) {
  // 使用更简单的方式处理页签高亮问题
  if (e.show) {
    // 弹窗打开时，发送自定义事件通知页面
    uni.$emit('popup-opened', { type: 'exchangeGoods' });

    // 添加类名到页面根元素
    setTimeout(() => {
      // 使用uni-app的选择器API
      const query = uni.createSelectorQuery();
      query.select('.uni-page-body').boundingClientRect(data => {
        if (data) {
          // 通过自定义事件通知页面添加类名
          uni.$emit('add-page-class', { className: 'popup-opened' });
        }
      }).exec();
    }, 50);
  } else {
    // 弹窗关闭时，发送自定义事件
    uni.$emit('popup-closed', { type: 'exchangeGoods' });

    // 移除类名
    uni.$emit('remove-page-class', { className: 'popup-opened' });
  }
}
```

```scss
/* 添加全局样式控制页签显示层级 */
/* 弹窗打开时隐藏页签高亮效果 */
.popup-opened .uni-tab__scroll {
  z-index: -1 !important;
}
```

### 代码修改（2023-07-13）

```scss
/* 修改前：内容区域样式 */
&-content {
  max-height: 700rpx;
  padding: 0 20rpx;
}

/* 修改后：内容区域样式添加overflow */
&-content {
  max-height: 700rpx;
  padding: 0 20rpx;
  overflow: hidden;
}
```

```scss
/* 修改前：商品项样式 */
&-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx;
  border-bottom: 1rpx solid #f5f5f5;
  background-color: #FFFFFF;
  margin: 16rpx 0;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);

  &.selected {
    background-color: #F0F7FF;
    border: 1rpx solid #D6E5FF;
  }
}

/* 修改后：商品项样式添加overflow */
&-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx;
  border-bottom: 1rpx solid #f5f5f5;
  background-color: #FFFFFF;
  margin: 16rpx 0;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
  position: relative;
  overflow: hidden;

  &.selected {
    background-color: #F0F7FF;
    border: 1rpx solid #D6E5FF;
  }
}
```

```scss
/* 修改前：选择按钮样式 */
&-select {
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  border: 1rpx solid #DDDDDD;
  background-color: #FFFFFF;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 30rpx;
  flex-shrink: 0;

  &.selected {
    border-color: #1A69D1;
    background-color: #1A69D1;
  }

  .select-icon {
    color: #fff;
    font-size: 20rpx;
  }
}

/* 修改后：选择按钮样式添加右侧边距 */
&-select {
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  border: 1rpx solid #DDDDDD;
  background-color: #FFFFFF;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 20rpx;
  margin-right: 10rpx;
  flex-shrink: 0;

  &.selected {
    border-color: #1A69D1;
    background-color: #1A69D1;
  }

  .select-icon {
    color: #fff;
    font-size: 20rpx;
  }
}
```

```js
/* 修改前：选择商品逻辑 */
function selectItem(index) {
  selectedIndex.value = index;
}

/* 修改后：选择商品逻辑添加取消选中功能 */
function selectItem(index) {
  // 如果点击的是已选中的商品，则取消选中
  if (selectedIndex.value === index) {
    selectedIndex.value = -1;
  } else {
    selectedIndex.value = index;
  }
}
```

### 代码修改（2023-07-14）

```scss
/* 修改前：内容区域样式 */
&-content {
  max-height: 700rpx;
  padding: 0 20rpx;
  overflow: hidden;
}

/* 修改后：内容区域样式添加盒模型属性 */
&-content {
  max-height: 700rpx;
  padding: 0 30rpx;
  overflow: hidden;
  box-sizing: border-box;
  width: 100%;
}
```

```scss
/* 修改前：商品项样式 */
&-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx;
  border-bottom: 1rpx solid #f5f5f5;
  background-color: #FFFFFF;
  margin: 16rpx 0;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
  position: relative;
  overflow: hidden;

  &.selected {
    background-color: #F0F7FF;
    border: 1rpx solid #D6E5FF;
  }
}

/* 修改后：商品项样式添加盒模型属性 */
&-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx;
  border-bottom: 1rpx solid #f5f5f5;
  background-color: #FFFFFF;
  margin: 16rpx 0;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
  position: relative;
  overflow: hidden;
  box-sizing: border-box;
  width: 100%;

  &.selected {
    background-color: #F0F7FF;
    border: 1rpx solid #D6E5FF;
    box-sizing: border-box;
  }
}
```

```scss
/* 修改前：商品内容区域样式 */
&-content {
  display: flex;
  flex: 1;
}

/* 修改后：商品内容区域样式添加宽度限制 */
&-content {
  display: flex;
  flex: 1;
  max-width: calc(100% - 70rpx);
  box-sizing: border-box;
}
```

## 后续建议

1. 可以考虑在弹窗中添加多选功能，让用户可以同时选择多个商品进行换货
2. 添加商品搜索功能，方便用户在大量商品中快速找到需要换货的商品
3. 监控用户对新功能的反馈，必要时进行进一步优化
4. 考虑添加商品排序功能，例如按价格、时间等排序
