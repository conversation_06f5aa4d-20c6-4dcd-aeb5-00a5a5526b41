# 包裹页面申请换货按钮位置修复

## 问题描述
在包裹页面中，"申请换货"按钮与商品金额部分重叠，导致用户界面显示异常。

## 修改内容
调整了"申请换货"按钮的垂直位置，将其向上移动，避免与金额部分重叠。

## 修改文件
- `pages/myPackage/myPackage.vue`

## 具体修改
将"申请换货"按钮的CSS样式中的`bottom`属性从`30rpx`修改为`80rpx`，使按钮位置上移，避免与金额显示重叠。

```scss
/* 修改前 */
&-goodStatus,
&-afterSale {
    position: absolute;
    z-index: 10;
    right: 0;
    bottom: 30rpx;
}

/* 修改后 */
&-goodStatus {
    position: absolute;
    z-index: 10;
    right: 0;
    bottom: 30rpx;
}

&-afterSale {
    position: absolute;
    z-index: 10;
    right: 0;
    bottom: 80rpx;
}
```

## 修改原理
通过调整按钮的垂直位置，使其不再与金额部分重叠，同时保持原有的样式和功能不变。这种修改不会影响其他UI元素和业务逻辑。

## 效果对比
修改前：申请换货按钮与金额显示重叠，影响用户体验。
修改后：申请换货按钮位置上移，与金额显示分开，界面更加清晰。

## 后续建议
可以考虑进一步优化商品卡片的布局，为各元素预留更合理的空间，避免类似的重叠问题。
