# 店铺分类页面接口对接实施方案文档

## 一、概述

本文档详细记录了店铺分类页面（pages/heartPool/heartPoolNew.vue）的接口对接实施方案，主要包括接口调用逻辑和字段绑定信息。

## 二、接口信息

### 1. 接口基本信息

- **接口URL**：`uni.api.findStoreManageList`
- **请求方式**：GET
- **功能描述**：获取店铺列表数据，支持分页和筛选

### 2. 请求参数

| 参数名 | 类型 | 必填 | 描述 | 示例值 |
| --- | --- | --- | --- | --- |
| storeType | Number | 是 | 店铺类型 | 15(品牌馆)、16(生活馆)、17(创业馆) |
| pattern | Number | 是 | 模式 | 0 |

| pageNo | Number | 是 | 页码 | 1 |
| pageSize | Number | 是 | 每页数量 | 10 |

## 三、字段绑定信息

### 1. 数据映射关系

店铺列表数据通过`formatShopList`计算属性进行处理，将API返回的数据映射到UI需要的格式：

| UI字段 | API字段 | 描述 | 备注 |
| --- | --- | --- | --- |
| id | storeManageId | 店铺管理ID | 用于跳转店铺详情页 |
| sysUserId | sysUserId | 系统用户ID | 用于跳转店铺详情页 |
| logo | logoAddr | 店铺logo地址 | 需要拼接图片域名：`imgUrl + shop.logoAddr` |
| name | storeName | 店铺名称 | - |
| level | storeLevel | 店铺等级 | 默认值：0 |
| levelName | storeLevelName | 店铺等级名称 | 默认值：'' |
| score | totalPerformance | 店铺业绩分 | 默认值：0 |
| city | city | 城市 | 默认值：'' |
| type | storeTypeName | 店铺类型名称 | 默认值：'' |
| mainType | mainType | 主要类型 | 默认值：'' |
| mainTypeName | mainTypeName | 主要类型名称 | 默认值：'' |
| tags | [mainTypeName, storeTypeName] | 店铺标签 | 过滤掉空值 |
| description | introduce | 店铺介绍 | 默认值：'' |
| phone | takeOutPhone | 联系电话 | 默认值：'' |

### 2. 代码实现

```javascript
// 将API返回的数据映射到UI需要的格式
const formatShopList = computed(() => {
  if (!list.value || !list.value.length) return [];

  return list.value.map(shop => {
    return {
      id: shop.storeManageId,
      sysUserId: shop.sysUserId,
      logo: imgUrl + shop.logoAddr, // 直接使用logoAddr字段
      name: shop.storeName,
      level: shop.storeLevel || 0,
      levelName: shop.storeLevelName || '',
      score: shop.totalPerformance || 0, // 使用totalPerformance作为店铺业绩分
      city: shop.city || '',
      type: shop.storeTypeName || '',
      mainType: shop.mainType || '',
      mainTypeName: shop.mainTypeName || '',
      tags: [shop.mainTypeName, shop.storeTypeName].filter(Boolean),
      description: shop.introduce || '', // 使用introduce字段作为店铺介绍
      phone: shop.takeOutPhone || ''
    };
  });
});
```

## 四、UI组件与数据绑定

### 0. 购物车按钮

页面右下角添加了购物车按钮，点击跳转到购物车页面，并显示购物车商品数量：

```html
<cartAction></cartAction>
```

购物车数量通过`cartCountHooks`钩子函数获取，并在页面显示时刷新：

```javascript
onShow(() => {
  // 页面显示时刷新购物车数量
  const { cartCountRefresh } = cartCountHooks();
  cartCountRefresh();
});
```

### 1. 店铺卡片

店铺卡片组件展示了店铺的基本信息，包括logo、名称、评分、等级、位置、类型、标签和介绍等。

```html
<view
  v-for="(shop, index) in formatShopList"
  :key="index"
  class="shop-card"
  @click="onCardClick(shop)"
>
  <view class="shop-header">
    <image class="shop-logo" :src="shop.logo" mode="aspectFill"/>
    <view class="shop-title">
      <text class="shop-name">{{ shop.name }}</text>
      <view class="shop-info-right">
        <view class="shop-score">{{ shop.score }}分</view>
        <view class="shop-level">{{ shop.levelName || 'Lv.' + shop.level }}</view>
      </view>
    </view>
  </view>
  <view class="shop-info">
    <view class="location-type">
      <uni-icons :size="14" color="#666666" type="location" />
      <text class="city">{{ shop.city }}</text>
      <uni-icons :size="14" color="#666666" type="shop" />
      <text class="type">{{ shop.type }}</text>
    </view>
  </view>
  <view v-if="shop.tags && shop.tags.length" class="shop-tags">
    <text
      v-for="(tag, tagIndex) in shop.tags.slice(0, 2)"
      :key="tagIndex"
      class="tag"
    >{{ tag }}</text>
  </view>
  <view v-if="shop.description" class="shop-description">
    {{ shop.description }}
  </view>
  <view class="shop-contact" @click.stop="onContactClick($event, shop)">
    <uni-icons :size="16" color="#4788F2" type="phone" />
    <text class="contact-text">联系</text>
  </view>
</view>
```

### 2. 标签页切换

标签页切换时，会更新`storeType`参数，重新请求店铺列表数据：

```javascript
const currentTab = ref(0);
const tabs = ['品牌馆', '生活馆', '创业馆'];
const storeTypeList = [15, 16, 17]; // 对应品牌馆、生活馆、创业馆的storeType值

const switchTab = (index) => {
  if (currentTab.value === index) return;
  currentTab.value = index;
  // 切换标签时重新加载数据
  searchOptions.value.storeType = storeTypeList[index];
  refresh();
};
```

### 3. 搜索功能

搜索功能通过点击搜索框跳转到专门的搜索页面（pages/search/search.vue）：

```html
<view class="search-box" @click="navTo('/pages/search/search')">
  <uni-icons :size="16" color="#AAAAAA" type="search" class="search-icon"/>
  <view class="search-placeholder">搜索您想资助的产品</view>
</view>
```

搜索页面实现了搜索功能和历史搜索记录功能，用户可以在搜索页面输入关键词进行搜索。

## 五、交互功能

### 1. 点击店铺卡片

点击店铺卡片时，会跳转到店铺详情页：

```javascript
const onCardClick = (shop) => {
  navToStoreIndex({
    storeManageId: shop.id,
    sysUserId: shop.sysUserId
  });
};
```

### 2. 点击联系按钮

点击联系按钮时，会拨打店铺的联系电话：

```javascript
const onContactClick = (event, shop) => {
  event.stopPropagation(); // 阻止事件冒泡，避免触发卡片点击

  if (shop.phone) {
    uni.makePhoneCall({
      phoneNumber: shop.phone,
      fail: () => {
        uni.showToast({
          title: '拨打电话失败',
          icon: 'none'
        });
      }
    });
  } else {
    uni.showToast({
      title: '暂无联系电话',
      icon: 'none'
    });
  }
};
```

### 3. 加载更多数据

滚动到底部时，会加载更多数据：

```javascript
const loadMore = () => {
  if (mode.value === 'loading' || mode.value === 'noMore') return;
  refresh(2); // 调用refresh的第二个模式，即加载更多
};
```

## 六、注意事项

1. **图片路径处理**：店铺logo需要拼接图片域名（`imgUrl + shop.logoAddr`）才能正确显示
2. **店铺介绍字段**：店铺介绍使用的是`introduce`字段，而非`storeIntroduce`字段
3. **店铺业绩分字段**：店铺业绩分使用的是`totalPerformance`字段，而非`storeHeartValue`字段
4. **标签显示**：标签使用`mainTypeName`和`storeTypeName`字段，最多显示2个标签
5. **空值处理**：所有字段都添加了默认值处理，避免因数据缺失导致的显示问题
6. **购物车按钮**：页面右下角添加了购物车按钮，点击跳转到购物车页面，并显示购物车商品数量

## 七、实施效果

实施后的店铺分类页面能够正确显示店铺列表数据，包括店铺logo、名称、业绩分、等级、位置、类型、标签和介绍等信息。用户可以通过标签页切换不同类型的店铺，通过搜索功能查找特定店铺，点击店铺卡片跳转到店铺详情页，点击联系按钮拨打店铺的联系电话。

## 八、后续优化建议

1. **图片加载优化**：添加图片加载失败的处理逻辑，显示默认图片
2. **搜索历史记录**：保存搜索历史记录，方便用户快速查找
3. **筛选功能**：添加更多筛选条件，如按业绩分排序、按距离排序等
4. **性能优化**：使用虚拟列表优化长列表的渲染性能

以上是店铺分类页面接口对接实施方案的详细文档，记录了接口调用逻辑和字段绑定信息，为后续维护和优化提供参考。
