# 个人页面新增营销模块设计方案

## 需求背景
当前"我的团队"和"邀请好友"功能位于"其他功能"板块中，但这两个功能与营销推广密切相关，需要单独拎出来形成一个新的板块，提高其可见性和用户使用率。

## 新板块名称
考虑到这两个功能与营销推广相关，推荐以下名称：

**首选方案：**
- **营销中心**（直接明了，突出营销属性）

**备选方案：**
- **推广中心**（强调推广功能）
- **营销推广**（综合表达营销和推广概念）
- **业务拓展**（侧重业务发展角度）
- **推广赚钱**（直接点明推广带来的收益）

## 视觉设计方案

新板块将保持与现有板块一致的视觉风格：

1. **布局结构**：
   - 白色背景卡片
   - 圆角边框（16rpx）
   - 轻微阴影效果（0 2rpx 10rpx rgba(0, 0, 0, 0.05)）
   - 上下间距30rpx

2. **标题样式**：
   - 字体大小：30rpx
   - 字体粗细：600（semi-bold）
   - 颜色：#333333
   - 底部外边距：24rpx

3. **图标排列**：
   - 两个功能图标水平排列
   - 每个图标下方显示对应功能名称
   - 图标大小与"其他功能"板块保持一致

## 位置安排

新板块将放置在"我的助力值"和"其他功能"板块之间，具体顺序为：
1. 我的包裹
2. 我的助力值
3. **新增板块**（营销中心）
4. 其他功能

## 功能内容

1. **我的团队**：从"其他功能"移出，放入新板块
2. **邀请好友**：从"其他功能"移出，放入新板块

## 交互行为

点击行为与原功能保持一致，不改变现有的业务逻辑和跳转流程。

## 实现考虑

1. **代码结构**：
   - 复用现有板块的样式结构
   - 只需添加新的板块组件，不需要修改现有功能逻辑

2. **性能影响**：
   - 影响极小，只是UI结构调整
   - 不涉及额外的数据请求或复杂计算

3. **用户体验**：
   - 提高了营销相关功能的可发现性
   - 使功能分类更加合理，减少用户寻找特定功能的时间
   - 突出了平台的营销推广特性

## 实施步骤

1. 在user.vue中添加新的板块组件
2. 从"其他功能"板块中移除"我的团队"和"邀请好友"功能
3. 将这两个功能添加到新板块中
4. 保持原有的点击事件和跳转逻辑不变
