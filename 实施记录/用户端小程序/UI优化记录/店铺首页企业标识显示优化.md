# 店铺首页企业标识显示优化

## 问题描述

在店铺首页（`pages/shopIndex/shopIndex.vue`）中，当 `storeStraight != 1` 时（非企业店铺），logo 右上角仍然显示「企业」标识。

问题原因：
1. 在模板中已经正确设置了条件判断，只有当 `storeStraight === '1'` 时才会设置 `data-enterprise` 属性
2. 但在CSS部分，企业标识的显示是通过 `&[data-enterprise]::before` 选择器实现的，这意味着只要有 `data-enterprise` 属性（即使值为空字符串），企业标识也会显示

## 修改内容

修改CSS选择器，确保只有当 `data-enterprise` 属性有非空值时才显示企业标识：

1. 修改企业标识显示的CSS选择器：
   ```css
   /* 修改前 */
   &[data-enterprise]::before {
       content: '企业';
       /* 其他样式... */
   }
   
   /* 修改后 */
   &[data-enterprise]:not([data-enterprise=""])::before {
       content: '企业';
       /* 其他样式... */
   }
   ```

2. 修改企业店铺点击效果的CSS选择器：
   ```css
   /* 修改前 */
   &[data-enterprise] {
       transition: transform 0.2s;
       /* 其他样式... */
   }
   
   /* 修改后 */
   &[data-enterprise]:not([data-enterprise=""]) {
       transition: transform 0.2s;
       /* 其他样式... */
   }
   ```

3. 修改点击后效果的CSS选择器：
   ```css
   /* 修改前 */
   &[data-enterprise]:active::after {
       transform: translateY(0);
   }
   
   /* 修改后 */
   &[data-enterprise]:not([data-enterprise=""]):active::after {
       transform: translateY(0);
   }
   ```

## 修改原理

使用 `:not([data-enterprise=""])` 伪类选择器，确保只有当 `data-enterprise` 属性有非空值时才应用相应的样式。这样，当 `storeStraight != 1` 时，虽然 `data-enterprise` 属性存在但值为空字符串，企业标识和相关效果不会显示。

## 效果比较

修改前：
- 当 `storeStraight != 1` 时（非企业店铺），logo 右上角仍然显示「企业」标识

修改后：
- 当 `storeStraight != 1` 时（非企业店铺），logo 右上角不再显示「企业」标识
- 当 `storeStraight = 1` 时（企业店铺），logo 右上角正常显示「企业」标识

## 后续建议

可以考虑进一步优化代码，在模板中直接使用 `v-if` 条件判断来控制企业标识的显示，而不是通过CSS选择器，这样可以使代码更加清晰和易于维护。
