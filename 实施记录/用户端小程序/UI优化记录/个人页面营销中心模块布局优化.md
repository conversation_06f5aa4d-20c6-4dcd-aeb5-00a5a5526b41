# 个人页面营销中心模块布局优化

## 需求背景
营销中心模块中的"我的团队"和"邀请好友"两个菜单项间距过大，显示效果不佳。需要调整布局，使两个菜单项从左到右紧凑排列。

## 实施内容

1. 布局方式调整：
   - 将营销中心模块的布局从网格布局(grid)改为弹性布局(flex)
   - 设置水平居中对齐(justify-content: center)
   - 设置合适的间距(gap: 120rpx)，使两个菜单项紧凑排列

2. 修改前后对比：
   - 修改前：使用grid布局，两个菜单项分别位于左右两侧，间距较大
   - 修改后：使用flex布局，两个菜单项居中紧凑排列，视觉效果更佳

## 实施代码

```scss
// 修改前
&-detail {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 24rpx;
    
    // ...其他样式保持不变
}

// 修改后
&-detail {
    display: flex;
    justify-content: center;
    gap: 120rpx;
    
    // ...其他样式保持不变
}
```

## 实施原理

1. **布局优化**：
   - flex布局比grid布局更适合少量元素的水平排列
   - 通过justify-content: center实现整体居中
   - 通过设置合适的gap值控制两个菜单项之间的间距

2. **视觉体验提升**：
   - 紧凑的排列方式更符合用户对菜单项的认知
   - 居中排列使界面更加平衡
   - 适当的间距保证了两个菜单项的视觉区分度

## 后续建议

1. **响应式优化**：
   - 可考虑根据屏幕宽度动态调整间距
   - 在小屏设备上可进一步减小间距

2. **交互优化**：
   - 可考虑添加点击反馈效果，提升用户体验
   - 可考虑在重要营销活动期间为菜单项添加醒目标记
