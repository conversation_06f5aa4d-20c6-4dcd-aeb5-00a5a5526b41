# 售后详情页面拒绝状态优化

## 问题描述

在售后详情页面(afterSaleDetail.vue)中，对于已拒绝状态(status == 5)的售后单，拒绝原因和拒绝时间的显示样式不够清晰，存在以下问题：

1. 拒绝原因和拒绝时间显示在同一行，没有明确的视觉分隔
2. 文本内容没有结构化展示，可读性较差
3. 缺乏与页面其他部分一致的样式风格

## 修改内容

### 1. 修改数据结构，使用结构化数据替代简单文本拼接

在`pages/afterSaleDetail/afterSaleDetail.vue`文件中，修改拒绝状态的数据处理方式：

```javascript
// 修改前
infoRes.title = info.value.refundType == 2 ? '店铺拒绝换货' : '店铺拒绝退款'
infoRes.time = info.value.updateTime
infoRes.content =
    `${info.value.refundExplain ? ('拒绝说明:' + info.value.refundExplain + '\n\n') : ''}拒绝原因:${info.value.refusedExplain}\n\n拒绝时间:${info.value.updateTime}`

// 修改后
infoRes.title = info.value.refundType == 2 ? '店铺拒绝换货' : '店铺拒绝退款'
infoRes.time = info.value.updateTime
// 不再使用简单的文本拼接，改为在模板中使用结构化数据
infoRes.refuseInfo = {
    explanation: info.value.refundExplain || '',
    reason: info.value.refusedExplain || '',
    time: info.value.updateTime || ''
}
```

### 2. 修改模板，使用结构化布局展示拒绝信息

```html
<!-- 修改前 -->
<view class="afterSaleDetailNew-top-spec">
    {{topInfo.content}} {{topInfo.time}}
</view>

<!-- 修改后 -->
<view class="afterSaleDetailNew-top-spec" v-if="topInfo.content">
    {{topInfo.content}}
</view>
<!-- 拒绝状态的结构化信息展示 -->
<view class="afterSaleDetailNew-top-refuse-info" v-if="topInfo.refuseInfo">
    <view class="refuse-item" v-if="topInfo.refuseInfo.explanation">
        <text class="refuse-label">拒绝说明:</text>
        <text class="refuse-value">{{topInfo.refuseInfo.explanation}}</text>
    </view>
    <view class="refuse-item">
        <text class="refuse-label">拒绝原因:</text>
        <text class="refuse-value">{{topInfo.refuseInfo.reason}}</text>
    </view>
    <view class="refuse-item">
        <text class="refuse-label">拒绝时间:</text>
        <text class="refuse-value">{{topInfo.refuseInfo.time}}</text>
    </view>
</view>
```

### 3. 添加样式，优化拒绝信息的视觉展示

```scss
&-top {
    // 现有样式...
    
    &-refuse-info {
        width: 90%;
        padding: 20rpx 30rpx;
        margin: 10rpx auto 20rpx;
        background-color: #f8f8f8;
        border-radius: 8rpx;
        
        .refuse-item {
            margin-bottom: 16rpx;
            display: flex;
            align-items: flex-start;
            
            &:last-child {
                margin-bottom: 0;
            }
            
            .refuse-label {
                color: #666666;
                font-size: 28rpx;
                min-width: 140rpx;
                margin-right: 10rpx;
            }
            
            .refuse-value {
                color: #333333;
                font-size: 28rpx;
                flex: 1;
            }
        }
    }
}
```

## 修改原则

1. 使用结构化数据和布局，提高信息的可读性
2. 保持与页面其他部分的样式风格一致
3. 使用适当的间距和对齐方式，提高视觉体验
4. 使用背景色和圆角，使拒绝信息区域更加突出

## 效果对比

### 修改前
- 拒绝原因和拒绝时间显示在同一行，没有明确的视觉分隔
- 文本内容没有结构化展示，可读性较差
- 缺乏与页面其他部分一致的样式风格

### 修改后
- 拒绝说明、拒绝原因和拒绝时间分行显示，每项有明确的标题和冒号
- 使用结构化布局，提高信息的可读性
- 使用背景色和圆角，使拒绝信息区域更加突出
- 与页面其他部分保持一致的样式风格

## 后续建议

1. 考虑为不同类型的拒绝原因添加不同的颜色标识，提高用户对拒绝原因的理解
2. 优化拒绝状态下的用户引导，提供更明确的后续操作建议
3. 考虑增加拒绝后的申诉或重新申请功能，提高用户体验
