# 添加品牌标签页到tabbar

## 需求描述

将新增的店铺类型推荐汇总页面（pages/helpPanel/helpPanelNew.vue）添加到 tabbar 中，放在店铺后面，名称为"品牌"。

## 修改内容

1. 在 pages.json 中添加 helpPanelNew.vue 页面的配置：
```json
{
  "path": "pages/helpPanel/helpPanelNew",
  "style": {
    "navigationBarTitleText": "品牌推荐"
  }
}
```

2. 在 tabBar 配置中添加新的 tabBar 项，放在店铺后面：
```json
{
  "pagePath": "pages/helpPanel/helpPanelNew",
  "iconPath": "static/tabbar/index_btn.png",
  "selectedIconPath": "static/tabbar/index_btn_sel.png",
  "text": "品牌"
}
```

## 实现原理

1. 在 pages.json 的 pages 数组中添加新页面的配置，确保页面能够被正确注册
2. 在 tabBar 的 list 数组中添加新的标签页配置，使其显示在底部导航栏中
3. 使用现有的图标资源作为临时图标，后续可以根据需要替换为专门的品牌图标

## 效果对比

修改前：
- tabbar 中只有"首页"、"店铺"和"个人"三个标签页

修改后：
- tabbar 中增加了"品牌"标签页，位于"店铺"和"个人"之间
- 点击"品牌"标签页可以导航到店铺类型推荐汇总页面

## 后续建议

1. 为"品牌"标签页设计专门的图标，以便与其他标签页区分
2. 优化 helpPanelNew.vue 页面的内容和样式，提升用户体验
3. 考虑根据用户反馈调整标签页的顺序和名称
