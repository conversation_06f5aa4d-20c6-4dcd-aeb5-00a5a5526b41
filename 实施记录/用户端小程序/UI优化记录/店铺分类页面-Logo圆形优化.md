# 店铺分类页面 - Logo圆形优化

## 问题描述

店铺分类页面（heartPoolNew.vue）中的店铺logo需要从方形改为圆形，并适当增大尺寸，以提升页面整体美观度。

## 修改内容

1. 将店铺logo改为圆形并增大尺寸：
```css
.shop-logo {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  border: 2rpx solid #FFFFFF;
  flex-shrink: 0;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  background-color: #f5f5f5;
  object-fit: cover;
}
```

2. 更新小屏幕设备的适配样式：
```css
@media screen and (max-width: 320px) {
  .shop-logo {
    width: 100rpx;
    height: 100rpx;
    border-radius: 50%;
  }
}
```

## 修改原理

1. 将border-radius设置为50%，使logo呈现为完美的圆形
2. 将logo尺寸从100rpx增加到120rpx，使其在页面中更加突出
3. 添加object-fit: cover属性，确保图片内容能够正确填充圆形区域
4. 保持原有的边框和阴影效果，提升立体感
5. 更新小屏幕设备的适配样式，确保在不同设备上都能正常显示

## 效果对比

修改前：
- 店铺logo为方形（border-radius: 16rpx）
- 尺寸为100rpx x 100rpx

修改后：
- 店铺logo为圆形（border-radius: 50%）
- 尺寸增大为120rpx x 120rpx
- 图片内容能够正确填充圆形区域

## 注意事项

此次修改仅涉及店铺logo的样式调整，不影响任何业务逻辑和功能。
