# 创业积分字段修正

## 问题描述

在店铺首页中，创业积分数据显示为空，经检查发现是字段绑定错误导致的问题。

## 修改内容

将创业积分的数据绑定从原来的 `totalPerformance` 字段修改为 `totalPerformancem` 字段。

### 修改前代码
```html
<view class="shopIndex-cnt-storeInfo-right-thi">
    创业积分:{{infos.totalPerformance}}
</view>
```

### 修改后代码
```html
<view class="shopIndex-cnt-storeInfo-right-thi">
    创业积分:{{infos.totalPerformancem}}
</view>
```

## 修改原理

1. **数据绑定修正**：
   - 将模板中的数据绑定表达式从 `infos.totalPerformance` 更改为 `infos.totalPerformancem`
   - 确保前端显示与后端返回的数据字段保持一致

2. **问题解决**：
   - 修正后，店铺首页将正确显示创业积分数据
   - 避免了因字段名不匹配导致的数据显示为空的问题

## 后续建议

1. **数据字段统一**：
   - 建议与后端开发人员沟通，统一字段命名规范
   - 考虑在API文档中明确记录各字段的名称和用途

2. **数据预处理**：
   - 可以考虑在数据获取后进行预处理，确保所有需要的字段都有合理的默认值
   - 例如：`infos.value.totalPerformancem = infos.value.totalPerformancem || '0'`

3. **字段映射**：
   - 如果后端API可能会变更字段名称，可以考虑在前端添加字段映射层
   - 这样可以在不修改模板的情况下适应API变更
