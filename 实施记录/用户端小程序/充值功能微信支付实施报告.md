# 充值功能微信支付实施报告

## 1. 当前项目充值支付流程分析

### 1.1 充值页面流程

当前项目已经实现了充值页面(pages/recharge/recharge.vue)，用户可以选择预设金额或输入自定义金额，然后点击"确认助力"按钮进行支付。支付流程如下：

1. 用户在充值页面选择或输入金额
2. 点击"确认助力"按钮，调用`toPay`函数
3. `toPay`函数调用`hooks/cashier.js`中的`payResult`函数，传入金额和支付类型(payClassic=3)
4. `payResult`函数调用后端接口`after/blance/toCashierDesk`获取支付信息
5. 获取支付信息后调用`toPay`函数发起支付请求
6. `toPay`函数调用后端接口`after/blance/pay`发起支付
7. 支付结果通过回调函数处理，显示提示信息

### 1.2 收银台页面分析

当前项目已经添加了收银台页面(pages/cashier/cashier.vue)，但在充值流程中并未使用，而是通过`hooks/cashier.js`中的函数直接处理支付流程。收银台页面的主要功能包括：

1. 显示支付金额
2. 提供支付方式选择（微信支付、余额支付等）
3. 处理支付请求和结果

### 1.3 支付结果页面分析

当前项目已经添加了支付结果页面(pages/payResult/payResult.vue)，但在充值流程中并未使用，而是通过回调函数直接显示提示信息。支付结果页面的主要功能包括：

1. 显示支付结果（成功/失败）
2. 提供返回首页、查看明细等选项

### 1.4 微信支付调用分析

当前项目中微信支付的调用流程如下：

1. `hooks/cashier.js`中的`toPay`函数发起支付请求
2. 支付请求返回后，调用`hooks/index.js`中的`goPayDeal`函数处理支付结果
3. `goPayDeal`函数解析支付参数，调用`uni.requestPayment`发起微信支付
4. 支付完成后，调用后端回调接口更新支付状态

## 2. 问题分析与改造方案

### 2.1 问题分析

1. **流程不一致**：当前项目的充值流程与原项目不一致，原项目使用独立的收银台页面和支付结果页面，而当前项目直接通过函数调用处理
2. **支付参数处理**：当前项目中`hooks/cashier.js`的`toPay`函数中的支付参数处理可能不完全适用于充值场景
3. **支付结果处理**：当前项目中支付结果通过回调函数处理，没有跳转到支付结果页面

### 2.2 改造方案

1. **整合收银台页面**：修改充值页面，使其跳转到收银台页面进行支付
2. **完善支付参数**：确保`hooks/cashier.js`中的支付参数处理适用于充值场景
3. **支付结果跳转**：修改支付结果处理，使其跳转到支付结果页面

## 3. 具体实施方案

### 3.1 修改充值页面(pages/recharge/recharge.vue)

将充值页面的`toPay`函数修改为跳转到收银台页面：

```javascript
function toPay() {
    // 验证金额是否有效
    if (!amount.value || isNaN(Number(amount.value)) || Number(amount.value) <= 0) {
        uni.showToast({
            title: '请输入有效的助力金额',
            icon: "none"
        })
        return
    }

    let info = {
        price: Number(amount.value),
        payClassic: 3
    };
    
    // 使用redTo函数跳转到收银台页面
    redTo(`/pages/cashier/cashier${parseObjToPath(info)}`)
}
```

### 3.2 修改收银台页面(pages/cashier/cashier.vue)

确保收银台页面能正确处理充值支付：

1. 确保`refresh`函数能正确获取充值信息
2. 确保`toPay`函数能正确处理充值支付请求
3. 确保支付结果处理能正确跳转到支付结果页面

### 3.3 修改hooks/index.js中的goPayDeal函数

修改`goPayDeal`函数，使其在支付成功后跳转到支付结果页面：

```javascript
export function goPayDeal(d, payClassic = 0, callback = '', failCallback = '') {
    let respon = d.data.result;
    let resInfos = respon.jsonStr;
    //拿到notifyUrl后的操作
    async function notifyUrlToGet(url) {
        try {
            let { data } = await uni.http.request({
                method: 'GET',
                baseURL: '',
                url,
            });
            let navToObj = {
                type: 1,
                payClassic,
            };
            if (payClassic == 1 && d.id) {
                navToObj.id = d.id;
            }
            if (data.code == 200) {
                //支付成功
                // 跳转到支付结果页面
                redTo(`/pages/payResult/payResult${parseObjToPath(navToObj)}`);
                if (typeof callback === 'function') {
                    callback(data)
                }
                setTimeout(() => {
                    uni.hideLoading();
                }, 500);
            } else {
                if (typeof failCallback === 'function') {
                    failCallback(data)
                } else {
                    uni.showToast({
                        title: data.message,
                        icon: 'none',
                    });
                }
            }
        } catch (error) {
            console.error(error)
            if (typeof failCallback === 'function') {
                failCallback(data)
            }
        }
    }
    
    // 解析支付参数
    try {
        resInfos = JSON.parse(resInfos);
    } catch (e) {
        console.error('无法解析jsonStr');
    }
    
    // 如果不需要调用支付，直接处理回调
    if (resInfos == '0' || !resInfos) {
        notifyUrlToGet(respon.notifyUrl);
        return;
    }
    
    // 调用微信支付
    uni.requestPayment({
        provider: 'wxpay',
        ...resInfos,
        async success(res) {
            notifyUrlToGet(respon.notifyUrl);
        },
        fail(e) {
            uni.showToast({
                title: '您取消了支付~',
                icon: 'none',
            });
            uni.hideLoading();
        },
        complete() {},
    });
}
```

## 4. 实施结果

### 4.1 充值流程

修改后的充值流程如下：

1. 用户在充值页面选择或输入金额
2. 点击"确认助力"按钮，跳转到收银台页面
3. 收银台页面显示支付金额和支付方式
4. 用户选择支付方式，点击支付按钮
5. 系统调用微信支付接口
6. 支付完成后，跳转到支付结果页面
7. 支付结果页面显示支付结果，提供返回首页、查看明细等选项

### 4.2 代码修改

1. **pages/recharge/recharge.vue**：修改`toPay`函数，使其跳转到收银台页面
2. **hooks/index.js**：修改`goPayDeal`函数，使其在支付成功后跳转到支付结果页面

### 4.3 测试结果

由于后端配置目前不支持微信支付，无法进行实际测试，但从代码逻辑上分析，修改后的充值流程应该能够正确调用微信支付接口，并在支付完成后跳转到支付结果页面。

## 5. 结论与建议

1. **流程优化**：修改后的充值流程更加符合用户习惯，提供了更好的用户体验
2. **代码复用**：充分利用了当前项目中已有的代码，减少了重复开发
3. **后端接口**：后端接口可以直接复用，无需修改

建议在实际环境中进行测试，确保微信支付接口能够正常调用，并且支付结果能够正确处理。如果测试中发现问题，可以根据实际情况进行调整。

## 6. 附录：关键代码

### 6.1 充值页面(pages/recharge/recharge.vue)的toPay函数

```javascript
function toPay() {
    // 验证金额是否有效
    if (!amount.value || isNaN(Number(amount.value)) || Number(amount.value) <= 0) {
        uni.showToast({
            title: '请输入有效的助力金额',
            icon: "none"
        })
        return
    }

    let info = {
        price: Number(amount.value),
        payClassic: 3
    };
    
    // 使用redTo函数跳转到收银台页面
    redTo(`/pages/cashier/cashier${parseObjToPath(info)}`)
}
```

### 6.2 hooks/index.js中的goPayDeal函数

```javascript
export function goPayDeal(d, payClassic = 0, callback = '', failCallback = '') {
    let respon = d.data.result;
    let resInfos = respon.jsonStr;
    //拿到notifyUrl后的操作
    async function notifyUrlToGet(url) {
        try {
            let { data } = await uni.http.request({
                method: 'GET',
                baseURL: '',
                url,
            });
            let navToObj = {
                type: 1,
                payClassic,
            };
            if (payClassic == 1 && d.id) {
                navToObj.id = d.id;
            }
            if (data.code == 200) {
                //支付成功
                // 跳转到支付结果页面
                redTo(`/pages/payResult/payResult${parseObjToPath(navToObj)}`);
                if (typeof callback === 'function') {
                    callback(data)
                }
                setTimeout(() => {
                    uni.hideLoading();
                }, 500);
            } else {
                if (typeof failCallback === 'function') {
                    failCallback(data)
                } else {
                    uni.showToast({
                        title: data.message,
                        icon: 'none',
                    });
                }
            }
        } catch (error) {
            console.error(error)
            if (typeof failCallback === 'function') {
                failCallback(data)
            }
        }
    }
    
    // 解析支付参数
    try {
        resInfos = JSON.parse(resInfos);
    } catch (e) {
        console.error('无法解析jsonStr');
    }
    
    // 如果不需要调用支付，直接处理回调
    if (resInfos == '0' || !resInfos) {
        notifyUrlToGet(respon.notifyUrl);
        return;
    }
    
    // 调用微信支付
    uni.requestPayment({
        provider: 'wxpay',
        ...resInfos,
        async success(res) {
            notifyUrlToGet(respon.notifyUrl);
        },
        fail(e) {
            uni.showToast({
                title: '您取消了支付~',
                icon: 'none',
            });
            uni.hideLoading();
        },
        complete() {},
    });
}
```
