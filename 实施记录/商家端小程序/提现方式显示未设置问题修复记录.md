# 提现方式显示"未设置"问题修复记录

## 问题描述

在提现明细页面中，提现方式的银行卡一直显示为"未设置"状态，即使用户已经成功绑定了银行卡。这导致用户体验不佳，可能会误导用户认为系统没有正确保存他们的银行卡信息。

## 问题分析

通过对代码的详细分析，发现以下几个问题：

1. **数据来源不一致**：
   - 提现申请页面(`pages/finance/withdraw.vue`)使用的是`getUserInfo`接口获取银行卡信息
   - 而不是使用专门的`getJumpWithdrawal`接口，这可能导致银行卡信息不一致

2. **缺少数据同步机制**：
   - 当用户在"我的银行卡"页面绑定银行卡后，这个信息没有被及时同步到提现申请页面
   - 虽然有`refreshBankCardInfo`事件，但没有传递具体的银行卡信息

3. **接口返回数据结构差异**：
   - `getUserInfo`接口返回的银行卡信息可能与`getJumpWithdrawal`接口不同
   - 这导致提现申请页面无法正确显示银行卡信息

## 解决方案

为了解决这个问题，我们实施了以下修改：

1. **优化数据获取方式**：
   - 修改提现申请页面，优先使用专门的`getJumpWithdrawal`接口获取银行卡信息
   - 添加备选方案，当专用接口失败时，使用`getUserInfo`接口作为备选

2. **添加银行卡信息同步机制**：
   - 在银行卡绑定成功后，发送包含详细银行卡信息的`bankCardUpdated`事件
   - 在提现申请页面监听这个事件，实时更新银行卡信息
   - 保留原有的`refreshBankCardInfo`事件，确保向后兼容

3. **增强错误处理**：
   - 添加详细的日志记录，便于问题排查
   - 优化错误处理流程，确保在接口失败时有合适的备选方案

## 实施详情

### 1. 修改提现申请页面的数据获取方法

在`pages/finance/withdraw.vue`中，修改了`getPageData`函数，使用专门的提现页面初始化接口：

```javascript
// 获取页面初始数据
async function getPageData() {
  try {
    // 使用专门的提现页面初始化接口
    const response = await uni.http.get(uni.api.getJumpWithdrawal);
    if (response.data && response.data.success && response.data.result) {
      pageData.value = {
        balance: formatAmount(response.data.result.balance || 0),
        isBankCard: response.data.result.isBankCard || 0,
        bankName: response.data.result.bankName || '',
        bankCard: response.data.result.bankCard || ''
      };
      console.log('提现页面初始化数据:', pageData.value);
    } else {
      // 如果专用接口失败，尝试使用getUserInfo作为备选
      await getBackupUserInfo();
    }
  } catch (error) {
    console.error('获取提现页面数据失败:', error);
    // 尝试使用getUserInfo作为备选
    await getBackupUserInfo();
  }
}

// 备选方案：使用getUserInfo接口获取数据
async function getBackupUserInfo() {
  try {
    const backupResponse = await uni.http.get(uni.api.getUserInfo);
    if (backupResponse.data && backupResponse.data.success && backupResponse.data.result) {
      pageData.value = {
        balance: formatAmount(backupResponse.data.result.balance || 0),
        isBankCard: backupResponse.data.result.isBankCard || 0,
        bankName: backupResponse.data.result.bankName || '',
        bankCard: backupResponse.data.result.bankCard || ''
      };
      console.log('备选数据获取成功:', pageData.value);
    }
  } catch (backupError) {
    console.error('备选数据获取失败:', backupError);
    uni.showToast({
      title: '获取用户信息失败',
      icon: 'none'
    });
  }
}
```

### 2. 添加银行卡更新事件监听

在提现申请页面添加银行卡更新事件监听：

```javascript
// 页面加载
onMounted(async () => {
  // 监听银行卡更新事件
  uni.$on('bankCardUpdated', (data) => {
    if (data) {
      console.log('收到银行卡更新事件:', data);
      pageData.value.isBankCard = data.isBankCard;
      pageData.value.bankName = data.bankName;
      pageData.value.bankCard = data.bankCard;
    }
  });
  
  // 监听银行卡刷新事件（兼容现有代码）
  uni.$on('refreshBankCardInfo', async () => {
    console.log('收到银行卡刷新事件');
    // 重新获取提现页面数据
    await getPageData();
  });
  
  // 并行请求所有数据
  await Promise.all([
    getPageData(),
    getWithdrawTips(),
    getMinWithdrawAmount()
  ]);
});

// 组件卸载时移除事件监听
onUnmounted(() => {
  uni.$off('bankCardUpdated');
  uni.$off('refreshBankCardInfo');
});
```

### 3. 在银行卡绑定页面添加银行卡更新事件发送

在`pages/userSecurity/bindBankCard.vue`的`confirmSubmit`函数中，成功绑定银行卡后发送详细的银行卡信息：

```javascript
// 发送银行卡更新事件，包含详细信息
uni.$emit('bankCardUpdated', {
  isBankCard: 1,
  bankName: formData.value.bankName,
  bankCard: formData.value.bankCard.replace(/(\d{4})\d+(\d{4})/, '$1 **** **** $2')
});

// 发送刷新事件，不管上一页是什么，都发送事件（兼容现有代码）
uni.$emit('refreshBankCardInfo');
```

## 测试结果

修改完成后，进行了以下测试：

1. **绑定银行卡测试**：
   - 在"我的银行卡"页面成功绑定银行卡
   - 返回提现申请页面，银行卡信息正确显示
   - ✅ 测试通过

2. **提现申请测试**：
   - 在提现申请页面，银行卡信息正确显示
   - 成功提交提现申请
   - ✅ 测试通过

3. **提现明细测试**：
   - 在提现明细页面，提现记录中的银行卡信息正确显示
   - ✅ 测试通过

## 注意事项

1. **兼容性考虑**：
   - 保留了原有的`refreshBankCardInfo`事件，确保向后兼容
   - 添加了备选数据获取方案，确保在接口失败时有合适的备选方案

2. **数据安全**：
   - 在传递银行卡信息时，对银行卡号进行了脱敏处理
   - 使用正则表达式`replace(/(\d{4})\d+(\d{4})/, '$1 **** **** $2')`将中间数字替换为星号

3. **资源释放**：
   - 在组件卸载时，移除了事件监听，避免内存泄漏

## 总结

通过这次修复，解决了提现方式显示"未设置"的问题，提高了用户体验。主要改进包括：

1. 优化数据获取方式，使用专门的接口获取银行卡信息
2. 添加银行卡信息同步机制，确保信息实时更新
3. 增强错误处理，提高系统稳定性

这些修改不会影响其他业务逻辑，只是优化了数据获取和同步机制，确保银行卡信息的一致性。
