# 提现明细页面状态调整记录

## 调整概述

根据用户需求，对提现明细页面进行了以下调整：

1. 修复了实际到账金额的绑定问题，使用 `amount` 字段代替 `actualAmount` 字段
2. 优化了金额符号与数值之间的间距，增加了空格，提高美观度
3. 调整了页签状态文本，使其更加符合业务需求

## 详细调整内容

### 1. 金额绑定修复

将实际到账金额的绑定从 `actualAmount` 改为 `amount`：

```html
<view class="actual-amount">
    <text class="actual-label">实际到账</text>
    <text class="actual-value">¥ {{ item.amount }}</text>
</view>
```

### 2. 金额符号间距优化

在所有金额显示处，在人民币符号 ¥ 后添加空格，提高美观度：

```html
<!-- 大金额显示 -->
<text class="amount-value-large">¥ {{ item.money || item.amount }}</text>

<!-- 申请金额 -->
<text class="amount-value">¥ {{ item.money || item.amount }}</text>

<!-- 提现服务费 -->
<text class="fee-value">¥ {{ item.serviceCharge }}</text>

<!-- 实际到账 -->
<text class="actual-value">¥ {{ item.amount }}</text>
```

### 3. 页签状态调整

调整了页签状态文本，使其更加符合业务需求：

```javascript
// 标签页数据
const tabs = ['所有', '待审核', '待打款', '已付款', '失败'];
```

### 4. 状态映射调整

调整了状态映射，确保正确显示各状态的提现记录：

```javascript
// 获取对应状态的提现记录
const statusMap = ['', '0', '1', '2', '3']; // 所有、待审核、待打款、已付款、无效
```

### 5. 状态文本调整

调整了状态文本显示，使其与页签保持一致：

```javascript
// 获取状态文本
function getStatusText(status) {
    const statusMap = {
        '0': '待审核',
        '1': '待打款',
        '2': '已付款',
        '3': '无效'
    };
    return statusMap[status] || '未知状态';
}
```

## 调整效果

通过以上调整，提现明细页面的状态显示更加符合业务需求，金额显示更加美观，实际到账金额也能正确显示。具体效果包括：

1. 页签显示为：所有、待审核、待打款、已付款、失败
2. 金额符号与数值之间有适当的空格，提高了美观度
3. 实际到账金额正确显示，使用 `amount` 字段

这些调整不影响页面的整体布局和样式，仅对数据绑定和文本显示进行了优化，提高了用户体验和业务准确性。
