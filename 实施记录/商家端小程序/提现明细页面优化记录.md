# 提现明细页面优化记录

## 优化目标

根据原型参考图优化提现明细页面的界面，使其更加协调美观，同时保持业务逻辑不变。主要优化了以下两个页面：

1. 提现明细列表页面 (`pages/finance/withdraw-detail.vue`)
2. 提现明细详情页面 (`pages/finance/withdraw-detail-info.vue`)

## 优化内容

### 1. 提现明细列表页面优化

#### 1.1 布局优化

- 调整了提现记录列表项的布局，使其更加符合原型图的设计
- 优化了银行卡信息的展示方式，使用了统一的银行卡图标
- 添加了金额的突出显示，使用更大的字体展示提现金额
- 调整了状态标签的样式，使用彩色背景更加醒目

#### 1.2 样式优化

- 添加了`amount-value-large`样式，使金额显示更加突出
- 优化了状态标签样式，添加了背景色和圆角
- 调整了提现金额信息区域的样式，使用灰色背景和圆角
- 优化了列表项内部各元素的间距和对齐方式

#### 1.3 代码示例

```html
<!-- 提现记录列表项 -->
<view class="withdraw-item-content">
  <view class="withdraw-bank-info">
    <image src="@/static/rechargeBalance/bankLogo.png" class="bank-icon"></image>
    <text class="bank-name">{{ item.bankName }} ({{ item.bankCard }})</text>
  </view>
  <view class="withdraw-amount-row">
    <text class="amount-value-large">¥{{ item.amount }}</text>
    <text class="status-tag" :class="getStatusClass(item.status)">{{ getStatusText(item.status) }}</text>
  </view>
  <view class="withdraw-amount-info">
    <view class="withdraw-amount">
      <text class="amount-label">申请金额</text>
      <text class="amount-value">¥{{ item.amount }}</text>
    </view>
    <view class="service-fee">
      <text class="fee-label">提现服务费</text>
      <text class="fee-value">¥{{ item.serviceCharge }}</text>
    </view>
    <view class="actual-amount">
      <text class="actual-label">实际到账</text>
      <text class="actual-value">¥{{ item.actualAmount }}</text>
    </view>
  </view>
</view>
```

```css
.withdraw-amount-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 20rpx 0;
}

.amount-value-large {
  font-size: 30px;
  color: #333333;
  font-weight: bold;
}

.status-tag {
  font-size: 14px;
  padding: 4rpx 12rpx;
  border-radius: 6rpx;
  font-weight: 500;
}

.status-pending {
  color: #FFFFFF;
  background-color: #FF9900;
}

.status-processing {
  color: #FFFFFF;
  background-color: #3399FF;
}

.status-success {
  color: #FFFFFF;
  background-color: #33CC66;
}

.status-failed {
  color: #FFFFFF;
  background-color: #FF3333;
}
```

### 2. 提现明细详情页面优化

#### 2.1 布局优化

- 重新设计了详情页面的布局，分为银行卡信息区域和提现详情区域
- 突出显示提现金额，使用更大的字体
- 使用统一的银行卡图标，与列表页面保持一致
- 优化了详情信息的展示方式，使用灰色背景区分

#### 2.2 样式优化

- 添加了银行卡信息区域的样式
- 优化了金额显示，使用更大的字体
- 调整了详情列表的样式，使用灰色背景和更合理的间距
- 保持了与列表页面一致的视觉风格

#### 2.3 代码示例

```html
<!-- 银行卡信息 -->
<view class="detail-card">
  <view class="bank-info-section">
    <view class="bank-info-row">
      <image src="@/static/rechargeBalance/bankLogo.png" class="bank-icon"></image>
      <text class="bank-name">{{ detailInfo.bankName }} ({{ detailInfo.bankCard }})</text>
    </view>
    <view class="amount-row">
      <text class="amount-value-large">¥{{ detailInfo.amount || '0.00' }}</text>
    </view>
  </view>
  
  <!-- 提现详情 -->
  <view class="withdraw-details-section">
    <view class="detail-item">
      <text class="item-label">提现金额</text>
      <text class="item-value">¥{{ detailInfo.amount || '0.00' }}</text>
    </view>
    <!-- 其他详情项... -->
  </view>
</view>
```

```css
/* 银行卡信息区域 */
.bank-info-section {
  margin-bottom: 30rpx;
}

.bank-info-row {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.bank-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 10rpx;
}

.bank-name {
  font-size: 16px;
  color: #333333;
  font-weight: 500;
}

.amount-row {
  margin: 30rpx 0;
}

.amount-value-large {
  font-size: 36px;
  color: #333333;
  font-weight: bold;
}

/* 提现详情区域 */
.withdraw-details-section {
  background-color: #F8F8F8;
  border-radius: 8rpx;
  padding: 20rpx;
}
```

## 优化效果

通过这次优化，提现明细页面的界面更加协调美观，主要改进包括：

1. **视觉层次更加清晰**：
   - 突出显示重要信息（如提现金额）
   - 使用背景色和间距区分不同的信息区域

2. **统一的设计风格**：
   - 列表页面和详情页面保持一致的视觉风格
   - 使用统一的银行卡图标和状态标签样式

3. **更好的用户体验**：
   - 信息展示更加清晰直观
   - 状态标签更加醒目，用户可以快速识别提现状态

## 注意事项

1. **保持业务逻辑不变**：
   - 本次优化仅涉及界面样式，不影响业务逻辑
   - 所有功能和数据处理保持不变

2. **图片资源使用**：
   - 使用了`@/static/rechargeBalance/bankLogo.png`作为银行卡图标
   - 确保该图片资源存在并可访问

3. **兼容性考虑**：
   - 样式设计考虑了不同屏幕尺寸的适配
   - 使用了相对单位（rpx）确保在不同设备上显示一致
