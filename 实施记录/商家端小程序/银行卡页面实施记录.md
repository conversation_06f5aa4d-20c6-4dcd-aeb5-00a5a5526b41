# 银行卡页面实施记录

## 实施概述

根据需求分析报告，实现了"银行卡"功能页面，该页面位于账号安全模块下，用于展示和管理商家的银行卡信息。页面提供了两种状态视图：未绑定银行卡状态和已绑定银行卡状态，并允许用户进行绑定或修改银行卡信息的操作。

## 实施内容

### 1. 页面创建

创建了以下页面：

1. **银行卡页面**：`pages/userSecurity/bankCards.vue`
   - 显示银行卡信息或未绑定状态
   - 提供"去绑定"或"修改绑定"按钮

2. **绑定银行卡页面**：`pages/userSecurity/bindBankCard.vue`
   - 提供表单用于输入银行卡信息
   - 支持选择账户类型（对私/对公）
   - 提供银行选择功能

### 2. 页面配置

在`pages.json`中添加了页面配置：

```json
{
  "path": "pages/userSecurity/bankCards",
  "style": {
    "navigationBarTitleText": "银行卡",
    "navigationBarBackgroundColor": "#ffffff",
    "navigationStyle": "custom"
  }
},
{
  "path": "pages/userSecurity/bindBankCard",
  "style": {
    "navigationBarTitleText": "绑定银行卡",
    "navigationBarBackgroundColor": "#ffffff",
    "navigationStyle": "custom"
  }
}
```

### 3. 数据流实现

#### 3.1 银行卡页面

1. **数据获取**：
   - 页面初始化时，调用`/back/storeBankCard/getStoreBankCardByStoreManageId`接口获取银行卡信息
   - 根据返回结果判断是否已绑定银行卡，显示对应的视图

2. **页面跳转**：
   - 未绑定状态下，点击"去绑定"按钮跳转到绑定银行卡页面
   - 已绑定状态下，点击"修改绑定"按钮跳转到绑定银行卡页面，并传递`hasBind=1`参数

#### 3.2 绑定银行卡页面

1. **数据初始化**：
   - 页面加载时，调用`/front/sysBank/getSysBankList`接口（POST请求）获取银行列表
   - 如果是修改模式，调用`/back/storeBankCard/getStoreBankCardByStoreManageId`接口获取已有银行卡信息

2. **表单提交**：
   - 用户填写表单后，点击"确定"按钮
   - 进行表单验证
   - 弹出确认对话框
   - 确认后，调用`/back/storeBankCard/addStoreBankCard`接口提交银行卡信息

### 4. 安全性实现

1. **敏感信息脱敏**：
   - 对银行卡号进行部分隐藏，如显示为"6217 **** **** 7890"
   - 对身份证号进行部分隐藏，如显示为"3501******2345"

2. **表单验证**：
   - 实现了手机号格式验证
   - 实现了银行卡号格式验证
   - 实现了身份证号格式验证
   - 户名和证件号设置后无法更改，防止误操作

3. **操作确认机制**：
   - 绑定银行卡时使用确认对话框，防止误操作
   - 提供温馨提示，告知用户某些信息设置后无法更改

## 技术实现

### 1. 页面布局

#### 1.1 银行卡页面

- 使用flex布局实现整体结构
- 未绑定状态下，居中显示提示信息和"去绑定"按钮
- 已绑定状态下，使用列表展示银行卡信息，底部固定"修改绑定"按钮

#### 1.2 绑定银行卡页面

- 顶部使用选项卡切换账户类型（对私/对公）
- 表单区域使用垂直排列的表单项
- 底部固定"确定"按钮
- 使用弹出层实现银行选择器

### 2. 数据处理

#### 2.1 银行卡信息格式化

```javascript
// 格式化银行卡号（中间部分用星号替代）
function formatBankCard(cardNumber) {
  if (!cardNumber) return '';
  if (cardNumber.length <= 8) return cardNumber;

  const prefix = cardNumber.substring(0, 4);
  const suffix = cardNumber.substring(cardNumber.length - 4);
  const maskedPart = '*'.repeat(cardNumber.length - 8);

  return `${prefix} ${maskedPart} ${suffix}`;
}

// 格式化身份证号（中间部分用星号替代）
function formatIdCard(idNumber) {
  if (!idNumber) return '';
  if (idNumber.length <= 8) return idNumber;

  const prefix = idNumber.substring(0, 4);
  const suffix = idNumber.substring(idNumber.length - 4);
  const maskedPart = '*'.repeat(idNumber.length - 8);

  return `${prefix}${maskedPart}${suffix}`;
}
```

#### 2.2 表单验证

```javascript
// 表单验证
function validateForm() {
  // 手机号验证
  if (!formData.value.phone) {
    uni.showToast({
      title: '请输入手机号',
      icon: 'none'
    });
    return false;
  }
  if (!/^1\d{10}$/.test(formData.value.phone)) {
    uni.showToast({
      title: '手机号格式不正确',
      icon: 'none'
    });
    return false;
  }

  // 银行卡号验证
  if (!formData.value.bankCard) {
    uni.showToast({
      title: '请输入银行卡号',
      icon: 'none'
    });
    return false;
  }
  if (formData.value.bankCard.length < 16 || formData.value.bankCard.length > 19) {
    uni.showToast({
      title: '银行卡号格式不正确',
      icon: 'none'
    });
    return false;
  }

  // 更多验证...

  return true;
}
```

### 3. 接口调用

#### 3.1 获取银行卡信息

```javascript
// 获取银行卡信息
async function getStoreBankCardInfo() {
  loading.value = true;
  try {
    const response = await uni.http.post('/back/storeBankCard/getStoreBankCardByStoreManageId');
    if (response.data && response.data.success) {
      if (response.data.result) {
        bankCardInfo.value = response.data.result;
        hasBind.value = true;
      } else {
        hasBind.value = false;
      }
    } else {
      uni.showToast({
        title: response.data?.message || '获取银行卡信息失败',
        icon: 'none'
      });
    }
  } catch (error) {
    console.error('获取银行卡信息失败', error);
    uni.showToast({
      title: '获取银行卡信息失败',
      icon: 'none'
    });
  } finally {
    loading.value = false;
  }
}
```

#### 3.2 提交银行卡信息

```javascript
// 确认提交
async function confirmSubmit() {
  loading.value = true;
  try {
    const response = await uni.http.post('/back/storeBankCard/addStoreBankCard', formData.value);
    if (response.data && response.data.success) {
      uni.showToast({
        title: '绑定成功',
        icon: 'success'
      });

      // 延迟返回上一页
      setTimeout(() => {
        uni.navigateBack();
      }, 1500);
    } else {
      uni.showToast({
        title: response.data?.message || '绑定失败',
        icon: 'none'
      });
    }
  } catch (error) {
    console.error('绑定银行卡失败', error);
    uni.showToast({
      title: '绑定失败，请稍后重试',
      icon: 'none'
    });
  } finally {
    loading.value = false;
  }
}
```

## 用户体验优化

1. **状态反馈**：
   - 使用加载动画提供操作反馈
   - 操作成功后显示成功提示
   - 操作失败时显示错误信息

2. **表单体验**：
   - 实现实时表单验证
   - 提供具体的错误提示信息
   - 对敏感信息进行脱敏处理

3. **交互优化**：
   - 使用弹出层实现银行选择器
   - 使用确认对话框防止误操作
   - 提供温馨提示，告知用户重要信息

4. **视觉优化**：
   - 使用红色主题色，与应用整体风格一致
   - 表单项布局清晰，易于填写
   - 按钮样式醒目，易于操作

## 测试情况

1. **功能测试**：
   - 验证页面能够正确获取银行卡信息
   - 验证表单验证逻辑是否正确
   - 验证银行卡绑定功能是否正常

2. **界面测试**：
   - 验证页面布局在不同尺寸设备上的表现
   - 验证表单元素的交互效果

3. **异常处理测试**：
   - 验证网络异常情况下的错误提示
   - 验证服务器返回错误时的处理逻辑

## 总结

本次实施完成了"银行卡"功能页面的开发，实现了银行卡信息的展示、绑定和修改功能。页面设计符合需求分析报告中的要求，提供了良好的用户体验和必要的安全性验证。

在实施过程中，遵循了项目的技术栈和代码规范，使用了Vue 3的Composition API进行开发，保持了与项目其他部分的一致性。同时，对敏感信息进行了脱敏处理，增强了安全性。

未来可以考虑进一步优化，如支持绑定多张银行卡、完善"对公"账户功能等，以提供更丰富的银行卡管理功能。
