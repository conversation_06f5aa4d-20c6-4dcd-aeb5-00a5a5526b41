# UniApp Project Development Scaffold Implementation Steps

本文档详细描述了将「八闽助业集市」用户端小程序转换为通用UniApp项目开发脚手架(UniApp Project Development Scaffold)的具体实施步骤。

## 前期准备

1. **项目备份**
   - 创建完整项目备份
   - 创建新的Git分支进行转换工作

2. **环境准备**
   - 确保开发环境正常
   - 安装必要的依赖和工具

## 第一阶段：项目结构梳理与分析

### 步骤1.1：分析现有项目结构

```bash
# 分析目录结构
find . -type d -not -path "*/node_modules/*" -not -path "*/unpackage/*" -not -path "*/.git/*" | sort

# 分析文件类型分布
find . -type f -not -path "*/node_modules/*" -not -path "*/unpackage/*" -not -path "*/.git/*" | grep -v "DS_Store" | awk -F. '{print $NF}' | sort | uniq -c | sort -nr
```

### 步骤1.2：识别核心文件和业务文件

1. **核心框架文件**：
   - App.vue
   - main.js
   - manifest.json
   - pages.json
   - uni.scss

2. **业务文件**：
   - pages/下的业务页面
   - components/下的业务组件
   - store/下的业务状态
   - hooks/api.js中的业务API

### 步骤1.3：确定目标项目结构

创建目标项目结构文档，明确各目录的用途和职责。

## 第二阶段：清理业务页面和组件

### 步骤2.1：清理业务页面

1. **移除业务页面**

需要移除的页面目录：
```
pages/afterSaleDetail
pages/afterSaleList
pages/afterSaleOrderDetail
pages/agreement
pages/applyForAfterSale
pages/cart
pages/commissionDetails
pages/confirmOrder
pages/heartFeedbackDetail
pages/heartPool
pages/helpPanel
pages/login (保留但重构)
pages/logistics
pages/myAddress
pages/myPackage
pages/opAddress
pages/orderDetail
pages/pendingHeartValueDetail
pages/phoneLogin
pages/productCollection
pages/productInfo
pages/productionAbout
pages/recharge
pages/revenueDetail
pages/search
pages/selectBankCard
pages/shopIndex
pages/storeCollection
pages/unavailableHeartValueDetail
pages/user (保留但重构)
pages/withDraw
pages/withDrawLs
packageTest/browseHistory
packageTest/storeCredentials
packageUser/* (全部移除)
```

2. **创建示例页面**

创建以下示例页面：
```
pages/index/index.vue (示例首页)
pages/login/login.vue (通用登录页)
pages/profile/profile.vue (通用个人中心)
pages/templates/list/list.vue (列表页模板)
pages/templates/detail/detail.vue (详情页模板)
pages/templates/form/form.vue (表单页模板)
```

3. **更新pages.json**

更新pages.json，移除业务页面配置，添加示例页面配置。

### 步骤2.2：清理业务组件

1. **移除业务组件**

需要移除的组件：
```
components/activityBanner
components/cancelOrder
components/cartAction
components/linkAddress
components/payAboutModal
components/peopleDetail
components/productDetail
components/specificationWrap
```

2. **保留和优化通用组件**

保留的组件：
```
components/empty (通用空状态组件)
components/numberInput (数字输入组件)
components/triangle (三角形组件)
components/w-picker (选择器组件)
```

3. **创建基础UI组件**

创建以下基础UI组件：
```
components/base/button/button.vue
components/base/input/input.vue
components/base/modal/modal.vue
components/base/card/card.vue
components/base/loading/loading.vue
components/layout/page/page.vue (页面容器)
components/layout/header/header.vue (页面头部)
components/layout/footer/footer.vue (页面底部)
```

## 第三阶段：重构核心功能模块

### 步骤3.1：网络请求模块重构

1. **清理API定义**

移除hooks/api.js中的业务API定义，创建新的API模板：

```javascript
// hooks/api.js
const api = {
  // 认证相关
  login: '/api/auth/login',
  logout: '/api/auth/logout',
  refreshToken: '/api/auth/refresh-token',

  // 用户相关
  getUserInfo: '/api/user/info',
  updateUserInfo: '/api/user/update',

  // 示例API
  getList: '/api/example/list',
  getDetail: '/api/example/detail',
  create: '/api/example/create',
  update: '/api/example/update',
  delete: '/api/example/delete',
};

uni.api = api;
```

2. **优化请求工具**

创建或优化utils/request.js，实现通用请求工具：

```javascript
// utils/request.js
import { useUserStore } from '@/store';

// 请求拦截器
const requestInterceptor = (config) => {
  const userStore = useUserStore();
  if (userStore.token) {
    config.header = {
      ...config.header,
      'Authorization': `Bearer ${userStore.token}`
    };
  }
  return config;
};

// 响应拦截器
const responseInterceptor = (response) => {
  // 处理响应
  if (response.statusCode === 200) {
    return response.data;
  }

  // 处理错误
  if (response.statusCode === 401) {
    // 处理未授权
    const userStore = useUserStore();
    userStore.logout();
    uni.navigateTo({ url: '/pages/login/login' });
  }

  return Promise.reject(response);
};

// 创建请求方法
const request = (options) => {
  const config = requestInterceptor(options);

  return new Promise((resolve, reject) => {
    uni.request({
      ...config,
      success: (res) => {
        try {
          const result = responseInterceptor(res);
          resolve(result);
        } catch (error) {
          reject(error);
        }
      },
      fail: (err) => {
        reject(err);
      }
    });
  });
};

// 导出请求方法
export const http = {
  get: (url, params = {}) => request({ url, method: 'GET', data: params }),
  post: (url, data = {}) => request({ url, method: 'POST', data }),
  put: (url, data = {}) => request({ url, method: 'PUT', data }),
  delete: (url, params = {}) => request({ url, method: 'DELETE', data: params }),
};
```

3. **创建请求钩子**

创建hooks/useRequest.js，实现请求钩子：

```javascript
// hooks/useRequest.js
import { ref } from 'vue';
import { http } from '@/utils/request';

export function useRequest(url, options = {}) {
  const data = ref(null);
  const loading = ref(false);
  const error = ref(null);

  const { immediate = true, method = 'GET', params = {}, onSuccess, onError } = options;

  const execute = async (customParams = {}) => {
    loading.value = true;
    error.value = null;

    try {
      const mergedParams = { ...params, ...customParams };
      let result;

      switch (method.toUpperCase()) {
        case 'GET':
          result = await http.get(url, mergedParams);
          break;
        case 'POST':
          result = await http.post(url, mergedParams);
          break;
        case 'PUT':
          result = await http.put(url, mergedParams);
          break;
        case 'DELETE':
          result = await http.delete(url, mergedParams);
          break;
        default:
          result = await http.get(url, mergedParams);
      }

      data.value = result;
      onSuccess && onSuccess(result);
      return result;
    } catch (err) {
      error.value = err;
      onError && onError(err);
      return Promise.reject(err);
    } finally {
      loading.value = false;
    }
  };

  if (immediate) {
    execute();
  }

  return {
    data,
    loading,
    error,
    execute,
  };
}
```

### 步骤3.2：状态管理重构

1. **清理业务状态**

移除store/index.js中的业务状态，保留通用状态：

```javascript
// store/index.js
import { defineStore } from 'pinia';

// 用户状态
export const useUserStore = defineStore('user', {
  state: () => ({
    userInfo: {},
    token: uni.getStorageSync('token') || ''
  }),
  actions: {
    setUserInfo(userInfo) {
      this.userInfo = userInfo || {};
    },
    setToken(token) {
      this.token = token;
      uni.setStorageSync('token', token);
    },
    clearUserInfo() {
      this.userInfo = {};
    },
    clearToken() {
      this.token = '';
      uni.removeStorageSync('token');
    },
    logout() {
      this.clearUserInfo();
      this.clearToken();
    }
  }
});

// 应用设置状态
export const useAppSettingStore = defineStore('appSetting', {
  state: () => ({
    theme: uni.getStorageSync('theme') || 'light',
    language: uni.getStorageSync('language') || 'zh-CN',
    fontSize: uni.getStorageSync('fontSize') || 'medium'
  }),
  actions: {
    setTheme(theme) {
      this.theme = theme;
      uni.setStorageSync('theme', theme);
    },
    setLanguage(language) {
      this.language = language;
      uni.setStorageSync('language', language);
    },
    setFontSize(fontSize) {
      this.fontSize = fontSize;
      uni.setStorageSync('fontSize', fontSize);
    }
  }
});
```

2. **创建模块化状态**

创建store/modules目录，实现模块化状态管理：

```
store/modules/user.js
store/modules/app.js
store/modules/example.js
```

### 步骤3.3：工具函数优化

1. **整理现有工具函数**

分析utils目录下的工具函数，移除业务相关函数。

2. **创建通用工具函数**

创建以下通用工具函数：

```
utils/date.js (日期处理)
utils/format.js (格式化处理)
utils/validate.js (验证函数)
utils/storage.js (存储处理)
utils/device.js (设备信息)
```

## 第四阶段：配置与文档完善

### 步骤4.1：项目配置优化

1. **更新manifest.json**

更新应用名称、图标、版本等信息。

2. **优化App.vue**

移除业务逻辑，保留核心初始化逻辑：

```vue
<script>
export default {
  onLaunch: function() {
    console.log('App Launch');
    // 初始化逻辑
  },
  onShow: function() {
    console.log('App Show');
  },
  onHide: function() {
    console.log('App Hide');
  }
}
</script>

<style>
/* 全局样式 */
</style>
```

3. **优化main.js**

```javascript
import { createSSRApp } from 'vue';
import { createPinia } from 'pinia';
import App from './App.vue';

// 创建应用
export function createApp() {
  const app = createSSRApp(App);

  // 状态管理
  const pinia = createPinia();
  app.use(pinia);

  // 全局配置
  app.config.globalProperties.$config = {
    baseUrl: process.env.NODE_ENV === 'development'
      ? 'http://localhost:3000'
      : 'https://api.example.com'
  };

  return {
    app
  };
}
```

### 步骤4.2：完善文档

1. **创建项目说明文档**

创建docs/README.md，描述项目概述、结构和使用方法。

2. **创建开发指南**

创建docs/development-guide.md，描述开发规范和流程。

3. **创建组件文档**

创建docs/components.md，描述组件使用方法和示例。

4. **创建API文档**

创建docs/api.md，描述API使用方法和示例。

## 测试与验证

每完成一个阶段，进行以下测试：

1. **编译测试**：确保项目能够正常编译
2. **运行测试**：确保项目能够正常运行
3. **功能测试**：测试保留的核心功能是否正常工作

## 总结

通过以上步骤，可以将「八闽助业集市」用户端小程序转换为一个通用的UniApp项目开发脚手架(UniApp Project Development Scaffold)。该脚手架具有清晰的项目结构、完善的基础功能、通用UI组件和工具函数库，可以作为新项目的起点，快速开发各类UniApp应用。
