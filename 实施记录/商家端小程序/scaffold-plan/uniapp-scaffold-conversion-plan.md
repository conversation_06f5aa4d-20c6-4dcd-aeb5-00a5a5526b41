# UniApp Project Development Scaffold Conversion Plan

## 项目背景

当前项目「八闽助业集市」是一个基于UniApp开发的用户端小程序，包含了大量业务逻辑和页面。本方案旨在将该项目转换为一个UniApp项目开发脚手架(UniApp Project Development Scaffold)，移除所有业务相关代码，保留框架结构和通用功能。

## 项目结构分析

通过对项目的分析，当前项目结构如下：

- **页面(pages)**：包含首页、分类、个人中心、商品详情、订单等业务页面
- **分包(subPackages)**：包含用户相关和测试相关的分包页面
- **组件(components)**：包含业务组件和通用组件
- **状态管理(store)**：使用Pinia进行状态管理，包含用户信息、设置等状态
- **工具函数(utils)**：包含通用工具函数
- **网络请求(hooks)**：包含API定义和请求封装
- **静态资源(static)**：包含图片等静态资源

## 转换目标

将现有项目转换为一个UniApp项目开发脚手架(UniApp Project Development Scaffold)，具有以下特点：

1. 清晰的项目结构
2. 完善的基础功能（路由、状态管理、网络请求等）
3. 通用UI组件
4. 工具函数库
5. 无业务逻辑代码

## 渐进式实施计划

### 第一阶段：项目结构梳理与分析

1. **分析现有项目结构**
   - 识别核心框架代码和业务代码
   - 确定需要保留的通用功能
   - 确定需要移除的业务功能

2. **制定目标项目结构**
   - 设计清晰的目录结构
   - 确定核心模块和功能

### 第二阶段：清理业务页面和组件

1. **清理业务页面**
   - 移除所有业务相关页面
   - 创建示例页面和模板页面
   - 更新pages.json配置

2. **清理业务组件**
   - 移除业务相关组件
   - 保留和优化通用组件
   - 创建基础UI组件库

### 第三阶段：重构核心功能模块

1. **网络请求模块重构**
   - 移除业务API定义
   - 优化请求拦截器
   - 创建通用请求函数和示例

2. **状态管理重构**
   - 移除业务状态
   - 保留通用状态（如用户认证、主题设置等）
   - 优化状态管理结构

3. **工具函数优化**
   - 整理现有工具函数
   - 移除业务相关函数
   - 添加常用工具函数

### 第四阶段：配置与文档完善

1. **项目配置优化**
   - 优化manifest.json
   - 更新项目基础信息
   - 配置通用环境变量

2. **完善文档**
   - 编写项目说明文档
   - 编写开发指南
   - 编写组件和API使用文档

## 具体实施细节

### 1. 页面清理

需要移除的业务页面：

- 首页业务页面(pages/index)
- 商品相关页面(pages/productInfo, pages/heartPool等)
- 订单相关页面(pages/myPackage, pages/orderDetail等)
- 用户中心业务页面(pages/user)
- 其他业务页面(pages/cart, pages/search等)

保留/创建的基础页面：

- 新的示例首页
- 登录页面模板
- 设置页面模板
- 列表页面模板
- 详情页面模板

### 2. 组件清理

需要移除的业务组件：

- 商品相关组件(productDetail等)
- 购物车相关组件(cartAction等)
- 支付相关组件(payAboutModal等)
- 其他业务组件

保留/创建的通用组件：

- 基础UI组件(按钮、输入框、弹窗等)
- 布局组件(页面容器、卡片等)
- 功能组件(上拉加载、下拉刷新等)

### 3. API和请求清理

- 移除所有业务API定义
- 创建示例API和请求函数
- 优化请求拦截器和响应处理

### 4. 状态管理清理

- 移除业务状态(如购物车、订单等)
- 保留基础状态(如用户认证、主题设置等)
- 创建示例状态管理模块

### 5. 静态资源清理

- 移除业务相关图片和资源
- 保留基础UI资源
- 添加示例资源

## 目标项目结构

```
project/
├── components/         # 通用组件
│   ├── base/           # 基础UI组件
│   ├── business/       # 业务组件模板
│   └── layout/         # 布局组件
├── hooks/              # 钩子函数
│   ├── useRequest.js   # 请求钩子
│   └── useAuth.js      # 认证钩子
├── pages/              # 页面
│   ├── index/          # 示例首页
│   ├── login/          # 登录页模板
│   └── templates/      # 页面模板
├── static/             # 静态资源
├── store/              # 状态管理
│   ├── modules/        # 状态模块
│   └── index.js        # 状态入口
├── utils/              # 工具函数
│   ├── request.js      # 请求工具
│   ├── storage.js      # 存储工具
│   └── common.js       # 通用工具
├── App.vue             # 应用入口
├── main.js             # 主文件
├── manifest.json       # 配置文件
├── pages.json          # 页面配置
└── uni.scss            # 全局样式
```

## 注意事项

1. **保持渐进式实施**：按照计划逐步实施，每完成一个模块进行测试
2. **保留核心功能**：确保认证、路由等核心功能正常工作
3. **文档完善**：为每个模块和功能编写清晰的文档
4. **代码规范**：遵循统一的代码规范和命名约定
5. **示例代码**：为主要功能提供示例代码

## 实施时间线

1. **第一阶段**：项目结构梳理与分析 - 1天
2. **第二阶段**：清理业务页面和组件 - 2-3天
3. **第三阶段**：重构核心功能模块 - 2-3天
4. **第四阶段**：配置与文档完善 - 1-2天

总计：6-9天

## 结论

通过本方案的实施，可以将「八闽助业集市」用户端小程序转换为一个UniApp项目开发脚手架(UniApp Project Development Scaffold)，移除所有业务相关代码，保留框架结构和通用功能。该脚手架将具有清晰的项目结构、完善的基础功能、通用UI组件和工具函数库，可以作为新项目的起点，快速开发各类UniApp应用。
