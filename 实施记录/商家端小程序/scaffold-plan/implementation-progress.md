# UniApp Project Development Scaffold 实施进度

## 第一阶段：项目结构梳理与分析

### 完成情况

- [x] 分析现有项目结构
- [x] 识别核心框架代码和业务代码
- [x] 确定需要保留的通用功能
- [x] 确定需要移除的业务功能
- [x] 制定目标项目结构

### 分析结果

#### 核心框架代码

- App.vue - 应用入口
- main.js - 主文件
- manifest.json - 配置文件
- pages.json - 页面配置
- uni.scss - 全局样式
- store/index.js - 状态管理
- hooks/request.js - 请求封装
- hooks/config.js - 配置文件
- utils/index.js - 工具函数

#### 需要保留的通用功能

1. **核心框架结构**：
   - App.vue（移除业务逻辑，保留核心初始化逻辑）
   - main.js（保留框架初始化逻辑）
   - manifest.json（更新应用信息）
   - pages.json（移除业务页面配置，添加示例页面配置）
   - uni.scss（保留全局样式变量）

2. **网络请求模块**：
   - hooks/request.js（优化请求拦截器和响应处理）
   - 创建新的API模板

3. **状态管理**：
   - store/index.js（移除业务状态，保留用户认证、主题设置等通用状态）

4. **工具函数**：
   - utils/index.js（保留通用工具函数，移除业务相关函数）

5. **通用组件**：
   - components/empty（通用空状态组件）
   - components/numberInput（数字输入组件）
   - components/triangle（三角形组件）
   - components/w-picker（选择器组件）

#### 需要移除的业务功能

1. **业务页面**：
   - pages/afterSaleDetail
   - pages/afterSaleList
   - pages/afterSaleOrderDetail
   - pages/agreement
   - pages/applyForAfterSale
   - pages/cart
   - pages/commissionDetails
   - pages/confirmOrder
   - pages/heartFeedbackDetail
   - pages/heartPool
   - pages/helpPanel
   - pages/logistics
   - pages/myAddress
   - pages/myPackage
   - pages/opAddress
   - pages/orderDetail
   - pages/pendingHeartValueDetail
   - pages/phoneLogin
   - pages/productCollection
   - pages/productInfo
   - pages/productionAbout
   - pages/recharge
   - pages/revenueDetail
   - pages/search
   - pages/selectBankCard
   - pages/shopIndex
   - pages/storeCollection
   - pages/unavailableHeartValueDetail
   - pages/withDraw
   - pages/withDrawLs
   - packageTest/browseHistory
   - packageTest/storeCredentials
   - packageUser/* (全部移除)

2. **业务组件**：
   - components/activityBanner
   - components/cancelOrder
   - components/cartAction
   - components/linkAddress
   - components/payAboutModal
   - components/peopleDetail
   - components/productDetail
   - components/specificationWrap

3. **业务API**：
   - hooks/api.js 中的业务API定义

4. **业务状态**：
   - store/index.js 中的业务状态（如购物车、订单等）

5. **业务静态资源**：
   - static/ 下的业务相关图片和资源

#### 目标项目结构

```
project/
├── components/         # 通用组件
│   ├── base/           # 基础UI组件
│   ├── business/       # 业务组件模板
│   └── layout/         # 布局组件
├── hooks/              # 钩子函数
│   ├── useRequest.js   # 请求钩子
│   └── useAuth.js      # 认证钩子
├── pages/              # 页面
│   ├── index/          # 示例首页
│   ├── login/          # 登录页模板
│   └── templates/      # 页面模板
├── static/             # 静态资源
├── store/              # 状态管理
│   ├── modules/        # 状态模块
│   └── index.js        # 状态入口
├── utils/              # 工具函数
│   ├── request.js      # 请求工具
│   ├── storage.js      # 存储工具
│   └── common.js       # 通用工具
├── App.vue             # 应用入口
├── main.js             # 主文件
├── manifest.json       # 配置文件
├── pages.json          # 页面配置
└── uni.scss            # 全局样式
```

## 第二阶段：清理业务页面和组件

### 完成情况

- [x] 移除业务页面
  - [x] 删除业务相关页面目录（afterSaleDetail、cart、productInfo等）
  - [x] 删除子包目录（packageTest、packageUser）
- [x] 创建示例页面和模板页面
  - [x] 创建首页 (pages/index/index.vue)
  - [x] 复用原有微信快捷登录页 (pages/login/login.vue)
  - [x] 创建个人中心页 (pages/profile/profile.vue)
  - [x] 创建列表页模板 (pages/templates/list/list.vue)
  - [x] 创建详情页模板 (pages/templates/detail/detail.vue)
  - [x] 创建表单页模板 (pages/templates/form/form.vue)
- [x] 更新pages.json配置
- [x] 移除业务组件
  - [x] 删除业务相关组件（activityBanner、cancelOrder、cartAction等）
  - [x] 保留通用组件（empty、numberInput、triangle、w-picker等）
- [x] 创建基础UI组件库
  - [x] 创建按钮组件 (components/base/button/button.vue)
  - [x] 创建输入框组件 (components/base/input/input.vue)
  - [x] 创建卡片组件 (components/base/card/card.vue)
  - [x] 创建页面布局组件 (components/layout/page/page.vue)
- [x] 清理静态资源
  - [x] 删除业务相关静态资源目录
  - [x] 保留通用静态资源（tabbar图标等）

### 实施结果

1. **页面结构**：
   - 删除了所有业务相关页面目录，包括子包目录
   - 创建了新的示例首页，展示脚手架的功能和模板页面
   - 复用了原有的微信快捷登录页
   - 创建了个人中心页，展示用户信息和常用功能
   - 创建了列表页、详情页和表单页模板，可用于快速开发新功能

2. **组件库**：
   - 删除了业务相关组件，保留了通用组件
   - 创建了基础UI组件，包括按钮、输入框、卡片等
   - 创建了页面布局组件，提供统一的页面结构

3. **静态资源**：
   - 删除了业务相关的静态资源目录
   - 保留了通用的静态资源，如tabbar图标

4. **配置更新**：
   - 更新了pages.json，移除了业务页面配置，添加了示例页面配置
   - 更新了tabBar配置，简化为首页和个人中心两个选项

## 下一阶段计划

第三阶段：优化核心功能模块
1. 优化网络请求模块
2. 重构状态管理
3. 完善工具函数
4. 添加主题配置
5. 优化全局样式

## 实施说明

### 关于子包的处理

在实施过程中，我们将模板页面从子包移动到了主包中，这样做的原因是：

1. 简化项目结构：对于脚手架项目，我们希望保持结构简单清晰
2. 方便开发者使用：将模板页面放在主包中，可以更直观地展示给开发者
3. 减少子包管理复杂性：对于小型项目，不需要过多的子包划分

当然，在实际应用开发中，开发者可以根据项目规模和需求，决定是否使用子包来组织代码。子包对于大型项目的优势包括：

1. 按需加载：减少首次加载时间
2. 代码组织：更好地组织功能模块
3. 性能优化：减少主包体积
