# UniApp Project Development Scaffold Directory Structure

本文档详细描述了转换后的UniApp项目开发脚手架(UniApp Project Development Scaffold)的目录结构和各部分的职责。

## 顶层目录结构

```
project/
├── components/         # 通用组件
├── hooks/              # 钩子函数
├── pages/              # 页面
├── static/             # 静态资源
├── store/              # 状态管理
├── utils/              # 工具函数
├── App.vue             # 应用入口
├── main.js             # 主文件
├── manifest.json       # 配置文件
├── pages.json          # 页面配置
└── uni.scss            # 全局样式
```

## 详细目录结构

### components/ - 组件目录

```
components/
├── base/               # 基础UI组件
│   ├── button/         # 按钮组件
│   │   └── button.vue
│   ├── input/          # 输入框组件
│   │   └── input.vue
│   ├── modal/          # 弹窗组件
│   │   └── modal.vue
│   ├── card/           # 卡片组件
│   │   └── card.vue
│   ├── loading/        # 加载组件
│   │   └── loading.vue
│   └── toast/          # 提示组件
│       └── toast.vue
├── business/           # 业务组件模板
│   ├── list-item/      # 列表项组件
│   │   └── list-item.vue
│   └── form-item/      # 表单项组件
│       └── form-item.vue
├── layout/             # 布局组件
│   ├── page/           # 页面容器
│   │   └── page.vue
│   ├── header/         # 页面头部
│   │   └── header.vue
│   └── footer/         # 页面底部
│       └── footer.vue
├── empty/              # 空状态组件
│   └── empty.vue
├── numberInput/        # 数字输入组件
│   └── numberInput.vue
└── w-picker/           # 选择器组件
    └── w-picker.vue
```

### hooks/ - 钩子函数目录

```
hooks/
├── useRequest.js       # 请求钩子
├── useAuth.js          # 认证钩子
├── useForm.js          # 表单钩子
├── useList.js          # 列表钩子
└── useRouter.js        # 路由钩子
```

### pages/ - 页面目录

```
pages/
├── index/              # 示例首页
│   └── index.vue
├── login/              # 登录页
│   └── login.vue
├── profile/            # 个人中心
│   └── profile.vue
└── templates/          # 页面模板
    ├── list/           # 列表页模板
    │   └── list.vue
    ├── detail/         # 详情页模板
    │   └── detail.vue
    └── form/           # 表单页模板
        └── form.vue
```

### static/ - 静态资源目录

```
static/
├── images/             # 图片资源
│   ├── common/         # 通用图片
│   ├── icons/          # 图标
│   └── placeholders/   # 占位图
├── fonts/              # 字体资源
└── styles/             # 样式资源
    └── variables.scss  # 样式变量
```

### store/ - 状态管理目录

```
store/
├── modules/            # 状态模块
│   ├── user.js         # 用户状态
│   ├── app.js          # 应用状态
│   └── example.js      # 示例状态
└── index.js            # 状态入口
```

### utils/ - 工具函数目录

```
utils/
├── request.js          # 请求工具
├── storage.js          # 存储工具
├── date.js             # 日期工具
├── format.js           # 格式化工具
├── validate.js         # 验证工具
├── device.js           # 设备工具
└── common.js           # 通用工具
```

## 核心文件说明

### App.vue - 应用入口

应用入口文件，包含应用生命周期钩子和全局样式。

```vue
<script>
export default {
  onLaunch: function() {
    console.log('App Launch');
    // 初始化逻辑
  },
  onShow: function() {
    console.log('App Show');
  },
  onHide: function() {
    console.log('App Hide');
  }
}
</script>

<style>
/* 全局样式 */
</style>
```

### main.js - 主文件

应用主文件，创建Vue应用实例，注册全局插件和配置。

```javascript
import { createSSRApp } from 'vue';
import { createPinia } from 'pinia';
import App from './App.vue';

// 创建应用
export function createApp() {
  const app = createSSRApp(App);

  // 状态管理
  const pinia = createPinia();
  app.use(pinia);

  // 全局配置
  app.config.globalProperties.$config = {
    baseUrl: process.env.NODE_ENV === 'development'
      ? 'http://localhost:3000'
      : 'https://api.example.com'
  };

  return {
    app
  };
}
```

### manifest.json - 配置文件

应用配置文件，包含应用名称、图标、版本等信息。

### pages.json - 页面配置

页面配置文件，定义页面路由、窗口样式、tabBar等。

```json
{
  "pages": [
    {
      "path": "pages/index/index",
      "style": {
        "navigationBarTitleText": "首页"
      }
    },
    {
      "path": "pages/login/login",
      "style": {
        "navigationBarTitleText": "登录"
      }
    },
    {
      "path": "pages/profile/profile",
      "style": {
        "navigationBarTitleText": "个人中心"
      }
    }
  ],
  "subPackages": [
    {
      "root": "pages/templates",
      "pages": [
        {
          "path": "list/list",
          "style": {
            "navigationBarTitleText": "列表页模板"
          }
        },
        {
          "path": "detail/detail",
          "style": {
            "navigationBarTitleText": "详情页模板"
          }
        },
        {
          "path": "form/form",
          "style": {
            "navigationBarTitleText": "表单页模板"
          }
        }
      ]
    }
  ],
  "globalStyle": {
    "navigationBarTextStyle": "black",
    "navigationBarTitleText": "UniApp Project Development Scaffold",
    "navigationBarBackgroundColor": "#F8F8F8",
    "backgroundColor": "#F8F8F8"
  },
  "tabBar": {
    "color": "#7A7E83",
    "selectedColor": "#3cc51f",
    "borderStyle": "black",
    "backgroundColor": "#ffffff",
    "list": [
      {
        "pagePath": "pages/index/index",
        "iconPath": "static/images/icons/home.png",
        "selectedIconPath": "static/images/icons/home-active.png",
        "text": "首页"
      },
      {
        "pagePath": "pages/profile/profile",
        "iconPath": "static/images/icons/user.png",
        "selectedIconPath": "static/images/icons/user-active.png",
        "text": "我的"
      }
    ]
  }
}
```

### uni.scss - 全局样式

全局样式变量和混合器。

```scss
/* 颜色变量 */
$uni-primary: #3cc51f;
$uni-success: #4cd964;
$uni-warning: #f0ad4e;
$uni-error: #dd524d;
$uni-info: #909399;

/* 文字颜色 */
$uni-text-color: #333;
$uni-text-color-inverse: #fff;
$uni-text-color-grey: #999;
$uni-text-color-placeholder: #808080;
$uni-text-color-disable: #c0c0c0;

/* 背景颜色 */
$uni-bg-color: #ffffff;
$uni-bg-color-grey: #f8f8f8;
$uni-bg-color-hover: #f1f1f1;
$uni-bg-color-mask: rgba(0, 0, 0, 0.4);

/* 边框颜色 */
$uni-border-color: #c8c7cc;

/* 尺寸变量 */
$uni-font-size-sm: 24rpx;
$uni-font-size-base: 28rpx;
$uni-font-size-lg: 32rpx;

/* 图片尺寸 */
$uni-img-size-sm: 40rpx;
$uni-img-size-base: 52rpx;
$uni-img-size-lg: 80rpx;

/* 边距 */
$uni-spacing-row-sm: 10rpx;
$uni-spacing-row-base: 20rpx;
$uni-spacing-row-lg: 30rpx;

/* 透明度 */
$uni-opacity-disabled: 0.3;

/* 文章场景相关 */
$uni-color-title: #2c405a;
$uni-font-size-title: 40rpx;
$uni-color-subtitle: #555;
$uni-font-size-subtitle: 36rpx;
$uni-color-paragraph: #3f536e;
$uni-font-size-paragraph: 30rpx;
```

## 组件使用示例

### 基础组件

```vue
<template>
  <view class="container">
    <base-button type="primary" @click="handleClick">按钮</base-button>
    <base-input v-model="inputValue" placeholder="请输入内容" />
    <base-card title="卡片标题">
      <view>卡片内容</view>
    </base-card>
  </view>
</template>

<script setup>
import { ref } from 'vue';
import BaseButton from '@/components/base/button/button.vue';
import BaseInput from '@/components/base/input/input.vue';
import BaseCard from '@/components/base/card/card.vue';

const inputValue = ref('');
const handleClick = () => {
  console.log('按钮点击');
};
</script>
```

### 布局组件

```vue
<template>
  <layout-page>
    <layout-header title="页面标题" />
    <view class="content">
      页面内容
    </view>
    <layout-footer />
  </layout-page>
</template>

<script setup>
import LayoutPage from '@/components/layout/page/page.vue';
import LayoutHeader from '@/components/layout/header/header.vue';
import LayoutFooter from '@/components/layout/footer/footer.vue';
</script>
```

### 钩子函数使用

```vue
<template>
  <view class="container">
    <view v-if="loading">加载中...</view>
    <view v-else-if="error">加载失败: {{ error.message }}</view>
    <view v-else>
      <view v-for="(item, index) in data" :key="index">
        {{ item.name }}
      </view>
    </view>
    <button @click="refresh">刷新</button>
  </view>
</template>

<script setup>
import { useRequest } from '@/hooks/useRequest';

const { data, loading, error, execute: refresh } = useRequest('/api/example/list', {
  method: 'GET',
  params: { page: 1, size: 10 },
  onSuccess: (res) => {
    console.log('请求成功', res);
  },
  onError: (err) => {
    console.error('请求失败', err);
  }
});
</script>
```

### 状态管理使用

```vue
<template>
  <view class="container">
    <view>用户名: {{ userStore.userInfo.username }}</view>
    <button @click="logout">退出登录</button>
  </view>
</template>

<script setup>
import { useUserStore } from '@/store';

const userStore = useUserStore();

const logout = () => {
  userStore.logout();
  uni.navigateTo({ url: '/pages/login/login' });
};
</script>
```

## 结论

通过以上目录结构和示例，可以清晰地了解转换后的UniApp项目开发脚手架(UniApp Project Development Scaffold)的结构和使用方法。该脚手架具有清晰的项目结构、完善的基础功能、通用UI组件和工具函数库，可以作为新项目的起点，快速开发各类UniApp应用。
