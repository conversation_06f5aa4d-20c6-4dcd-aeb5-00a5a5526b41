# UniApp Project Development Scaffold

这是一个基于UniApp框架的项目开发脚手架，提供了清晰的项目结构、完善的基础功能、通用UI组件和工具函数库，可以作为新项目的起点，快速开发各类UniApp应用。

## 项目特点

- **清晰的项目结构**：采用模块化、组件化的项目结构，便于维护和扩展
- **完善的基础功能**：内置路由、状态管理、网络请求等基础功能
- **通用UI组件**：提供丰富的基础UI组件和布局组件
- **工具函数库**：包含常用的工具函数，提高开发效率
- **示例页面**：提供多种页面模板，便于快速开发

## 技术栈

- Vue 3
- UniApp
- Pinia (状态管理)
- SCSS (样式预处理器)

## 目录结构

```
project/
├── components/         # 通用组件
│   ├── base/           # 基础UI组件
│   ├── business/       # 业务组件模板
│   └── layout/         # 布局组件
├── hooks/              # 钩子函数
│   ├── useRequest.js   # 请求钩子
│   └── useAuth.js      # 认证钩子
├── pages/              # 页面
│   ├── index/          # 示例首页
│   ├── login/          # 登录页模板
│   └── templates/      # 页面模板
├── static/             # 静态资源
├── store/              # 状态管理
│   ├── modules/        # 状态模块
│   └── index.js        # 状态入口
├── utils/              # 工具函数
│   ├── request.js      # 请求工具
│   ├── storage.js      # 存储工具
│   └── common.js       # 通用工具
├── App.vue             # 应用入口
├── main.js             # 主文件
├── manifest.json       # 配置文件
├── pages.json          # 页面配置
└── uni.scss            # 全局样式
```

详细的目录结构说明请参考 [uniapp-scaffold-structure.md](./uniapp-scaffold-structure.md)。

## 快速开始

1. **克隆项目**

```bash
git clone <repository-url>
cd <project-name>
```

2. **安装依赖**

```bash
npm install
# 或
yarn install
```

3. **运行项目**

```bash
# 运行到微信小程序
npm run dev:mp-weixin
# 或
yarn dev:mp-weixin

# 运行到H5
npm run dev:h5
# 或
yarn dev:h5

# 运行到App
npm run dev:app
# 或
yarn dev:app
```

4. **构建项目**

```bash
# 构建微信小程序
npm run build:mp-weixin
# 或
yarn build:mp-weixin

# 构建H5
npm run build:h5
# 或
yarn build:h5

# 构建App
npm run build:app
# 或
yarn build:app
```

## 功能模块

### 1. 网络请求

基于uni.request封装的请求模块，支持请求拦截、响应拦截、错误处理等功能。

```javascript
// 示例：发起GET请求
import { http } from '@/utils/request';

// 方式一：直接使用
http.get('/api/example/list', { page: 1, size: 10 })
  .then(res => {
    console.log('请求成功', res);
  })
  .catch(err => {
    console.error('请求失败', err);
  });

// 方式二：使用钩子函数
import { useRequest } from '@/hooks/useRequest';

const { data, loading, error, execute } = useRequest('/api/example/list', {
  method: 'GET',
  params: { page: 1, size: 10 }
});
```

### 2. 状态管理

基于Pinia的状态管理，支持模块化管理状态。

```javascript
// 示例：使用用户状态
import { useUserStore } from '@/store';

const userStore = useUserStore();

// 获取用户信息
console.log(userStore.userInfo);

// 设置用户信息
userStore.setUserInfo({ username: 'example' });

// 退出登录
userStore.logout();
```

### 3. 路由导航

基于uni-app的路由导航，封装了常用的导航方法。

```javascript
// 示例：页面导航
import { useRouter } from '@/hooks/useRouter';

const { navigateTo, redirectTo, switchTab, navigateBack } = useRouter();

// 导航到新页面
navigateTo('/pages/example/example');

// 重定向到新页面
redirectTo('/pages/example/example');

// 切换到Tab页面
switchTab('/pages/index/index');

// 返回上一页
navigateBack();
```

### 4. 组件使用

提供了丰富的基础UI组件和布局组件，便于快速开发。

```vue
<!-- 示例：使用基础组件 -->
<template>
  <view class="container">
    <base-button type="primary" @click="handleClick">按钮</base-button>
    <base-input v-model="inputValue" placeholder="请输入内容" />
    <base-card title="卡片标题">
      <view>卡片内容</view>
    </base-card>
  </view>
</template>

<script setup>
import { ref } from 'vue';
import BaseButton from '@/components/base/button/button.vue';
import BaseInput from '@/components/base/input/input.vue';
import BaseCard from '@/components/base/card/card.vue';

const inputValue = ref('');
const handleClick = () => {
  console.log('按钮点击');
};
</script>
```

## 开发指南

详细的开发指南请参考 [uniapp-scaffold-implementation-steps.md](./uniapp-scaffold-implementation-steps.md)。

## 转换方案

本脚手架是基于「八闽助业集市」用户端小程序转换而来，转换方案请参考 [UniApp Project Development Scaffold Conversion Plan](./uniapp-scaffold-conversion-plan.md)。

## 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

## 许可证

[MIT](LICENSE)
