# 八闽助业集市余额提现方案

## 1. 方案概述

### 1.1 业务背景

八闽助业集市平台为会员提供余额提现功能，允许会员将账户中的余额提现到绑定的银行卡中。提现流程包括会员发起提现申请、平台审核、打款处理等环节，需要严格的验证和控制机制确保资金安全。

### 1.2 业务流程概述

会员提现业务流程主要包括以下几个环节：

1. **会员发起提现申请**：会员在前端输入提现金额和银行卡信息，系统进行各种限制条件验证
2. **管理员审核提现申请**：后台管理员对提现申请进行审核，决定是否通过
3. **提现打款处理**：对审核通过的提现申请进行打款操作
4. **提现明细查询**：会员可以查询自己的提现记录和状态

### 1.3 提现状态流转

提现申请在系统中的状态流转如下：

- **待审核（0）**：会员提交申请后的初始状态
- **待打款（1）**：管理员审核通过后的状态
- **已付款（2）**：打款完成后的状态
- **无效（3）**：审核不通过的状态

## 2. 系统设计

### 2.1 数据模型设计

#### 2.1.1 核心实体

1. **MemberWithdrawDeposit（会员提现记录）**

```java
public class MemberWithdrawDeposit {
    private String id;                 // 主键
    private String memberListId;       // 会员ID
    private BigDecimal money;          // 提现金额
    private String status;             // 提现状态：0-待审核，1-待打款，2-已付款，3-无效
    private String withdrawalType;     // 提现类型：0-自动转账，1-手动打款
    private String bankCardNumber;     // 银行卡号
    private String bankName;           // 银行名称
    private String cardholder;         // 持卡人姓名
    private String identityNumber;     // 身份证号
    private String auditPerson;        // 审核人
    private Date auditTime;            // 审核时间
    private String auditRemark;        // 审核备注
    private String remitPerson;        // 打款人
    private Date remitTime;            // 打款时间
    private String remitRemark;        // 打款备注
    private Integer year;              // 年份（用于统计）
    private Integer month;             // 月份（用于统计）
    private Integer day;               // 日期（用于统计）
    private Date createTime;           // 创建时间
    private Date updateTime;           // 更新时间
}
```

2. **MemberBankCard（会员银行卡）**

```java
public class MemberBankCard {
    private String id;                 // 主键
    private String memberListId;       // 会员ID
    private String carType;            // 卡类型：0-银行卡
    private String bankCardNumber;     // 银行卡号
    private String bankName;           // 银行名称
    private String cardholder;         // 持卡人姓名
    private String identityNumber;     // 身份证号
    private Date createTime;           // 创建时间
}
```

3. **MemberList（会员信息）**

```java
public class MemberList {
    private String id;                 // 主键
    private String nickName;           // 昵称
    private BigDecimal balance;        // 账户余额
    // 其他会员信息字段
}
```

#### 2.1.2 数据表关系

1. **会员表(member_list)** 与 **提现记录表(member_withdraw_deposit)** 是一对多关系
2. **会员表(member_list)** 与 **银行卡表(member_bank_card)** 是一对多关系
3. **提现记录表(member_withdraw_deposit)** 与 **银行卡表(member_bank_card)** 通过会员ID关联

### 2.2 接口设计

#### 2.2.1 前端接口（会员端）

1. **提现申请接口**

```
URL: /after/memberWithdrawDeposit/withdrawalCard
Method: POST
参数:
  - money: 提现金额
  - memberBankCardId: 银行卡ID（可选）
返回:
  - 提现申请结果
```

2. **提现明细查询接口**

```
URL: /after/memberWithdrawDeposit/findMemberWithdrawDepositPageByMemberId
Method: GET
参数:
  - pattern: 提现状态筛选（-1:全部；0：待审核；1：待打款；2：已付款；3：无效）
  - pageNo: 页码
  - pageSize: 每页记录数
返回:
  - 提现记录列表
```

#### 2.2.2 后台接口（管理端）

1. **提现审核接口**

```
URL: /memberWithdrawDeposit/audit
Method: POST
参数:
  - MemberWithdrawDepositVO: 包含审核信息的对象
返回:
  - 审核结果
```

2. **提现打款接口**

```
URL: /memberWithdrawDeposit/remit
Method: POST
参数:
  - MemberWithdrawDepositVO: 包含打款信息的对象
返回:
  - 打款结果
```

## 3. 业务规则与限制条件

### 3.1 提现限制条件

系统对提现操作设置了多种限制条件，这些条件从系统字典表中获取：

#### 3.1.1 时间限制

1. **每周特定日期可提现**
   - 配置项：`withdraw_open_week`
   - 示例值：`1,2,3,4,5`（表示周一至周五可提现）
   - 实现逻辑：获取当前是星期几，检查是否在允许提现的星期范围内

2. **每天特定时间段可提现**
   - 配置项：`withdraw_start_time` 和 `withdraw_end_time`
   - 示例值：`09:00:00` 和 `17:00:00`（表示每天9点至17点可提现）
   - 实现逻辑：获取当前时间，检查是否在允许提现的时间范围内

#### 3.1.2 金额限制

1. **提现金额必须是特定倍数**
   - 配置项：`withdraw_appoint_integers_and_multiples`
   - 示例值：`100`（表示提现金额必须是100的倍数）
   - 实现逻辑：检查提现金额是否能被指定数值整除

2. **单笔提现最大金额限制**
   - 配置项：`single_withdraw_maximum`
   - 示例值：`10000`（表示单笔最大提现金额为10000元）
   - 实现逻辑：检查提现金额是否超过最大限制

#### 3.1.3 次数限制

1. **单日提现次数限制**
   - 配置项：`withdraw_everyday_times`
   - 示例值：`3`（表示每人每天最多提现3次）
   - 实现逻辑：统计会员当天已提现次数，检查是否超过限制

2. **同一身份证单日提现次数限制**
   - 配置项：`same_identity_withdraw_everyday_times`
   - 示例值：`5`（表示同一身份证每天最多提现5次）
   - 实现逻辑：统计同一身份证当天已提现次数，检查是否超过限制

#### 3.1.4 其他限制

1. **用户必须绑定银行卡**
   - 实现逻辑：检查用户是否有绑定的银行卡，如无则提示先绑定银行卡

2. **账户余额必须大于等于提现金额**
   - 实现逻辑：检查用户账户余额是否足够

### 3.2 提现打款方式

系统支持两种提现打款方式：

1. **自动转账**
   - 条件：`withdrawalType = 0`
   - 处理方法：调用 `transferPay` 方法，可能对接第三方支付接口实现自动打款

2. **手动打款**
   - 条件：`withdrawalType != 0`
   - 处理方法：调用 `remit` 方法，由管理员手动确认打款完成

## 4. 详细业务流程

### 4.1 提现申请流程

1. **参数验证**
   - 验证提现金额是否为正数
   - 验证会员余额是否充足

2. **提现限制条件验证**
   - 验证当前是否在允许提现的时间范围内
   - 验证提现金额是否符合倍数要求
   - 验证是否超过单笔最大提现金额
   - 验证是否超过单日提现次数限制
   - 验证是否超过同一身份证单日提现次数限制

3. **银行卡信息获取**
   - 如果指定了银行卡ID，则获取该银行卡信息
   - 否则获取会员最新添加的银行卡信息

4. **创建提现记录**
   - 创建提现记录，设置初始状态为"待审核"
   - 扣减会员账户余额
   - 记录提现年、月、日信息（用于统计）

### 4.2 提现审核流程

1. **获取审核信息**
   - 获取当前登录的管理员信息
   - 获取审核意见和备注

2. **更新提现记录状态**
   - 如果审核通过，将状态更新为"待打款"
   - 如果审核不通过，将状态更新为"无效"，并将扣减的余额返还给会员

3. **记录审核信息**
   - 记录审核人、审核时间和审核备注

### 4.3 提现打款流程

1. **判断提现类型**
   - 如果是自动转账（withdrawalType=0），调用 transferPay 方法
   - 如果是手动打款，调用 remit 方法

2. **更新提现记录状态**
   - 将状态更新为"已付款"
   - 记录打款人、打款时间和打款备注

### 4.4 提现明细查询流程

1. **组织查询参数**
   - 设置会员ID
   - 设置提现状态筛选条件
   - 设置分页参数

2. **执行查询**
   - 调用 `findMemberWithdrawDepositPageByMemberId` 方法查询提现记录
   - 返回分页结果

## 5. 代码实现

### 5.1 提现申请实现

```java
/**
 * 提现申请 到审核
 * @param money 提现金额
 * @param memberBankCardId 银行卡ID
 * @param request HTTP请求
 * @return 提现申请结果
 */
@RequestMapping("withdrawalCard")
@ResponseBody
public Result<String> withdrawalCard(
                                    @RequestParam(name = "money",required = true) BigDecimal money,
                                    @RequestParam(name = "memberBankCardId",required = false) String memberBankCardId,
                                    HttpServletRequest request) {
    Result<String> result = new Result<>();

    String memberId = request.getAttribute("memberId").toString();
    // 1. 参数验证
    if (money.doubleValue() < 0) {
        return result.error500("请输入正确金额!");
    }
    
    // 2. 提现时间限制验证
    // 2.1 验证提现星期
    String withdrawOpenWeek = iSysDictService.queryTableDictTextByKey("sys_dict_item","item_value","item_text","withdraw_open_week");
    Calendar calendar = Calendar.getInstance();
    String week = calendar.get(Calendar.DAY_OF_WEEK) - 1 + "";
    if (StringUtils.indexOf(withdrawOpenWeek, week) == -1) {
        return result.error500("该时间段不允许提现。。。。");
    }
    
    // 2.2 验证提现时间段
    String withdrawStartTime = iSysDictService.queryTableDictTextByKey("sys_dict_item","item_value","item_text","withdraw_start_time");
    String withdrawEndTime = iSysDictService.queryTableDictTextByKey("sys_dict_item","item_value","item_text","withdraw_end_time");
    try {
        Date startTime = DateUtils.parseDate(DateUtils.formatDate() + " " + withdrawStartTime, "yyyy-MM-dd HH:mm:ss");
        Date endTime = DateUtils.parseDate(DateUtils.formatDate() + " " + withdrawEndTime, "yyyy-MM-dd HH:mm:ss");
        if (new Date().getTime() >= startTime.getTime() && new Date().getTime() <= endTime.getTime()) {
            // 允许提现
        } else {
            return result.error500("该时间段不允许提现。。。。");
        }
    } catch (ParseException e) {
        e.printStackTrace();
    }
    
    // 3. 提现金额限制验证
    // 3.1 验证提现金额倍数
    String withdrawAppointIntegersAndMultiples = iSysDictService.queryTableDictTextByKey("sys_dict_item","item_value","item_text","withdraw_appoint_integers_and_multiples");
    if (!withdrawAppointIntegersAndMultiples.equals("-1")) {
        if ((money.doubleValue() % Double.parseDouble(withdrawAppointIntegersAndMultiples)) != 0) {
            return result.error500("提现金额必须是" + withdrawAppointIntegersAndMultiples + "倍数");
        }
    }
    
    // 3.2 验证单笔最大提现金额
    String singleWithdrawMaximum = iSysDictService.queryTableDictTextByKey("sys_dict_item","item_value","item_text","single_withdraw_maximum");
    if (StringUtils.isNotBlank(singleWithdrawMaximum) && !singleWithdrawMaximum.equals("-1")) {
        if (money.doubleValue() > new BigDecimal(singleWithdrawMaximum).doubleValue()) {
            return result.error500("单笔最高提现金额不能超过：" + singleWithdrawMaximum + "元");
        }
    }
    
    // 4. 提现次数限制验证
    // 4.1 验证单日提现次数
    String withdrawEverydayTimes = iSysDictService.queryTableDictTextByKey("sys_dict_item","item_value","item_text","withdraw_everyday_times");
    if (StringUtils.isNotBlank(withdrawEverydayTimes) && !withdrawEverydayTimes.equals("-1")) {
        long withdrawalCount = iMemberWithdrawDepositService.count(new LambdaQueryWrapper<MemberWithdrawDeposit>()
                .eq(MemberWithdrawDeposit::getYear, calendar.get(Calendar.YEAR))
                .eq(MemberWithdrawDeposit::getMonth, calendar.get(Calendar.MONTH) + 1)
                .eq(MemberWithdrawDeposit::getDay, calendar.get(Calendar.DAY_OF_MONTH))
                .eq(MemberWithdrawDeposit::getMemberListId, memberId));
        if (withdrawalCount >= new BigDecimal(withdrawEverydayTimes).intValue()) {
            return result.error500("每日最多只能提现：" + withdrawEverydayTimes + "次");
        }
    }
    
    // 5. 获取会员和银行卡信息
    MemberList member = iMemberListService.getById(memberId);
    MemberBankCard bankCard = null;
    if (StrUtil.isNotBlank(memberBankCardId)) {
        bankCard = iMemberBankCardService.getById(memberBankCardId);
    }
    if (bankCard == null) {
        bankCard = iMemberBankCardService.getOne(new LambdaQueryWrapper<MemberBankCard>()
                .eq(MemberBankCard::getCarType, "0")
                .eq(MemberBankCard::getMemberListId, memberId)
                .orderByDesc(MemberBankCard::getCreateTime)
                .last("limit 1"));
    }
    
    // 6. 验证同一身份证提现次数
    String sameIdentityWithdrawEverydayTimes = iSysDictService.queryTableDictTextByKey("sys_dict_item","item_value","item_text","same_identity_withdraw_everyday_times");
    if (StringUtils.isNotBlank(sameIdentityWithdrawEverydayTimes) && !sameIdentityWithdrawEverydayTimes.equals("-1")) {
        long memberWithdrawDepositCount = iMemberWithdrawDepositService.count(new LambdaQueryWrapper<MemberWithdrawDeposit>()
                .eq(MemberWithdrawDeposit::getYear, calendar.get(Calendar.YEAR))
                .eq(MemberWithdrawDeposit::getMonth, calendar.get(Calendar.MONTH) + 1)
                .eq(MemberWithdrawDeposit::getDay, calendar.get(Calendar.DAY_OF_MONTH))
                .eq(MemberWithdrawDeposit::getIdentityNumber, bankCard.getIdentityNumber())
                .eq(MemberWithdrawDeposit::getCardholder, bankCard.getCardholder()));
        if (memberWithdrawDepositCount >= Integer.parseInt(sameIdentityWithdrawEverydayTimes)) {
            return result.error500("同一身份证单天不能超过：" + sameIdentityWithdrawEverydayTimes + "次，提现");
        }
    }
    
    // 7. 验证银行卡和余额
    if (oConvertUtils.isEmpty(bankCard)) {
        return result.error500("用户未绑定银行卡!");
    }
    if (member.getBalance().doubleValue() < money.doubleValue()) {
        return result.error500("提现金额不足!");
    } else {
        // 8. 提现申请处理
        boolean b = iMemberWithdrawDepositService.withdrawalCard(money, member, bankCard);
        if (b) {
            result.success("提现成功!请等待管理员审核!");
        } else {
            result.error500("提现失败!");
        }
    }
    
    return result;
}
```

### 5.2 提现审核实现

```java
/**
 * 审批提现申请
 * @param memberWithdrawDepositVO 提现审核信息
 * @return 审核结果
 */
@AutoLog(value = "审批")
@ApiOperation(value = "审批", notes = "审批")
@PostMapping("/audit")
public Result<MemberWithdrawDeposit> audit(@RequestBody MemberWithdrawDepositVO memberWithdrawDepositVO) {
    // 1. 获取当前登录管理员信息
    LoginUser user = (LoginUser) SecurityUtils.getSubject().getPrincipal();
    memberWithdrawDepositVO.setRealname(user.getRealname());
    
    // 2. 调用审核服务
    return memberWithdrawDepositService.audit(memberWithdrawDepositVO);
}
```

### 5.3 提现打款实现

```java
/**
 * 提现打款处理
 * @param memberWithdrawDepositVO 提现打款信息
 * @param request HTTP请求
 * @return 打款结果
 */
@AutoLog(value = "打款")
@ApiOperation(value = "打款", notes = "打款")
@PostMapping("/remit")
public Result<String> remit(@RequestBody MemberWithdrawDepositVO memberWithdrawDepositVO, HttpServletRequest request) {
    // 1. 获取提现记录
    MemberWithdrawDeposit memberWithdrawDeposit = memberWithdrawDepositService.getById(memberWithdrawDepositVO.getId());
    
    // 2. 根据提现类型选择打款方式
    if (memberWithdrawDeposit.getWithdrawalType().equals("0")) {
        // 自动转账
        return this.transferPay(memberWithdrawDepositVO, request);
    } else {
        // 手动打款
        LoginUser user = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        memberWithdrawDepositVO.setRealname(user.getRealname());
        return memberWithdrawDepositService.remit(memberWithdrawDepositVO);
    }
}
```

### 5.4 提现明细查询实现

```java
/**
 * 提现明细查询
 * @param pattern 提现状态筛选
 * @param request HTTP请求
 * @param pageNo 页码
 * @param pageSize 每页记录数
 * @return 提现记录列表
 */
@RequestMapping("findMemberWithdrawDepositPageByMemberId")
@ResponseBody
public Result<IPage<Map<String,Object>>> findMemberWithdrawDepositPageByMemberId(
        @RequestParam(name = "pattern", required = true) Integer pattern,
        HttpServletRequest request,
        @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
        @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize) {
    
    // 1. 获取会员ID
    String memberId = request.getAttribute("memberId").toString();
    Result<IPage<Map<String,Object>>> result = new Result<>();
    
    // 2. 组织查询参数
    Page<Map<String,Object>> page = new Page<Map<String,Object>>(pageNo, pageSize);
    Map<String,Object> paramObjectMap = Maps.newHashMap();
    paramObjectMap.put("memberId", memberId);
    paramObjectMap.put("pattern", pattern);
    
    // 3. 执行查询
    result.setResult(iMemberWithdrawDepositService.findMemberWithdrawDepositPageByMemberId(page, paramObjectMap));
    result.success("查询提现金额列表");
    
    return result;
}
```

## 6. 安全性设计

### 6.1 身份验证

1. **会员端接口验证**
   - 通过 HTTP 请求中的 `memberId` 属性验证用户身份
   - 确保会员只能操作自己的提现记录

2. **后台接口验证**
   - 通过 `SecurityUtils.getSubject().getPrincipal()` 获取当前登录的管理员信息
   - 记录操作人信息，便于追踪责任

### 6.2 数据验证

1. **参数验证**
   - 验证提现金额是否为正数
   - 验证提现金额是否符合倍数要求
   - 验证是否超过单笔最大提现金额

2. **业务规则验证**
   - 验证当前是否在允许提现的时间范围内
   - 验证是否超过单日提现次数限制
   - 验证是否超过同一身份证单日提现次数限制

3. **银行卡验证**
   - 验证银行卡信息的有效性
   - 验证持卡人身份信息

### 6.3 事务管理

提现申请、审核、打款等操作应在事务中执行，确保数据一致性：

1. **提现申请事务**
   - 创建提现记录
   - 扣减会员账户余额

2. **提现审核事务**
   - 更新提现记录状态
   - 如果审核不通过，返还会员账户余额

3. **提现打款事务**
   - 更新提现记录状态
   - 记录打款信息

## 7. 优化建议

### 7.1 代码优化

1. **提现限制条件优化**
   - 将多个限制条件检查封装成独立方法，提高代码可读性和可维护性
   - 对于返回错误信息，统一格式和提示语

2. **异常处理优化**
   - 增加全局异常处理，统一处理业务异常和系统异常
   - 对关键操作进行异常捕获和日志记录

### 7.2 功能优化

1. **异步处理**
   - 对于打款操作，使用异步处理，避免阻塞主流程
   - 打款结果通过回调方式更新提现状态

2. **提现费率**
   - 增加提现费率配置，支持按比例或固定金额收取提现手续费
   - 在提现申请时计算并展示手续费，让用户明确了解费用

3. **风控机制**
   - 增加风控规则，对异常提现行为进行监控和拦截
   - 例如短时间内多次提现、提现金额突然增大等情况

### 7.3 用户体验优化

1. **提现进度查询**
   - 提供更详细的提现进度查询功能，让用户了解提现处理状态
   - 对于长时间未处理的提现申请，可以主动推送提醒

2. **提现预估到账时间**
   - 根据历史数据分析，提供提现预估到账时间
   - 在用户提交提现申请后，展示预计到账时间范围

3. **常用银行卡管理**
   - 提供常用银行卡管理功能，方便用户快速选择提现银行卡
   - 支持设置默认提现银行卡

## 8. 总结

八闽助业集市余额提现方案通过严格的验证和控制机制，确保会员提现操作的安全性和可靠性。系统通过多层次的限制条件（时间、金额、次数等）控制提现行为，通过审核和打款两个环节确保提现操作的安全性，并通过提现明细查询功能提供透明的资金流向记录。

该方案具有以下特点：

1. **安全可靠**：通过多重验证和控制机制，确保提现操作的安全性
2. **灵活配置**：通过系统字典表配置提现限制条件，便于灵活调整
3. **流程完善**：提现申请、审核、打款、查询等环节流程完善，职责明确
4. **可扩展性强**：支持多种提现方式，可根据需求扩展新的提现渠道

通过本方案的实施，可以为八闽助业集市平台会员提供安全、便捷的余额提现服务，提升用户体验和平台信誉。
