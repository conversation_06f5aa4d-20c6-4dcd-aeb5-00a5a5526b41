# 我要开店功能分析报告

## 1. 功能概述

"我要开店"功能是小程序中允许用户申请开设自己店铺的重要功能。用户可以通过支付一定费用（年费或终身费用）来开通店铺，开店后可以获得店铺管理权限，进行商品管理、订单管理等操作。

## 2. 页面布局分析

### 2.1 入口位置

"我要开店"功能的入口位于小程序的"我的"页面（`pages/user/user.vue`）中的"更多"板块。具体位置在页面底部的功能列表中：

```html
<view class="user-bottom-wrap-detail" @click="navTo('/pages/openShop/openShop')">
    <image src="@/static/img/users/gd5.png" mode="heightFix"></image>
    <view>
        我要开店
    </view>
</view>
```

用户点击此入口后，会通过`navTo`函数跳转到开店页面。

### 2.2 开店页面布局

开店页面（`pages/openShop/openShop.vue`）主要分为以下几个部分：

1. **基本信息填写区域**：
   - 联系人姓名输入框
   - 手机号输入框
   - 验证码输入框及获取验证码按钮
   - 门店名称输入框
   - 关联体验馆名称输入框
   - 所在城市选择器
   - 详细地址输入框

2. **开店类型选择区域**：
   - 年费权限选项
   - 终身权限选项

3. **底部操作区域**：
   - 立即开通按钮（显示所选类型和价格）

## 3. 页面交互逻辑

### 3.1 表单验证

开店页面包含多项表单验证逻辑：

1. **联系人验证**：使用正则表达式`/^[\u4E00-\u9FA5]{2,4}$/`验证联系人姓名必须为2-4个中文字符
2. **手机号验证**：使用正则表达式`/^1[3456789]\d{9}$/`验证手机号格式
3. **验证码验证**：验证用户输入的验证码是否为空
4. **门店名称验证**：验证门店名称是否为空
5. **所在城市验证**：验证是否选择了所在城市
6. **详细地址验证**：验证是否填写了详细地址

### 3.2 验证码获取

用户点击"获取验证码"按钮后：
1. 首先验证手机号格式是否正确
2. 调用后端接口`verificationCode`发送验证码
3. 启动倒计时（60秒），倒计时期间不能重复获取验证码

### 3.3 地址选择

用户点击"所在城市"区域后：
1. 调用`showAddressModal`函数显示地址选择弹窗
2. 用户选择省市区后，通过`confirmCallback`函数处理选择结果
3. 根据不同的地区类型（直辖市或普通省份）格式化地址显示

### 3.4 开店类型选择

页面加载时会从后端获取两种开店类型的价格：
1. 年费权限
2. 终身权限

用户可以点击选择不同的开店类型，选中的类型会显示在底部按钮上。

### 3.5 提交开店申请

用户点击"立即开通"按钮后：
1. 执行表单验证
2. 调用地图API获取详细地址的经纬度
3. 提交开店申请到后端
4. 调起支付流程

## 4. 数据交互逻辑

### 4.1 页面初始化数据

页面加载时，通过`refresh`函数调用后端接口`openStoreParam`获取开店价格参数：
```javascript
async function refresh() {
    uni.showLoading({
        mask: true
    })
    let {
        data
    } = await uni.http.post(uni.api.openStoreParam)
    let list = [];
    for (let key in data.result) {
        if (data.result[key] && data.result[key] > 0) {
            if (key == 'lifelong') {
                //终身
                list.push({
                    openType: 1,
                    price: data.result[key],
                    title: '终身权限'
                })
            }
            if (key == 'packYears') {
                //年费
                list.push({
                    openType: 0,
                    price: data.result[key],
                    title: '年费权限'
                })
            }
        }
    }
    payList.value = list;
    uni.hideLoading()
}
```

### 4.2 验证码获取

通过调用`verificationCode`接口获取手机验证码：
```javascript
async function getCode() {
    if (!phoneReg.test(submitInfo.value.bossPhone)) {
        uni.showToast({
            title: '手机号格式不正确',
            icon: 'none'
        })
        return;
    }
    uni.showLoading({
        mask: true
    })
    await uni.http.post(uni.api.verificationCode, {
        phone: submitInfo.value.bossPhone
    })
    countDown.createTimer();
    uni.hideLoading()
}
```

### 4.3 地址经纬度获取

提交开店申请前，通过调用`getAddsress`接口获取详细地址的经纬度：
```javascript
let {
    data: res
} = await uni.http.get(uni.api.getAddsress, {
    params: info
});
submitInfo.value.latitude = res.result[0].location.lat; //维度
submitInfo.value.longitude = res.result[0].location.lng; //经度
```

### 4.4 提交开店申请

通过调用`openTheStore`接口提交开店申请：
```javascript
let {
    data
} = await uni.http.post(uni.api.openTheStore, submitInfo.value);
goPayDeal({
    data
}, -1)
```

## 5. 后端接口交互逻辑

### 5.1 获取开店价格参数接口

- **接口路径**：`front/storeManage/openStoreParam`
- **请求方式**：POST
- **返回数据**：包含终身价格和年费价格的对象
  ```json
  {
    "result": {
      "lifelong": "终身价格",
      "packYears": "年费价格"
    }
  }
  ```

### 5.2 获取验证码接口

- **接口路径**：`after/message/verificationCode`
- **请求方式**：POST
- **请求参数**：
  ```json
  {
    "phone": "手机号"
  }
  ```
- **处理逻辑**：后端生成验证码并发送到用户手机，同时将验证码存储在Redis中，用于后续验证

### 5.3 获取地址经纬度接口

- **接口路径**：`uni.api.getAddsress`
- **请求方式**：GET
- **请求参数**：
  ```json
  {
    "address": "完整地址",
    "pageSize": 1,
    "pageIndex": 1
  }
  ```
- **返回数据**：包含地址经纬度信息的对象

### 5.4 提交开店申请接口

- **接口路径**：`after/storeManage/openTheStore`
- **请求方式**：POST
- **请求参数**：
  ```json
  {
    "bossName": "联系人姓名",
    "bossPhone": "联系人手机号",
    "verificationCode": "验证码",
    "storeName": "门店名称",
    "subStoreName": "关联体验馆名称",
    "sysAreaId": "区域ID",
    "areaAddress": "城市区域地址",
    "storeAddress": "详细地址",
    "openType": "开通类型",
    "longitude": "经度",
    "latitude": "纬度"
  }
  ```
- **返回数据**：包含支付信息的对象，用于调起支付

## 6. 后端业务逻辑实现

### 6.1 获取开店价格参数实现

后端通过查询系统字典表获取开店价格参数：
```java
@RequestMapping("openStoreParam")
@ResponseBody
public Result<Map<String, Object>> openStoreParam() {
    Result<Map<String, Object>> result = new Result<>();
    Map<String, Object> objectMap = Maps.newHashMap();
    //终生价
    objectMap.put("lifelong", iSysDictService.queryTableDictTextByKey("sys_dict_item", "item_value", "item_text", "lifelong"));
    //包年价
    objectMap.put("packYears", iSysDictService.queryTableDictTextByKey("sys_dict_item", "item_value", "item_text", "pack_years"));

    result.setResult(objectMap);
    result.success("获取开店价格参数成功");
    return result;
}
```

### 6.2 提交开店申请实现

后端处理开店申请的主要逻辑：
1. 验证参数合法性
2. 验证验证码是否正确
3. 创建店铺记录
4. 生成支付参数
5. 返回支付信息

```java
@RequestMapping("openTheStore")
@ResponseBody
public Result<Map<String, Object>> openTheStore(@RequestBody Map<String, Object> storeParamMap) {
    StoreParamDTO storeParamDTO = BeanUtil.mapToBean(storeParamMap, StoreParamDTO.class, true);

    String memberId = storeParamDTO.getMemberId();
    Result<Map<String, Object>> result = new Result<>();
    Map<String, Object> objectMap = Maps.newHashMap();
    //验证码验证
    String smscode = storeParamDTO.getVerificationCode();
    Object code = redisUtil.get(storeParamDTO.getBossPhone());
    if (code == null) {
        result.error500("手机号找不到验证码");
        return result;
    }
    if (!code.toString().equals(smscode)) {
        result.error500("验证码输入不正确，请重新输入");
        return result;
    }
    
    // 创建店铺记录
    StoreManage storeManage = new StoreManage();
    // 设置店铺信息
    // ...
    
    // 生成支付参数
    // ...
    
    objectMap.put("storeManage", BeanUtil.beanToMap(storeManage,false,false));
    result.setResult(objectMap);
    result.success("开店调起支付");
    return result;
}
```

### 6.3 支付成功回调实现

支付成功后，系统会调用`paySuccess`方法完成开店流程：
```java
@Override
@Transactional
public void paySuccess(String id) {
    // 更新订单信息
    StoreManage storeManage = this.getById(id);
    
    if (storeManage.getPayStatus().equals("0")) {
        //状态修改成已支付
        storeManage.setPayStatus("1");
        //设置开通时间
        Calendar calendar = Calendar.getInstance();
        storeManage.setStartTime(calendar.getTime());
        storeManage.setPayTime(calendar.getTime());
        if (storeManage.getOpenType().equals("0")) {
            calendar.add(1, Calendar.YEAR);
            storeManage.setEndTime(calendar.getTime());
        }
        storeManage.setStatus("1");
        storeManage.setPayType("0");
        
        //生成系统店铺用户
        // ...
        
        //设置用户绑定
        storeManage.setSysUserId(user.getId());
        //增加店铺缴费记录
        // ...
        
        //标识会员已开店
        MemberList memberList = iMemberListService.getById(storeManage.getMemberListId());
        if(memberList!=null) {
            memberList.setIsOpenStore("1");
            // ...
        }
        
        this.saveOrUpdate(storeManage);
    }
}
```

## 7. 总结

"我要开店"功能是一个完整的业务流程，从前端页面交互到后端业务处理，涉及多个接口和数据处理逻辑。主要包括：

1. 用户填写开店信息
2. 选择开店类型（年费或终身）
3. 提交开店申请
4. 支付开店费用
5. 后端处理开店申请并创建店铺

该功能的实现涉及表单验证、地址选择、验证码验证、支付流程等多个方面，是小程序中重要的商业功能之一。
