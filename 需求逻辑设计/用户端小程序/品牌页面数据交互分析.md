# 品牌页面数据交互分析

## 一、页面跳转流程分析

### 1. 首页"查看更多"按钮跳转逻辑

在首页(`pages/index/index.vue`)中，有三个不同类型的"查看更多"按钮，分别对应品牌馆、生活馆和创业馆。点击这些按钮会跳转到`pages/helpPanel/helpPanel.vue`页面，并传递不同的`type`参数：

```vue
<!-- 品牌馆 -->
<view class="index-container-comWrap-top-ct-right" @click="navTo('/pages/helpPanel/helpPanel?type=1')">
  查看更多
  <image src="@/static/index/arrow-right-blue.png" mode=""></image>
</view>

<!-- 生活馆 -->
<view class="index-container-comWrap-top-ct-right" @click="navTo('/pages/helpPanel/helpPanel?type=2')">
  查看更多
  <image src="@/static/index/arrow-right-blue.png" mode=""></image>
</view>

<!-- 创业馆 -->
<view class="index-container-comWrap-top-ct-right" @click="navTo('/pages/helpPanel/helpPanel?type=3')">
  查看更多
  <image src="@/static/index/arrow-right-blue.png" mode=""></image>
</view>
```

跳转使用的是`navTo`函数，该函数定义在`hooks/index.js`中：

```javascript
//navigate跳转
export function navTo(url, callback = '') {
  uni.navigateTo({
    url,
    fail() {
      switchTo(url);
    },
    success() {
      callback && callback();
    },
  });
}
```

### 2. 参数传递与页面初始化

当用户点击"查看更多"按钮时，会将对应的`type`参数传递给`helpPanel.vue`页面：
- 品牌馆：`type=1`
- 生活馆：`type=2`
- 创业馆：`type=3`

在`helpPanel.vue`页面的`onLoad`生命周期函数中，会根据传入的`type`参数设置页面标题和数据请求参数：

```javascript
onLoad((o) => {
  if (o.type) {
    const typeList = ['品牌馆', '生活馆', '创业馆']
    const storeTypeList = ['15', '16', '17']
    pageType.value = o.type
    storeType.value = storeTypeList[o.type - 1]
    uni.setNavigationBarTitle({
      title: typeList[pageType.value - 1]
    })
    refreshPage()
  }
})
```

这里根据`type`参数设置了：
- `pageType`：页面类型（1、2、3）
- `storeType`：对应的店铺类型ID（15、16、17）
- 页面标题：根据类型设置为"品牌馆"、"生活馆"或"创业馆"

然后调用`refreshPage()`函数加载页面数据。

## 二、数据交互逻辑

`helpPanel.vue`页面主要与后端进行三种数据交互：

### 1. 广告数据获取

页面顶部的轮播图数据通过`findarketingAdvertisingList`接口获取：

```javascript
const refreshPage = async () => {
  const {
    data: {
      result
    }
  } = await uni.http.get(uni.api.findarketingAdvertisingList, {
    params: {
      pattern: 0
    }
  })
  advList.value = result.map(i => {
    i.pictureAddr = `${uni.env.IMAGE_URL}${i.pictureAddr}`
    return i
  })
  // ...
}
```

**请求参数**：
- `pattern`: 0（固定值）

**响应数据处理**：
- 将返回的结果赋值给`advList`
- 处理图片地址，添加基础URL前缀

### 2. 热门商品数据获取

页面中部的热门商品数据通过`searchHostStoreGoodList`接口获取：

```javascript
const refreshPage = async () => {
  // ...
  const searchHostStoreGoodData = await uni.http.get(uni.api.searchHostStoreGoodList, {
    params: {
      storeType: storeType.value
    }
  })
  hotList.value = searchHostStoreGoodData.data.result || []
  refresh()
}
```

**请求参数**：
- `storeType`: 根据页面类型设置（15、16或17）

**响应数据处理**：
- 将返回的结果赋值给`hotList`
- 热门商品数据通过`hotWrap`组件展示

### 3. 店铺列表数据获取

页面底部的店铺列表数据通过`findStoreManageList`接口获取，使用了`listGet`工具函数：

```javascript
const {
  mode,
  list,
  refresh
} = listGet({
  apiUrl: uni.api.findStoreManageList,
  isReqTypeReq: false,
  options: computed(() => ({
    storeType: storeType.value,
    pattern: 0
  })),
})
```

**请求参数**：
- `storeType`: 根据页面类型设置（15、16或17）
- `pattern`: 0（固定值）
- `pageNo`: 页码（由`listGet`函数自动管理）
- `pageSize`: 每页数量（由`listGet`函数自动管理，默认10）

**响应数据处理**：
- 将返回的结果赋值给`list`
- 店铺列表数据通过`peopleDetail`组件展示

## 三、数据展示组件

### 1. 热门商品展示 - hotWrap组件

热门商品通过`hotWrap`组件展示，该组件定义在`pages/helpPanel/components/hotWrap/hotWrap.vue`：

```vue
<template>
  <view class="hotWrap" @click="toDetail()">
    <image v-if="data?.goodName" class="hotWrap-banner" :src="imgUrl + parseImgurl(data.mainPicture)?.[0]"></image>
    <view v-if="data?.goodName" class="hotWrap-bottom">
      <view class="hotWrap-bottom-name">
        {{data.goodName}}
      </view>
      <view class="hotWrap-bottom-btn" v-if="showGo">
        GO
      </view>
    </view>
  </view>
</template>
```

**数据属性**：
- `data`: 商品数据对象
- `wrapWidth`: 组件宽度（默认180rpx）
- `showGo`: 是否显示GO按钮（默认true）
- `banBgColor`: 背景颜色（默认#4788F2）
- `fontColor`: 字体颜色（默认white）

**交互逻辑**：
- 点击商品会调用`navToGoodDetail`函数跳转到商品详情页

### 2. 店铺列表展示 - peopleDetail组件

店铺列表通过`peopleDetail`组件展示，该组件定义在`pages/heartPool/components/peopleDetail/peopleDetail.vue`：

```vue
<template>
  <view class="peopleDetail-outWrap">
    <view class="peopleDetail">
      <view class="peopleDetail-pic">
        <image :src="imgUrl + storeInfo.logoAddr" mode="aspectFill"></image>
      </view>
      <view class="peopleDetail-det">
        <view class="peopleDetail-det-title">
          {{storeInfo.storeName}}
        </view>
        <view class="peopleDetail-det-desc">
          {{storeInfo.introduce}}
        </view>
        <view class="peopleDetail-det-dz">
          创业积分:{{storeInfo.totalPerformance}}
        </view>
        <view class="peopleDetail-det-btn" @click="navToStoreIndex(storeInfo)">
          立即助力
        </view>
      </view>
    </view>
    <!-- 店铺商品展示 -->
    <template v-if="storeInfo.storeGoods?.length">
      <!-- 商品列表 -->
    </template>
  </view>
</template>
```

**数据属性**：
- `storeInfo`: 店铺信息对象

**展示内容**：
- 店铺Logo
- 店铺名称
- 店铺介绍
- 创业积分
- "立即助力"按钮
- 店铺商品列表（横向滚动）

**交互逻辑**：
- 点击"立即助力"按钮会调用`navToStoreIndex`函数跳转到店铺首页
- 点击商品会调用`navToGoodDetail`函数跳转到商品详情页

## 四、数据流转图

```
首页"查看更多"按钮
    ↓
跳转到helpPanel.vue（传递type参数）
    ↓
onLoad生命周期（设置storeType和页面标题）
    ↓
调用refreshPage()函数
    ↓
┌─────────────────────┬─────────────────────┬─────────────────────┐
│                     │                     │                     │
↓                     ↓                     ↓
获取广告数据           获取热门商品数据        获取店铺列表数据
findarketingAdvertisingList  searchHostStoreGoodList  findStoreManageList
│                     │                     │
↓                     ↓                     ↓
advList               hotList              list
│                     │                     │
↓                     ↓                     ↓
轮播图展示             热门商品展示          店铺列表展示
                     hotWrap组件           peopleDetail组件
```

## 五、接口详细说明

### 1. 广告接口 (findarketingAdvertisingList)

**请求URL**: `/front/MarketingAdvertising/findarketingAdvertisingList`
**请求方式**: GET
**请求参数**:
- `pattern`: 0（固定值）

**响应数据结构**:
```javascript
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "result": [
      {
        "pictureAddr": "图片地址（相对路径）",
        "title": "广告标题",
        "goToType": "跳转类型",
        // 其他广告相关字段
      }
    ]
  }
}
```

### 2. 热门商品接口 (searchHostStoreGoodList)

**请求URL**: `/front/goodList/searchHostStoreGoodList`
**请求方式**: GET
**请求参数**:
- `storeType`: 店铺类型ID（15、16或17）

**响应数据结构**:
```javascript
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "result": [
      {
        "goodId": "商品ID",
        "goodName": "商品名称",
        "mainPicture": "商品主图（可能包含多张图片）",
        "marketPrice": "市场价",
        "salesVolume": "销售量",
        "salesPerformance": "销售业绩/积分",
        // 其他商品相关字段
      }
    ]
  }
}
```

### 3. 店铺列表接口 (findStoreManageList)

**请求URL**: `/front/storeManage/findStoreManageList`
**请求方式**: GET
**请求参数**:
- `storeType`: 店铺类型ID（15、16或17）
- `pattern`: 0（固定值）
- `pageNo`: 页码
- `pageSize`: 每页数量

**响应数据结构**:
```javascript
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "result": {
      "total": "总记录数",
      "records": [
        {
          "storeManageId": "店铺ID",
          "storeName": "店铺名称",
          "logoAddr": "店铺logo地址（相对路径）",
          "introduce": "店铺介绍",
          "totalPerformance": "创业积分",
          "storeGoods": [
            {
              "id": "商品ID",
              "goodName": "商品名称",
              "mainPicture": "商品主图",
              // 其他商品相关字段
            }
          ],
          // 其他店铺相关字段
        }
      ]
    }
  }
}
```

## 六、交互事件处理

### 1. 广告点击事件

广告点击事件通过`adNavTo`函数处理，该函数定义在`hooks/index.js`中：

```javascript
//广告点击
export function adNavTo(item) {
  if (item.goToType == 1) {
    //商品详情
    navToGoodDetail(item);
  } else if (item.goToType == 3) {
    //发现详情
    navTo(`/pages/findDetail/findDetail?id=${item.marketingMaterialListId}`);
  } else if (item.goToType == 2 && item.pictureDetails) {
    //详情图
    navTo(`/pages/piDetShow/piDetShow?pictureDetails=${item.pictureDetails}&title=${item.title}`);
  } else if (item.goToType == 4) {
    //封坛礼包跳转
    navTo(`/pages/ftGiftBagList/ftGiftBagList?sysUserId=${item.sysUserId}`);
  }
}
```

根据广告的`goToType`属性，跳转到不同的页面：
- `goToType=1`: 商品详情页
- `goToType=2`: 详情图页面
- `goToType=3`: 发现详情页
- `goToType=4`: 封坛礼包页面

### 2. 热门商品点击事件

热门商品点击事件在`hotWrap`组件中处理：

```javascript
function toDetail() {
  if (props.data?.goodName) {
    navToGoodDetail(props.data)
  }
}
```

点击热门商品会调用`navToGoodDetail`函数跳转到商品详情页。

### 3. 店铺"立即助力"按钮点击事件

店铺"立即助力"按钮点击事件在`peopleDetail`组件中处理：

```vue
<view class="peopleDetail-det-btn" @click="navToStoreIndex(storeInfo)">
  立即助力
</view>
```

点击"立即助力"按钮会调用`navToStoreIndex`函数跳转到店铺首页：

```javascript
//跳转到店铺首页
export function navToStoreIndex(item) {
  navTo(
    `/pages/shopIndex/shopIndex?storeManageId=${item.storeManageId || item.id || item.storeId}&sysUserId=${item.sysUserId}`
  );
}
```

## 七、总结

`helpPanel.vue`页面是一个展示特定类型店铺（品牌馆、生活馆或创业馆）的页面，主要包含三部分内容：
1. 顶部轮播广告
2. 中部热门商品推荐
3. 底部店铺列表

页面通过三个不同的接口获取数据：
1. `findarketingAdvertisingList`: 获取广告数据
2. `searchHostStoreGoodList`: 获取热门商品数据
3. `findStoreManageList`: 获取店铺列表数据

页面的主要交互逻辑包括：
1. 点击广告跳转到相应页面
2. 点击热门商品跳转到商品详情页
3. 点击"立即助力"按钮跳转到店铺首页
4. 点击店铺商品跳转到商品详情页

页面的数据加载流程是：
1. 从首页点击"查看更多"按钮，传递`type`参数
2. 在`onLoad`生命周期中根据`type`参数设置`storeType`和页面标题
3. 调用`refreshPage`函数加载页面数据
4. 分别调用三个接口获取不同部分的数据
5. 将数据渲染到页面上
