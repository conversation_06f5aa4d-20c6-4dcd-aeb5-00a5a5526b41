# 提现申请功能业务逻辑分析报告

## 1. 概述

本文档对用户端小程序提现申请功能进行详细分析，包括提现申请和提现明细两个主要功能模块的前端页面交互逻辑、数据交互逻辑以及后端接口的业务逻辑实现。

## 2. 功能模块

提现申请功能主要包含以下模块：

1. **提现申请页面**：用户输入提现金额，选择银行卡，提交提现申请
2. **提现明细页面**：查看历史提现记录及状态
3. **银行卡管理**：添加、选择银行卡

## 3. 前端页面交互逻辑

### 3.1 提现申请页面 (pages/withDraw/withDraw.vue)

#### 3.1.1 页面结构

提现申请页面主要包含以下几个部分：
- 顶部导航栏：包含"结算助力"标题和"结算明细"入口
- 提现金额输入区：用户输入提现金额，显示可提现余额
- 实际到账金额显示区：显示扣除手续费后的实际到账金额
- 结算渠道选择区：选择银行卡
- 温馨提醒区：显示提现规则和注意事项
- 底部提交按钮：提交提现申请

#### 3.1.2 交互流程

1. **页面初始化**：
   - 加载用户信息（余额）
   - 获取提现温馨提示文本 (getWithdrawWarmPrompt)
   - 获取最小提现额度 (getWithdrawMinimum)
   - 监听银行卡选择事件

2. **金额输入**：
   - 用户输入提现金额
   - 实时计算手续费和实际到账金额 (withdrawXchanger)
   - 显示实际到账金额

3. **银行卡选择**：
   - 点击银行卡区域跳转到银行卡选择页面
   - 选择银行卡后通过事件回传选中的银行卡信息

4. **提交提现**：
   - 点击"立即提现"按钮
   - 进行各项验证（余额是否足够、是否选择银行卡等）
   - 弹出确认提示
   - 确认后提交提现申请 (withdrawalCard)
   - 显示提交结果

### 3.2 提现明细页面 (pages/withDrawLs/withDrawLs.vue)

#### 3.2.1 页面结构

提现明细页面主要包含以下几个部分：
- 顶部状态标签页：全部、待审核、成功、失败
- 提现记录列表：显示提现类型、时间、金额等信息
- 空状态提示：无记录时显示

#### 3.2.2 交互流程

1. **页面初始化**：
   - 默认加载"全部"状态的提现记录

2. **状态切换**：
   - 点击顶部标签切换不同状态的提现记录
   - 根据选中状态请求对应的提现记录数据

3. **记录展示**：
   - 展示提现记录列表，包括提现渠道（微信、支付宝、银行卡）、时间、金额等信息

## 4. 数据交互逻辑

### 4.1 提现申请页面

#### 4.1.1 API接口调用

1. **获取提现温馨提示**：
   ```javascript
   uni.http.get(uni.api.getWithdrawWarmPrompt)
   ```

2. **获取最小提现额度**：
   ```javascript
   uni.http.get(uni.api.getWithdrawMinimum)
   ```

3. **计算提现金额和手续费**：
   ```javascript
   uni.http.get(uni.api.withdrawXchanger, {
     params: { money: price.value * 1 || 0 }
   })
   ```

4. **提交提现申请**：
   ```javascript
   uni.http.post(uni.api.withdrawalCard, {
     money: price.value * 1 || 0,
     memberBankCardId: selectedBankCardInfo.value.id
   })
   ```

#### 4.1.2 数据监听

页面实现了对提现金额的监听，当金额变化时自动计算手续费和实际到账金额：

```javascript
watch(price, async (n) => {
  if (users.token) {
    let { data: res } = await uni.http.get(uni.api.withdrawXchanger, {
      params: { money: price.value * 1 || 0 }
    });
    if (n) {
      amountDeducted.value = res.result.serviceCharge;
      resultPrice.value = res.result.amount;
    } else {
      amountDeducted.value = 0
      resultPrice.value = 0
    }
  }
}, { immediate: true, deep: true })
```

### 4.2 提现明细页面

#### 4.2.1 API接口调用

1. **获取提现记录列表**：
   ```javascript
   // 通过listGet钩子函数调用
   listGet({
     options: reqOptions,
     apiUrl: uni.api.findMemberWithdrawDepositPageByMemberId,
     pdIsLogin: true
   })
   ```

#### 4.2.2 状态映射

页面定义了提现状态和提现类型的映射关系：

```javascript
const statusDescribtionList = ['待审核', '待打款', '已打款', '无效']
const withdrawalTypeTextList = ['微信', '支付宝', '银行卡']
```

请求参数根据选中的标签页动态生成：

```javascript
let reqOptions = computed(() => {
  const patternList = [-1, 0, 1, 3]
  return {
    pattern: patternList[tabIndex.value]
  }
})
```

## 5. 后端接口业务逻辑

### 5.1 提现申请相关接口

#### 5.1.1 获取提现温馨提示 (getWithdrawWarmPrompt)

**接口路径**：`after/memberWithdrawDeposit/getWithdrawWarmPrompt`

**实现逻辑**：
- 从系统字典表中获取提现说明文本
- 返回提现说明文本，多条说明用`\n`分隔

```java
@RequestMapping("getWithdrawWarmPrompt")
@ResponseBody
public Result<?> getWithdrawWarmPrompt(){
    //获取提现说明
    String queryTableDictTextByKey = iSysDictService.queryTableDictTextByKey("sys_dict_item", "item_value", "item_text", "withdraw_warm_prompt");
    return Result.ok(queryTableDictTextByKey);
}
```

#### 5.1.2 获取最小提现额度 (getWithdrawMinimum)

**接口路径**：`after/memberWithdrawDeposit/getWithdrawMinimum`

**实现逻辑**：
- 从系统字典表中获取最小提现额度配置
- 返回最小提现额度

```java
@RequestMapping("getWithdrawMinimum")
@ResponseBody
public Result<?> getWithdrawMinimum(){
    //获取最低提现金额
    String queryTableDictTextByKey = iSysDictService.queryTableDictTextByKey("sys_dict_item", "item_value", "item_text", "withdraw_minimum");
    return Result.ok(queryTableDictTextByKey);
}
```

#### 5.1.3 提现金额计算 (withdrawXchanger)

**接口路径**：`after/memberWithdrawDeposit/withdrawXchanger`

**实现逻辑**：
- 获取提现相关配置（最小提现金额、提现阈值、手续费类型和比例等）
- 根据提现金额计算手续费（根据是否超过阈值使用不同的计算方式）
- 计算实际到账金额（提现金额减去手续费）
- 返回手续费和实际到账金额

```java
@RequestMapping("withdrawXchanger")
@ResponseBody
public Result<?> withdrawXchanger(@RequestParam(required = false,defaultValue = "0")String money){
    money = StrUtil.blankToDefault(money,"0");
    HashMap<String, Object> map = new HashMap<>();
    //最小提现金额
    String withdrawMinimum = iSysDictService.queryTableDictTextByKey("sys_dict_item", "item_value", "item_text", "withdraw_minimum");
    //获取字典配置提现阈值，大于此数据值则手续费按照大于的取，小于等于此数据值则手续费小于的取
    String withdrawThreshold = iSysDictService.queryTableDictTextByKey("sys_dict_item", "item_value", "item_text", "withdraw_threshold");
    //提现手续费小于阈值类型：0：固定费用（fixed）；1：百分比（percent）；
    String withdrawServiceChargeLessType = iSysDictService.queryTableDictTextByKey("sys_dict_item", "item_value", "item_text", "withdraw_service_charge_less_type");
    
    // 计算手续费和实际到账金额
    BigDecimal moneyBigDecimal = new BigDecimal(money);
    BigDecimal serviceCharge = BigDecimal.ZERO;
    
    // 根据阈值和手续费类型计算手续费
    if (moneyBigDecimal.doubleValue() <= Double.valueOf(withdrawThreshold)) {
        // 小于阈值的计算逻辑
    } else {
        // 大于阈值的计算逻辑
    }
    
    // 计算实际到账金额
    BigDecimal amount = moneyBigDecimal.subtract(serviceCharge);
    
    map.put("serviceCharge", serviceCharge);
    map.put("amount", amount);
    
    return Result.ok(map);
}
```

#### 5.1.4 提交提现申请 (withdrawalCard)

**接口路径**：`after/memberWithdrawDeposit/withdrawalCard`

**实现逻辑**：
1. 验证提现金额是否合法
2. 验证用户是否绑定银行卡
3. 验证用户余额是否足够
4. 创建提现记录
5. 扣减用户可用余额
6. 返回提现结果

```java
@RequestMapping("withdrawalCard")
@ResponseBody
public Result<String> withdrawalCard(
        @RequestParam(name = "money",required = true) BigDecimal money,
        @RequestParam(name = "memberBankCardId",required = false) String memberBankCardId,
        HttpServletRequest request){
    Result<String> result = new Result<>();
    
    String memberId = request.getAttribute("memberId").toString();
    if (money.doubleValue()<0){
        return result.error500("请输入正确金额!");
    }
    
    // 验证和处理逻辑
    
    if (member.getBalance().doubleValue()<money.doubleValue()){
        return result.error500("提现金额不足!");
    } else {
        //提现申请
        boolean b = iMemberWithdrawDepositService.withdrawalCard(money, member, bankCard);
        if (b){
            result.success("提现成功!请等待管理员审核!");
        } else {
            result.error500("提现失败!");
        }
    }
    
    return result;
}
```

### 5.2 提现明细相关接口

#### 5.2.1 获取提现记录列表 (findMemberWithdrawDepositPageByMemberId)

**接口路径**：`after/memberWithdrawDeposit/findMemberWithdrawDepositPageByMemberId`

**实现逻辑**：
- 根据会员ID和状态筛选条件查询提现记录
- 分页返回提现记录列表

```java
@Override
public IPage<Map<String, Object>> findMemberWithdrawDepositPageByMemberId(Page<Map<String, Object>> page, Map<String, Object> paramObjectMap) {
    return baseMapper.findMemberWithdrawDepositPageByMemberId(page, paramObjectMap);
}
```

SQL查询实现：

```xml
<select id="findMemberWithdrawDepositPageByMemberId" resultType="map">
    SELECT mwd.`id`,
           DATE_FORMAT(
                   time_application,
                   '%Y-%m-%d %H:%i'
           )                    AS timeApplication,
           (
               CASE
                   mwd.`withdrawal_type`
                   WHEN 0
                       THEN '微信钱包'
                   WHEN 1
                       THEN '支付宝'
                   WHEN 2
                       THEN CONCAT(
                           mwd.`bank_name`,
                           '(',
                           RIGHT(bank_card, 4),
                           ')'
                            )
                   END
               )                    AS withdrawalType,
           mwd.`money`,
           mwd.`status`
    FROM
        `member_withdraw_deposit` mwd
    WHERE mwd.`del_flag` = '0'
      AND mwd.`member_list_id` = #{paramObjectMap.memberId}
    <if test="paramObjectMap.pattern != null and paramObjectMap.pattern != -1">
        AND mwd.`status` = #{paramObjectMap.pattern}
    </if>
    ORDER BY mwd.`create_time` DESC
</select>
```

## 6. 核心业务流程

### 6.1 提现申请流程

1. **前端页面初始化**：
   - 加载用户信息（余额）
   - 获取提现温馨提示和最小提现额度
   - 监听银行卡选择事件

2. **用户操作**：
   - 输入提现金额
   - 选择银行卡
   - 点击提交按钮

3. **前端验证**：
   - 验证用户是否登录
   - 验证余额是否足够
   - 验证提现金额是否大于0
   - 验证提现金额是否超过可提现余额
   - 验证提现金额是否达到最小提现额度
   - 验证是否选择银行卡

4. **提交提现申请**：
   - 调用提现接口
   - 显示提交结果

5. **后端处理**：
   - 验证提现金额和用户余额
   - 创建提现记录
   - 扣减用户可用余额
   - 返回处理结果

### 6.2 提现状态流转

提现申请的状态流转如下：
1. **待审核(0)**：提现申请提交后的初始状态
2. **待打款(1)**：管理员审核通过后的状态
3. **已付款(2)**：管理员完成打款操作后的状态
4. **无效(3)**：管理员拒绝提现申请的状态

## 7. 数据模型

### 7.1 提现记录表 (member_withdraw_deposit)

主要字段：
- id：主键
- member_list_id：会员ID
- order_no：提现单号
- money：提现金额
- withdrawal_type：提现类型（0：微信；1：支付宝；2：银行卡）
- service_charge：手续费
- amount：实际到账金额
- time_application：申请时间
- status：状态（0：待审核；1：待打款；2：已付款；3：无效）
- bank_card：银行卡号
- bank_name：银行名称
- cardholder：持卡人姓名
- identity_number：身份证号
- opening_bank：开户分支行

### 7.2 银行卡表 (member_bank_card)

主要字段：
- id：主键
- member_list_id：会员ID
- bank_name：银行名称
- bank_card：银行卡号
- cardholder：持卡人姓名
- car_type：卡类型（0：银行卡；1：支付宝）
- phone：银行绑定手机号
- identity_number：身份证号
- opening_bank：开户分支行
- is_default：是否默认（0：否；1：是）

## 8. 总结

提现申请功能是用户将平台账户余额提现到银行卡的重要功能，整个流程包括提现申请和提现明细查询两个主要部分。前端负责用户交互和数据展示，后端负责业务逻辑处理和数据持久化。系统通过多重验证确保提现操作的安全性，通过状态流转管理提现申请的生命周期。
