# 新增/编辑商品页面接口分析（reusePage=3）

本文档分析了在商品新增/编辑页面中，当`reusePage=3`（店铺用户）时，"保存草稿箱"和"提交并上架"操作调用的后端接口及提交的数据结构。

## 1. 接口调用分析

### 1.1 保存草稿箱

当用户点击"保存草稿箱"按钮时，会调用`prevStep(0)`方法，该方法会执行以下操作：

1. 调用后端接口：`/goodStoreList/goodStoreList/addNew`
2. 请求方法：POST
3. 请求完成后跳转到：`/good/GoodStoreListDraftList`（草稿箱列表页面）

相关代码：
```javascript
// 保存草稿箱
httpAction(this.url.addgoodStore, values, 'post')
  .then(res => {
    if (res.success) {
      this.$message.success(res.message)
      this.$emit('ok')
      this.$router.push({ path: `/good/GoodStoreListDraftList`, query: { isrush: true } })
    } else {
      this.$message.warning(res.message)
    }
  })
  .finally(() => {
    this.close()
  })
```

### 1.2 提交并上架

当用户点击"提交并上架"按钮时，会调用`prevStep(1)`方法，该方法会执行以下操作：

1. 首先进行数据校验（规格、价格、库存等）
2. 校验通过后，调用后端接口：`/goodStoreList/goodStoreList/addNew`（与保存草稿箱相同）
3. 请求方法：POST
4. 请求完成后跳转到：`/good/GoodStoreListList`（商品列表页面）

相关代码：
```javascript
// 提交并上架
httpAction(this.url.addgoodStore, values, 'post')
  .then(res => {
    if (res.success) {
      this.$message.success(res.message)
      this.$emit('ok')
      this.$router.push({ path: `/good/GoodStoreListList`, query: { isrush: true } })
    } else {
      this.$message.warning(res.message)
    }
  })
  .finally(() => {
    this.close()
  })
```

## 2. 数据结构分析

### 2.1 提交的数据结构

当`reusePage=3`（店铺用户）时，提交的数据结构包含以下主要字段：

#### 2.1.1 基本信息

| 字段名 | 类型 | 说明 | 是否必填 |
|-------|------|------|---------|
| sysUserId | String | 用户ID | 是 |
| goodTypeId | String | 商品分类ID | 是 |
| goodName | String | 商品名称 | 是 |
| mainPicture | String | 商品主图（JSON字符串） | 是 |
| goodDescribe | String | 商品描述 | 是 |
| detailsGoods | String | 商品详情图（JSON字符串） | 否 |
| goodVideo | String | 商品视频 | 否 |
| commitmentCustomers | String | 服务承诺（逗号分隔的字符串） | 是 |

#### 2.1.2 价格与库存信息

| 字段名 | 类型 | 说明 | 是否必填 |
|-------|------|------|---------|
| price | Number | 销售价 | 是 |
| costPrice | Number | 成本价 | 是 |
| vipPrice | Number | 会员价 | 是 |
| repertory | Number | 库存 | 是 |
| marketPrice | Number | 市场价 | 否 |

#### 2.1.3 规格信息

| 字段名 | 类型 | 说明 | 是否必填 |
|-------|------|------|---------|
| isSpecification | Number | 是否有规格（0:无规格, 1:有规格） | 是 |
| specification | String | 规格定义（JSON字符串） | 条件必填 |
| GoodListSpecificationVOs | Array | 商品规格列表 | 条件必填 |

##### specification字段详解

`specification`字段是一个JSON字符串，用于定义商品的规格结构。它与`GoodListSpecificationVOs`字段密切相关，前者定义规格的名称和可选值，后者定义每个具体规格组合的价格、库存等信息。

`specification`字段的结构如下：

```json
[
  {
    "CommodityStyle": "颜色",
    "classification": [
      {"value": "白色"},
      {"value": "黑色"}
    ]
  },
  {
    "CommodityStyle": "尺码",
    "classification": [
      {"value": "S"},
      {"value": "M"},
      {"value": "L"}
    ]
  }
]
```

其中：
- `CommodityStyle`：规格名称（如"颜色"、"尺码"等）
- `classification`：该规格的可选值数组
- `value`：具体的规格值（如"白色"、"S码"等）

##### GoodListSpecificationVOs字段详解

当`isSpecification=1`时，`GoodListSpecificationVOs`数组包含所有规格组合的详细信息：

```json
[
  {
    "specification": "白色,S",
    "price": 100.00,
    "costPrice": 80.00,
    "vipPrice": 90.00,
    "repertory": 100,
    "skuNo": "T001-S-W",
    "specificationPicture": "upload/goodPic/20230602/spec_white_s.jpg"
  },
  {
    "specification": "白色,M",
    "price": 100.00,
    "costPrice": 80.00,
    "vipPrice": 90.00,
    "repertory": 100,
    "skuNo": "T001-M-W"
  }
]
```

其中：
- `specification`：规格组合名称，由`specification`字段中定义的规格值组合而成，多个规格值用逗号分隔
- `price`：该规格组合的销售价
- `costPrice`：该规格组合的成本价
- `vipPrice`：该规格组合的会员价
- `repertory`：该规格组合的库存
- `skuNo`：该规格组合的SKU编码
- `specificationPicture`：该规格组合的图片（可选）

##### specification与GoodListSpecificationVOs的对应关系

两者的对应关系如下：
1. `specification`定义了规格的结构和可选值
2. `GoodListSpecificationVOs`中的每个元素对应一个具体的规格组合
3. `GoodListSpecificationVOs`中的`specification`字段值是由`specification`中定义的规格值按顺序组合而成
4. 例如，如果`specification`定义了"颜色"（白色、黑色）和"尺码"（S、M、L），则`GoodListSpecificationVOs`会包含6个元素，分别对应"白色,S"、"白色,M"、"白色,L"、"黑色,S"、"黑色,M"、"黑色,L"

**注意**：这两个字段必须保持一致，否则会导致数据不一致的问题。前端在提交数据时会自动根据用户在界面上的操作生成这两个字段的值，确保它们的对应关系正确。

#### 2.1.4 配送信息

| 字段名 | 类型 | 说明 | 是否必填 |
|-------|------|------|---------|
| distribution | String | 配送方式（0:快递发货, 1:自提核销） | 是 |
| providerTemplateId | String | 运费模板ID | 条件必填 |
| weight | Number | 物流重量 | 条件必填 |

#### 2.1.5 状态信息

| 字段名 | 类型 | 说明 | 是否必填 |
|-------|------|------|---------|
| delFlag | Number | 删除状态（0:未删除） | 是 |
| frameStatus | Number | 上下架状态（0:下架, 1:上架） | 是 |
| status | Number | 启用停用状态（0:停用, 1:启用） | 是 |

### 2.2 保存草稿箱与提交并上架的区别

两个操作调用的是同一个接口，但在提交的数据中有以下区别：

1. **保存草稿箱**：
   - 不会进行严格的数据校验
   - 可以保存不完整的商品信息
   - 保存后商品状态为草稿状态

2. **提交并上架**：
   - 会进行严格的数据校验（价格、库存、规格等）
   - 要求商品信息完整
   - 提交后商品状态为上架状态（frameStatus=1, status=1）

## 3. 数据验证规则

### 3.1 必填字段验证

系统对以下字段进行必填验证：
- 商品分类（goodTypeId）
- 商品名称（goodName）
- 商品主图（mainPicture）
- 商品描述（goodDescribe）
- 服务承诺（commitmentCustomers）
- 运费模板（providerTemplateId，当distribution=0时）

### 3.2 价格与库存验证

当`reusePage=3`（店铺用户）时，系统对价格和库存进行以下验证：

```javascript
// 无规格商品验证
result = this.data2.every(item => {
  return item.price * 1 > 0 && item.costPrice * 1 > 0 && item.vipPrice * 1 > 0 && item.repertory * 1 >= 0
})

// 有规格商品验证
result = this.data.every(item => {
  return item.costPrice * 1 > 0 && item.repertory * 1 >= 0 && item.vipPrice * 1 > 0 && item.price * 1 > 0
})
```

验证规则：
- 销售价（price）必须大于0
- 成本价（costPrice）必须大于0
- 会员价（vipPrice）必须大于0
- 库存（repertory）必须大于等于0

### 3.3 图片验证

- 商品主图：必须上传，且不超过5张
- 商品详情图：可选，最多50张
- 图片格式：仅支持JPG、PNG、GIF格式
- 图片大小：不超过5MB

## 4. 接口响应处理

接口响应结构：

```json
{
  "success": true/false,
  "message": "操作成功/失败信息",
  "result": {}
}
```

响应处理逻辑：
1. 如果`success=true`，显示成功消息，并跳转到相应页面
2. 如果`success=false`，显示错误消息，不进行页面跳转

## 5. 请求报文示例

### 5.1 无规格商品请求报文示例

```json
{
  "sysUserId": "**********",
  "goodTypeId": "100001",
  "goodName": "有机蔬菜礼盒",
  "mainPicture": "{\"0\":\"upload/goodPic/20230601/main_pic_1.jpg\",\"1\":\"upload/goodPic/20230601/main_pic_2.jpg\"}",
  "goodDescribe": "新鲜有机蔬菜，产地直发",
  "detailsGoods": "{\"0\":\"upload/goodPic/20230601/detail_1.jpg\",\"1\":\"upload/goodPic/20230601/detail_2.jpg\"}",
  "goodVideo": "upload/goodVideo/20230601/video.mp4",
  "commitmentCustomers": "0,1",
  "price": 99.00,
  "costPrice": 60.00,
  "vipPrice": 89.00,
  "repertory": 100,
  "marketPrice": 129.00,
  "isSpecification": 0,
  "distribution": "0",
  "providerTemplateId": "10001",
  "weight": 2.5,
  "delFlag": 0,
  "frameStatus": 1,
  "status": 1
}
```

### 5.2 有规格商品请求报文示例

```json
{
  "sysUserId": "**********",
  "goodTypeId": "100002",
  "goodName": "纯棉T恤",
  "mainPicture": "{\"0\":\"upload/goodPic/20230602/tshirt_main_1.jpg\",\"1\":\"upload/goodPic/20230602/tshirt_main_2.jpg\"}",
  "goodDescribe": "100%纯棉，舒适透气",
  "detailsGoods": "{\"0\":\"upload/goodPic/20230602/tshirt_detail_1.jpg\",\"1\":\"upload/goodPic/20230602/tshirt_detail_2.jpg\"}",
  "commitmentCustomers": "0,1",
  "isSpecification": 1,
  "specification": "[{\"CommodityStyle\":\"颜色\",\"classification\":[{\"value\":\"白色\"},{\"value\":\"黑色\"}]},{\"CommodityStyle\":\"尺码\",\"classification\":[{\"value\":\"S\"},{\"value\":\"M\"},{\"value\":\"L\"}]}]",
  "GoodListSpecificationVOs": [
    {
      "specification": "白色,S",
      "price": 79.00,
      "costPrice": 40.00,
      "vipPrice": 69.00,
      "repertory": 50,
      "skuNo": "T001-S-W"
    },
    {
      "specification": "白色,M",
      "price": 79.00,
      "costPrice": 40.00,
      "vipPrice": 69.00,
      "repertory": 100,
      "skuNo": "T001-M-W"
    },
    {
      "specification": "白色,L",
      "price": 79.00,
      "costPrice": 40.00,
      "vipPrice": 69.00,
      "repertory": 80,
      "skuNo": "T001-L-W"
    },
    {
      "specification": "黑色,S",
      "price": 79.00,
      "costPrice": 40.00,
      "vipPrice": 69.00,
      "repertory": 50,
      "skuNo": "T001-S-B"
    },
    {
      "specification": "黑色,M",
      "price": 79.00,
      "costPrice": 40.00,
      "vipPrice": 69.00,
      "repertory": 100,
      "skuNo": "T001-M-B"
    },
    {
      "specification": "黑色,L",
      "price": 79.00,
      "costPrice": 40.00,
      "vipPrice": 69.00,
      "repertory": 80,
      "skuNo": "T001-L-B"
    }
  ],
  "distribution": "0",
  "providerTemplateId": "10001",
  "weight": 0.3,
  "delFlag": 0,
  "frameStatus": 1,
  "status": 1
}
```

### 5.3 自提核销商品请求报文示例

```json
{
  "sysUserId": "**********",
  "goodTypeId": "100003",
  "goodName": "瑜伽课程体验券",
  "mainPicture": "{\"0\":\"upload/goodPic/20230603/yoga_main_1.jpg\",\"1\":\"upload/goodPic/20230603/yoga_main_2.jpg\"}",
  "goodDescribe": "专业瑜伽课程，新手友好",
  "detailsGoods": "{\"0\":\"upload/goodPic/20230603/yoga_detail_1.jpg\",\"1\":\"upload/goodPic/20230603/yoga_detail_2.jpg\"}",
  "commitmentCustomers": "0",
  "price": 199.00,
  "costPrice": 100.00,
  "vipPrice": 159.00,
  "repertory": 50,
  "marketPrice": 299.00,
  "isSpecification": 0,
  "distribution": "1",
  "pickUpQrCode": "upload/qrcode/20230603/yoga_qrcode.jpg",
  "pickUpQrAddr": "北京市朝阳区健康路100号瑜伽中心",
  "pickUpVerificationStatus": 0,
  "delFlag": 0,
  "frameStatus": 1,
  "status": 1
}
```

## 6. 总结

当`reusePage=3`（店铺用户）时：

1. **保存草稿箱**和**提交并上架**都调用`/goodStoreList/goodStoreList/addNew`接口
2. 两者提交的数据结构基本相同，但**提交并上架**会进行更严格的数据校验
3. **保存草稿箱**成功后跳转到草稿箱列表页面
4. **提交并上架**成功后跳转到商品列表页面
5. 店铺用户的商品信息必须包含销售价、成本价、会员价和库存等信息
6. 根据配送方式不同（快递发货或自提核销），需要提供不同的配送相关信息
