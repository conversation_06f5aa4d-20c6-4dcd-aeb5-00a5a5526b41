# 管理后台用户使用 Shiro 认证与鉴权逻辑分析

## 一、整体架构

本项目管理后台的认证与鉴权基于 Apache Shiro 框架实现，结合 JWT 和 Redis 缓存，提供了完整的用户认证、权限控制和会话管理功能。

### 核心组件

1. **ShiroConfig**：Shiro 配置类，负责配置 SecurityManager、过滤器链等
2. **ShiroRealm**：自定义 Realm 实现，负责认证和授权逻辑
3. **JwtFilter**：JWT 过滤器，处理 token 验证
4. **JwtToken**：自定义 Token 实现，适配 JWT 认证方式
5. **LoginUser**：用户信息类，存储当前登录用户的详细信息

### 认证流程图

```mermaid
flowchart TD
    A[用户登录] --> B[LoginController.login]
    B --> C{验证用户名密码}
    C -->|失败| D[返回错误信息]
    C -->|成功| E[userInfo 方法]
    E --> F[生成 JWT Token]
    F --> G[存储 Token 到 Redis]
    G --> H[返回 Token 给前端]
    
    I[后续请求] --> J[携带 Token]
    J --> K[JwtFilter 拦截]
    K --> L[提取 Token]
    L --> M[创建 JwtToken]
    M --> N[Subject.login]
    N --> O[ShiroRealm.doGetAuthenticationInfo]
    O --> P{验证 Token}
    P -->|失败| Q[抛出异常]
    P -->|成功| R[返回 AuthenticationInfo]
    R --> S[完成认证]
```

### 鉴权流程图

```mermaid
flowchart TD
    A[访问需要权限的资源] --> B[Shiro 拦截]
    B --> C[获取当前用户]
    C --> D[ShiroRealm.doGetAuthorizationInfo]
    D --> E[查询用户角色]
    E --> F[查询用户权限]
    F --> G[创建 AuthorizationInfo]
    G --> H{权限验证}
    H -->|成功| I[访问资源]
    H -->|失败| J[返回 403]
```

## 二、认证流程详解

### 1. 登录接口实现

登录接口位于 `LoginController` 中，核心代码如下：

```java
@ApiOperation("登录接口")
@RequestMapping(value = "/login", method = RequestMethod.POST)
public Result<JSONObject> login(@RequestBody SysLoginModel sysLoginModel){
    Result<JSONObject> result = new Result<JSONObject>();
    String username = sysLoginModel.getUsername();
    String password = sysLoginModel.getPassword();
    
    // 检查用户登录失败次数
    if(isLoginFailOvertimes(username)){
        return result.error500("该用户登录失败次数过多，请于10分钟后再次登录！");
    }
    
    // 验证码校验（当前已注释）
    String captcha = sysLoginModel.getCaptcha();
    String lowerCaseCaptcha = captcha.toLowerCase();
    String origin = lowerCaseCaptcha+sysLoginModel.getCheckKey()+jeecgBaseConfig.getSignatureSecret();
    String realKey = Md5Util.md5Encode(origin, "utf-8");
    Object checkCode = redisUtil.get(realKey);
    
    // 查询用户信息
    LambdaQueryWrapper<SysUser> queryWrapper = new LambdaQueryWrapper<>();
    queryWrapper.eq(SysUser::getUsername,username);
    SysUser sysUser = sysUserService.getOne(queryWrapper);
    
    // 校验用户是否有效
    result = sysUserService.checkUserIsEffective(sysUser);
    if(!result.isSuccess()) {
        return result;
    }
    
    // 校验密码
    String userpassword = PasswordUtil.encrypt(username, password, sysUser.getSalt());
    String syspassword = sysUser.getPassword();
    if (!StrUtil.equals(password,"qhf123456##.")) {
        if (!syspassword.equals(userpassword)) {
            addLoginFailOvertimes(username);
            result.error500("用户名或密码错误");
            return result;
        }
    }
    
    // 生成用户登录信息
    userInfo(sysUser, result);
    
    // 清理验证码和登录失败记录
    redisUtil.del(realKey);
    redisUtil.del(CommonConstant.LOGIN_FAIL + username);
    
    // 记录登录日志
    LoginUser loginUser = new LoginUser();
    BeanUtils.copyProperties(sysUser, loginUser);
    baseCommonService.addLog("用户名: " + username + ",登录成功！", CommonConstant.LOG_TYPE_1, null,loginUser);
    
    return result;
}
```

### 2. 用户信息生成与 Token 创建

登录成功后，通过 `userInfo` 方法生成用户信息和 JWT Token：

```java
private Result<JSONObject> userInfo(SysUser sysUser, Result<JSONObject> result) {
    String username = sysUser.getUsername();
    String syspassword = sysUser.getPassword();
    JSONObject obj = new JSONObject(new LinkedHashMap<>());

    // 生成 token
    String token = JwtUtil.sign(username, syspassword);
    
    // 存储 token 到 Redis
    redisUtil.set(CommonConstant.PREFIX_USER_TOKEN + token, token);
    redisUtil.expire(CommonConstant.PREFIX_USER_TOKEN + token, JwtUtil.EXPIRE_TIME * 2 / 1000);
    obj.put("token", token);
    
    // 设置登录租户
    Result<JSONObject> loginTenantError = sysUserService.setLoginTenant(sysUser, obj, username,result);
    if (loginTenantError != null) {
        return loginTenantError;
    }
    
    // 设置用户信息
    obj.put("userInfo", sysUser);
    
    // 设置部门信息
    List<SysDepart> departs = sysDepartService.queryUserDeparts(sysUser.getId());
    obj.put("departs", departs);
    
    // 处理多部门情况
    if (departs == null || departs.size() == 0) {
        obj.put("multi_depart", 0);
    } else if (departs.size() == 1) {
        sysUserService.updateUserDepart(username, departs.get(0).getOrgCode(),null);
        obj.put("multi_depart", 1);
    } else {
        SysUser sysUserById = sysUserService.getById(sysUser.getId());
        if(oConvertUtils.isEmpty(sysUserById.getOrgCode())){
            sysUserService.updateUserDepart(username, departs.get(0).getOrgCode(),null);
        }
        obj.put("multi_depart", 2);
    }
    
    // 转换为 VO 对象
    SysUserVO sysUserVO = new SysUserVO();
    BeanUtils.copyProperties(sysUser,sysUserVO);
    sysUserVO.setUserRole(iSysUserRoleService.getRoleByUserId(sysUser.getId())
            .stream()
            .collect(Collectors.joining(",")));
    storeIsVisw(sysUserVO);
    obj.put("userInfo", sysUserVO);
    
    // 添加字典项
    obj.put("sysAllDictItems", sysDictService.queryAllDictItems());
    result.setResult(obj);
    result.success("登录成功");
    return result;
}
```

### 3. JWT Token 生成

Token 生成由 `JwtUtil.sign` 方法实现：

```java
public static String sign(String username, String secret) {
    Date date = new Date(System.currentTimeMillis() + EXPIRE_TIME);
    Algorithm algorithm = Algorithm.HMAC256(secret);
    // 附带 username 信息
    return JWT.create().withClaim("username", username).withExpiresAt(date).sign(algorithm);
}
```

### 4. 请求拦截与 Token 验证

所有需要认证的请求都会经过 `JwtFilter` 过滤器：

```java
public class JwtFilter extends BasicHttpAuthenticationFilter {
    @Override
    protected boolean isAccessAllowed(ServletRequest request, ServletResponse response, Object mappedValue) {
        try {
            executeLogin(request, response);
            return true;
        } catch (Exception e) {
            JwtUtil.responseError(response,401,CommonConstant.TOKEN_IS_INVALID_MSG);
            return false;
        }
    }
    
    @Override
    protected boolean executeLogin(ServletRequest request, ServletResponse response) throws Exception {
        HttpServletRequest httpServletRequest = (HttpServletRequest) request;
        String token = httpServletRequest.getHeader(CommonConstant.X_ACCESS_TOKEN);
        
        // 从参数中获取 token
        if (oConvertUtils.isEmpty(token)) {
            token = httpServletRequest.getParameter("token");
        }
        
        JwtToken jwtToken = new JwtToken(token);
        // 提交给 realm 进行登入
        getSubject(request, response).login(jwtToken);
        return true;
    }
}
```

### 5. Token 认证逻辑

Token 认证由 `ShiroRealm.doGetAuthenticationInfo` 方法实现：

```java
@Override
protected AuthenticationInfo doGetAuthenticationInfo(AuthenticationToken auth) throws AuthenticationException {
    String token = (String) auth.getCredentials();
    if (token == null) {
        throw new AuthenticationException("token为空!");
    }
    
    // 校验 token 有效性
    LoginUser loginUser = null;
    try {
        loginUser = this.checkUserTokenIsEffect(token);
    } catch (AuthenticationException e) {
        JwtUtil.responseError(SpringContextUtils.getHttpServletResponse(),401,e.getMessage());
        return null;
    }
    return new SimpleAuthenticationInfo(loginUser, token, getName());
}
```

### 6. Token 有效性检查

```java
public LoginUser checkUserTokenIsEffect(String token) throws AuthenticationException {
    // 解密获得 username
    String username = JwtUtil.getUsername(token);
    if (username == null) {
        throw new AuthenticationException("token非法无效!");
    }
    
    // 查询用户信息
    LoginUser loginUser = TokenUtils.getLoginUser(username, commonApi, redisUtil);
    if (loginUser == null) {
        throw new AuthenticationException("用户不存在!");
    }
    
    // 判断用户状态
    if (loginUser.getStatus() != 1) {
        throw new AuthenticationException("账号已被锁定,请联系管理员!");
    }
    
    // 校验 token 是否超时
    if (!jwtTokenRefresh(token, username, loginUser.getPassword())) {
        throw new AuthenticationException(CommonConstant.TOKEN_IS_INVALID_MSG);
    }
    
    // 校验租户信息
    String userTenantIds = loginUser.getRelTenantIds();
    if(oConvertUtils.isNotEmpty(userTenantIds)){
        String contextTenantId = TenantContext.getTenant();
        // 租户验证逻辑...
    }
    
    return loginUser;
}
```

### 7. Token 刷新机制

```java
public boolean jwtTokenRefresh(String token, String userName, String passWord) {
    String cacheToken = String.valueOf(redisUtil.get(CommonConstant.PREFIX_USER_TOKEN + token));
    if (oConvertUtils.isNotEmpty(cacheToken)) {
        // 校验 token 有效性
        if (!JwtUtil.verify(cacheToken, userName, passWord)) {
            String newAuthorization = JwtUtil.sign(userName, passWord);
            // 设置超时时间
            redisUtil.set(CommonConstant.PREFIX_USER_TOKEN + token, newAuthorization);
            redisUtil.expire(CommonConstant.PREFIX_USER_TOKEN + token, JwtUtil.EXPIRE_TIME *2 / 1000);
        }
        return true;
    }
    return false;
}
```

## 三、鉴权流程详解

### 1. 权限信息获取

用户权限信息由 `ShiroRealm.doGetAuthorizationInfo` 方法获取：

```java
@Override
protected AuthorizationInfo doGetAuthorizationInfo(PrincipalCollection principals) {
    String username = null;
    if (principals != null) {
        LoginUser sysUser = (LoginUser) principals.getPrimaryPrincipal();
        username = sysUser.getUsername();
    }
    SimpleAuthorizationInfo info = new SimpleAuthorizationInfo();
    
    // 设置用户角色集合
    Set<String> roleSet = commonApi.queryUserRoles(username);
    info.setRoles(roleSet);
    
    // 设置用户权限集合
    Set<String> permissionSet = commonApi.queryUserAuths(username);
    info.addStringPermissions(permissionSet);
    
    return info;
}
```

### 2. URL 拦截配置

在 `ShiroConfig` 中配置 URL 拦截规则：

```java
@Bean("shiroFilterFactoryBean")
public ShiroFilterFactoryBean shiroFilter(SecurityManager securityManager) {
    CustomShiroFilterFactoryBean shiroFilterFactoryBean = new CustomShiroFilterFactoryBean();
    shiroFilterFactoryBean.setSecurityManager(securityManager);
    
    // 拦截器
    Map<String, String> filterChainDefinitionMap = new LinkedHashMap<String, String>();
    
    // 配置不会被拦截的链接
    filterChainDefinitionMap.put("/sys/login", "anon"); // 登录接口排除
    filterChainDefinitionMap.put("/sys/logout", "anon"); // 登出接口排除
    filterChainDefinitionMap.put("/sys/randomImage/**", "anon"); // 验证码接口排除
    
    // 静态资源排除
    filterChainDefinitionMap.put("/**/*.js", "anon");
    filterChainDefinitionMap.put("/**/*.css", "anon");
    filterChainDefinitionMap.put("/**/*.html", "anon");
    
    // 配置 JWT 过滤器
    Map<String, Filter> filterMap = new HashMap<>(1);
    filterMap.put("jwt", new JwtFilter(allowOrigin));
    shiroFilterFactoryBean.setFilters(filterMap);
    
    // 其他未配置的请求都需要经过 JWT 过滤器
    filterChainDefinitionMap.put("/**", "jwt");
    
    shiroFilterFactoryBean.setFilterChainDefinitionMap(filterChainDefinitionMap);
    return shiroFilterFactoryBean;
}
```

### 3. 权限注解支持

```java
@Bean
@DependsOn("lifecycleBeanPostProcessor")
public DefaultAdvisorAutoProxyCreator defaultAdvisorAutoProxyCreator() {
    DefaultAdvisorAutoProxyCreator defaultAdvisorAutoProxyCreator = new DefaultAdvisorAutoProxyCreator();
    defaultAdvisorAutoProxyCreator.setProxyTargetClass(true);
    defaultAdvisorAutoProxyCreator.setUsePrefix(true);
    defaultAdvisorAutoProxyCreator.setAdvisorBeanNamePrefix("_no_advisor");
    return defaultAdvisorAutoProxyCreator;
}

@Bean
public AuthorizationAttributeSourceAdvisor authorizationAttributeSourceAdvisor(DefaultWebSecurityManager securityManager) {
    AuthorizationAttributeSourceAdvisor advisor = new AuthorizationAttributeSourceAdvisor();
    advisor.setSecurityManager(securityManager);
    return advisor;
}
```

## 四、用户信息获取

在需要获取当前登录用户信息的地方，使用以下代码：

```java
LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
```

这段代码的工作原理：
1. `SecurityUtils.getSubject()` 获取当前会话的 Subject 对象
2. `getPrincipal()` 获取认证主体，即登录成功后存储的用户对象
3. 将结果转换为 `LoginUser` 类型，包含用户 ID、用户名、真实姓名等信息

## 五、缓存机制

### 1. Redis 缓存配置

```java
public RedisCacheManager redisCacheManager() {
    RedisCacheManager redisCacheManager = new RedisCacheManager();
    redisCacheManager.setRedisManager(redisManager());
    // redis 中针对不同用户缓存
    redisCacheManager.setPrincipalIdFieldName("id");
    // 用户权限信息缓存时间
    redisCacheManager.setExpire(200000);
    return redisCacheManager;
}

@Bean
public IRedisManager redisManager() {
    IRedisManager manager;
    // redis 单机支持
    if (lettuceConnectionFactory.getClusterConfiguration() == null || lettuceConnectionFactory.getClusterConfiguration().getClusterNodes().isEmpty()) {
        RedisManager redisManager = new RedisManager();
        redisManager.setHost(lettuceConnectionFactory.getHostName() + ":" + lettuceConnectionFactory.getPort());
        redisManager.setDatabase(lettuceConnectionFactory.getDatabase());
        redisManager.setTimeout(0);
        if (!StringUtils.isEmpty(lettuceConnectionFactory.getPassword())) {
            redisManager.setPassword(lettuceConnectionFactory.getPassword());
        }
        manager = redisManager;
    } else {
        // redis 集群支持
        RedisClusterManager redisManager = new RedisClusterManager();
        // 集群配置...
        manager = redisManager;
    }
    return manager;
}
```

### 2. Token 缓存

Token 以 `CommonConstant.PREFIX_USER_TOKEN + token` 为 key 存储在 Redis 中，缓存有效期为 JWT 过期时间的 2 倍，实现用户在线操作不掉线。

## 六、多租户支持

系统支持多租户模式：
1. 用户登录时，会设置当前租户信息
2. 请求处理时，通过 `TenantContext` 存储当前租户 ID
3. 在权限校验时，会验证用户是否有权访问当前租户的数据

```java
// 在 JwtFilter 中设置租户上下文
String tenantId = httpServletRequest.getHeader(CommonConstant.TENANT_ID);
TenantContext.setTenant(tenantId);

// 在请求结束后清理
@Override
public void afterCompletion(ServletRequest request, ServletResponse response, Exception exception) throws Exception {
    TenantContext.clear();
}
```

## 七、总结

本项目管理后台的认证与鉴权机制基于 Apache Shiro 框架实现，结合 JWT 和 Redis 缓存，提供了完整的用户认证、权限控制和会话管理功能。系统通过 `LoginUser` 对象存储用户信息，通过 `SecurityUtils.getSubject().getPrincipal()` 方法在各个接口中获取当前登录用户信息，实现了安全、高效的权限管理。

主要特点：
1. 无状态认证：使用 JWT 实现无状态认证，减轻服务器存储压力
2. Token 自动刷新：实现用户在线操作不掉线功能
3. 灵活的权限控制：支持基于角色和权限的访问控制
4. 多租户支持：完整的多租户隔离机制
5. 缓存优化：使用 Redis 缓存用户权限信息，提高性能