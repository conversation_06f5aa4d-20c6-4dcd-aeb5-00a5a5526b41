# 订单流程完整图解

## 一、订单提交与支付整体流程

```mermaid
flowchart TD
    A[用户提交订单] --> B[系统处理订单信息]
    B --> C[生成待支付订单]
    C --> D[用户完成支付]
    D --> E[系统接收支付回调]
    E --> F[更新订单状态]
    F --> G[处理佣金和资金分配]
    G --> H[订单完成]
```

## 二、订单提交详细流程

```mermaid
flowchart TD
    A[用户提交订单] --> B[AfterOrderController.submitOrder]
    B --> C[获取会员和收货地址信息]
    C --> D{订单类型?}
    D -->|店铺订单| E[OrderStoreListServiceImpl.submitOrderStoreGoods]
    D -->|平台订单| F[OrderListServiceImpl.submitOrderGoods]
    E --> G[创建店铺订单主表记录]
    G --> H[创建店铺订单子表记录]
    H --> I[计算运费]
    I --> J[处理订单商品]
    J --> K[计算订单金额]
    K --> L[计算分享折扣]
    L --> M[计算实付款]
    M --> N[处理佣金逻辑]
    N --> O[计算店铺实际到账金额]
    O --> P[保存订单信息]
    P --> Q[返回订单信息]
    F --> R[创建平台订单记录]
    R --> S[处理平台订单商品]
    S --> T[计算平台订单金额]
    T --> U[保存平台订单信息]
    U --> V[返回平台订单信息]
    Q --> W[生成支付参数]
    V --> W
    W --> X[返回支付信息]
```

## 三、订单支付回调处理流程

```mermaid
flowchart TD
    A[支付完成] --> B[支付平台回调]
    B --> C[FrontPayController.payCallback]
    C --> D[解析支付结果]
    D --> E{订单类型?}
    E -->|店铺订单| F[OrderStoreListServiceImpl.paySuccessOrder]
    E -->|平台订单| G[OrderListServiceImpl.paySuccessOrder]
    F --> H[更新店铺订单状态]
    H --> I[设置支付方式和金额]
    I --> J[记录支付时间和流水号]
    J --> K[处理分销佣金记录]
    K --> L[处理店铺到账金额]
    L --> M[返回处理结果]
    G --> N[更新平台订单状态]
    N --> O[设置支付方式和金额]
    O --> P[记录支付时间和流水号]
    P --> Q[处理分销佣金记录]
    Q --> R[返回处理结果]
```

## 四、订单金额计算流程

```mermaid
flowchart TD
    A[开始计算订单金额] --> B[计算商品总价]
    B --> C[计算运费]
    C --> D[计算分享折扣]
    D --> E[计算应付款]
    E --> F[计算实付款]
    F --> G[计算商品利润]
    G --> H[计算分销佣金]
    H --> I[计算净利润]
    I --> J[计算平台佣金]
    J --> K[计算企业家分佣]
    K --> L[计算店铺实际到账金额]
```

## 五、佣金计算流程

### 5.1 分享佣金计算流程

```mermaid
flowchart TD
    A[开始计算分享佣金] --> B{有推广人和商品?}
    B -->|否| C[返回零佣金]
    B -->|是| D[遍历订单商品]
    D --> E[计算商品分享折扣]
    E --> F[获取分佣比例]
    F --> G[计算商品佣金]
    G --> H[创建分销记录]
    H --> I[更新商品记录]
    I --> J[计算总佣金]
    J --> K[创建会员充值记录]
    K --> L[返回总佣金]
```

### 5.2 企业家分佣计算流程

```mermaid
flowchart TD
    A[计算店铺实际到账金额] --> B[计算平台佣金]
    B --> C[计算初始到账金额]
    C --> D[获取企业家分佣设置]
    D --> E{有分佣设置?}
    E -->|否| F[不计算企业家分佣]
    E -->|是| G[遍历分佣设置]
    G --> H[计算企业家分佣金额]
    H --> I[创建分佣记录]
    I --> J[更新当前可分配金额]
    J --> K[计算企业家分佣总额]
    K --> L[更新最终到账金额]
```

## 六、佣金到账流程

```mermaid
flowchart TD
    A[订单支付成功] --> B[创建佣金记录]
    B --> C[佣金进入冻结状态]
    C --> D[订单确认收货]
    D --> E[佣金从冻结状态转为可用余额]
    E --> F[生成资金流水记录]
```

## 七、订单状态流转图

```mermaid
stateDiagram-v2
    [*] --> 待支付: 提交订单
    待支付 --> 待发货: 支付成功
    待支付 --> 已关闭: 超时未支付/手动取消
    待发货 --> 待收货: 商家发货
    待收货 --> 已完成: 确认收货
    已完成 --> [*]

    note right of 待支付
        状态码: 0
        可进行操作: 支付、取消
    end note

    note right of 待发货
        状态码: 1
        可进行操作: 发货
    end note

    note right of 待收货
        状态码: 2
        可进行操作: 确认收货、查看物流
    end note

    note right of 已完成
        状态码: 3
        可进行操作: 评价
    end note

    note right of 已关闭
        状态码: 4
        可进行操作: 删除
    end note
```

## 八、资金流向图（优化后）

```mermaid
flowchart TD
    A[用户支付] --> B[订单实付款]
    B --> C[平台佣金10%]
    B --> D[店铺收入90%]
    D --> E[分销佣金20%]
    D --> F[企业家分佣]
    D --> G[店铺最终收入60%]
    E --> H[一级推广人]
    E --> I[分享折扣10%]
    F --> J[企业家账户]
    I --> K[用户折扣]
```

## 八.1、金额计算逻辑优化说明

### 优化前后对比

**优化前**：
1. 分享折扣基于商品总价计算
2. 分享佣金基于折扣后的金额计算
3. 分享折扣和分享佣金是独立计算的

**优化后**：
1. 分享折扣仍然基于商品总价计算
2. 分享佣金也基于原始商品总价计算
3. 分享折扣是从分享佣金中得来的，实际分佣比例 = 原分佣比例 - 分享折扣比例

### 优化示例

以商品总价100元为例：

**优化前**：
- 分享折扣比例10%，折扣金额 = 100 × 10% = 10元
- 折扣后金额 = 100 - 10 = 90元
- 分享佣金比例30%，佣金金额 = 90 × 30% = 27元
- 平台服务费10%，金额 = 90 × 10% = 9元（基于折扣后金额）
- 商家最终所得 = 90 - 27 - 9 = 54元

**优化后**：
- 分享折扣比例10%，折扣金额 = 100 × 10% = 10元
- 分享佣金原始比例30%，实际分佣比例 = 30% - 10% = 20%
- 分享佣金金额 = 100 × 20% = 20元
- 平台服务费10%，金额 = 100 × 10% = 10元（基于原始商品总价）
- 商家最终所得 = 100 - 10 - 20 - 10 = 60元

## 九、订单数据表关系

```mermaid
erDiagram
    OrderStoreList ||--o{ OrderStoreSubList : "包含"
    OrderStoreSubList ||--o{ OrderStoreGoodRecord : "包含"
    OrderStoreList }|--|| MemberList : "归属"
    OrderStoreList }|--|| StoreManage : "归属"
    OrderStoreGoodRecord }|--|| GoodStoreList : "关联"
    OrderStoreGoodRecord }|--|| GoodStoreSpecification : "关联"
    MemberRechargeRecord }|--|| OrderStoreList : "关联"
    StoreRechargeRecord }|--|| OrderStoreList : "关联"

    OrderStoreList {
        string id PK
        string orderNo "订单号"
        string memberListId "会员ID"
        string sysUserId "店铺ID"
        string status "订单状态"
        decimal goodsTotal "商品总价"
        decimal shipFee "运费"
        decimal customaryDues "应付款"
        decimal actualPayment "实付款"
        decimal shareDiscountAmount "分享折扣金额"
        decimal shareDiscountRatio "分享折扣比例"
        decimal distributionCommission "分销佣金"
        decimal platformCommissionAmount "平台佣金"
        decimal entrepreneurCommissionAmount "企业家分佣"
        decimal actuallyReceivedAmount "店铺实际到账金额"
    }

    OrderStoreSubList {
        string id PK
        string orderStoreListId "店铺订单ID"
        string orderNo "订单号"
        string status "订单状态"
        decimal goodsTotal "商品总价"
        decimal shipFee "运费"
        decimal customaryDues "应付款"
        decimal actualPayment "实付款"
    }

    OrderStoreGoodRecord {
        string id PK
        string orderStoreSubListId "店铺子订单ID"
        string goodStoreListId "商品ID"
        string goodStoreSpecificationId "规格ID"
        decimal price "单价"
        decimal amount "数量"
        decimal total "总价"
        decimal shareCommission "分享佣金"
    }

    MemberRechargeRecord {
        string id PK
        string memberListId "会员ID"
        string tradeNo "交易号"
        string payType "支付类型"
        string tradeStatus "交易状态"
        decimal amount "金额"
    }

    StoreRechargeRecord {
        string id PK
        string storeManageId "店铺ID"
        string tradeNo "交易号"
        string payType "支付类型"
        string tradeStatus "交易状态"
        decimal amount "金额"
    }
```

## 十、关键代码调用关系

```mermaid
sequenceDiagram
    participant User as 用户
    participant Controller as AfterOrderController
    participant OrderService as OrderStoreListService
    participant PayController as FrontPayController

    User->>Controller: 提交订单 (submitOrder)
    Controller->>OrderService: 处理店铺订单 (submitOrderStoreGoods)
    OrderService-->>Controller: 返回订单信息
    Controller-->>User: 返回支付参数

    User->>PayController: 支付完成回调
    PayController->>OrderService: 处理支付结果 (paySuccessOrder)
    OrderService-->>PayController: 返回处理结果
    PayController-->>User: 返回支付结果
```
