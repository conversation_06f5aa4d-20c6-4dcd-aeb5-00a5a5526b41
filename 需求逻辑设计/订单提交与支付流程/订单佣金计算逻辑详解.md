# 订单佣金计算逻辑详解

## 一、佣金体系概述

系统中的佣金体系主要包含三种类型：

1. **分享佣金**：用户通过分享商品链接，当其他用户通过该链接购买商品时，分享者获得的佣金
2. **分享折扣**：通过分享链接购买商品的用户享受的价格折扣
3. **企业家分佣**：店铺收入中分配给企业家的佣金部分

整体佣金分配流程如下：

```mermaid
flowchart TD
    A[订单实付款] --> B[平台佣金10%]
    A --> C[店铺收入90%]
    C --> D[分销佣金]
    C --> E[企业家分佣]
    C --> F[店铺最终收入]
```

## 二、分享佣金计算逻辑

### 2.1 分享佣金计算流程

分享佣金的计算在`processMemberCommission`方法中实现：

```mermaid
flowchart TD
    A[开始计算分享佣金] --> B{有推广人和商品?}
    B -->|否| C[返回零佣金]
    B -->|是| D[遍历订单商品]
    D --> E[计算商品分享折扣]
    E --> F[获取分佣比例]
    F --> G[计算商品佣金]
    G --> H[创建分销记录]
    H --> I[更新商品记录]
    I --> J[计算总佣金]
    J --> K[创建会员充值记录]
    K --> L[返回总佣金]
```

### 2.2 分享佣金计算核心代码（优化后）

```java
// 获取分享折扣比例
BigDecimal shareDiscountRatio = BigDecimal.ZERO;
if (orderStoreList != null && orderStoreList.getShareDiscountRatio() != null &&
    orderStoreList.getShareDiscountRatio().compareTo(BigDecimal.ZERO) > 0) {
    shareDiscountRatio = orderStoreList.getShareDiscountRatio();
}

// 分佣比例
BigDecimal shareCommissionRate = goodStoreList.getShareCommissionRate();
if (shareCommissionRate == null || CompareUtil.compare(shareCommissionRate, BigDecimal.ZERO) <= 0) {
    if (marketingStoreDistributionSetting != null) {
        shareCommissionRate = marketingStoreDistributionSetting.getCommonFirst();
    }
}

// 计算商品的分享折扣金额 - 基于原始商品总价
BigDecimal itemShareDiscountAmount = BigDecimal.ZERO;
if (shareDiscountRatio.compareTo(BigDecimal.ZERO) > 0) {
    itemShareDiscountAmount = total.multiply(shareDiscountRatio)
        .divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP);
}

// 计算实际分佣比例 - 分享折扣是从分享佣金中得来的
BigDecimal actualCommissionRate = shareCommissionRate;
if (shareCommissionRate != null && shareDiscountRatio.compareTo(BigDecimal.ZERO) > 0) {
    // 实际分佣比例 = 原分佣比例 - 分享折扣比例
    actualCommissionRate = shareCommissionRate.subtract(shareDiscountRatio);
    // 确保分佣比例不小于0
    if (actualCommissionRate.compareTo(BigDecimal.ZERO) < 0) {
        actualCommissionRate = BigDecimal.ZERO;
    }
}

// 计算一级推广佣金 - 基于原始商品总价
if (actualCommissionRate != null && CompareUtil.compare(actualCommissionRate, BigDecimal.ZERO) > 0) {
    MemberDistributionRecord memberDistributionRecord = new MemberDistributionRecord()
        // ...
        .setCommission(NumberUtil.mul(total, actualCommissionRate).divide(new BigDecimal(100), 2, RoundingMode.DOWN));
}
```

### 2.3 分享佣金计算要点（优化后）

1. **佣金计算基数**：原始商品总价（未减去分享折扣）
2. **分享折扣与分佣关系**：分享折扣是从分享佣金中得来的，实际分佣比例 = 原分佣比例 - 分享折扣比例
3. **佣金比例来源**：
   - 优先使用商品设置的分享佣金比例（`goodStoreList.getShareCommissionRate()`）
   - 如果商品未设置，则使用分销设置中的默认比例（`marketingStoreDistributionSetting.getCommonFirst()`）
4. **佣金计算公式**：原始商品总价 × 实际分佣比例 ÷ 100（因为比例以百分比存储）
5. **佣金精度**：保留2位小数，向下取整

## 三、分享折扣计算逻辑

### 3.1 分享折扣计算流程

分享折扣在订单提交时计算，基于商品总价：

```mermaid
flowchart TD
    A[获取分享折扣比例] --> B{折扣比例>0?}
    B -->|否| C[不计算折扣]
    B -->|是| D[计算折扣金额]
    D --> E[设置订单折扣字段]
    E --> F[从应付款中扣除折扣]
    F --> G[从实付款中扣除折扣]
```

### 3.2 分享折扣计算核心代码

```java
// 计算分享折扣金额
if (shareDiscountRatio != null && shareDiscountRatio.compareTo(BigDecimal.ZERO) > 0) {
    // 计算折扣金额
    shareDiscountAmount = totalPrice.multiply(shareDiscountRatio).divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP);
}

// 设置分享折扣相关字段
orderStoreList.setShareDiscountAmount(shareDiscountAmount);
orderStoreList.setShareDiscountRatio(shareDiscountRatio);

// 应付款 (减去分享折扣)
orderStoreList.setCustomaryDues(totalPrice.subtract(ObjectUtil.defaultIfNull(orderStoreList.getCoupon(), new BigDecimal("0"))).subtract(shareDiscountAmount));
// 实付款 (减去分享折扣)
orderStoreList.setActualPayment(totalPrice.subtract(ObjectUtil.defaultIfNull(orderStoreList.getCoupon(), new BigDecimal("0"))).subtract(shareDiscountAmount));
```

### 3.3 分享折扣计算要点

1. **折扣计算基数**：商品总价（未包含运费）
2. **折扣比例来源**：通过请求头传入的`shareDiscountRatio`参数
3. **折扣计算公式**：商品总价 × 折扣比例 ÷ 100
4. **折扣精度**：保留2位小数，四舍五入
5. **折扣应用**：从应付款和实付款中直接扣除折扣金额

## 四、企业家分佣计算逻辑

### 4.1 企业家分佣计算流程

企业家分佣在`processStoreActuallyReceivedAmount`方法中实现：

```mermaid
flowchart TD
    A[计算店铺实际到账金额] --> B[计算平台佣金]
    B --> C[计算初始到账金额]
    C --> D[获取企业家分佣设置]
    D --> E{有分佣设置?}
    E -->|否| F[不计算企业家分佣]
    E -->|是| G[遍历分佣设置]
    G --> H[计算企业家分佣金额]
    H --> I[创建分佣记录]
    I --> J[更新当前可分配金额]
    J --> K[计算企业家分佣总额]
    K --> L[更新最终到账金额]
```

### 4.2 企业家分佣计算核心代码

```java
// 计算平台佣金金额 = 商品总价 * 10%
BigDecimal platformCommissionAmount = NumberUtil.mul(orderStoreList.getGoodsTotal(), new BigDecimal("0.1"))
        .setScale(2, RoundingMode.DOWN);

// 设置平台佣金金额字段
orderStoreList.setPlatformCommissionAmount(platformCommissionAmount);

// 实际到账金额 = 商品总价 - 平台佣金 - 分销佣金 - 分享折扣
// 注意：分销佣金已经在processMemberCommission方法中考虑了分享折扣
BigDecimal actuallyReceivedAmount = orderStoreList.getGoodsTotal()
        .subtract(platformCommissionAmount)
        .subtract(actualBrokerage)
        .subtract(orderStoreList.getShareDiscountAmount())
        .setScale(2, RoundingMode.DOWN);

// 店铺最终实际到账金额
BigDecimal lasteActuallyReceivedAmount = actuallyReceivedAmount;

// 店铺实际到账金额还需给企业家分佣 - 分账设置
List<StoreCashierRouting> storeCashierRoutings = iStoreCashierRoutingService.list(new LambdaQueryWrapper<StoreCashierRouting>()
        .eq(StoreCashierRouting::getStoreManageId, storeManage.getId())
        .eq(StoreCashierRouting::getFashionableType, "1")
        .eq(StoreCashierRouting::getAccountType, "2")
        .orderByDesc(StoreCashierRouting::getCreateTime));

// 企业家分销佣金金额总计
BigDecimal entrepreneurCommissionAmount = BigDecimal.ZERO;

if (!storeCashierRoutings.isEmpty()) {
    // 当前实时到账金额，随着企业家分佣而减少
    BigDecimal currentAmount = lasteActuallyReceivedAmount;

    for (StoreCashierRouting storeCashierRouting : storeCashierRoutings) {
        if (storeCashierRouting.getFashionableWay().equals("0")) {
            // 根据当前实时到账金额计算企业家分佣
            BigDecimal div = currentAmount
                    .multiply(storeCashierRouting.getFashionableProportion().divide(new BigDecimal(100)))
                    .setScale(2, RoundingMode.DOWN);

            if (div.doubleValue() <= 0) {
                continue;
            }

            // 更新企业家分佣总额和当前可分配金额
            entrepreneurCommissionAmount = entrepreneurCommissionAmount.add(div);
            currentAmount = currentAmount.subtract(div);

            // 创建企业家分佣记录
            // ...
        }
    }

    // 更新最终到账金额
    lasteActuallyReceivedAmount = currentAmount;
}
```

### 4.3 企业家分佣计算要点

1. **分佣计算基数**：商品总价的90%（扣除平台佣金）减去分销佣金和分享折扣后的金额
2. **分佣比例来源**：`StoreCashierRouting`表中的设置
   - `fashionableType="1"`：代言人类型
   - `accountType="2"`：账户类型为分佣
   - `fashionableWay="0"`：按比例分佣
3. **分佣计算公式**：当前可分配金额 × 分佣比例 ÷ 100
4. **分佣精度**：保留2位小数，向下取整
5. **分佣特点**：
   - 分佣是链式计算的，每次分佣后，可分配金额会减少
   - 按创建时间倒序处理，确保最新的设置优先生效

## 五、佣金到账流程

### 5.1 佣金冻结与解冻

佣金计算后不会立即到账，而是经历以下流程：

```mermaid
flowchart TD
    A[订单支付成功] --> B[创建佣金记录]
    B --> C[佣金进入冻结状态]
    C --> D[订单确认收货]
    D --> E[佣金从冻结状态转为可用余额]
    E --> F[生成资金流水记录]
```

### 5.2 佣金到账核心代码

1. **支付成功时冻结佣金**：
   ```java
   // 分配分销佣金
   iMemberListService.saveOrUpdate(memberList
           .setAccountFrozen(memberList.getAccountFrozen().add(mrrs.getAmount())));
   ```

2. **确认收货后解冻佣金**：
   ```java
   // 将分销冻结金额转为金额
   iMemberListService.saveOrUpdate(memberList
           .setAccountFrozen(memberList.getAccountFrozen().subtract(mrrs.getAmount()))
           .setBalance(memberList.getBalance().add(mrrs.getAmount())));
   ```

## 六、金额计算逻辑优化说明

### 6.1 优化前后对比

**优化前**：
1. 分享折扣基于商品总价计算
2. 分享佣金基于折扣后的金额计算
3. 分享折扣和分享佣金是独立计算的

**优化后**：
1. 分享折扣仍然基于商品总价计算
2. 分享佣金也基于原始商品总价计算
3. 分享折扣是从分享佣金中得来的，实际分佣比例 = 原分佣比例 - 分享折扣比例

### 6.2 优化示例

以商品总价100元为例：

**优化前**：
- 分享折扣比例10%，折扣金额 = 100 × 10% = 10元
- 折扣后金额 = 100 - 10 = 90元
- 分享佣金比例30%，佣金金额 = 90 × 30% = 27元
- 平台服务费10%，金额 = 90 × 10% = 9元（基于折扣后金额）
- 商家最终所得 = 90 - 27 - 9 = 54元

**优化后**：
- 分享折扣比例10%，折扣金额 = 100 × 10% = 10元
- 分享佣金原始比例30%，实际分佣比例 = 30% - 10% = 20%
- 分享佣金金额 = 100 × 20% = 20元
- 平台服务费10%，金额 = 100 × 10% = 10元（基于原始商品总价）
- 商家最终所得 = 100 - 10 - 20 - 10 = 60元

### 6.3 优化总结

订单佣金计算是电商系统中的重要组成部分，涉及多种佣金类型和复杂的计算逻辑：

1. **分享佣金**：基于原始商品总价，按实际分佣比例（原分佣比例减去分享折扣比例）计算
2. **分享折扣**：基于原始商品总价，按分享折扣比例计算，从分享佣金中得来
3. **企业家分佣**：基于店铺实际到账金额，按企业家分佣设置计算，逻辑保持不变

优化后的金额计算逻辑更加清晰，分享折扣与分享佣金的关系更加明确，同时保持了原有的方法调用逻辑不变。整个佣金体系设计合理，能够有效支持多级分销和企业家分佣的业务需求，同时通过佣金冻结机制保障了交易的安全性。
