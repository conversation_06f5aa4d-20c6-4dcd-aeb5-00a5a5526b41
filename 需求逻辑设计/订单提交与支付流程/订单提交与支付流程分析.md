# 订单提交与支付流程分析

## 一、整体流程概述

订单提交与支付流程主要包含以下几个关键步骤：

1. 用户提交订单（`/submitOrder`接口）
2. 系统处理订单信息并生成待支付订单（`submitOrderStoreGoods`方法）
3. 用户完成支付
4. 系统接收支付回调并处理订单状态（`paySuccessOrder`方法）

整体流程图如下：

```mermaid
flowchart TD
    A[用户提交订单] --> B[系统处理订单信息]
    B --> C[生成待支付订单]
    C --> D[用户完成支付]
    D --> E[系统接收支付回调]
    E --> F[更新订单状态]
    F --> G[处理佣金和资金分配]
    G --> H[订单完成]
```

## 二、订单提交流程详解

### 2.1 入口点

订单提交的入口点是`AfterOrderController`中的`submitOrder`方法：

```java
@RequestMapping("submitOrder")
@ResponseBody
public Result<Map<String, Object>> submitOrder(String ids,
                                               String memberShippingAddressId,
                                               String orderJson,
                                               @RequestHeader(defaultValue = "") String sysUserId,
                                               @RequestHeader(defaultValue = "") String longitude,
                                               @RequestHeader(defaultValue = "") String latitude,
                                               @RequestHeader(name = "softModel", required = false, defaultValue = "") String softModel,
                                               @RequestAttribute(value = "memberId", required = false) String memberId,
                                               @RequestHeader(value = "tMemberId", required = false) String tMemberId,
                                               @RequestHeader(value = "shareDiscountRatio", required = false, defaultValue = "0") BigDecimal shareDiscountRatio,
                                               HttpServletRequest request)
```

该方法接收多个参数，包括：
- `ids`：购物车商品ID列表
- `memberShippingAddressId`：收货地址ID
- `orderJson`：订单JSON数据
- `memberId`：会员ID
- `tMemberId`：推广人会员ID
- `shareDiscountRatio`：分享折扣比例

### 2.2 订单处理核心逻辑

订单提交后，系统会调用`OrderStoreListServiceImpl`中的`submitOrderStoreGoods`方法处理店铺订单：

```java
OrderStoreList orderStoreList = iOrderStoreListService.submitOrderStoreGoods(s, memberId, orderJson, memberShippingAddress, longitude, latitude, tMemberId);
```

### 2.3 订单创建详细流程

`submitOrderStoreGoods`方法的主要处理流程如下：

```mermaid
flowchart TD
    A[获取会员和店铺信息] --> B[创建订单主表记录]
    B --> C[创建订单子表记录]
    C --> D[计算运费]
    D --> E[处理订单商品]
    E --> F[计算订单金额]
    F --> G[计算分享折扣]
    G --> H[计算实付款]
    H --> I[处理佣金逻辑]
    I --> J[计算店铺实际到账金额]
    J --> K[保存订单信息]
```

## 三、金额计算逻辑

### 3.1 基础金额计算

在`submitOrderStoreGoods`方法中，系统会计算多种金额：

1. **商品总价计算**：
   ```java
   // 遍历店铺商品，计算总价
   for (Map<String, Object> m : myStoreGoods) {
       // 计算单个商品总价
       BigDecimal total = NumberUtil.mul((BigDecimal) m.get("price"), ((BigDecimal) m.get("quantity")));
       // 累加到总价
       totalPrice = totalPrice.add(orderStoreGoodRecord.getTotal());
   }
   ```

2. **运费计算**：
   ```java
   // 根据配送方式计算运费
   if (StrUtil.equals(distribution, "0")) {
       // 快递配送
       freight = iStoreTemplateService.calculateFreight(myStoreGoods, memberShippingAddress.getSysAreaId());
   } else if (StrUtil.equals(distribution, "2")) {
       // 同城配送
       freight = ObjectUtil.defaultIfNull(jsonGoods.getBigDecimal("freight"), BigDecimal.ZERO);
   }
   ```

3. **应付款计算**：
   ```java
   // 应付款 (减去分享折扣)
   orderStoreList.setCustomaryDues(totalPrice.subtract(ObjectUtil.defaultIfNull(orderStoreList.getCoupon(), new BigDecimal("0"))).subtract(shareDiscountAmount));
   // 加上运费
   orderStoreList.setCustomaryDues(orderStoreList.getCustomaryDues().add(freight));
   ```

4. **实付款计算**：
   ```java
   // 实付款 (减去分享折扣)
   orderStoreList.setActualPayment(totalPrice.subtract(ObjectUtil.defaultIfNull(orderStoreList.getCoupon(), new BigDecimal("0"))).subtract(shareDiscountAmount));
   // 加上运费
   orderStoreList.setActualPayment(orderStoreList.getActualPayment().add(freight));
   ```

### 3.2 分享折扣计算

分享折扣是基于商品总价计算的：

```java
// 计算分享折扣金额
if (shareDiscountRatio != null && shareDiscountRatio.compareTo(BigDecimal.ZERO) > 0) {
    // 计算折扣金额
    shareDiscountAmount = totalPrice.multiply(shareDiscountRatio).divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP);
}

// 设置分享折扣相关字段
orderStoreList.setShareDiscountAmount(shareDiscountAmount);
orderStoreList.setShareDiscountRatio(shareDiscountRatio);
```

### 3.3 利润计算

```java
// 商品利润 = 实付款 - 成本价
orderStoreList.setProfit(orderStoreList.getActualPayment().subtract(costPrice));

// 净利润 = 商品利润 - 分销佣金
orderStoreList.setRetainedProfits(orderStoreList.getProfit().subtract(actualBrokerage));
```

## 四、佣金计算逻辑

### 4.1 分销佣金计算

分销佣金计算在`processMemberCommission`方法中实现：

```mermaid
flowchart TD
    A[获取订单和推广人信息] --> B{有推广人和商品?}
    B -->|否| C[返回零佣金]
    B -->|是| D[遍历订单商品]
    D --> E[计算商品分享折扣]
    E --> F[获取分佣比例]
    F --> G[计算商品佣金]
    G --> H[创建分销记录]
    H --> I[更新商品记录]
    I --> J[计算总佣金]
    J --> K[创建会员充值记录]
    K --> L[返回总佣金]
```

核心计算逻辑（优化后）：

```java
// 获取分享折扣比例
BigDecimal shareDiscountRatio = BigDecimal.ZERO;
if (orderStoreList != null && orderStoreList.getShareDiscountRatio() != null &&
    orderStoreList.getShareDiscountRatio().compareTo(BigDecimal.ZERO) > 0) {
    shareDiscountRatio = orderStoreList.getShareDiscountRatio();
}

// 分佣比例
BigDecimal shareCommissionRate = goodStoreList.getShareCommissionRate();
if (shareCommissionRate == null || CompareUtil.compare(shareCommissionRate, BigDecimal.ZERO) <= 0) {
    if (marketingStoreDistributionSetting != null) {
        shareCommissionRate = marketingStoreDistributionSetting.getCommonFirst();
    }
}

// 计算商品的分享折扣金额 - 基于原始商品总价
BigDecimal itemShareDiscountAmount = BigDecimal.ZERO;
if (shareDiscountRatio.compareTo(BigDecimal.ZERO) > 0) {
    itemShareDiscountAmount = total.multiply(shareDiscountRatio)
        .divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP);
}

// 计算实际分佣比例 - 分享折扣是从分享佣金中得来的
BigDecimal actualCommissionRate = shareCommissionRate;
if (shareCommissionRate != null && shareDiscountRatio.compareTo(BigDecimal.ZERO) > 0) {
    // 实际分佣比例 = 原分佣比例 - 分享折扣比例
    actualCommissionRate = shareCommissionRate.subtract(shareDiscountRatio);
    // 确保分佣比例不小于0
    if (actualCommissionRate.compareTo(BigDecimal.ZERO) < 0) {
        actualCommissionRate = BigDecimal.ZERO;
    }
}

// 计算一级推广佣金 - 基于原始商品总价
if (actualCommissionRate != null && CompareUtil.compare(actualCommissionRate, BigDecimal.ZERO) > 0) {
    MemberDistributionRecord memberDistributionRecord = new MemberDistributionRecord()
        // ...
        .setCommission(NumberUtil.mul(total, actualCommissionRate).divide(new BigDecimal(100), 2, RoundingMode.DOWN));
}
```

### 4.2 企业家分佣计算

企业家分佣在`processStoreActuallyReceivedAmount`方法中实现：

```java
// 店铺实际到账金额还需给企业家分佣 - 分账设置
List<StoreCashierRouting> storeCashierRoutings = iStoreCashierRoutingService.list(new LambdaQueryWrapper<StoreCashierRouting>()
        .eq(StoreCashierRouting::getStoreManageId, storeManage.getId())
        .eq(StoreCashierRouting::getFashionableType, "1")
        .eq(StoreCashierRouting::getAccountType, "2")
        .orderByDesc(StoreCashierRouting::getCreateTime));

// 企业家分销佣金金额总计
BigDecimal entrepreneurCommissionAmount = BigDecimal.ZERO;

if (!storeCashierRoutings.isEmpty()) {
    // 当前实时到账金额，随着企业家分佣而减少
    BigDecimal currentAmount = lasteActuallyReceivedAmount;

    for (StoreCashierRouting storeCashierRouting : storeCashierRoutings) {
        if (storeCashierRouting.getFashionableWay().equals("0")) {
            // 根据当前实时到账金额计算企业家分佣
            BigDecimal div = currentAmount
                    .multiply(storeCashierRouting.getFashionableProportion().divide(new BigDecimal(100)))
                    .setScale(2, RoundingMode.DOWN);

            // 更新企业家分佣总额和当前可分配金额
            entrepreneurCommissionAmount = entrepreneurCommissionAmount.add(div);
            currentAmount = currentAmount.subtract(div);

            // 创建企业家分佣记录
            // ...
        }
    }

    // 更新最终到账金额
    lasteActuallyReceivedAmount = currentAmount;
}
```

## 五、支付成功回调处理

### 5.1 支付回调入口

支付成功后，系统会调用`paySuccessOrder`方法处理订单状态：

```java
@Override
@Transactional
public Boolean paySuccessOrder(String id, PayOrderCarLog payOrderCarLog) {
    // 获取订单信息
    OrderStoreList orderStoreList = this.getById(id);

    // 更新订单状态和支付信息
    // ...

    // 处理分销佣金
    // ...

    // 处理店铺到账金额
    // ...

    return true;
}
```

### 5.2 支付回调处理流程

```mermaid
flowchart TD
    A[接收支付回调] --> B[获取订单信息]
    B --> C[更新订单状态]
    C --> D[设置支付方式和金额]
    D --> E[记录支付时间和流水号]
    E --> F[处理分销佣金记录]
    F --> G[处理店铺到账金额]
```

### 5.3 资金分配处理

支付成功后，系统会处理多种资金分配：

1. **分销佣金处理**：
   ```java
   // 分销佣金记录处理
   LambdaQueryWrapper<MemberRechargeRecord> memberRechargeRecordLambdaQueryWrapper = new LambdaQueryWrapper<MemberRechargeRecord>()
           .eq(MemberRechargeRecord::getTradeNo, orderStoreList.getOrderNo())
           .eq(MemberRechargeRecord::getDelFlag,"0")
           .eq(MemberRechargeRecord::getGoAndCome, "0")
           .eq(MemberRechargeRecord::getTradeStatus, "0");

   if (iMemberRechargeRecordService.count(memberRechargeRecordLambdaQueryWrapper) > 0) {
       List<MemberRechargeRecord> memberRechargeRecords = iMemberRechargeRecordService.list(memberRechargeRecordLambdaQueryWrapper);
       memberRechargeRecords.forEach(mrrs -> {
           // 修改分销记录状态
           iMemberRechargeRecordService.saveOrUpdate(mrrs.setTradeStatus("2"));
           MemberList memberList = iMemberListService.getById(mrrs.getMemberListId());
           if (StrUtil.equals(mrrs.getPayType(), "3")) {
               // 分配分销佣金
               iMemberListService.saveOrUpdate(memberList
                       .setAccountFrozen(memberList.getAccountFrozen().add(mrrs.getAmount())));
           } else if (StrUtil.equals(mrrs.getPayType(), "49")) {
               // 分配企业家佣金
               iMemberListService.saveOrUpdate(memberList
                       .setAccountFrozen(memberList.getAccountFrozen().add(mrrs.getAmount())));
           }
       });
   }
   ```

2. **店铺到账金额处理**：
   ```java
   // 店铺到账金额明细
   LambdaQueryWrapper<StoreRechargeRecord> storeRechargeRecordLambdaQueryWrapper = new LambdaQueryWrapper<StoreRechargeRecord>()
           .eq(StoreRechargeRecord::getTradeNo, orderStoreList.getOrderNo())
           .eq(StoreRechargeRecord::getTradeType, "0")
           .eq(StoreRechargeRecord::getTradeStatus, "0")
           .eq(StoreRechargeRecord::getPayType, "0");
   iStoreRechargeRecordService.list(storeRechargeRecordLambdaQueryWrapper).forEach(srr -> {
       iStoreRechargeRecordService.saveOrUpdate(srr.setTradeStatus("2"));
       StoreManage storeManage = iStoreManageService.getById(srr.getStoreManageId());
       iStoreManageService.saveOrUpdate(storeManage
               .setAccountFrozen(storeManage.getAccountFrozen().add(srr.getAmount())));
   });
   ```

## 六、金额计算逻辑优化

### 6.1 优化前后对比

**优化前**：
1. 分享折扣基于商品总价计算
2. 分享佣金基于折扣后的金额计算
3. 分享折扣和分享佣金是独立计算的

**优化后**：
1. 分享折扣仍然基于商品总价计算
2. 分享佣金也基于原始商品总价计算
3. 分享折扣是从分享佣金中得来的，实际分佣比例 = 原分佣比例 - 分享折扣比例

### 6.2 优化示例

以商品总价100元为例：

**优化前**：
- 分享折扣比例10%，折扣金额 = 100 × 10% = 10元
- 折扣后金额 = 100 - 10 = 90元
- 分享佣金比例30%，佣金金额 = 90 × 30% = 27元
- 平台服务费10%，金额 = 90 × 10% = 9元（基于折扣后金额）
- 商家最终所得 = 90 - 27 - 9 = 54元

**优化后**：
- 分享折扣比例10%，折扣金额 = 100 × 10% = 10元
- 分享佣金原始比例30%，实际分佣比例 = 30% - 10% = 20%
- 分享佣金金额 = 100 × 20% = 20元
- 平台服务费10%，金额 = 100 × 10% = 10元（基于原始商品总价）
- 商家最终所得 = 100 - 10 - 20 - 10 = 60元

## 七、总结

订单提交与支付流程是电商系统的核心业务逻辑，涉及多个环节和复杂的金额计算。主要特点包括：

1. **多级佣金分配**：系统支持分销佣金和企业家分佣两种佣金分配机制
2. **分享折扣机制**：通过分享获得的订单可享受折扣，折扣金额从商品总价中扣除，且折扣是从分享佣金中得来的
3. **资金冻结机制**：佣金和店铺收入先进入冻结状态，待订单确认后再转为可用余额
4. **多种支付方式**：支持现金支付、余额支付和福利金支付等多种方式

优化后的金额计算逻辑更加清晰，分享折扣与分享佣金的关系更加明确，同时保持了原有的方法调用逻辑不变。整个流程设计合理，各环节职责明确，能够有效支持电商平台的订单处理和资金分配需求。
