# 八闽助业集市会员佣金计算逻辑

## 1. 核心计算逻辑

### 1.1 佣金来源
- **定义**：仅计算直接下级（一级分销）产生的佣金
- **数据来源**：订单表的 `distribution_commission` 字段
- **订单状态**：
  - 只统计状态为 3（已完成）和 5（已收货）的订单
  - 排除已删除的订单（del_flag = 0）

### 1.2 订单类型分类
- **品牌馆订单**：order_type = 15
- **生活馆订单**：order_type = 16
- **创业馆订单**：order_type = 17

### 1.3 佣金计算SQL
```sql
SELECT 
    osl.order_type AS orderType,
    IFNULL(SUM(osl.distribution_commission), 0) AS commission
FROM order_store_list osl
WHERE 
    osl.status IN ('3', '5')  -- 只统计已完成和已收货的订单
    AND osl.del_flag = '0'    -- 排除已删除的订单
    AND osl.promoter = #{memberId}  -- 仅计算直接下级的佣金
    AND (
        DATE_FORMAT(osl.create_time, '%Y-%m-%d') >= #{startDate} OR #{startDate} IS NULL
        AND DATE_FORMAT(osl.create_time, '%Y-%m-%d') <= #{endDate} OR #{endDate} IS NULL
    )
GROUP BY osl.order_type
```