## 任务目标：
1. 分析店铺订单的下单流程，详细梳理订单状态流转过程
2. 分析下单过程中的会员分销佣金、店铺资金、店铺给企业家的佣金等计算逻辑
3. 输出详细的逻辑方案说明文档（markdown格式）
4. 用mermaid绘制关键流程的流程图

## 分析范围：
1. 商品下单接口：@AfterOrderController.java#L1119-1216 和 @OrderStoreListServiceImpl.java#L574-854
2. 订单支付成功回调接口：@OrderStoreListServiceImpl.java#L856-986
3. 取消订单接口：@AfterOrderController.java#L279-330
4. 订单发货接口：@OrderStoreListServiceImpl.java#L1380-1436
5. 订单确认收货接口：@AfterOrderController.java#L230-277

## 数据参考：
1. 数据库结构：@[docs/业务架构文档/数据库设计]目录下的建表语句
2. 业务文档：@[docs]目录下的相关文档

## 特别说明：
1. 仅关注店铺订单，其他类型订单直接忽略
2. 分模块输出，确保每个模块内容完整且易于理解
3. 流程图使用mermaid语法绘制，便于后续维护