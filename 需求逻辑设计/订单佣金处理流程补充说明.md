# 订单佣金处理流程补充说明

## 一、订单创建阶段补充说明

在`OrderStoreListServiceImpl.submitOrderStoreGoods`方法中实现：

1. **会员佣金计算优化**
   - 通过`processMemberCommission`方法处理会员佣金计算，确保佣金计算的准确性和代码可维护性
   - 该方法接收订单号、订单商品列表、推广人信息、分销设置和会员ID等参数
   - 返回计算后的总佣金金额，用于后续处理

2. **店铺实际到账金额计算**
   - 通过`processStoreActuallyReceivedAmount`方法计算店铺实际到账金额
   - 计算公式：实际到账金额 = 实付款 × 90%(扣除平台佣金) - 分销佣金 - 企业家佣金
   - 记录平台佣金金额（`platformCommissionAmount`）：实付款 × 10%
   - 记录企业家分销佣金金额（`entrepreneurCommissionAmount`）：企业家分佣总额

## 二、企业家分佣逻辑详解

企业家分佣是八闽助业集市特有的佣金分配机制，主要在`processStoreActuallyReceivedAmount`方法中实现：

### 2.1 企业家分佣基本流程

1. **分佣设置获取**
   - 通过`StoreCashierRouting`表获取店铺的企业家分佣设置
   - 筛选条件：
     - `fashionableType="1"`：代言人类型
     - `accountType="2"`：账户类型为分佣
   - 按创建时间倒序排列，确保最新的设置优先生效

2. **分佣金额计算**
   - 初始实际到账金额 = 实付款 × 90%(扣除平台佣金) - 分销佣金
   - 企业家分佣基于实时到账金额计算，而非固定的初始金额
   - 每次计算分佣时，都基于当前最新的实时到账金额，确保分佣总额不会超过实际可分配金额

3. **分佣记录创建**
   - 为每个企业家创建`MemberRechargeRecord`记录
   - 设置交易类型为`payType="49"`（代言人佣金）
   - 设置交易状态为`tradeStatus="0"`（未支付）
   - 记录关联的订单号，便于后续追踪

4. **实际到账金额更新**
   - 每次分佣后，更新实时到账金额和最终到账金额
   - 确保最终到账金额准确反映了扣除所有分佣后的实际金额

### 2.2 企业家分佣计算示例

假设一笔订单：
- 实付款：1000元
- 平台佣金：100元（10%）
- 分销佣金：50元
- 初始实际到账金额：1000 × 90% - 50 = 850元

企业家分佣计算：
1. 第一个企业家分佣比例为10%：
   - 分佣金额 = 850 × 10% = 85元
   - 剩余金额 = 850 - 85 = 765元
2. 第二个企业家分佣比例为10%：
   - 分佣金额 = 765 × 10% = 76.5元
   - 剩余金额 = 765 - 76.5 = 688.5元
3. 最终店铺实际到账金额：688.5元
4. 企业家分销佣金总额：85 + 76.5 = 161.5元

### 2.3 企业家分佣数据记录

1. **订单表字段**
   - `platformCommissionAmount`：平台佣金金额
   - `entrepreneurCommissionAmount`：企业家分销佣金金额总计
   - `actuallyReceivedAmount`：店铺最终实际到账金额

2. **企业家分佣记录**
   - 每个企业家的分佣记录存储在`MemberRechargeRecord`表中
   - 通过`tradeNo`关联到原始订单
   - 通过`payType="49"`标识为代言人佣金

3. **店铺到账记录**
   - 在`StoreRechargeRecord`表中创建店铺实际到账金额记录
   - 记录最终到账金额，即扣除所有佣金后的金额

## 三、订单佣金处理优化建议补充

1. **佣金计算实时性**
   - 企业家分佣应基于实时到账金额计算，而非固定的初始金额
   - 这确保了分佣总额不会超过实际可分配金额，避免了资金计算错误

2. **佣金数据透明化**
   - 在订单表中记录平台佣金金额和企业家分销佣金金额
   - 便于后期数据追溯和统计分析，提高系统透明度

3. **日志记录完善**
   - 在企业家分佣过程中记录详细日志
   - 包括分佣前金额、分佣金额和分佣后金额
   - 便于问题排查和数据核对

4. **代码结构优化**
   - 将佣金计算逻辑抽取为独立方法
   - 提高代码可读性和可维护性
   - 便于后期功能扩展和逻辑调整

## 四、订单流程与佣金状态完整图示

```
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│   订单创建阶段   │     │  支付成功回调阶段 │     │   确认收货阶段   │
└────────┬────────┘     └────────┬────────┘     └────────┬────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│ 计算会员分销佣金 │     │ 更新会员佣金状态 │     │ 更新会员佣金状态 │
│ processMember   │     │ tradeStatus="2" │     │ tradeStatus="5" │
│ Commission      │     └────────┬────────┘     └────────┬────────┘
└────────┬────────┘              │                       │
         │                       ▼                       ▼
         ▼              ┌─────────────────┐     ┌─────────────────┐
┌─────────────────┐     │ 添加到冻结账户   │     │ 从冻结账户转移到 │
│ 计算企业家分佣   │     │ accountFrozen   │     │ 可用余额 balance │
│ processStore    │     └─────────────────┘     └────────┬────────┘
│ ActuallyReceived│                                      │
│ Amount          │                                      ▼
└────────┬────────┘                             ┌─────────────────┐
         │                                      │ 更新总佣金统计   │
         ▼                                      │ totalCommission │
┌─────────────────┐                             └────────┬────────┘
│ 创建佣金记录     │                                      │
│ tradeStatus="0" │                                      ▼
└────────┬────────┘                             ┌─────────────────┐
         │                                      │ 生成资金流水记录 │
         └──────────────────────────────────────► ─────────────────┘
```

## 五、数据表字段补充说明

### 5.1 OrderStoreList（订单表）新增字段

1. **平台佣金金额**
   - 字段名：`platform_commission_amount`
   - 类型：decimal(10,2)
   - 说明：记录平台收取的佣金金额，计算公式为实付款的10%

2. **企业家分销佣金金额**
   - 字段名：`entrepreneur_commission_amount`
   - 类型：decimal(10,2)
   - 说明：记录订单中所有企业家分佣的总金额

3. **订单号**
   - 字段名：`order_no`
   - 类型：varchar(50)
   - 说明：关联订单的唯一标识，便于后期数据追溯
