# 用户端/商家端认证逻辑分析

## 1. 认证架构概述

八闽助业集市项目采用了多端认证机制，主要包括：
- 管理后台：使用 Shiro 进行认证与鉴权
- 用户端（小程序、APP等）：使用自定义 JWT Token 认证
- 商家端（小程序、APP等）：使用自定义 JWT Token 认证

本文主要分析用户端和商家端的认证逻辑。

## 2. 认证拦截器实现

系统使用 `JwtInterceptor` 作为自定义拦截器，负责拦截和验证用户端/商家端的请求。该拦截器实现了 Spring 的 `HandlerInterceptor` 接口，在请求处理前进行 Token 验证。

### 2.1 拦截器路径规则

`JwtInterceptor` 根据请求路径前缀区分不同类型的请求：

| 路径前缀 | 端口类型 | 是否需要登录 | 说明 |
| --- | --- | --- | --- |
| `/front/` | 用户端 | 否 | 公开接口，不需要登录即可访问 |
| `/after/` | 用户端 | 是 | 需要登录才能访问的接口 |
| `/back/` | 商家端 | 是 | 商家端接口，需要登录才能访问 |

### 2.2 Token 验证流程

1. **获取请求路径**：从请求中获取请求路径，判断属于哪种类型的接口
2. **获取 Token**：从请求头中获取 `X-AUTH-TOKEN` 参数作为认证 Token
3. **获取软件模型**：从请求头中获取 `softModel` 参数，用于区分不同客户端
4. **路径判断与处理**：
   - 对于 `/front/` 开头的路径：公开接口，不强制要求 Token，有 Token 则验证并设置用户信息
   - 对于 `/after/` 开头的路径：需要 Token，验证失败则返回错误信息
   - 对于 `/back/` 开头的路径：商家端接口，需要 Token，验证失败则返回错误信息

## 3. Token 管理机制

系统使用 `TokenManager` 接口及其实现类 `RedisTokenManager` 管理 Token 的创建、验证和删除。

### 3.1 Token 生成

Token 使用 JWT (JSON Web Token) 技术生成，包含以下信息：
- 用户 ID：作为 Token 的主体
- 签发时间：Token 的创建时间
- 签名：使用 HS256 算法和密钥进行签名

```java
// Token 生成代码
String token = Jwts.builder()
    .setId(memberId)
    .setSubject(memberId)
    .setIssuedAt(new Date())
    .signWith(SignatureAlgorithm.HS256, JwtConstants.JWT_SECRET)
    .compact();
```

### 3.2 Token 存储

生成的 Token 存储在 Redis 中，键名格式为 `{softModel}member={memberId}`，有效期为 168 小时（7 天）。

```java
// Token 存储代码
redisTemplate.boundValueOps(softModel+"member="+memberId)
    .set(token, JwtConstants.TOKEN_EXPIRES_HOUR, TimeUnit.HOURS);
```

### 3.3 Token 验证

Token 验证包括两个步骤：
1. 解析 Token 获取用户 ID
2. 从 Redis 中获取存储的 Token 并与请求中的 Token 比对

```java
// Token 验证代码
String token = (String) redisTemplate.boundValueOps(softModel+"member="+model.getMemberId()).get();
if (token == null || !token.equals(model.getToken())) {
    return false;
}
```

验证成功后，会延长 Token 的过期时间，实现"滑动过期"机制。

## 4. 用户表设计 (member_list)

### 4.1 核心字段说明

用户信息存储在 `member_list` 表中，核心字段包括：

| 字段名 | 类型 | 说明 |
| --- | --- | --- |
| id | varchar(50) | 主键ID |
| phone | varchar(13) | 手机号 |
| nick_name | varchar(100) | 会员昵称 |
| head_portrait | varchar(255) | 头像绝对地址 |
| openid | varchar(50) | 微信的openid |
| union_id | varchar(50) | 微信的unionid |
| session_key | varchar(50) | 微信会话密钥 |
| status | varchar(1) | 状态：0：停用；1：启用 |
| member_type | varchar(1) | 会员类型：0：普通客户；2：销售队长；3：销售队员 |
| promoter_type | varchar(1) | 推广人类型：0：店铺；1：会员；2：平台 |
| promoter | varchar(50) | 推广人ID（店铺或会员ID） |
| unique_id | bigint | 唯一自增长ID，用于拼接层级路径 |
| member_level | int | 会员分销层级，第一层级为1，递增 |
| member_path | text | 会员层级路径 |

### 4.2 设计思路

1. **用户唯一标识**：系统支持多种用户唯一标识方式，通过 `user_unique_identity` 配置项控制
   - 0：手机号 (phone)
   - 1：微信 openid
   - 2：微信 unionid
   - 3：手机号 (phone)

2. **分销关系设计**：
   - 使用 `promoter` 字段存储推荐人ID
   - 使用 `member_level` 记录用户在分销体系中的层级
   - 使用 `member_path` 记录完整的分销路径，便于查询上下级关系

3. **多端登录支持**：
   - 支持微信小程序登录 (openid)
   - 支持微信公众号登录 (gz_openid)
   - 支持APP登录 (app_openid)
   - 支持账号密码登录 (account_number, password)

## 5. 用户登录流程

### 5.1 微信小程序快捷登录流程

微信小程序登录是用户端最常用的登录方式，流程如下：

```mermaid
sequenceDiagram
    participant 用户 as 用户
    participant 小程序 as 微信小程序
    participant 微信服务器 as 微信服务器
    participant 后端 as 后端服务
    participant Redis as Redis

    用户->>小程序: 点击登录
    小程序->>微信服务器: 获取code
    微信服务器-->>小程序: 返回code
    小程序->>微信服务器: 获取用户加密数据
    微信服务器-->>小程序: 返回encryptedData和iv
    小程序->>后端: 请求登录(code,encryptedData,iv)
    后端->>微信服务器: 请求openid和session_key
    微信服务器-->>后端: 返回openid和session_key
    后端->>后端: 解密用户信息
    
    alt 用户不存在
        后端->>后端: 创建新用户
        alt 配置了分销绑定关系场景(被推荐人注册)
            后端->>后端: 设置分销关系
        end
    else 用户已存在
        后端->>后端: 更新用户信息
    end
    
    后端->>Redis: 生成并存储Token
    后端-->>小程序: 返回Token和用户信息
    小程序-->>用户: 登录成功
```

### 5.2 分销关系绑定逻辑

在用户注册过程中，系统会根据配置决定是否建立分销关系：

1. 查询分销设置 `MarketingDistributionSetting`
2. 检查 `distributionBuild` 字段是否包含特定场景值
   - 0：被推荐人注册
   - 其他场景值：被推荐人充值助力值、被推荐人兑换产品等
3. 如果配置了相应场景，则调用 `setLoginRegister` 方法建立分销关系

## 6. 用户端接口规范

### 6.1 请求头参数规范

用户端接口请求头必须包含以下参数：

| 参数名 | 是否必须 | 说明 |
| --- | --- | --- |
| X-AUTH-TOKEN | 条件必须 | 用户认证Token，`/after/` 开头的接口必须提供 |
| softModel | 是 | 客户端类型：0：小程序；1：android；2：ios；3：H5 |
| sysUserId | 否 | 系统用户ID，某些特殊场景需要 |
| tMemberId | 否 | 推荐人ID，用于建立分销关系 |

在业务代码中，通过 `LoginMemberUtil` 工具类获取登录会员ID。

```java
String memberId = LoginMemberUtil.getLoginMemberId();
```

### 6.2 接口路径规范

用户端接口路径遵循以下规范：

| 路径前缀 | 是否需要登录 | 示例 | 说明 |
| --- | --- | --- | --- |
| /front/ | 否 | /front/weixin/loginByCode | 公开接口，不需要登录 |
| /after/ | 是 | /after/member/info | 需要登录的接口 |

### 6.3 响应格式规范

所有接口统一返回 `Result<T>` 格式的JSON数据：

```json
{
  "success": true,
  "message": "操作成功",
  "code": 200,
  "result": {
    // 具体的业务数据
  },
  "timestamp": 1648888888888
}
```

错误响应示例：

```json
{
  "success": false,
  "message": "token为空，请先登录~",
  "code": 666,
  "timestamp": 1648888888888
}
```

## 7. 商家端认证逻辑

商家端认证逻辑与用户端类似，但使用不同的路径前缀 `/back/`。商家端接口必须提供有效的 Token，验证失败会返回错误信息。

商家端 Token 的生成、存储和验证机制与用户端相同，但在拦截器中使用 `sysUserId` 作为身份标识，而不是 `memberId`。

```mermaid
sequenceDiagram
    participant 商家 as 商家
    participant 客户端 as 商家端App/小程序
    participant 后端 as 后端服务
    participant Redis as Redis

    商家->>客户端: 输入账号密码
    客户端->>后端: 请求登录
    后端->>后端: 验证账号密码
    后端->>Redis: 生成并存储Token
    后端-->>客户端: 返回Token和商家信息
    
    客户端->>后端: 请求业务接口(/back/...)
    后端->>后端: JwtInterceptor拦截
    后端->>Redis: 验证Token
    
    alt Token有效
        后端->>后端: 处理业务请求
        后端-->>客户端: 返回业务数据
    else Token无效
        后端-->>客户端: 返回认证错误(code:666)
    end
```

## 8. 安全考虑

1. **Token 有效期**：Token 默认有效期为 168 小时（7天），使用滑动过期机制
2. **请求限流**：系统实现了基于 IP 的请求限流机制，防止恶意请求
3. **密钥安全**：JWT 使用固定密钥 `JWT_SECRET` 进行签名，建议定期更换
4. **用户状态检查**：系统支持检查用户状态 (status)，可以禁用异常账号

## 9. 总结

八闽助业集市项目采用了基于 JWT 的自定义认证机制，通过路径前缀区分不同类型的接口，实现了用户端和商家端的认证逻辑。系统使用 Redis 存储 Token，支持多种登录方式，并实现了分销关系的自动建立。

认证流程清晰，安全机制完善，为多端应用提供了统一的认证解决方案。