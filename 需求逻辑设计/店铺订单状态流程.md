# 店铺订单状态流程分析文档

## 一、订单状态流转过程

1. **下单阶段**
   - 用户提交订单，生成待支付订单（status=0）
   - 调用`submitOrderStoreGoods`方法创建订单记录
   - 设置订单基础信息：订单号、会员信息、收货地址等

2. **支付阶段**
   - 用户完成支付，订单状态更新为已支付（status=1）
   - 调用`paySuccessOrder`方法处理支付成功逻辑
   - 更新订单支付状态和时间

3. **发货阶段**
   - 商家发货，订单状态更新为已发货（status=2）
   - 调用`ordereDlivery`方法处理发货逻辑
   - 更新物流信息和发货时间

4. **确认收货阶段**
   - 用户确认收货，订单状态更新为已完成（status=3）
   - 调用`affirmOrder`方法处理确认收货逻辑
   - 更新订单完成时间和状态

5. **取消订单**
   - 用户或系统取消订单，订单状态更新为已取消（status=4）
   - 调用`abrogateOrder`方法处理取消逻辑
   - 更新订单取消原因和时间

## 二、关键计算逻辑

1. **会员分销佣金计算**
   - 在`processMemberCommission`方法中实现
   - 根据分销设置和商品价格计算佣金
   - 考虑一级推广人的佣金比例

2. **店铺资金计算**
   - 在`processStoreActuallyReceivedAmount`方法中实现
   - 计算店铺实际到账金额
   - 扣除平台佣金和其他费用

3. **店铺额外给会员的佣金**
   - 在`processMemberCommission`方法中实现
   - 根据店铺设置的分佣比例计算
   - 考虑不同会员等级的分佣差异

## 三、详细逻辑方案说明

1. **订单创建流程**
   - 用户提交订单请求
   - 系统验证商品库存、价格等信息
   - 生成唯一订单号
   - 创建订单主表和子表记录
   - 计算订单总金额（包括商品金额、运费等）
   - 更新商品库存
   - 返回订单创建结果

2. **支付流程**
   - 用户发起支付请求
   - 系统验证订单状态
   - 调用支付接口完成支付
   - 更新订单支付状态
   - 记录支付日志
   - 发送支付成功通知

3. **发货流程**
   - 商家确认发货
   - 系统验证订单支付状态
   - 更新订单发货状态
   - 记录物流信息
   - 发送发货通知
   - 更新库存信息

4. **确认收货流程**
   - 用户确认收货
   - 系统验证订单发货状态
   - 更新订单完成状态
   - 计算并分配分销佣金
   - 更新会员积分
   - 发送完成通知

## 四、Mermaid流程图

```mermaid
graph TD
    A[用户提交订单] --> B{库存验证}
    B -- 库存充足 --> C[生成订单]
    B -- 库存不足 --> D[返回库存不足提示]
    C --> E[计算订单金额]
    E --> F[创建订单记录]
    F --> G[返回订单创建结果]
    G --> H[用户支付]
    H --> I{支付成功?}
    I -- 成功 --> J[更新订单状态]
    I -- 失败 --> K[返回支付失败提示]
    J --> L[商家发货]
    L --> M[更新发货状态]
    M --> N[用户确认收货]
    N --> O[更新完成状态]
    O --> P[计算分销佣金]
    P --> Q[更新会员积分]
    Q --> R[发送完成通知]
```

## 五、订单取消流程

1. **用户主动取消**
   - 用户发起取消请求
   - 系统验证订单状态（仅允许取消未支付或已支付未发货订单）
   - 更新订单状态为已取消
   - 释放库存
   - 如果已支付，则发起退款流程
   - 发送取消通知

2. **系统自动取消**
   - 订单超时未支付（通常30分钟）
   - 系统定时任务检测超时订单
   - 自动更新订单状态为已取消
   - 释放库存
   - 发送取消通知

## 六、异常处理机制

1. **支付异常处理**
   - 支付失败：记录失败原因，允许用户重新支付
   - 支付超时：自动取消订单，释放库存
   - 支付金额不符：记录异常，人工介入处理

2. **发货异常处理**
   - 库存不足：通知商家补货，延迟发货
   - 物流异常：更新物流状态，通知用户
   - 发货超时：系统提醒商家，记录超时原因

3. **退款异常处理**
   - 退款失败：记录失败原因，人工介入处理
   - 退款金额异常：记录异常，人工审核
   - 退款超时：系统提醒，升级处理

## 七、Mermaid流程图（取消流程）

```mermaid
graph TD
    A[用户发起取消] --> B{订单状态验证}
    B -- 可取消 --> C[更新订单状态]
    B -- 不可取消 --> D[返回取消失败提示]
    C --> E{是否已支付}
    E -- 是 --> F[发起退款流程]
    E -- 否 --> G[释放库存]
    F --> H[更新退款状态]
    G --> I[发送取消通知]
    H --> I
```

## 八、Mermaid流程图（异常处理）

```mermaid
graph TD
    A[异常发生] --> B{异常类型}
    B -- 支付异常 --> C[记录失败原因]
    B -- 发货异常 --> D[通知商家]
    B -- 退款异常 --> E[人工介入]
    C --> F[允许重新支付]
    D --> G[延迟发货]
    E --> H[人工审核]
    F --> I[流程结束]
    G --> I
    H --> I
```

## 九、分销佣金计算逻辑详解

在店铺订单系统中，分销佣金计算是一个重要的环节，主要在`processMemberCommission`方法中实现。以下是详细的分销佣金计算流程：

### 1. 分销佣金计算前提

- 必须有推广人（一级推广会员）
- 订单商品列表不能为空
- 分销设置必须有效

### 2. 分销佣金计算流程

1. **逐个商品计算分佣**
   - 遍历订单中的每个商品
   - 获取商品的分佣比例（优先使用商品自身的分佣比例，如果没有则使用店铺的通用分佣比例）
   - 根据商品实际支付金额和分佣比例计算佣金金额
   - 创建分销记录并关联到商品

2. **更新商品记录中的分佣信息**
   - 根据订单号和商品信息精确定位商品记录
   - 更新商品记录中的分佣金额

3. **计算总分销佣金**
   - 将所有商品的分佣金额累加得到总分销佣金

4. **创建佣金到账记录**
   - 如果总分销佣金大于0，创建会员充值记录
   - 设置充值记录的相关信息（会员ID、支付类型、金额、订单号等）
   - 保存充值记录

### 3. 分销佣金计算公式

```
商品分佣金额 = 商品实际支付金额 × 分佣比例 ÷ 100
总分销佣金 = 所有商品分佣金额之和
```

### 4. 分销佣金到账时机

分销佣金在用户确认收货后立即到账，在`affirmOrder`方法中处理。具体流程如下：

1. 用户确认收货
2. 系统更新订单状态为已完成
3. 查询分销记录集合
4. 如果存在分销记录，则更新分销会员的佣金信息
5. 分销佣金立即到账，状态更新为已到账

## 十、店铺资金计算逻辑详解

店铺资金计算是在`processStoreActuallyReceivedAmount`方法中实现的，主要计算店铺实际到账金额。以下是详细的计算流程：

### 1. 平台佣金计算

```
平台佣金金额 = 订单实际支付金额 × 10%
```

平台佣金是平台向店铺收取的服务费，固定为订单实际支付金额的10%。

### 2. 店铺实际到账金额计算

```
店铺实际到账金额 = 订单实际支付金额 × 90% - 分销佣金
```

店铺实际到账金额是在扣除平台佣金和分销佣金后的金额。

### 3. 企业家分佣计算

店铺实际到账金额还需要给企业家分佣，计算流程如下：

1. **获取店铺的分账设置**
   - 查询店铺的分账设置记录
   - 按创建时间降序排序，确保使用最新的设置

2. **计算企业家分佣金额**
   - 遍历分账设置记录
   - 根据当前实时到账金额和分佣比例计算企业家分佣金额
   - 创建企业家分佣记录
   - 更新当前实时到账金额和最终到账金额

3. **计算企业家分销佣金总额**
   - 累加所有企业家的分佣金额
   - 更新订单中的企业家分销佣金金额字段

### 4. 创建店铺实际到账金额记录

最终，系统创建店铺实际到账金额记录，包含以下信息：

- 店铺信息
- 支付类型
- 金额（最终到账金额）
- 交易状态
- 订单号
- 备注信息

### 5. 店铺资金到账时机

店铺资金在用户确认收货后立即到账，在`affirmOrder`方法中处理。

## 十一、订单状态机设计

店铺订单系统采用状态机设计模式，清晰地定义了订单的各个状态及其转换关系。

### 1. 订单状态定义

| 状态码 | 状态名称 | 描述 |
| --- | --- | --- |
| 0 | 待支付 | 订单创建后的初始状态 |
| 1 | 已支付 | 用户完成支付后的状态 |
| 2 | 已发货 | 商家完成发货后的状态 |
| 3 | 已完成 | 用户确认收货后的状态 |
| 4 | 已取消 | 订单被取消后的状态 |

### 2. 状态转换规则

```mermaid
stateDiagram-v2
    [初始状态] --> 待支付: 创建订单
    待支付 --> 已支付: 用户支付
    待支付 --> 已取消: 超时未支付/用户取消
    已支付 --> 已发货: 商家发货
    已支付 --> 已取消: 用户取消/商家取消
    已发货 --> 已完成: 用户确认收货/自动确认
```

### 3. 状态转换触发条件

1. **待支付 -> 已支付**
   - 触发条件：用户完成支付
   - 处理方法：`paySuccessOrder`

2. **待支付 -> 已取消**
   - 触发条件：超时未支付（通常30分钟）或用户主动取消
   - 处理方法：`abrogateOrder`或`cancelOrderStoreListJob`

3. **已支付 -> 已发货**
   - 触发条件：商家完成发货
   - 处理方法：`ordereDlivery`

4. **已支付 -> 已取消**
   - 触发条件：用户申请取消或商家同意取消
   - 处理方法：`refundAndAbrogateOrder`

5. **已发货 -> 已完成**
   - 触发条件：用户确认收货或自动确认收货（通常7天）
   - 处理方法：`affirmOrder`或`confirmReceiptOrderJob`