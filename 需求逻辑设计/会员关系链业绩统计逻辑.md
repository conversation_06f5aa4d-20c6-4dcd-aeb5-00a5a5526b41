# 八闽助业集市会员关系链业绩统计逻辑

## 1. 业务背景

八闽助业集市平台采用分销模式，会员可以通过推荐其他用户注册成为会员，形成分销关系链。平台需要根据会员的分销关系链计算业绩，用于佣金分配和等级评定等功能。

## 2. 核心业务需求

1. 准确统计会员及其下级分销链所有订单的业绩数据
2. 按照不同店铺类型（品牌馆、生活馆、创业馆）分类统计业绩
3. 确保会员推荐关系变更后，历史业绩数据不受影响
4. 业绩数据需要实时计算，保证数据准确性

## 3. 数据模型

### 3.1 核心数据表

- **会员表(member_list)**：存储会员基本信息，包含会员ID、唯一标识(unique_id)、推荐人ID等
- **订单表(order_store_list)**：存储订单信息，包含订单金额、实际收款金额(actually_received_amount)、店铺类型、订单状态等
- **分销设置表(marketing_distribution_setting)**：存储分销相关配置，包括分销比例、绑定关系场景等

### 3.2 关键字段说明

- **会员表(member_list)**
  - `id`：会员ID，主键
  - `unique_id`：会员唯一标识，用于构建分销路径
  - `parent_id`：推荐人ID，表示该会员的直接上级
  - `path`：分销路径，记录完整的上下级关系链

- **订单表(order_store_list)**
  - `actually_received_amount`：实际收款金额，用于计算业绩
  - `order_type`：订单类型，15-品牌馆，16-生活馆，17-创业馆
  - `status`：订单状态，3-已完成，5-交易成功

## 4. 业绩计算逻辑

### 4.1 业绩计算原则

1. **业绩来源**：会员的业绩来自于自身及其下级分销链所有已完成（状态为3或5）的订单
2. **计算依据**：业绩计算基于订单的实际收款金额(actually_received_amount)
3. **历史数据不变**：会员推荐关系变更不影响历史业绩数据

### 4.2 业绩计算方法

业绩计算采用基于分销路径(path)的查询方式，具体步骤如下：

1. 获取目标会员的唯一标识(unique_id)
2. 查询订单表中所有包含该会员唯一标识的分销路径的订单
3. 根据订单类型分类统计不同类型的业绩
4. 汇总计算总业绩

### 4.3 SQL实现

```sql
SELECT 
    order_type,
    SUM(actually_received_amount) as performance 
FROM 
    order_store_list 
WHERE 
    (status = '3' OR status = '5') 
    AND (member_id = #{memberId} OR FIND_IN_SET(#{uniqueId}, path)) 
GROUP BY 
    order_type
```

## 5. 会员关系变更与业绩计算

### 5.1 会员关系变更场景

会员关系变更主要发生在以下场景：

1. 会员主动更换推荐人
2. 系统自动调整分销关系（如推荐人账号注销）
3. 管理员手动调整分销关系

### 5.2 会员关系变更对业绩的影响

**核心原则：会员推荐人变更后，历史业绩数据不会跟随变动**

具体实现机制：

1. **订单固化分销路径**：订单生成时会记录当时的分销路径(path)，该路径一旦生成不再变更
2. **基于路径查询**：业绩查询基于订单中固化的分销路径，而非会员表中的实时路径
3. **唯一标识不变**：会员的唯一标识(unique_id)在整个生命周期内保持不变，确保路径查询的准确性

### 5.3 变更前后的业绩归属

- **变更前**：变更前产生的订单业绩归属于原分销链
- **变更后**：变更后产生的新订单业绩归属于新分销链
- **历史业绩**：历史业绩不会因为关系变更而重新分配

## 6. 技术实现

### 6.1 接口设计

```java
/**
 * 统计会员分销业绩
 * @param memberId 会员ID
 * @return 包含各类业绩数据的Map
 */
Map<String, BigDecimal> calculateMemberPerformance(String memberId);
```

### 6.2 业绩计算流程

1. 初始化业绩数据为零
2. 获取会员的唯一标识(unique_id)
3. 调用Mapper查询订单表中的业绩数据
4. 根据订单类型分类统计不同类型的业绩
5. 计算总业绩并返回结果

### 6.3 代码实现

```java
@Override
public Map<String, BigDecimal> calculateMemberPerformance(String memberId) {
    Map<String, BigDecimal> performanceMap = new HashMap<>();
    
    // 初始化各类业绩为0
    performanceMap.put("totalPerformance", BigDecimal.ZERO);
    performanceMap.put("helpFarmersPerformance", BigDecimal.ZERO); // 品牌馆业绩(15)
    performanceMap.put("helpDisabledPerformance", BigDecimal.ZERO); // 生活馆业绩(16)
    performanceMap.put("helpStudentPerformance", BigDecimal.ZERO);  // 创业馆业绩(17)
    
    // 获取会员的uniqueId
    MemberList member = memberListService.getById(memberId);
    if (member == null || member.getUniqueId() == null) {
        return performanceMap;
    }
    
    // 调用Mapper查询订单表中的业绩数据
    List<Map<String, Object>> performanceList = baseMapper.queryMemberPerformanceByPath(
        memberId, member.getUniqueId().toString());
    
    // 计算总业绩
    BigDecimal totalPerformance = BigDecimal.ZERO;
    
    // 处理查询结果
    for (Map<String, Object> map : performanceList) {
        String orderType = (String) map.get("order_type");
        BigDecimal performance = (BigDecimal) map.get("performance");
        
        if (performance != null) {
            totalPerformance = totalPerformance.add(performance);
            
            // 根据订单类型分类统计
            if ("15".equals(orderType)) {
                performanceMap.put("helpFarmersPerformance", performance);
            } else if ("16".equals(orderType)) {
                performanceMap.put("helpDisabledPerformance", performance);
            } else if ("17".equals(orderType)) {
                performanceMap.put("helpStudentPerformance", performance);
            }
        }
    }
    
    performanceMap.put("totalPerformance", totalPerformance);
    return performanceMap;
}
```

## 7. 业务场景示例

### 场景一：正常业绩计算

会员A推荐了会员B和会员C，会员B又推荐了会员D。此时会员A的业绩包括：
- 会员A自己的订单业绩
- 会员B的订单业绩
- 会员C的订单业绩
- 会员D的订单业绩

### 场景二：会员关系变更

会员D原本由会员B推荐，后来变更为由会员C直接推荐：
- 变更前：会员D的订单业绩计入会员B和会员A的业绩
- 变更后：会员D的新订单业绩计入会员C和会员A的业绩
- 变更前会员D产生的订单业绩仍然计入会员B和会员A的历史业绩中，不会因关系变更而重新分配

## 8. 注意事项与优化建议

### 8.1 性能优化

1. **索引优化**：为order_store_list表的path字段、member_id字段和status字段创建合适的索引
2. **缓存策略**：考虑对高频查询的会员业绩数据进行缓存
3. **分批处理**：处理大量会员业绩数据时采用分批处理策略

### 8.2 数据一致性

1. **事务控制**：在会员关系变更时使用事务确保数据一致性
2. **定时校验**：定期校验业绩数据，确保计算准确性

### 8.3 扩展性考虑

1. **业绩计算规则可配置**：将业绩计算规则设计为可配置的，以适应业务变化
2. **多维度业绩统计**：支持按时间段、产品类型等多维度统计业绩

## 9. 总结

八闽助业集市会员关系链业绩统计逻辑通过固化订单分销路径的方式，确保了会员推荐关系变更不影响历史业绩数据，同时通过实时计算保证了业绩数据的准确性。该设计既满足了业务需求，又保证了系统的可扩展性和性能。
