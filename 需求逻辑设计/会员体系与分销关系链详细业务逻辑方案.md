# 八闽助业集市会员体系与分销关系链详细业务逻辑方案

## 一、会员类型体系

根据代码分析，系统中存在多种会员类型，通过`memberType`字段区分：

| 会员类型值 | 含义 | 设置场景 |
|----------|------|---------|
| "0" | 普通会员 | 小程序快捷登录注册 |
| "1" | VIP会员（预留字段暂未使用） | 未在代码片段中体现 |
| "2" | 企业家 | 后台管理系统添加 |
| "3" | 销售队员（（预留字段暂未使用）） | 从图片4中可见 |

## 二、会员注册来源

系统通过`registrationSource`字段记录会员的注册来源：

| 注册来源值 | 含义 | 设置场景 |
|----------|------|---------|
| "0" | 小程序注册 | 用户通过微信小程序自行注册 |
| "1" | 后台添加 | 管理员通过后台管理系统添加 |

## 三、推广人类型

系统通过`promoterType`字段区分推广人类型：

| 推广人类型值 | 含义 | 应用场景 |
|------------|------|---------|
| "0" | 店铺（（预留字段暂未使用）） | 由店铺推广的会员 |
| "1" | 会员 | 由其他会员推广的会员 |
| "2" | 平台 | 自然客，无推广人或默认平台为推广人 |

## 四、会员关系链结构

系统采用"路径记录"方式维护会员关系链，主要通过以下字段：

1. **uniqueId**：会员唯一自增ID，用于构建关系链路径
2. **memberPath**：会员层级路径，记录从顶层到当前会员的完整推荐链
3. **memberLevel**：会员在分销体系中的层级深度，从1开始递增
4. **promoter**：直接推广人ID
5. **promoterType**：推广人类型

### 路径记录格式

会员路径采用`uniqueId`串联，格式为：
```
上级uniqueId->上上级uniqueId->...->当前会员uniqueId
```

例如：
- 一级会员（无推广人）：`123`（仅包含自己的uniqueId）
- 二级会员（有一级推广人）：`100->123`（推广人uniqueId->自己的uniqueId）
- 三级会员（有二级推广人）：`50->100->123`（上上级uniqueId->上级uniqueId->自己的uniqueId）

## 五、会员注册与关系链建立流程

### 5.1 小程序端快捷登录注册流程

1. **注册信息设置**
   - 设置会员类型：`memberType="0"`（普通会员）
   - 设置注册来源：`registrationSource="0"`（小程序注册）
   - 初始化余额、福利金等数值为0
   - 设置分享次数为系统配置值

2. **推广关系处理**
   - 默认设置为自然客：`promoterType="2"`（平台）
   - 设置会员层级：`memberLevel=1`（第一层级）
   - 设置会员路径：`memberPath=自身uniqueId`

3. **推广关系绑定**
   - 如果传入推广人ID（tMemberId），可以通过`setLoginRegister`方法建立推广关系

### 5.2 后台添加企业家会员流程

1. **基本信息验证**
   - 验证手机号是否已被注册

2. **会员信息设置**
   - 设置会员类型：`memberType="2"`（企业家）
   - 设置注册来源：`registrationSource="1"`（后台添加）
   - 设置助梦家标识：`isLoveAmbassador="1"`（是助梦家）
   - 初始化余额、福利金等数值为0
   - 设置分享次数为系统配置值

3. **推广关系处理**
   - 如果指定了推广人（企业家）：
     - 验证推广人是否存在
     - 设置推广人类型：`promoterType="1"`（会员）
     - 设置推广人ID：`promoter=推广人ID`
     - 设置会员层级：`memberLevel=推广人层级+1`
     - 保存会员信息后，设置会员路径：`memberPath=推广人路径->自身uniqueId`
   
   - 如果未指定推广人：
     - 设置为自然客：`promoterType="2"`（平台）
     - 设置会员层级：`memberLevel=1`（第一层级）
     - 设置会员路径：`memberPath=自身uniqueId`

4. **生成个人二维码**
   - 如果未设置二维码，则生成并保存会员个人推广二维码

### 5.3 推广关系绑定方法（setPromoter）

该方法用于建立会员间的推广关系，主要逻辑：

1. **验证推广人**
   - 检查推广人是否存在

2. **获取分销设置**
   - 查询系统中的分销设置，确定是否有门槛要求

3. **无门槛情况**
   - 如果分销设置无门槛（`isThreshold="0"`）：
     - 设置推广人类型为会员：`promoterType="1"`
     - 设置推广人ID：`promoter=推广人ID`
     - 设置会员层级：`memberLevel=推广人层级+1`
     - 设置会员路径：`memberPath=推广人路径->自身uniqueId`

4. **有门槛情况**
   - 如果分销设置有门槛（`isThreshold="1"`）：
     - 检查推广人是否为助梦家（`isLoveAmbassador="1"`）
     - 如果是助梦家，则建立推广关系，设置方式同无门槛情况
     - 如果不是助梦家，则不建立推广关系

## 六、会员分销层级与佣金计算

系统通过`memberLevel`和`memberPath`实现多级分销：

1. **层级确定**
   - 直接通过`memberLevel`字段确定会员在分销体系中的层级
   - 第一层级为1，每下一级递增1

2. **团队查询**
   - 通过查询`memberPath`中包含特定会员ID的记录，可以获取该会员的所有下级
   - 例如：`SELECT * FROM member_list WHERE member_path LIKE '%会员ID%'`

3. **佣金计算**
   - 系统记录了会员的累计佣金（`totalCommission`）
   - 已提现金额（`haveWithdrawal`）
   - 冻结金额（`accountFrozen`）
   - 根据不同层级的分销规则，可以计算不同层级的佣金分配

## 七、特殊角色与权限

### 7.1 助梦家

系统通过`isLoveAmbassador`字段标识会员是否为助梦家：
- "0"：不是助梦家
- "1"：是助梦家

助梦家在分销系统中具有特殊权限：
- 当分销设置有门槛时，只有助梦家才能作为推广人
- 后台添加的企业家会员默认设置为助梦家

### 7.2 员工标识

系统通过`isEmployee`字段标识会员是否为员工：
- "0"：不是员工
- "1"：是员工

## 八、会员关系链应用场景

1. **分销佣金计算**
   - 根据会员路径和层级，计算不同层级的分销佣金

2. **团队管理**
   - 查询特定会员的所有下级，实现团队管理功能

3. **业绩统计**
   - 统计团队业绩（`totalPerformance`）
   - 统计特定类型业绩（助农、助残、助学等）

4. **层级权益**
   - 根据会员层级，提供不同的权益和功能

## 九、会员体系与分销关系链完整流程图

```
┌─────────────────┐     ┌─────────────────┐
│  小程序快捷登录  │     │  后台添加企业家  │
└────────┬────────┘     └────────┬────────┘
         │                       │
         ▼                       ▼
┌─────────────────┐     ┌─────────────────┐
│  设置基本会员信息 │     │  设置基本会员信息 │
│ memberType="0"  │     │ memberType="2"  │
│ registrationSource="0"│ │ registrationSource="1"│
└────────┬────────┘     └────────┬────────┘
         │                       │
         ▼                       ▼
┌─────────────────┐     ┌─────────────────┐
│   是否有推广人?   │     │   是否有推广人?   │
└────────┬────────┘     └────────┬────────┘
    ┌────┴────┐             ┌────┴────┐
    │        │             │        │
    ▼        ▼             ▼        ▼
┌─────┐  ┌─────┐       ┌─────┐  ┌─────┐
│ 有  │  │ 无  │       │ 有  │  │ 无  │
└──┬──┘  └──┬──┘       └──┬──┘  └──┬──┘
   │        │             │        │
   ▼        ▼             ▼        ▼
┌─────────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐
│ 调用setPromoter │ │设置自然客关系│ │验证推广人存在│ │设置自然客关系│
│ (当前被注释)    │ │promoterType="2"│ └────┬────────┘ │promoterType="2"│
└─────────────────┘ │memberLevel=1 │      │          │memberLevel=1 │
                    │memberPath=   │      ▼          │memberPath=   │
                    │自身uniqueId  │ ┌─────────────┐ │自身uniqueId  │
                    └─────────────┘ │设置推广关系  │ └─────────────┘
                                    │promoterType="1"│
                                    │memberLevel=   │
                                    │推广人级别+1   │
                                    │memberPath=    │
                                    │推广人路径->自身│
                                    └─────────────┘
```

## 十、系统配置与扩展性

1. **分销设置**
   - 系统通过`MarketingDistributionSetting`表配置分销规则
   - 可设置是否有门槛（`isThreshold`）
   - 可配置不同层级的佣金比例

2. **会员等级**
   - 系统支持设置会员等级（`memberGradeId`）
   - 不同等级可能有不同的权益和分销权限

3. **推广码与二维码**
   - 每个会员有唯一的推广码（`promotionCode`）
   - 系统自动生成会员个人二维码（`qrcodeAddr`）
   - 通过推广码/二维码可以建立推广关系

## 十一、总结

八闽助业集市项目采用了完善的会员体系和多级分销关系链结构，通过`memberPath`、`memberLevel`等字段实现了灵活的推广关系管理。系统支持多种会员类型和注册来源，并通过特殊角色（如助梦家）实现了分销权限的精细化控制。

该设计方案具有以下优点：
1. 查询效率高：直接通过字段获取推广关系，无需多次查询
2. 层级清晰：通过memberLevel直接获取层级深度
3. 关系完整：memberPath记录完整推荐链路径
4. 扩展性强：可通过配置调整分销规则和权限

通过这种设计，系统能够有效支持多级分销推广、团队管理、佣金计算等核心业务功能。


## 十二、订单佣金处理完整流程

订单佣金处理是八闽助业集市分销系统的核心环节，整个流程分为三个主要阶段：订单创建、支付成功回调和确认收货。每个阶段都有明确的佣金状态变化和处理逻辑。

### 12.1 订单创建阶段

在`OrderStoreListServiceImpl.submitOrderStoreGoods`方法中实现：

1. **确定订单的推广人关系**
   - 默认设置系统运营人员为推广人
   - 如果会员已有推广人，则使用会员的推广人
   - 如果会员是自然客且当前有推广人参数，则尝试绑定推广人

2. **计算佣金金额**
   - 获取推广人信息和分销设置
   - 计算商品佣金：优先使用商品设置的分销比例，如无则使用店铺公共配置

3. **创建佣金记录**
   - 创建`MemberRechargeRecord`记录，设置状态为"未支付"（`tradeStatus="0"`）
   - 保存分销明细记录，关联到充值记录

### 12.2 支付成功回调阶段

在`OrderStoreListServiceImpl.paySuccessOrder`方法中实现：

1. **订单状态更新**
   - 根据配送方式（`distribution`）更新订单状态：
     - 快递配送（0）或上门配送（2）：订单状态更新为"待发货"（1）
     - 同城自提（1）：订单状态直接更新为"待收货"（2），并生成自提二维码

2. **推广人绑定逻辑**
   - 只有当订单的推广人类型为"会员"（`promoterType="1"`）且会员没有绑定推广人时才执行绑定
   - 调用`setPromoter`方法绑定推广人关系，这是一个完整的绑定过程，会更新会员的推广人信息
   - 不会绑定平台运营人员：如果订单的推广人类型是平台（`promoterType="2"`），不会执行绑定操作

3. **佣金处理逻辑**
   - 查询与订单关联的所有分销记录（`tradeStatus="0"`未支付状态）
   - 将分销记录状态更新为"待结算"（`tradeStatus="2"`）
   - 根据支付类型（`payType`）处理不同类型的佣金：
     - "3"：普通分销佣金
     - "49"：代言人佣金
   - 将佣金金额添加到会员的冻结账户（`accountFrozen`），而不是直接添加到可用余额（`balance`）
   - 这表明佣金此时处于"冻结"状态，需要在后续确认收货后才会解冻

### 12.3 确认收货阶段

在`OrderStoreListServiceImpl.affirmOrder`方法中实现：

1. **订单状态更新**
   - 将订单状态更新为"交易成功"（3）
   - 设置确认收货时间（`deliveryTime`）

2. **佣金处理逻辑**
   - 查询与订单关联的所有待结算分销记录（`tradeStatus="2"`）
   - 根据支付类型（`payType`）更新会员的总佣金统计（`totalCommission`）
   - 将佣金从冻结账户（`accountFrozen`）转移到可用余额（`balance`）
   - 将分销记录状态更新为"交易完成"（`tradeStatus="5"`）
   - 生成资金流水记录，记录佣金发放情况

### 12.4 佣金状态流转图

```
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│   订单创建阶段   │     │  支付成功回调阶段 │     │   确认收货阶段   │
└────────┬────────┘     └────────┬────────┘     └────────┬────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│ 创建佣金记录     │     │ 更新佣金记录状态 │     │ 更新佣金记录状态 │
│ tradeStatus="0" │     │ tradeStatus="2" │     │ tradeStatus="5" │
└────────┬────────┘     └────────┬────────┘     └────────┬────────┘
         │                       │                       │
         │                       ▼                       ▼
         │              ┌─────────────────┐     ┌─────────────────┐
         │              │ 添加到冻结账户   │     │ 从冻结账户转移到 │
         │              │ accountFrozen   │     │ 可用余额 balance │
         │              └─────────────────┘     └────────┬────────┘
         │                                               │
         │                                               ▼
         │                                      ┌─────────────────┐
         │                                      │ 更新总佣金统计   │
         │                                      │ totalCommission │
         │                                      └────────┬────────┘
         │                                               │
         │                                               ▼
         └───────────────────────────────────► ┌─────────────────┐
                                               │ 生成资金流水记录 │
                                               └─────────────────┘
```

### 12.5 佣金处理关键字段说明

1. **MemberRechargeRecord（会员充值记录）表**
   - `tradeStatus`：交易状态
     - "0"：未支付
     - "1"：进行中
     - "2"：待结算
     - "3"：待打款
     - "4"：待退款
     - "5"：交易完成
     - "6"：已退款
     - "7"：交易关闭
   
   - `payType`：支付类型
     - "3"：普通分销佣金
     - "49"：代言人佣金
   
   - `goAndCome`：收支类型
     - "0"：收入
     - "1"：支出

2. **MemberList（会员列表）表**
   - `balance`：可用余额
   - `accountFrozen`：冻结账户金额
   - `totalCommission`：累计佣金

3. **MemberDistributionRecord（会员分销明细）表**
   - `commission`：佣金金额
   - `distributionRatio`：分销比例
   - `memberRechargeRecordId`：关联的会员充值记录ID

### 12.6 佣金处理优化建议

1. **佣金处理逻辑封装**
   - 将佣金处理逻辑封装为独立的方法，提高代码可读性和可维护性
   - 例如：`processMemberCommission(String orderNo, String fromStatus, String toStatus)`

2. **异常处理完善**
   - 在佣金处理过程中增加异常处理和事务回滚机制，确保佣金处理的原子性
   - 例如：在冻结佣金失败时，回滚订单状态更新

3. **多级分销支持**
   - 当前代码只处理了直接推广人的佣金，可以考虑增加对多级分销的支持
   - 通过解析`memberPath`字段，获取完整的推广链路，并计算各级推广人的佣金

4. **日志记录完善**
   - 在关键节点增加详细的日志记录，方便后续问题排查
   - 例如：记录佣金计算过程、冻结和解冻操作等

5. **佣金退款处理**
   - 增加对订单退款情况下佣金处理的逻辑
   - 例如：如果订单退款，需要从推广人账户扣除已发放的佣金

## 十三、总结

八闽助业集市项目采用了完善的会员体系和多级分销关系链结构，通过`memberPath`、`memberLevel`等字段实现了灵活的推广关系管理。系统支持多种会员类型和注册来源，并通过特殊角色（如助梦家）实现了分销权限的精细化控制。

佣金处理流程采用三阶段设计（创建→冻结→解冻），确保佣金只有在订单确认收货后才真正发放给推广人，避免了订单取消或退款时的佣金处理问题。同时，推广人关系的绑定在支付成功回调时完成，确保了推广关系的准确性。

该设计方案具有以下优点：
1. 查询效率高：直接通过字段获取推广关系，无需多次查询
2. 层级清晰：通过memberLevel直接获取层级深度
3. 关系完整：memberPath记录完整推荐链路径
4. 扩展性强：可通过配置调整分销规则和权限
5. 佣金安全：通过三阶段处理确保佣金发放的安全性和准确性

通过这种设计，系统能够有效支持多级分销推广、团队管理、佣金计算等核心业务功能。

# 订单佣金处理流程补充说明

## 一、订单创建阶段补充说明

在`OrderStoreListServiceImpl.submitOrderStoreGoods`方法中实现：

1. **会员佣金计算优化**
   - 通过`processMemberCommission`方法处理会员佣金计算，确保佣金计算的准确性和代码可维护性
   - 该方法接收订单号、订单商品列表、推广人信息、分销设置和会员ID等参数
   - 返回计算后的总佣金金额，用于后续处理

2. **店铺实际到账金额计算**
   - 通过`processStoreActuallyReceivedAmount`方法计算店铺实际到账金额
   - 计算公式：实际到账金额 = 实付款 × 90%(扣除平台佣金) - 分销佣金 - 企业家佣金
   - 记录平台佣金金额（`platformCommissionAmount`）：实付款 × 10%
   - 记录企业家分销佣金金额（`entrepreneurCommissionAmount`）：企业家分佣总额

## 二、企业家分佣逻辑详解

企业家分佣是八闽助业集市特有的佣金分配机制，主要在`processStoreActuallyReceivedAmount`方法中实现：

### 2.1 企业家分佣基本流程

1. **分佣设置获取**
   - 通过`StoreCashierRouting`表获取店铺的企业家分佣设置
   - 筛选条件：
     - `fashionableType="1"`：代言人类型
     - `accountType="2"`：账户类型为分佣
   - 按创建时间倒序排列，确保最新的设置优先生效

2. **分佣金额计算**
   - 初始实际到账金额 = 实付款 × 90%(扣除平台佣金) - 分销佣金
   - 企业家分佣基于实时到账金额计算，而非固定的初始金额
   - 每次计算分佣时，都基于当前最新的实时到账金额，确保分佣总额不会超过实际可分配金额

3. **分佣记录创建**
   - 为每个企业家创建`MemberRechargeRecord`记录
   - 设置交易类型为`payType="49"`（代言人佣金）
   - 设置交易状态为`tradeStatus="0"`（未支付）
   - 记录关联的订单号，便于后续追踪

4. **实际到账金额更新**
   - 每次分佣后，更新实时到账金额和最终到账金额
   - 确保最终到账金额准确反映了扣除所有分佣后的实际金额

### 2.2 企业家分佣计算示例

假设一笔订单：
- 实付款：1000元
- 平台佣金：100元（10%）
- 分销佣金：50元
- 初始实际到账金额：1000 × 90% - 50 = 850元

企业家分佣计算：
1. 第一个企业家分佣比例为10%：
   - 分佣金额 = 850 × 10% = 85元
   - 剩余金额 = 850 - 85 = 765元
2. 第二个企业家分佣比例为10%：
   - 分佣金额 = 765 × 10% = 76.5元
   - 剩余金额 = 765 - 76.5 = 688.5元
3. 最终店铺实际到账金额：688.5元
4. 企业家分销佣金总额：85 + 76.5 = 161.5元

### 2.3 企业家分佣数据记录

1. **订单表字段**
   - `platformCommissionAmount`：平台佣金金额
   - `entrepreneurCommissionAmount`：企业家分销佣金金额总计
   - `actuallyReceivedAmount`：店铺最终实际到账金额

2. **企业家分佣记录**
   - 每个企业家的分佣记录存储在`MemberRechargeRecord`表中
   - 通过`tradeNo`关联到原始订单
   - 通过`payType="49"`标识为代言人佣金

3. **店铺到账记录**
   - 在`StoreRechargeRecord`表中创建店铺实际到账金额记录
   - 记录最终到账金额，即扣除所有佣金后的金额

## 三、订单佣金处理优化建议补充

1. **佣金计算实时性**
   - 企业家分佣应基于实时到账金额计算，而非固定的初始金额
   - 这确保了分佣总额不会超过实际可分配金额，避免了资金计算错误

2. **佣金数据透明化**
   - 在订单表中记录平台佣金金额和企业家分销佣金金额
   - 便于后期数据追溯和统计分析，提高系统透明度

3. **日志记录完善**
   - 在企业家分佣过程中记录详细日志
   - 包括分佣前金额、分佣金额和分佣后金额
   - 便于问题排查和数据核对

4. **代码结构优化**
   - 将佣金计算逻辑抽取为独立方法
   - 提高代码可读性和可维护性
   - 便于后期功能扩展和逻辑调整

## 四、订单流程与佣金状态完整图示

```
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│   订单创建阶段   │     │  支付成功回调阶段 │     │   确认收货阶段   │
└────────┬────────┘     └────────┬────────┘     └────────┬────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│ 计算会员分销佣金 │     │ 更新会员佣金状态 │     │ 更新会员佣金状态 │
│ processMember   │     │ tradeStatus="2" │     │ tradeStatus="5" │
│ Commission      │     └────────┬────────┘     └────────┬────────┘
└────────┬────────┘              │                       │
         │                       ▼                       ▼
         ▼              ┌─────────────────┐     ┌─────────────────┐
┌─────────────────┐     │ 添加到冻结账户   │     │ 从冻结账户转移到 │
│ 计算企业家分佣   │     │ accountFrozen   │     │ 可用余额 balance │
│ processStore    │     └─────────────────┘     └────────┬────────┘
│ ActuallyReceived│                                      │
│ Amount          │                                      ▼
└────────┬────────┘                             ┌─────────────────┐
         │                                      │ 更新总佣金统计   │
         ▼                                      │ totalCommission │
┌─────────────────┐                             └────────┬────────┘
│ 创建佣金记录     │                                      │
│ tradeStatus="0" │                                      ▼
└────────┬────────┘                             ┌─────────────────┐
         │                                      │ 生成资金流水记录 │
         └──────────────────────────────────────► ─────────────────┘
```

## 五、数据表字段补充说明

### 5.1 OrderStoreList（订单表）新增字段

1. **平台佣金金额**
   - 字段名：`platform_commission_amount`
   - 类型：decimal(10,2)
   - 说明：记录平台收取的佣金金额，计算公式为实付款的10%

2. **企业家分销佣金金额**
   - 字段名：`entrepreneur_commission_amount`
   - 类型：decimal(10,2)
   - 说明：记录订单中所有企业家分佣的总金额

3. **订单号**
   - 字段名：`order_no`
   - 类型：varchar(50)
   - 说明：关联订单的唯一标识，便于后期数据追溯

