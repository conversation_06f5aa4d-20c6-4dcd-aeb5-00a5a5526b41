# 🔍 uniapp技术栈解剖报告

## 🏗️ 核心框架
![Vue](https://img.shields.io/badge/Vue-3.0.0+-brightgreen) ![uni-app](https://img.shields.io/badge/uni--app-3.0.0+-blue)

- **框架版本**：根据manifest.json中的`compilerVersion: 3`和`vueVersion: "3"`可知，项目使用Vue 3开发
- **编译器版本**：uni-app编译器版本为3.x
- **创建方式**：通过HBuilderX创建（无根目录package.json）

## 🎨 UI框架
![uView](https://img.shields.io/badge/uView-v2.x-orange) ![uni-ui](https://img.shields.io/badge/uni--ui-latest-lightblue)

- **主要UI库**：
  - **uView UI**：使用uView 2.x版本组件库（uv-badge/uv-icon/uv-sticky/uv-tabs等）
  - **uni-ui**：官方组件库（uni-badge/uni-countdown/uni-popup等）
  - **自定义组件**：项目包含多个自定义组件（如activityBanner/cancelOrder等）

## 📊 状态管理
![Pinia](https://img.shields.io/badge/Pinia-latest-yellow)

- **状态管理方案**：使用Pinia进行状态管理
- **Store模块**：
  - `userStore`：用户信息相关
  - `frontSetting`：程序设置相关
  - `countDownStore`：倒计时相关
  - `locationInfo`：地理位置相关
  - `getAllBeShared`：分享信息相关
  - `setSaleChannelStoreInfo`：销售渠道信息
  - `recordFullPagePath`：页面路径记录
  - `recordIndexPopUp`：首页弹窗状态

## 🌐 网络请求库
![luch-request](https://img.shields.io/badge/luch--request-v3.0.8-brightgreen)

- **请求库**：使用luch-request（v3.0.8）
- **封装特点**：
  - 请求/响应拦截器
  - 统一错误处理
  - Token自动携带
  - 跨端适配（通过条件编译区分平台）
  - 自动处理登录失效（code 666）
  - 全局挂载：`uni.http`

## 📱 小程序原生能力
![微信小程序](https://img.shields.io/badge/微信小程序-API-green)

- **微信小程序**：
  - **AppID**：wx8bb02b064cee39b4
  - **原生API调用**：
    - `wx.getMenuButtonBoundingClientRect()`：获取胶囊按钮位置信息
    - `wx.chooseMessageFile`：选择会话文件（uni-file-picker组件中）

## 🔄 条件编译标记
![条件编译](https://img.shields.io/badge/条件编译-多平台-blue)

- **主要条件编译标记**：
  - `// #ifdef MP-WEIXIN`：微信小程序专用代码
  - `// #ifdef H5`：H5专用代码
  - `// #ifdef APP-PLUS`：App专用代码
  - `// #ifdef VUE3`：Vue3专用代码
  - `// #ifdef MP`：所有小程序平台
  - `// #ifdef APP-NVUE`：App nvue页面专用

## 🔄 跨端兼容性处理

- **请求适配**：根据平台设置不同的`softModel`值
  ```js
  // #ifdef MP-WEIXIN
  'softModel': 0,
  // #endif
  // #ifdef H5
  'softModel': 3,
  // #endif
  ```

- **UI适配**：组件内部使用条件编译区分不同平台样式
  ```js
  /* #ifdef H5 */
  // H5专用样式
  /* #endif */
  ```

- **API兼容**：使用条件编译区分平台API调用
  ```js
  // #ifdef MP-WEIXIN
  safeBottom = `calc(${screenHeight - safeArea.bottom}px + ${bottomMore}rpx)`
  // #endif
  // #ifndef MP-WEIXIN
  safeBottom = `calc(${safeAreaInsets.bottom}px + ${bottomMore}rpx)`
  // #endif
  ```

- **Vue版本兼容**：同时支持Vue2和Vue3
  ```js
  // #ifndef VUE3
  import Vue from 'vue'
  // Vue2代码
  // #endif

  // #ifdef VUE3
  import { createSSRApp } from 'vue'
  // Vue3代码
  // #endif
  ```
