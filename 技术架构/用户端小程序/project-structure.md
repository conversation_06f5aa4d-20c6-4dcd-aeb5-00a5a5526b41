# 🕵️‍♂️ uniapp项目结构侦查报告

## 📋 核心配置文件
- 📄 manifest.json (应用配置清单，包含应用标识、版本、模块权限等)
- 📄 pages.json (页面路由配置，定义页面路径、窗口样式、导航条等)
- 📄 main.js (应用入口文件，Vue实例初始化)
- 📄 App.vue (应用组件，定义应用生命周期)
- 📄 uni.scss (全局样式变量)

## 📁 页面与组件
- 📂 `/pages/` (页面目录，包含50个页面文件)
  - 🖥️ 各种.vue页面文件
- 📂 `/components/` (自定义组件目录)
  - 📂 `/activityBanner/` - 🧩 activityBanner.vue
  - 📂 `/cancelOrder/` - 🧩 cancelOrder.vue
  - 📂 `/cartAction/` - 🧩 cartAction.vue
  - 📂 `/empty/` - 🧩 empty.vue
  - 📂 `/linkAddress/` - 🧩 linkAddress.vue
  - 📂 `/mxDatepicker/` - 🧩 mxDatepicker.vue
  - 📂 `/numberInput/` - 🧩 numberInput.vue
  - 📂 `/payAboutModal/` - 🧩 payAboutModal.vue
  - 📂 `/specificationWrap/` - 🧩 specificationWrap.vue
  - 📂 `/triangle/` - 🧩 triangle.vue
  - 📂 `/w-picker/` - 🧩 多个日期选择相关组件

## 🔌 uni-app特有目录
- 📂 `/uni_modules/` (uni扩展组件目录)
  - 📦 `lime-painter` (海报绘制组件)
  - 📦 `uni-badge` (徽标组件)
  - 📦 `uni-countdown` (倒计时组件)
  - 📦 `uni-file-picker` (文件选择组件)
  - 📦 `uni-icons` (图标组件)
  - 📦 `uni-nav-bar` (导航栏组件)
  - 📦 `uni-popup` (弹出层组件)
  - 📦 `uni-scss` (样式库)
  - 📦 `uni-steps` (步骤条组件)
  - 📦 `uni-transition` (过渡动画组件)
  - 📦 `uv-badge` (uv徽标组件)
  - 📦 `uv-icon` (uv图标组件)
  - 📦 `uv-sticky` (uv吸顶组件)
  - 📦 `uv-tabs` (uv标签页组件)
  - 📦 `uv-ui-tools` (uv工具库)
  - 📦 `zebra-swiper` (轮播图组件)
- 📂 `/unpackage/` (打包编译目录)

## 🖼️ 静态资源
- 📂 `/static/` (静态资源目录)
  - 🖼️ 多个图片资源 (.png文件)
  - 📂 `/afterSaleOrderDetail/` (售后订单详情相关资源)
  - 📂 `/applyForAfterSale/` (申请售后相关资源)
  - 📂 `/cart/` (购物车相关资源)
  - 📂 `/confirmOrder/` (确认订单相关资源)
  - 📂 `/heartMeter/` (心意计相关资源)
  - 📂 `/heartPool/` (心意池相关资源)
  - 📂 `/helpPanel/` (帮助面板相关资源)
  - 📂 `/index/` (首页相关资源)
  - 📂 `/login/` (登录相关资源)
  - 📂 `/myAddress/` (我的地址相关资源)
  - 📂 `/productCollection/` (商品收藏相关资源)
  - 📂 `/productInfo/` (商品信息相关资源)
  - 📂 `/search/` (搜索相关资源)
  - 📂 `/tabbar/` (底部导航栏相关资源)
  - 📂 `/user/` (用户中心相关资源)

## 🛠️ 工具与功能模块
- 📂 `/hooks/` (钩子函数目录)
  - 🔄 api.js (API接口)
  - 💰 cashier.js (收银相关)
  - ⚙️ config.js (配置)
  - 🛒 getCartCount.js (获取购物车数量)
  - 📌 index.js (入口文件)
  - 🌐 request.js (请求封装)
- 📂 `/js_sdk/` (JavaScript SDK目录)
  - 📂 `/luch-request/` (网络请求库)
- 📂 `/store/` (状态管理目录)
- 📂 `/utils/` (工具函数目录)

## 🔧 其他配置文件
- 📄 uni.promisify.adaptor.js (Promise适配器)
- 📄 .gitignore (Git忽略配置)

### 📝 侦探笔记
1. 项目使用了uni-app框架开发，未发现vite.config.js或webpack.config.js等构建配置文件，可能使用默认配置
2. 静态资源采用按功能模块分类的组织方式，便于管理
3. 大量使用uni_modules组件，提高了开发效率
4. 项目结构清晰，符合uni-app标准项目结构
5. 从目录结构可以看出，这是一个商城类小程序，包含商品、购物车、订单、用户等核心功能模块
