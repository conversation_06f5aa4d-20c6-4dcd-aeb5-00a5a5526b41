#### 规则依据
合理的页面配置和组织方式可以提高应用的加载性能，优化用户体验，提高代码的可维护性。

### 6.3 页面跳转与参数传递

#### 规则描述
- 使用uni.navigateTo、uni.redirectTo等API进行页面跳转
- 使用对象形式传递参数，避免硬编码URL参数
- 大量数据传递使用全局状态管理
- 注意处理参数编码问题

#### 正确示例
```js
// 跳转到商品详情页
const goToProductDetail = (productId) => {
  uni.navigateTo({
    url: '/pages/product/detail/detail',
    success: (res) => {
      // 向打开的页面传递数据
      res.eventChannel.emit('productData', { id: productId })
    }
  })
}

// 在目标页面接收数据
onLoad() {
  const eventChannel = this.getOpenerEventChannel()
  eventChannel.on('productData', (data) => {
    this.productId = data.id
    this.fetchProductDetail()
  })
}
```

#### 错误示例
```js
// 错误：硬编码URL参数
const goToProductDetail = (productId) => {
  uni.navigateTo({
    url: `/pages/product/detail/detail?id=${productId}&from=list&time=${Date.now()}`
  })
}
```

#### 规则依据
规范的页面跳转和参数传递方式可以提高代码的可维护性，避免参数传递过程中的错误和编码问题。

### 6.4 页面生命周期管理

#### 规则描述
- 合理使用页面生命周期函数
- 在onLoad中进行数据初始化和参数获取
- 在onShow中处理页面显示时需要刷新的数据
- 在onUnload中清理资源和事件监听
- 注意页面切换时的数据保留问题

#### 正确示例
```js
// 页面生命周期管理
export default {
  data() {
    return {
      productId: '',
      productInfo: null,
      loading: false
    }
  },
  onLoad(options) {
    // 获取路由参数
    this.productId = options.id
    // 初始化数据
    this.fetchProductDetail()
    // 添加事件监听
    uni.$on('updateProduct', this.handleProductUpdate)
  },
  onShow() {
    // 页面显示时刷新数据，如从购物车返回时需要刷新库存
    this.refreshStockInfo()
  },
  onUnload() {
    // 清理事件监听
    uni.$off('updateProduct', this.handleProductUpdate)
  },
  methods: {
    async fetchProductDetail() {
      this.loading = true
      try {
        const { data } = await uni.http.get(uni.api.getProductDetail, {
          params: { id: this.productId }
        })
        this.productInfo = data.result
      } catch (error) {
        console.error('获取商品详情失败', error)
      } finally {
        this.loading = false
      }
    },
    refreshStockInfo() {
      // 刷新库存信息
    },
    handleProductUpdate(data) {
      // 处理商品更新事件
      if (data.id === this.productId) {
        this.fetchProductDetail()
      }
    }
  }
}
```

#### 错误示例
```js
// 错误：生命周期使用不当
export default {
  data() {
    return {
      productId: '',
      productInfo: null
    }
  },
  created() {
    // 错误：在created中获取路由参数，可能为空
    this.productId = this.$route.query.id
    this.fetchProductDetail()
  },
  // 错误：没有清理事件监听
  mounted() {
    uni.$on('updateProduct', this.handleProductUpdate)
  }
}
```

#### 规则依据
合理使用页面生命周期函数可以避免内存泄漏和数据异常问题，确保页面在不同状态下正常工作。
