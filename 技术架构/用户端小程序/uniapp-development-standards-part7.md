## 7. 条件编译规范

### 7.1 条件编译基本用法

#### 规则描述
- 使用条件编译处理不同平台的差异化代码
- 遵循uni-app官方条件编译语法
- 合理组织条件编译代码，保持代码可读性
- 避免过多嵌套条件编译块

#### 正确示例
```js
// JavaScript 中条件编译
const getSystemInfo = () => {
  // #ifdef APP-PLUS
  const systemInfo = uni.getSystemInfoSync()
  console.log('App环境：', systemInfo.platform)
  return systemInfo
  // #endif
  
  // #ifdef MP-WEIXIN
  const systemInfo = wx.getSystemInfoSync()
  console.log('微信小程序环境：', systemInfo.platform)
  return systemInfo
  // #endif
  
  // #ifdef H5
  console.log('H5环境')
  return {}
  // #endif
}
```

```vue
<!-- 模板中条件编译 -->
<template>
  <view class="container">
    <!-- #ifdef APP-PLUS -->
    <view class="app-only">仅App可见的内容</view>
    <!-- #endif -->
    
    <!-- #ifdef MP-WEIXIN -->
    <view class="mp-only">仅微信小程序可见的内容</view>
    <!-- #endif -->
    
    <!-- #ifdef H5 -->
    <view class="h5-only">仅H5可见的内容</view>
    <!-- #endif -->
    
    <!-- 所有平台都显示的内容 -->
    <view class="common">所有平台共用的内容</view>
  </view>
</template>
```

```css
/* 样式中条件编译 */
.container {
  padding: 20rpx;
}

/* #ifdef APP-PLUS */
.app-only {
  color: #FF0000;
}
/* #endif */

/* #ifdef MP-WEIXIN */
.mp-only {
  color: #00FF00;
}
/* #endif */

/* #ifdef H5 */
.h5-only {
  color: #0000FF;
}
/* #endif */
```

#### 错误示例
```js
// 错误：过度嵌套条件编译
const getSystemInfo = () => {
  // #ifdef APP-PLUS
  const systemInfo = uni.getSystemInfoSync()
  // #ifdef APP-PLUS-IOS
  console.log('iOS环境')
  // #ifdef APP-PLUS-IOS-X
  console.log('iPhoneX')
  // #endif
  // #endif
  // #endif
  
  // 错误：没有处理其他平台
  return systemInfo
}
```

#### 规则依据
合理使用条件编译可以有效处理多平台差异，提高代码的可维护性和可读性，避免运行时判断平台的性能开销。

### 7.2 条件编译组织策略

#### 规则描述
- 优先使用平台特有API而非条件编译
- 抽离平台差异代码到单独的文件
- 使用统一的条件编译标识
- 为不同平台提供统一的接口

#### 正确示例
```js
// 平台适配层 platform/index.js
// #ifdef APP-PLUS
import appAdapter from './app-adapter.js'
export default appAdapter
// #endif

// #ifdef MP-WEIXIN
import wxAdapter from './wx-adapter.js'
export default wxAdapter
// #endif

// #ifdef H5
import h5Adapter from './h5-adapter.js'
export default h5Adapter
// #endif
```

```js
// 各平台适配器实现相同接口
// platform/app-adapter.js
export default {
  login() {
    // App登录实现
  },
  share() {
    // App分享实现
  }
}

// platform/wx-adapter.js
export default {
  login() {
    // 微信小程序登录实现
  },
  share() {
    // 微信小程序分享实现
  }
}
```

```js
// 业务代码中统一调用
import platform from '@/platform/index.js'

// 不需要条件编译，统一接口
const login = () => {
  platform.login()
}
```

#### 错误示例
```js
// 错误：业务代码中大量条件编译
const login = () => {
  // #ifdef APP-PLUS
  // App登录逻辑
  // #endif
  
  // #ifdef MP-WEIXIN
  // 微信小程序登录逻辑
  // #endif
  
  // #ifdef H5
  // H5登录逻辑
  // #endif
}

const share = () => {
  // #ifdef APP-PLUS
  // App分享逻辑
  // #endif
  
  // #ifdef MP-WEIXIN
  // 微信小程序分享逻辑
  // #endif
  
  // #ifdef H5
  // H5分享逻辑
  // #endif
}
```

#### 规则依据
良好的条件编译组织策略可以减少代码重复，提高可维护性，使业务逻辑与平台适配分离，便于后续扩展和维护。

### 7.3 条件编译常见场景

#### 规则描述
- 针对不同平台的UI适配
- 平台特有API的调用
- 平台特有功能的实现
- 针对不同平台的性能优化

#### 常见场景示例
```js
// 1. 平台特有API调用
const scanCode = () => {
  // #ifdef APP-PLUS
  uni.scanCode({
    success: (res) => {
      console.log('扫码结果：', res.result)
    }
  })
  // #endif
  
  // #ifdef MP-WEIXIN
  wx.scanCode({
    success: (res) => {
      console.log('扫码结果：', res.result)
    }
  })
  // #endif
  
  // #ifdef H5
  alert('H5环境不支持扫码功能')
  // #endif
}

// 2. 平台特有功能实现
// #ifdef APP-PLUS
// 监听手机返回键
document.addEventListener('plusready', () => {
  plus.key.addEventListener('backbutton', () => {
    console.log('监听到返回键')
  })
})
// #endif

// 3. 性能优化
// #ifdef MP-WEIXIN || MP-ALIPAY
// 小程序环境使用分包加载
// #endif

// #ifdef H5
// H5环境使用懒加载
// #endif
```

#### 规则依据
针对不同场景合理使用条件编译可以提高应用的兼容性和用户体验，同时保持代码的可维护性。
