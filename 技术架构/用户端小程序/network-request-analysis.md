# 🌐 网络请求模块分析报告

## 📁 文件结构

```
heartful-mall-app/
├── hooks/
│   ├── request.js         # 主要请求封装文件
│   ├── api.js             # API 接口定义文件
│   └── index.js           # 工具函数（包含登录跳转等）
├── js_sdk/
│   └── luch-request/      # 第三方请求库
│       └── luch-request/
│           ├── index.js
│           ├── core/
│           │   ├── Request.js           # 核心请求类
│           │   ├── dispatchRequest.js   # 请求分发
│           │   ├── InterceptorManager.js # 拦截器管理
│           │   ├── defaults.js          # 默认配置
│           │   └── mergeConfig.js       # 配置合并
│           ├── adapters/                # 请求适配器
│           └── utils/                   # 工具函数
└── uni_modules/
    └── uv-ui-tools/
        └── libs/
            └── luch-request/           # UI 库中的 luch-request 副本
```

## 🔌 请求库与封装

项目使用 **luch-request** 作为基础请求库，这是一个专为 uni-app 设计的 HTTP 请求库，类似于 axios。

### 请求库特点

- Promise 风格的 API
- 支持请求/响应拦截器
- 支持自定义配置项合并
- 支持多种请求方法（GET、POST、PUT、DELETE 等）
- 支持不同平台条件编译

## ⚙️ 请求配置

### 基础 URL 配置

```js
// hooks/request.js
http.setConfig(config => {
  config.baseURL = uni.env.REQUEST_URL
  config.header = {
    'content-type': 'application/x-www-form-urlencoded'
  }
  return config
})
```

### 环境配置

项目使用 `hooks/config.js` 中的环境变量配置，通过 `uni.env` 全局对象访问：

```js
// 生产环境 API 地址
REQUEST_URL: 'https://www.zehuaiit.top/heartful-mall-api/'
```

### 默认配置

```js
// js_sdk/luch-request/luch-request/core/defaults.js
export default {
  baseURL: '',
  header: {},
  method: 'GET',
  dataType: 'json',
  responseType: 'text',
  timeout: 60000,
  validateStatus: function(status) {
    return status >= 200 && status < 300
  }
}
```

## 🔄 拦截器配置

### 请求拦截器

```js
// hooks/request.js
http.interceptors.request.use(config => {
  config.header = {
    // #ifdef MP-WEIXIN
    'softModel': 0,
    // #endif
    // #ifdef H5
    'softModel': 3,
    // #endif
    sysUserId: getAllBeSharedStore.info?.sysUserId || setSaleChannelStoreInfoStore.sysUserId ||
      setSaleChannelStoreInfoStore.intoStoreForSysUserId, // 优先绑定分享,其次是接口获得的销售渠道,再者是进店获得的销售渠道
    tMemberId: getAllBeSharedStore.info?.tMemberId || '', // 接收的分享人id
    longitude: locationStore.info.longitude, // 精度
    latitude: locationStore.info.latitude, // 纬度
    shareDiscountRatio: getAllBeSharedStore.shareDiscountRatio || '',
    "X-AUTH-TOKEN": uni.getStorageSync('token') || '',
    ...config.header,
  }
  return config
}, config => {
  return Promise.reject(config)
})
```

### 响应拦截器

```js
// hooks/request.js
http.interceptors.response.use(response => {
  if (response.data?.code == 666) {
    // Token 失效处理
    store.clearUserInfo();
    store.clearToken()
    toLoginAndBackPage()
    setTimeout(() => {
      uni.hideLoading()
    }, 500)
    return Promise.reject(response)
  } else if (response.data?.code == 500) {
    // 服务器错误处理
    uni.showToast({
      icon: 'none',
      title: response.data?.message || '网络异常，请稍后再试'
    })
    return Promise.reject(response)
  } else {
    return response
  }
}, response => {
  // 请求失败处理
  if (response.statusCode != 200) {
    uni.showToast({
      icon: 'none',
      title: response.data?.message || '网络异常，请稍后再试'
    })
  }
  return Promise.reject(response)
})
```

## 📋 API 接口管理

项目使用 `hooks/api.js` 集中管理所有 API 接口路径：

```js
const api = {
  // 用户认证
  loginByCode: '/front/weixin/loginByCode',
  
  // 首页相关
  indexNew: '/front/index/indexNew',
  findarketingAdvertisingList: '/front/MarketingAdvertising/findarketingAdvertisingList',
  
  // 商品相关
  searchHostStoreGoodList: "/front/goodList/searchHostStoreGoodList",
  findGoodListByGoodId: 'front/goodList/findGoodListByGoodId',
  
  // 订单相关
  promptlyAffirmOrder: 'after/order/promptlyAffirmOrder',
  submitOrder: "after/order/submitOrder",
  
  // 用户相关
  getMemberInfo: '/after/member/getMemberInfo',
  
  // ... 更多 API
};

uni.api = api;
```

## 🔄 拦截器工作流程

```mermaid
sequenceDiagram
    participant App as 应用代码
    participant ReqInt as 请求拦截器
    participant Adapter as 请求适配器
    participant ResInt as 响应拦截器
    participant Server as 服务器

    App->>ReqInt: 发起请求
    Note over ReqInt: 添加 Token、用户信息、位置信息等
    Note over ReqInt: 根据平台添加 softModel 标识
    ReqInt->>Adapter: 处理后的请求配置
    Adapter->>Server: 发送 HTTP 请求
    Server->>Adapter: 返回响应数据
    Adapter->>ResInt: 原始响应数据
    Note over ResInt: 检查响应状态码
    Note over ResInt: 处理特殊错误码 (666: Token 失效, 500: 服务器错误)
    ResInt->>App: 处理后的响应数据或错误
```

## 🔑 特殊处理逻辑

### Token 失效处理

当响应码为 666 时，执行以下操作：
1. 清除用户信息
2. 清除 Token
3. 跳转到登录页面
4. 隐藏加载提示

```js
if (response.data?.code == 666) {
  store.clearUserInfo();
  store.clearToken()
  toLoginAndBackPage()
  setTimeout(() => {
    uni.hideLoading()
  }, 500)
  return Promise.reject(response)
}
```

### 平台差异化处理

使用条件编译处理不同平台的请求头：

```js
// #ifdef MP-WEIXIN
'softModel': 0,  // 微信小程序
// #endif
// #ifdef H5
'softModel': 3,  // H5
// #endif
```

## 📊 请求函数封装模式

项目使用 Promise 风格的请求封装：

```js
// 基本用法示例
uni.http.get(uni.api.getMemberInfo).then(res => {
  // 处理成功响应
}).catch(err => {
  // 处理错误
})

// POST 请求示例
uni.http.post(uni.api.loginByCode, {
  code: code
}).then(res => {
  // 处理成功响应
}).catch(err => {
  // 处理错误
})
```

luch-request 库支持以下请求方法：
- `get(url, options)`
- `post(url, data, options)`
- `put(url, data, options)`
- `delete(url, data, options)`
- `upload(url, config)`
- `download(url, config)`

## 📝 重要默认配置

| 配置项 | 值 | 说明 |
|-------|-----|------|
| baseURL | uni.env.REQUEST_URL | API 基础路径 |
| content-type | application/x-www-form-urlencoded | 请求内容类型 |
| timeout | 60000 (60秒) | 请求超时时间 |
| dataType | json | 响应数据类型 |
| responseType | text | 响应类型 |

## 🔍 Mock 数据配置

项目中未发现专门的 Mock 数据配置，所有请求都直接发送到真实的后端 API。

## 📋 实际代码案例

### 案例1：微信登录（POST 请求）

```js
// pages/login/login.vue
async function wxLogin(e) {
  uni.showLoading({
    title: '登录中...',
    mask: true
  })
  
  try {
    let {
      data
    } = await uni.http.post(uni.api.loginByCode, {
      ...e.detail,
      code: code.value
    })
    uni.hideLoading();
    loginSuccessCallBack(data)
  } catch (e) {
    getCode();
  }
}
```

### 案例2：获取首页数据（GET 请求）

```js
// pages/index/index.vue
const refresh = async () => {
  const { data: { result } } = await uni.http.get(uni.api.indexNew)
  info.value = result
  const {
    cartCountRefresh
  } = cartCountHooks()
  cartCountRefresh()
}
```

### 案例3：获取用户信息（GET 请求 + 条件处理）

```js
// hooks/index.js
export async function getUserInfos() {
  if (userStore().token) {
    let {
      data
    } = await uni.http.get(uni.api.getMemberInfo);
    userStore().setUserInfo(data.result);
  } else {
    userStore().clearUserInfo();
  }
}
```

### 案例4：添加商品到购物车（POST 请求 + 异步/await）

```js
// hooks/index.js
export async function addToCart(goodId, skuId, buyNum = 1) {
  try {
    const { 
      data: { result } 
    } = await uni.http.post(uni.api.addGoodToShoppingCart, {
      goodId,
      skuId,
      buyNum
    })
    uni.showToast({
      title: '添加成功',
      icon: 'none'
    })
    return result
  } catch (error) {
    console.error(error)
  }
}
```

### 案例5：获取字典数据（带参数的 GET 请求）

```js
// pages/editUserProfile/editUserProfile.vue
async function getSexList() {
  const { 
    data: { result } 
  } = await uni.http.get(`${uni.api.getDicts}?code=sex`);
  sexList.value = result;
}
```

## 💡 总结与建议

### 优点

1. **集中管理 API**：所有 API 路径集中在 `hooks/api.js` 中管理，便于维护
2. **完善的拦截器**：实现了请求和响应拦截器，统一处理认证和错误
3. **全局实例**：通过 `uni.http` 和 `uni.api` 全局访问请求实例和 API 路径
4. **平台适配**：使用条件编译适配不同平台
5. **异步/await 风格**：大量使用现代 JavaScript 的异步处理方式，代码更清晰

### 改进建议

1. **缺少 API 分类**：可以将 API 按功能模块分类，提高可维护性
2. **缺少请求取消机制**：未实现请求取消功能，可能导致页面切换时请求冲突
3. **缺少请求重试机制**：对于网络不稳定情况，可添加请求重试逻辑
4. **缺少 Mock 数据**：可以添加 Mock 数据配置，便于前端独立开发和测试
5. **缺少请求日志**：可以添加请求日志记录，便于调试和问题排查

### 安全建议

1. **敏感信息保护**：确保不在请求中明文传输敏感信息
2. **HTTPS 使用**：确保生产环境使用 HTTPS 请求
3. **Token 刷新机制**：可以实现 Token 自动刷新机制，提高用户体验
