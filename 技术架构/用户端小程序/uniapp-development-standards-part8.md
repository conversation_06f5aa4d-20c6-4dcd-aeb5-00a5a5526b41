## 8. 性能优化规范

### 8.1 启动性能优化

#### 规则描述
- 减少启动时加载的资源和代码
- 合理使用分包加载
- 优化首屏渲染速度
- 延迟加载非关键资源

#### 正确示例
```js
// 1. 分包加载配置 (pages.json)
{
  "pages": [
    // 主包页面
    {
      "path": "pages/index/index"
    }
  ],
  "subPackages": [
    {
      "root": "pages/product",
      "pages": [
        {
          "path": "list/list"
        },
        {
          "path": "detail/detail"
        }
      ]
    },
    {
      "root": "pages/user",
      "pages": [
        {
          "path": "profile/profile"
        },
        {
          "path": "settings/settings"
        }
      ]
    }
  ]
}

// 2. 延迟加载非关键资源
onLoad() {
  // 首屏关键数据先加载
  this.loadCriticalData()
  
  // 非关键数据延迟加载
  setTimeout(() => {
    this.loadNonCriticalData()
  }, 2000)
}
```

#### 错误示例
```js
// 错误：所有页面放在主包中
{
  "pages": [
    {"path": "pages/index/index"},
    {"path": "pages/product/list/list"},
    {"path": "pages/product/detail/detail"},
    {"path": "pages/user/profile/profile"},
    {"path": "pages/user/settings/settings"},
    {"path": "pages/order/list/list"},
    {"path": "pages/order/detail/detail"}
    // 更多页面...
  ]
}

// 错误：启动时加载所有数据
onLoad() {
  Promise.all([
    this.loadBanners(),
    this.loadProducts(),
    this.loadCategories(),
    this.loadUserInfo(),
    this.loadRecommends(),
    this.loadHistory()
  ]).then(() => {
    this.isLoaded = true
  })
}
```

#### 规则依据
优化启动性能可以提高用户首次打开应用的体验，减少用户等待时间，降低用户流失率。

### 8.2 渲染性能优化

#### 规则描述
- 减少不必要的组件渲染
- 合理使用v-if和v-show
- 避免长列表渲染，使用虚拟列表
- 避免频繁更新视图

#### 正确示例
```vue
<!-- 1. 合理使用v-if和v-show -->
<template>
  <!-- 频繁切换的内容使用v-show -->
  <view v-show="isTabActive" class="tab-content">
    频繁切换的内容
  </view>
  
  <!-- 条件较少变化的内容使用v-if -->
  <view v-if="isLoggedIn" class="user-profile">
    用户信息内容
  </view>
  
  <!-- 2. 长列表使用虚拟列表 -->
  <recycle-list
    :list="longList"
    :item-size="itemSize"
    :buffer-scale="2"
  >
    <template v-slot:item="{ item }">
      <view class="list-item">{{ item.name }}</view>
    </template>
  </recycle-list>
  
  <!-- 3. 使用计算属性过滤数据 -->
  <view v-for="item in filteredList" :key="item.id">
    {{ item.name }}
  </view>
</template>

<script>
export default {
  data() {
    return {
      isTabActive: true,
      isLoggedIn: false,
      longList: Array.from({ length: 1000 }, (_, i) => ({ id: i, name: `Item ${i}` })),
      itemSize: 100,
      list: [],
      filter: ''
    }
  },
  computed: {
    // 使用计算属性避免在模板中进行复杂计算
    filteredList() {
      if (!this.filter) return this.list
      return this.list.filter(item => item.name.includes(this.filter))
    }
  },
  methods: {
    // 4. 避免频繁更新视图
    updateData() {
      // 批量更新数据，避免多次触发视图更新
      const newData = this.processData()
      this.list = newData
    }
  }
}
</script>
```

#### 错误示例
```vue
<!-- 错误：不合理使用v-if和v-show -->
<template>
  <!-- 错误：频繁切换的内容使用v-if -->
  <view v-if="isTabActive" class="tab-content">
    频繁切换的内容
  </view>
  
  <!-- 错误：直接渲染大量数据 -->
  <view v-for="item in longList" :key="item.id" class="list-item">
    {{ item.name }}
  </view>
  
  <!-- 错误：在模板中进行复杂计算 -->
  <view v-for="item in list.filter(i => i.name.includes(filter))" :key="item.id">
    {{ item.name }}
  </view>
</template>

<script>
export default {
  methods: {
    // 错误：频繁更新视图
    updateItems() {
      // 逐个更新数据，触发多次视图更新
      this.list.forEach((item, index) => {
        if (item.needsUpdate) {
          this.list[index] = { ...item, updated: true }
        }
      })
    }
  }
}
</script>
```

#### 规则依据
优化渲染性能可以提高应用的响应速度和流畅度，减少卡顿和闪烁，提升用户体验。

### 8.3 网络性能优化

#### 规则描述
- 减少不必要的网络请求
- 合理使用数据缓存
- 优化请求大小和频率
- 实现请求合并和防抖

#### 正确示例
```js
// 1. 使用缓存减少请求
const fetchCategoryList = async (forceRefresh = false) => {
  const cacheKey = 'category_list'
  const expireTime = 30 * 60 * 1000 // 30分钟
  
  // 检查缓存
  if (!forceRefresh) {
    const cacheData = uni.getStorageSync(cacheKey)
    if (cacheData && cacheData.time && (Date.now() - cacheData.time < expireTime)) {
      return cacheData.data
    }
  }
  
  // 无缓存或强制刷新，发起请求
  const { data } = await uni.http.get(uni.api.getCategoryList)
  // 更新缓存
  uni.setStorageSync(cacheKey, {
    time: Date.now(),
    data: data.result
  })
  return data.result
}

// 2. 请求合并
const fetchAllData = async () => {
  const [userRes, orderRes] = await Promise.all([
    uni.http.get(uni.api.getUserInfo),
    uni.http.get(uni.api.getOrderList)
  ])
  return {
    userInfo: userRes.data.result,
    orderList: orderRes.data.result
  }
}

// 3. 搜索防抖
import { debounce } from '@/utils/debounce'

const searchProducts = debounce(async (keyword) => {
  const { data } = await uni.http.get(uni.api.searchProducts, {
    params: { keyword }
  })
  this.searchResult = data.result
}, 300)
```

#### 错误示例
```js
// 错误：没有缓存策略
const fetchCategoryList = async () => {
  const { data } = await uni.http.get(uni.api.getCategoryList)
  return data.result
}

// 错误：串行请求
const fetchAllData = async () => {
  const userRes = await uni.http.get(uni.api.getUserInfo)
  const orderRes = await uni.http.get(uni.api.getOrderList)
  return {
    userInfo: userRes.data.result,
    orderList: orderRes.data.result
  }
}

// 错误：没有防抖处理
const searchProducts = async (keyword) => {
  const { data } = await uni.http.get(uni.api.searchProducts, {
    params: { keyword }
  })
  this.searchResult = data.result
}

// 输入框绑定
<input type="text" @input="searchProducts" />
```

#### 规则依据
优化网络性能可以减少数据传输量，降低服务器负载，提高应用响应速度，改善用户体验，尤其在网络条件不佳的情况下。

### 8.4 资源优化

#### 规则描述
- 优化图片资源大小和格式
- 合理使用字体图标
- 减少静态资源大小
- 延迟加载非关键资源

#### 正确示例
```vue
<!-- 1. 使用适当尺寸的图片 -->
<template>
  <!-- 根据实际显示尺寸选择图片 -->
  <image 
    src="/static/images/banner_small.jpg" 
    mode="widthFix"
    class="banner-image"
  ></image>
  
  <!-- 2. 使用字体图标代替图片图标 -->
  <text class="iconfont icon-home"></text>
  
  <!-- 3. 延迟加载图片 -->
  <image 
    v-if="isVisible"
    :src="imageUrl" 
    mode="aspectFill"
    class="lazy-image"
  ></image>
</template>

<script>
export default {
  data() {
    return {
      isVisible: false,
      imageUrl: '/static/images/product.jpg'
    }
  },
  onPageScroll(e) {
    // 根据滚动位置判断是否显示图片
    this.checkImageVisibility()
  },
  methods: {
    checkImageVisibility() {
      // 判断元素是否进入可视区域
      // ...
      this.isVisible = true
    }
  }
}
</script>
```

#### 错误示例
```vue
<!-- 错误：使用过大的图片资源 -->
<template>
  <!-- 错误：使用原图显示小尺寸图片 -->
  <image 
    src="/static/images/banner_original_4k.jpg" 
    style="width: 200rpx; height: 100rpx;"
  ></image>
  
  <!-- 错误：使用图片图标 -->
  <image src="/static/icons/home.png" class="icon"></image>
  
  <!-- 错误：不做懒加载处理 -->
  <image 
    v-for="item in 100" 
    :key="item"
    :src="`/static/images/product_${item}.jpg`" 
    class="product-image"
  ></image>
</template>
```

#### 规则依据
优化资源使用可以减少应用体积，加快加载速度，降低内存占用，提高应用整体性能和用户体验。

### 8.5 内存优化

#### 规则描述
- 及时释放不再使用的资源
- 避免内存泄漏
- 合理管理大型数据集
- 优化页面切换时的内存占用

#### 正确示例
```js
// 1. 页面卸载时清理资源
export default {
  data() {
    return {
      timer: null,
      observer: null,
      eventListeners: []
    }
  },
  onLoad() {
    // 设置定时器
    this.timer = setInterval(() => {
      this.updateData()
    }, 1000)
    
    // 创建观察者
    this.observer = new IntersectionObserver(entries => {
      // ...
    })
    
    // 添加事件监听
    const handler = () => { /* ... */ }
    uni.$on('customEvent', handler)
    this.eventListeners.push(['customEvent', handler])
  },
  onUnload() {
    // 清理定时器
    if (this.timer) {
      clearInterval(this.timer)
      this.timer = null
    }
    
    // 清理观察者
    if (this.observer) {
      this.observer.disconnect()
      this.observer = null
    }
    
    // 清理事件监听
    this.eventListeners.forEach(([event, handler]) => {
      uni.$off(event, handler)
    })
    this.eventListeners = []
  }
}

// 2. 分页加载大数据集
const loadDataByPage = async (page = 1, pageSize = 20) => {
  const { data } = await uni.http.get(uni.api.getList, {
    params: { page, pageSize }
  })
  return data.result
}
```

#### 错误示例
```js
// 错误：没有清理资源
export default {
  onLoad() {
    // 错误：没有保存定时器引用
    setInterval(() => {
      this.updateData()
    }, 1000)
    
    // 错误：没有在页面卸载时移除事件监听
    uni.$on('customEvent', this.handleEvent)
  },
  // 没有实现onUnload方法清理资源
  
  // 错误：一次性加载大量数据
  async loadAllData() {
    const { data } = await uni.http.get(uni.api.getList, {
      params: { limit: 1000 }
    })
    this.list = data.result
  }
}
```

#### 规则依据
优化内存使用可以避免应用崩溃，减少卡顿，提高应用稳定性和响应速度，尤其在低端设备上效果明显。
