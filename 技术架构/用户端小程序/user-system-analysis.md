# 用户系统全面分析报告

## 📑 目录

1. [基础登录流程分析](#1-基础登录流程分析)
2. [用户信息获取分析](#2-用户信息获取分析)
3. [登录状态检查分析](#3-登录状态检查分析)
4. [安全专项检查](#4-安全专项检查)
5. [优化建议输出](#5-优化建议输出)

## 1. 基础登录流程分析

### 1.1 登录主文件

**文件位置**：`@/pages/login/login.vue` 和 `@/pages/phoneLogin/phoneLogin.vue`

**登录方式**：
- 微信授权登录
- 手机号验证码登录
- 第三方登录

### 1.2 登录流程图

```mermaid
graph TD
    A[用户进入登录页] --> B{选择登录方式}
    B -->|微信授权| C[获取微信code]
    B -->|手机号验证| D[输入手机号]
    C --> E[调用loginByCode接口]
    D --> F[获取验证码]
    F --> G[验证手机号]
    G --> H[调用登录接口]
    E --> I[登录成功回调]
    H --> I
    I --> J[存储Token]
    J --> K[获取用户信息]
    K --> L[跳转首页]
```

### 1.3 登录关键代码

**Token存储方式**：
```javascript
// 存储Token
users.setToken(data.result['X-AUTH-TOKEN']);
uni.setStorageSync('token', token);
```

**登录成功回调**：
```javascript
// 登录成功回调函数
export async function loginSuccessCallBack(data) {
  const recordFullPagePathStore = recordFullPagePath();
  const users = userStore();
  users.setToken(data.result['X-AUTH-TOKEN']);
  getUserInfos();
  
  // 判断是否有记录的页面路径，有则跳转回去，无则跳转首页
  if (recordFullPagePathStore.pageFullPath) {
    uni.redirectTo({
      url: decodeURIComponent(recordFullPagePathStore.pageFullPath),
      success() {
        recordFullPagePathStore.setPageFullPath('');
      },
      fail() {
        // 如果普通页面跳转失败，尝试Tab页面跳转
        uni.switchTab({
          url: decodeURIComponent(recordFullPagePathStore.pageFullPath),
          success() {
            recordFullPagePathStore.setPageFullPath('');
          },
          fail() {
            uni.switchTab({
              url: '/pages/index/index',
            });
          },
        });
      },
    });
  } else {
    uni.switchTab({
      url: '/pages/index/index',
    });
  }
}
```

### 1.4 常见错误码处理

| 错误码 | 错误描述 | 处理方式 |
|-------|---------|---------|
| 1001 | 微信code失效 | 重新获取code |
| 666 | 登录状态失效 | 清除用户信息并跳转登录页 |
| 500 | 服务器错误 | 显示错误提示 |

## 2. 用户信息获取分析

### 2.1 用户信息API

**接口地址**：`GET /api/user/profile` (实际接口：`getMemberInfo`)

**调用时机**：
- 登录成功后
- App启动时（`App.vue`的`onLaunch`生命周期）

### 2.2 数据存储结构

```javascript
// Pinia存储结构
userStore: {
  token: 'xxxxxxxx', // 用户令牌
  userInfo: { 
    id: '123456',
    name: '张三',
    mobile: '138****1234', // 已脱敏
    avatarUrl: 'https://example.com/avatar.png',
    // 其他用户信息...
  }
}
```

### 2.3 用户信息获取代码

```javascript
// 获取用户信息
export async function getUserInfos() {
  if (userStore().token) {
    let { data } = await uni.http.get(uni.api.getMemberInfo);
    userStore().setUserInfo(data.result);
  } else {
    userStore().clearUserInfo();
  }
}
```

### 2.4 敏感字段处理

| 字段名 | 处理方式 | 展示方式 |
|-------|---------|---------|
| mobile | 服务端脱敏 | 138****1234 |
| token | 明文存储 | 本地存储 |

## 3. 登录状态检查分析

### 3.1 全局验证函数

**文件位置**：`@/hooks/index.js`中的`navToBeforeLogin`函数

**验证逻辑**：
```javascript
// 跳转前验证登录状态
export function navToBeforeLogin(url = '', shoudRecord = false) {
  if (!userStore().token) {
    if (shoudRecord) {
      let pages = getCurrentPages();
      let currentFullPath = pages[pages.length - 1]['$page']['fullPath']; //页面路径(带参数)
      const recordFullPagePathStore = recordFullPagePath();
      //如果当前页面不在登录页 则记录当前页面信息后跳转登录页
      if (currentFullPath.indexOf('login') == -1) {
        recordFullPagePathStore.setPageFullPath(currentFullPath);
      }
    }
    uni.reLaunch({
      url: '/pages/login/login',
    });
  } else if (url) {
    navTo(url);
  }
}
```

### 3.2 请求拦截器

**文件位置**：`@/hooks/request.js`

**请求前拦截**：
```javascript
// 请求前添加Token
http.interceptors.request.use(config => {
  config.header = {
    // ...其他头信息
    "X-AUTH-TOKEN": uni.getStorageSync('token') || '',
    ...config.header,
  }
  return config
})
```

**请求后拦截**：
```javascript
// 响应拦截处理Token失效
http.interceptors.response.use(response => {
  if (response.data?.code == 666) {
    store.clearUserInfo();
    store.clearToken()
    toLoginAndBackPage()
    setTimeout(() => {
      uni.hideLoading()
    }, 500)
    return Promise.reject(response)
  } 
  // ...其他状态码处理
})
```

### 3.3 登录状态检查流程图

```mermaid
graph TD
    A[发起请求] --> B{Token存在?}
    B -->|是| C[添加Token到请求头]
    B -->|否| D[不添加Token]
    C --> E[发送请求]
    D --> E
    E --> F{响应状态码}
    F -->|666| G[清除用户信息]
    G --> H[跳转登录页]
    F -->|200| I[正常返回数据]
    F -->|500| J[显示错误提示]
```

### 3.4 页面拦截逻辑

**实现方式**：通过`navToBeforeLogin`函数在需要登录的页面跳转前进行验证

**记录页面路径**：使用`recordFullPagePath`存储当前页面路径，登录成功后可以跳回原页面

## 4. 安全专项检查

### 4.1 Token存储安全性

| 检查项 | 结果 | 风险等级 | 说明 |
|-------|------|---------|------|
| Token存储位置 | uni.setStorageSync | 中 | 小程序环境相对安全，但未加密 |
| Token传输方式 | 请求头 | 低 | 标准做法，通过HTTPS传输 |
| Token失效处理 | 有 | 低 | 有完善的失效处理机制 |

### 4.2 敏感数据传输

| 检查项 | 结果 | 风险等级 | 说明 |
|-------|------|---------|------|
| HTTPS传输 | 是 | 低 | 所有请求均使用HTTPS |
| 手机号传输 | 明文 | 高 | 验证码请求中手机号未加密 |
| 位置信息传输 | 明文 | 中 | 经纬度信息明文传输 |

### 4.3 后台运行安全

**代码位置**：`App.vue`中的`onShow`和`onHide`生命周期

```javascript
onShow: function() {
  // 应用进入前台
  if (this.globalData.backgroundStart) {
    // 计算本次后台运行时长并累加
    const bgDuration = Math.floor((Date.now() - this.globalData.backgroundStart) / 1000);
    this.globalData.totalBackground += bgDuration;
    this.globalData.backgroundStart = null;
  }
  //如果进入后台时间过长，则清空分享相关信息
  if (this.globalData.totalBackground > uni.env.CLEAR_SHARE_CACHE_TIME * 60) {
    getAllBeShared().setInfo({});
    getAllBeShared().setShareDiscountRatio("");
  }
}
```

### 4.4 风险评估报告

| 风险项 | 风险描述 | 风险等级 | 建议措施 |
|-------|---------|---------|---------|
| Token明文存储 | Token未加密存储在本地 | 中 | 使用加密存储或增加有效期控制 |
| 手机号明文传输 | 验证码请求中手机号未加密 | 高 | 对手机号进行加密后传输 |
| 缺少Token刷新机制 | 无自动刷新Token机制 | 中 | 增加Token自动刷新功能 |
| 缺少防重复登录控制 | 可多设备同时登录 | 低 | 增加设备指纹或限制登录设备数 |

## 5. 优化建议输出

### 5.1 安全性优化

🔥 **紧急修复项**：

1. **手机号传输加密**：
   ```javascript
   // 修改前
   await uni.http.post(uni.api.appVerificationCode, {
     phone: submitInfo.value.phone,
     // ...其他参数
   });
   
   // 修改后
   await uni.http.post(uni.api.appVerificationCode, {
     phone: encryptData(submitInfo.value.phone), // 使用加密函数
     // ...其他参数
   });
   ```

2. **Token加密存储**：
   ```javascript
   // 修改前
   uni.setStorageSync('token', token);
   
   // 修改后
   uni.setStorageSync('token', encryptData(token));
   // 读取时解密
   const token = decryptData(uni.getStorageSync('token') || '');
   ```

### 5.2 用户体验优化

⚙️ **建议改进项**：

1. **增加Token自动刷新机制**：
   ```javascript
   // 在请求拦截器中增加Token过期判断和刷新逻辑
   if (isTokenExpired(token)) {
     try {
       const newToken = await refreshToken();
       config.header["X-AUTH-TOKEN"] = newToken;
     } catch (error) {
       // 刷新失败，跳转登录页
       toLoginAndBackPage();
     }
   }
   ```

2. **优化登录状态检查**：
   ```javascript
   // 增加统一的登录状态检查函数
   export function checkLoginStatus() {
     const token = uni.getStorageSync('token');
     if (!token) return false;
     
     // 增加本地Token有效性检查
     if (isTokenExpired(token)) {
       userStore().clearToken();
       return false;
     }
     
     return true;
   }
   ```

3. **记住登录状态功能**：
   增加"记住登录状态"选项，允许用户在安全环境下保持登录状态。

### 5.3 性能优化

🚀 **性能提升项**：

1. **减少重复请求用户信息**：
   ```javascript
   // 增加缓存控制，避免短时间内多次请求用户信息
   let lastFetchTime = 0;
   export async function getUserInfos(forceUpdate = false) {
     const now = Date.now();
     if (!forceUpdate && now - lastFetchTime < 60000) {
       return userStore().userInfo; // 返回缓存数据
     }
     
     if (userStore().token) {
       let { data } = await uni.http.get(uni.api.getMemberInfo);
       userStore().setUserInfo(data.result);
       lastFetchTime = now;
     } else {
       userStore().clearUserInfo();
     }
   }
   ```

2. **优化登录页加载速度**：
   预加载登录页资源，减少用户等待时间。

### 5.4 代码质量优化

🧹 **代码质量提升项**：

1. **统一错误处理**：
   ```javascript
   // 创建统一的错误处理函数
   export function handleApiError(error, customOptions = {}) {
     const defaultOptions = {
       showToast: true,
       redirectToLogin: false,
     };
     const options = { ...defaultOptions, ...customOptions };
     
     // 根据错误码处理
     switch (error.data?.code) {
       case 666:
         userStore().clearUserInfo();
         userStore().clearToken();
         if (options.redirectToLogin) {
           toLoginAndBackPage();
         }
         break;
       // 其他错误码处理...
     }
     
     if (options.showToast) {
       uni.showToast({
         icon: 'none',
         title: error.data?.message || '网络异常，请稍后再试'
       });
     }
     
     return Promise.reject(error);
   }
   ```

2. **增加代码注释**：
   为关键函数添加详细注释，提高代码可维护性。

### 5.5 自动化优化项

🤖 **自动化建议**：

1. **自动检测敏感信息泄露**：
   开发构建流程中增加敏感信息检测工具。

2. **自动化登录状态检测**：
   ```javascript
   // 定时检查登录状态
   setInterval(() => {
     if (userStore().token && isTokenExpired(userStore().token)) {
       // 尝试刷新Token
       refreshToken().catch(() => {
         userStore().clearToken();
         uni.showToast({
           icon: 'none',
           title: '登录已过期，请重新登录'
         });
       });
     }
   }, 300000); // 5分钟检查一次
   ```

## 总结

本次分析全面覆盖了心福商城小程序的用户系统，包括登录流程、用户信息获取、登录状态检查和安全性评估。通过分析发现了一些安全风险和优化空间，并提供了具体的改进建议。

建议优先解决手机号明文传输等高风险问题，同时逐步实施Token加密存储、自动刷新机制等优化措施，以提升系统的安全性和用户体验。
