# 🔧 UniApp 调试与维护实战宝典

## 🚀 本地开发启动命令

### 微信小程序开发

```bash
# HBuilderX 内置运行方式
1. 打开项目
2. 点击工具栏的"运行"按钮
3. 选择"运行到小程序模拟器" -> "微信开发者工具"

# 命令行方式（需先安装 cli）
# 安装 cli
npm install -g @vue/cli
npm install -g @dcloudio/vue-cli-plugin-uni

# 运行到微信小程序
npm run dev:mp-weixin

# 打包微信小程序
npm run build:mp-weixin
```

### 微信开发者工具设置

1. 确保已安装[微信开发者工具](https://developers.weixin.qq.com/miniprogram/dev/devtools/download.html)
2. 在 HBuilderX 中配置微信开发者工具路径：工具 -> 设置 -> 运行配置 -> 小程序运行配置
3. 首次运行需要在微信开发者工具中扫码登录并信任项目

## 🔥 紧急问题处理

### HBuilderX 白屏/闪退
🔥 **问题**：HBuilderX 启动白屏或闪退
✅ **解决方案**：
  - 删除 HBuilderX 安装目录下的 `plugins` 文件夹，重启 HBuilderX 自动重新下载插件
  - 检查 HBuilderX 版本是否与项目兼容，建议使用与项目创建时相同的主版本

### 编译失败
🔥 **问题**：编译失败，控制台报错
✅ **解决方案**：
  - 删除 `unpackage` 目录和 `node_modules` 目录
  - 重新执行 `npm install`
  - 重新编译项目

### 真机预览失败
🔥 **问题**：无法真机预览或预览后白屏
✅ **解决方案**：
  - 检查 manifest.json 中的 AppID 配置是否正确
  - 检查微信开发者工具是否为最新版本
  - 尝试清除微信开发者工具缓存：工具 -> 清除缓存 -> 清除全部缓存

## 📱 真机调试注意事项

### Android 设备连接

```bash
# 查看已连接设备
adb devices

# 如果显示 unauthorized，需要在手机上确认授权调试

# 如果设备未识别，尝试重启 adb 服务
adb kill-server
adb start-server

# 特定设备连接问题
# 小米手机：开启"USB调试（安全设置）"
# OPPO/vivo：开启"USB调试"后，再次连接时选择"传输文件"模式
```

### iOS 设备连接

- 确保 Mac 已安装最新版 iTunes
- 首次连接时在 iOS 设备上点击"信任此电脑"
- 对于 iOS 14+ 设备，需要在设置中允许调试

### 真机调试常见问题

1. **网络请求失败**：检查手机与电脑是否在同一网络环境
2. **热更新不生效**：尝试完全退出应用后重新打开
3. **调试器连接断开**：检查 USB 连接是否稳定，或尝试使用无线调试

## 🐞 常见编译错误解决方案

### Sass 版本冲突

🔥 **问题**：`this.getOptions is not a function` 或 Sass 相关错误
✅ **解决方案**：
```bash
# 卸载当前 sass-loader 和 node-sass
npm uninstall sass-loader node-sass

# 安装指定版本
npm install sass-loader@10.1.1 node-sass@6.0.1 --save-dev
```

### 组件未注册错误

🔥 **问题**：`Unknown custom element: <xxx>` 组件未注册错误
✅ **解决方案**：
- 检查组件引入路径是否正确
- 确认组件是否已在 `components` 选项中注册
- 对于 uni_modules 中的组件，检查 pages.json 中的 `easycom` 配置

### 条件编译问题

🔥 **问题**：条件编译代码在特定平台不生效
✅ **解决方案**：
- 确保条件编译注释格式正确：`<!-- #ifdef MP-WEIXIN -->` 和 `<!-- #endif -->`
- 检查平台标识是否正确：`MP-WEIXIN`、`APP-PLUS` 等
- 注意 JS 中的条件编译需要使用 `//#ifdef` 和 `//#endif` 格式

### 微信小程序特有错误

🔥 **问题**：`xxx is not defined` 或 `xxx of undefined`
✅ **解决方案**：
- 检查微信开发者工具版本是否最新
- 尝试在微信开发者工具中清除本地缓存
- 检查项目中是否使用了微信小程序不支持的 API 或语法

## ⚡ 性能优化检查点

### setData 滥用检测

🔥 **问题**：页面卡顿，尤其在滚动或频繁操作时
✅ **检查点**：
- 避免在 `onPageScroll` 等高频触发函数中直接修改数据
- 使用节流或防抖处理频繁触发的事件
- 减少不必要的数据更新，特别是大型数据对象

```js
// 不推荐
onPageScroll(e) {
  this.scrollTop = e.scrollTop; // 直接修改数据，可能导致频繁重渲染
}

// 推荐（使用节流）
import { throttle } from '@/utils/throttle';
onPageScroll: throttle(function(e) {
  this.scrollTop = e.scrollTop;
}, 200)
```

### 长列表优化

🔥 **问题**：长列表滚动卡顿
✅ **解决方案**：
- 使用 `recycle-list` 组件（仅 App 端支持）
- 实现虚拟列表，只渲染可视区域的数据
- 分页加载数据，避免一次性加载过多数据

### 图片资源优化

🔥 **问题**：页面加载慢，尤其是有多张图片的页面
✅ **解决方案**：
- 使用适当尺寸的图片，避免过大图片
- 图片懒加载：`<image lazy-load="true" />`
- 使用 CDN 加速图片加载
- 考虑使用 WebP 等更高效的图片格式

### 包体积优化

🔥 **问题**：小程序包体积超过限制（2MB 主包 / 20MB 总包体积）
✅ **解决方案**：
- 实施分包策略，将非首屏页面放入分包
- 压缩图片资源
- 移除未使用的组件和代码
- 使用按需引入第三方库

## 🛠️ 微信开发者工具配置要点

### 基础配置

1. **ES6 转 ES5**：建议开启，提高兼容性
2. **增强编译**：开启可优化小程序包体积
3. **上传时自动压缩**：建议开启
4. **样式自动补全**：建议开启
5. **代码自动热重载**：开发时建议开启

### 调试配置

1. **不校验合法域名**：开发阶段建议开启，生产环境必须关闭
2. **不检验 webview 域名**：同上
3. **开启 HTTP 请求调试**：帮助分析网络请求问题
4. **Console 面板日志级别**：开发时设为 `Debug`，便于查看所有日志

### 性能调试工具

1. **Audits 面板**：分析小程序性能问题
   - 启动耗时分析
   - 页面渲染分析
   - 内存泄漏检测
   
2. **Memory 面板**：分析内存占用情况
   - 定位内存泄漏
   - 分析对象引用关系

3. **Network 面板**：分析网络请求
   - 请求耗时分析
   - 请求大小优化
   - 并发请求控制

## 💡 推荐调试技巧

### 日志与断点调试

```js
// 1. 使用 console 输出不同级别日志
console.log('普通信息');
console.info('提示信息');
console.warn('警告信息');
console.error('错误信息');

// 2. 使用条件断点（在微信开发者工具中右键行号设置）

// 3. 使用 uni.report() 上报关键数据
uni.report('pageShow', {
  pageId: 'home',
  timestamp: Date.now()
});
```

### 远程调试

1. **真机调试**：在微信开发者工具中点击"编译"，使用手机微信扫码预览
2. **远程调试**：预览后点击右上角"..."，选择"打开调试"
3. **Whistle 代理**：配置手机代理，抓包分析请求

### 进阶使用技巧

1. **精准定位**：用 `@src/pages/index/index.vue` 指定具体文件分析
2. **对比分析**："请对比 pages.json 和实际路由跳转的逻辑差异"
3. **变更影响**："如果升级 uniapp 到 3.0，哪些组件需要适配？"
4. **安全检查**："列出所有使用的原生 API，评估隐私合规风险"

## 📋 常用命令速查表

| 功能 | 命令/操作 |
|-----|----------|
| 运行到微信开发者工具 | `npm run dev:mp-weixin` |
| 打包微信小程序 | `npm run build:mp-weixin` |
| 查看已连接设备 | `adb devices` |
| 重启 adb 服务 | `adb kill-server && adb start-server` |
| 清除 npm 缓存 | `npm cache clean --force` |
| 查看微信开发者工具日志 | 开发者工具 -> 菜单 -> 帮助 -> 开发者工具日志 |
| 小程序真机调试 | 点击"预览"按钮，使用微信扫描二维码 |

## 🚨 常见错误码速查表

| 错误码 | 描述 | 解决方案 |
|-------|------|---------|
| -10x | 系统错误码 | 重启微信开发者工具或设备 |
| -20x | 网络请求错误 | 检查网络连接和服务器状态 |
| 1000x | 小程序登录相关错误 | 检查 AppID 配置和登录流程 |
| 2000x | 小程序支付相关错误 | 检查支付参数和商户配置 |

## 🔍 示例组合提问

```prompt
请帮我全面解析商品详情页模块：
1. 定位文件路径 @src/pages/productInfo/productInfo.vue
2. 分析使用的组件及数据依赖
3. 绘制从首页跳转到该页面的参数传递流程图
4. 检查支付按钮的事件处理逻辑
5. 评估当前页面的 setData 使用是否合理
请用红色标注性能隐患，用绿色标记最佳实践
```
