# 用户信息获取示例代码

本文档提供了在项目不同场景下获取和使用用户信息的示例代码，可作为开发新页面时的参考。

## 1. 基础用户信息获取

以下是在页面中获取和使用用户信息的基础示例：

```javascript
<template>
  <view class="page">
    <!-- 用户已登录时显示用户信息 -->
    <view v-if="users.token">
      <view class="user-info">
        <image class="avatar" :src="users.userInfo.avatarUrl || '/static/missing-face.png'"></image>
        <view class="info">
          <text class="name">{{ users.userInfo.nickName }}</text>
          <text class="phone">{{ users.userInfo.phone }}</text>
        </view>
      </view>
    </view>
    
    <!-- 用户未登录时显示登录提示 -->
    <view v-else class="login-tip" @click="goToLogin">
      <text>请登录查看个人信息</text>
    </view>
  </view>
</template>

<script setup>
import { onShow } from '@dcloudio/uni-app';
import { userStore } from '@/store/index.js';
import { getUserInfos, navTo } from '@/hooks';

// 获取用户 store
const users = userStore();

// 页面显示时获取最新用户信息
onShow(() => {
  getUserInfos();
});

// 跳转到登录页
function goToLogin() {
  navTo('/pages/login/login');
}
</script>
```

## 2. 用户中心页面示例

用户中心页面通常需要展示用户的详细信息和相关数据：

```javascript
<template>
  <view class="user-center">
    <!-- 用户基本信息区域 -->
    <view class="user-header">
      <image class="bg" :src="imgUrl + '/static/user/bg.png'"></image>
      <view class="user-info">
        <image class="avatar" :src="users.userInfo.avatarUrl || '/static/missing-face.png'"></image>
        <view class="info" @click="navToBeforeLogin('/pages/userProfile/userProfile')">
          <template v-if="users.token">
            <view class="name">
              {{ users.userInfo.nickName }}
              <image src="@/static/right-arrow-white.png"></image>
            </view>
            <view class="extra">
              <image v-if="users.userInfo.isLoveAmbassador == 1" class="vip-icon" src="@/static/user/vip.png"></image>
              <text>创业业绩: {{ users.userInfo.totalPerformance || 0 }}</text>
            </view>
          </template>
          <view class="name" v-else>
            请登录
            <image src="@/static/right-arrow-white.png"></image>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 用户数据统计区域 -->
    <view class="user-stats">
      <!-- 这里可以展示订单数量、收藏数量等统计信息 -->
    </view>
    
    <!-- 功能菜单区域 -->
    <view class="menu-list">
      <!-- 各种功能入口 -->
    </view>
  </view>
</template>

<script setup>
import { onShow, onPullDownRefresh } from '@dcloudio/uni-app';
import { userStore, frontSetting } from '@/store/index.js';
import { getUserInfos, navTo, navToBeforeLogin } from '@/hooks';
import { computed, ref } from 'vue';

const imgUrl = uni.env.IMAGE_URL;
const users = userStore();
const loveAmbassador = computed(() => frontSetting().setting.loveAmbassador);
let info = ref({});

// 获取用户相关数量统计
async function getAboutCount() {
  if (users.token) {
    let { data } = await uni.http.get(uni.api.memberGoodAndOrderCount);
    info.value = data.result;
  } else {
    info.value = {};
  }
}

// 计算进度百分比
const progressBfb = computed(() => {
  if (users.token && loveAmbassador.value) {
    return parseFloat((users.userInfo.totalRechargeBalance / loveAmbassador.value * 100).toFixed(0));
  }
  return 0;
});

onShow(() => {
  getUserInfos();
  getAboutCount();
});

onPullDownRefresh(() => {
  getUserInfos();
  getAboutCount();
  setTimeout(() => {
    uni.stopPullDownRefresh();
  }, 1500);
});
</script>
```

## 3. 用户资料编辑页面示例

编辑用户资料时，需要先获取当前用户信息，然后提交修改：

```javascript
<template>
  <view class="edit-profile">
    <!-- 表单区域 -->
    <view class="form">
      <!-- 头像 -->
      <view class="form-item">
        <text class="label">头像</text>
        <button class="avatar-wrapper" open-type="chooseAvatar" @chooseavatar="onChooseAvatar">
          <image class="avatar" :src="userInfo.headPortrait || '/static/missing-face.png'"></image>
        </button>
      </view>
      
      <!-- 昵称 -->
      <view class="form-item">
        <text class="label">昵称</text>
        <input type="nickname" v-model="userInfo.nickName" placeholder="请输入昵称" />
      </view>
      
      <!-- 性别 -->
      <view class="form-item" @click="changeShowPicker">
        <text class="label">性别</text>
        <view class="value">
          {{ userSex }}
          <image src="@/static/right-arrow-gray.png"></image>
        </view>
      </view>
      
      <!-- 地区 -->
      <view class="form-item" @click="changeShowPicker">
        <text class="label">所在地区</text>
        <view class="value">
          {{ userInfo.areaAddr || '请选择' }}
          <image src="@/static/right-arrow-gray.png"></image>
        </view>
      </view>
    </view>
    
    <!-- 保存按钮 -->
    <view class="save-btn" @click="save">保存</view>
    
    <!-- 选择器组件 -->
    <!-- ... -->
  </view>
</template>

<script setup>
import { ref, computed, nextTick } from 'vue';
import { onLoad } from '@dcloudio/uni-app';
import { userStore } from '@/store/index.js';
import { getUserInfos, toUpPage } from '@/hooks';
import { uploadImg } from '@/utils';

const users = userStore();
const imgUrl = uni.env.IMAGE_URL;

// 用户信息表单数据
let userInfo = ref({
  nickName: users.userInfo.nickName,
  headPortrait: users.userInfo.avatarUrl,
  sex: users.userInfo.sex || '0',
  areaAddr: users.userInfo.areaAddr
});

// 性别字典
let sexDictsList = ref([]);
const userSex = computed(() => {
  return sexDictsList.value.find(i => i.value == userInfo.value.sex)?.text || '未知';
});

// 保存用户信息
async function save() {
  // 表单验证
  if (!userInfo.value.nickName) {
    uni.showToast({ title: '请填写昵称', icon: "none" });
    return;
  }
  if (!userInfo.value.headPortrait) {
    uni.showToast({ title: '请上传头像', icon: "none" });
    return;
  }
  
  uni.showLoading({ mask: true });
  
  // 处理头像上传
  const oldHeadPortrait = users.userInfo.avatarUrl;
  if (oldHeadPortrait !== userInfo.value.headPortrait) {
    let data = await uploadImg(userInfo.value.headPortrait);
    userInfo.value.headPortrait = imgUrl + data.message;
    await nextTick();
  }
  
  // 提交修改
  await uni.http.post(uni.api.completeInformation, userInfo.value);
  
  // 更新用户信息
  getUserInfos();
  
  uni.showToast({ icon: "none", title: '修改成功~' });
  setTimeout(() => {
    toUpPage();
  }, 500);
}

// 选择头像
function onChooseAvatar(e) {
  userInfo.value.headPortrait = e.detail.avatarUrl;
}

// 获取性别字典
async function getSexDicts() {
  let { data } = await uni.http.get(`${uni.api.getDicts}?code=sex`);
  sexDictsList.value = data.result || [];
}

onLoad(() => {
  getUserInfos();
  getSexDicts();
});
</script>
```

## 4. 需要登录权限的页面示例

对于需要登录才能访问的页面，可以使用 `navToBeforeLogin` 函数进行权限控制：

```javascript
<template>
  <view class="heart-meter">
    <!-- 助力值信息 -->
    <view class="heart-meter-wrap" @click="navTo('/pages/heartFeedback/heartFeedback')">
      <view class="heart-meter-wrap-desc">
        当前助力值 <image src="@/static/right-arrow-white.png"></image>
      </view>
      <view class="heart-meter-wrap-num">
        {{ users.userInfo.balance || 0 }}
      </view>
      <view class="heart-meter-wrap-btnAll">
        <view class="heart-meter-wrap-btnAll-first" @click.stop="navTo('/pages/recharge/recharge')">
          助力
        </view>
        <view class="heart-meter-wrap-btnAll-second" @click.stop="navTo('/pages/withDraw/withDraw')">
          结算
        </view>
      </view>
    </view>
    
    <!-- 其他助力值相关信息 -->
  </view>
</template>

<script setup>
import { onShow } from '@dcloudio/uni-app';
import { userStore } from '@/store/index.js';
import { getUserInfos, navTo, navToBeforeLogin } from '@/hooks';

const users = userStore();

// 页面显示时获取最新用户信息
onShow(() => {
  getUserInfos();
});

// 跳转到需要登录的页面
function goToOrderList() {
  navToBeforeLogin('/pages/orderList/orderList', true);
}
</script>
```

## 5. App.vue 中的用户信息初始化

在应用启动时，应该初始化用户信息：

```javascript
<script>
import { frontSetting, getAllBeShared } from '@/store';
import { getUserInfos } from '@/hooks';

export default {
  // 全局存储计时相关数据
  globalData: {
    backgroundStart: null, // 当前后台开始时间戳
    totalBackground: 0 // 累计后台时长（秒）
  },
  onLaunch: async function() {
    // 应用启动时获取用户信息
    getUserInfos();
    
    // 获取程序设置
    let { data } = await uni.http.get(uni.api.findMarketingDistributionSetting);
    frontSetting().setSetting(data.result);
  },
  onShow: function() {
    // 应用进入前台
    // ...
  },
  onHide: function() {
    // 应用进入后台
    // ...
  }
}
</script>
```

## 6. 分享功能中使用用户信息

在分享功能中，可能需要传递当前用户信息：

```javascript
// 分享相关函数
function getDefaultParam(shareType = 1) {
  return {
    TmemberName: userStore().userInfo.nickName || '', // 推广人用户名
    TmemberHeadPortrait: userStore().userInfo.avatarUrl || '', // 推广人用户头像
    phone: userStore().userInfo.phone || '', // 推广人用户手机号
    tMemberId: userStore().userInfo.id || '', // 推广人用户id
    sysUserId: setSaleChannelStoreInfo().sysUserId || setSaleChannelStoreInfo().intoStoreForSysUserId || userStore().userInfo.sysUserId || '', // 绑定店铺
    shareType, // 分享类型 1微信分享 2二维码分享
  };
}

// 分享给朋友
function onShareAppMessage() {
  const shareParams = getDefaultParam();
  return {
    title: '心福商城 - 分享好物',
    path: `/pages/index/index?${Object.entries(shareParams).map(([key, value]) => `${key}=${encodeURIComponent(value)}`).join('&')}`,
    imageUrl: '/static/share-image.png'
  };
}
```

以上示例涵盖了项目中常见的用户信息获取和使用场景，可根据实际需求进行调整和扩展。
