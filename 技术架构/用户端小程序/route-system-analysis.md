# 🧭 uniapp路由系统解析报告

## 📋 页面路径与配置

通过分析`pages.json`文件，我发现项目包含以下主要页面：

### 主要页面
- **首页**: `pages/index/index`（应用启动页）
- **分类**: `pages/heartPool/heartPool`
- **个人中心**: `pages/user/user`
- **商品详情**: `pages/productInfo/productInfo`
- **确认订单**: `pages/confirmOrder/confirmOrder`
- **登录**: `pages/login/login`
- **验证码登录**: `pages/phoneLogin/phoneLogin`

### 页面配置特点
- 部分页面使用自定义导航栏（`"navigationStyle": "custom"`）
- 部分页面启用下拉刷新（`"enablePullDownRefresh": true`）
- 全局导航栏背景色为`#f5f5f5`

## 🔄 TabBar配置

```json
"tabBar": {
    "color": "#7A7E83",
    "selectedColor": "#000000",
    "borderStyle": "white",
    "backgroundColor": "#ffffff",
    "iconWidth": "30px",
    "list": [
        {
            "pagePath": "pages/index/index",
            "iconPath": "static/tabbar/index_btn.png",
            "selectedIconPath": "static/tabbar/index_btn_sel.png",
            "text": "首页"
        }, 
        {
            "pagePath": "pages/heartPool/heartPool",
            "iconPath": "static/tabbar/heartPool_btn.png",
            "selectedIconPath": "static/tabbar/heartPool_btn_sel.png",
            "text": "分类"
        },
        {
            "pagePath": "pages/user/user",
            "iconPath": "static/tabbar/user_btn.png",
            "selectedIconPath": "static/tabbar/user_btn_sel.png",
            "text": "个人"
        }
    ]
}
```

**注意**：购物车标签页（`pages/cart/cart`）已被注释掉，当前只有3个标签页

## 📊 页面跳转关系图

```mermaid
graph TD
    A[首页] -->|点击商品| B[商品详情]
    A -->|点击店铺| C[店铺首页]
    A -->|点击分类| D[分类页]
    A -->|点击个人| E[个人中心]
    
    B -->|立即购买| F[确认订单]
    F -->|提交订单| G[支付]
    G -->|支付成功| H[订单详情]
    
    E -->|未登录| I[登录页]
    I -->|手机号登录| J[验证码登录]
    I -->|微信登录| E
    
    E -->|我的团队| K[团队页]
    E -->|收益明细| L[收益明细]
    E -->|助力值| M[助力值]
    E -->|收货地址| N[收货地址]
    N -->|新增地址| O[地址编辑]
    
    E -->|我的包裹| P[订单列表]
    P -->|查看订单| H
    H -->|查看物流| Q[物流详情]
    
    subgraph 登录流程
        I
        J
    end
    
    subgraph 购买流程
        B
        F
        G
        H
    end
```

## 🔒 路由拦截逻辑

项目中实现了多种路由拦截和导航控制机制：

### 1. Promise拦截器
在`uni.promisify.adaptor.js`中，添加了全局拦截器处理Promise返回值：

```javascript
uni.addInterceptor({
  returnValue (res) {
    if (!(!!res && (typeof res === "object" || typeof res === "function") && typeof res.then === "function")) {
      return res;
    }
    return new Promise((resolve, reject) => {
      res.then((res) => {
        if (!res) return resolve(res) 
        return res[0] ? reject(res[0]) : resolve(res[1])
      });
    });
  },
});
```

### 2. 登录拦截
在`hooks/index.js`中实现了登录拦截逻辑：

```javascript
// 登录拦截导航
export function navToBeforeLogin(url = '', shoudRecord = false) {
  if (!userStore().token) {
    if (shoudRecord) {
      let pages = getCurrentPages();
      let currentFullPath = pages[pages.length - 1]['$page']['fullPath'];
      const recordFullPagePathStore = recordFullPagePath();
      if (currentFullPath.indexOf('login') == -1) {
        recordFullPagePathStore.setPageFullPath(currentFullPath);
      }
    }
    uni.reLaunch({
      url: '/pages/login/login',
    });
  } else if (url) {
    navTo(url);
  }
}
```

### 3. 页面返回控制
提供了智能返回上一页的逻辑，如果没有上一页则返回首页：

```javascript
export function toUpPage() {
  if (getCurrentPages().length == 1) {
    uni.switchTab({
      url: '/pages/index/index',
    });
  } else {
    uni.navigateBack();
  }
}
```

### 4. 登录后返回原页面
记录登录前的页面路径，登录成功后返回：

```javascript
export function toLoginAndBackPage() {
  let pages = getCurrentPages();
  let currentFullPath = pages[pages.length - 1]['$page']['fullPath'];
  const recordFullPagePathStore = recordFullPagePath();
  if (currentRoute.indexOf('login') == -1) {
    recordFullPagePathStore.setPageFullPath(currentFullPath);
    uni.reLaunch({
      url: '/pages/login/login',
    });
  }
}
```

## 🚀 导航方法封装

项目封装了多种导航方法，简化页面跳转操作：

1. **普通导航**：`navTo(url, callback)`
2. **重定向**：`redTo(url)`
3. **Tab切换**：`switchTo(url)`
4. **登录拦截导航**：`navToBeforeLogin(url, shouldRecord)`
5. **商品详情导航**：`navToGoodDetail(item)`
6. **店铺首页导航**：`navToStoreIndex(item)`
7. **手机验证拦截**：`authPhone(type)`

## 📱 页面预加载策略

项目中未发现明确的`onLoadPreload`预加载策略实现。仅在`zebra-swiper`组件中有图片预加载相关配置：

```javascript
preloadImages: true
```

## 📝 总结

1. **路由结构**：基于uni-app标准路由，通过`pages.json`配置页面路径和样式
2. **导航栏**：混合使用默认导航栏和自定义导航栏
3. **拦截机制**：实现了登录拦截、Promise结果拦截等多种拦截逻辑
4. **导航封装**：封装了多种导航方法，简化页面跳转并处理各种场景
5. **状态保持**：通过Pinia状态管理记录页面路径，实现登录后返回原页面功能

这种路由设计使应用在保持灵活性的同时，能够有效控制用户访问权限，提供流畅的导航体验。
