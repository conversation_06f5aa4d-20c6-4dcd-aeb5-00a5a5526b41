# 心福商城小程序分享与邀请好友功能分析

## 1. 需求说明与流程

### 1.1 功能概述

心福商城小程序的分享与邀请好友功能主要围绕以下几个核心场景展开：

1. **商品分享**：用户可以分享商品给好友，好友通过分享链接购买商品后，分享者可获得助力值（佣金）
2. **邀请注册**：用户可以邀请好友注册成为新用户，建立推广关系
3. **个人海报分享**：用户可以生成并分享个人专属海报，通过海报进入小程序的用户会与分享者建立绑定关系

### 1.2 业务流程

#### 1.2.1 商品分享流程

```mermaid
graph TD
    A[用户浏览商品] --> B{点击分享按钮}
    B --> C[选择分享方式]
    C --> D1[微信好友分享]
    C --> D2[生成分享海报]
    D1 --> E1[好友接收分享链接]
    D2 --> E2[保存海报到相册]
    E2 --> E3[用户分享海报给好友]
    E3 --> E1
    E1 --> F[好友点击进入小程序]
    F --> G[系统记录分享关系]
    G --> H[好友浏览商品]
    H --> I{好友下单购买}
    I -->|是| J[分享者获得助力值]
    I -->|否| K[无奖励]
```

#### 1.2.2 邀请注册流程

```mermaid
graph TD
    A[用户进入我的海报页面] --> B[系统生成专属邀请码]
    B --> C[用户保存并分享海报]
    C --> D[好友扫码进入小程序]
    D --> E[系统记录邀请关系]
    E --> F{好友是否注册}
    F -->|是| G[建立永久绑定关系]
    F -->|否| H[好友注册账号]
    H --> G
    G --> I[好友后续消费]
    I --> J[邀请人获得团队业绩和分享佣金]
```

### 1.3 核心业务规则

1. **分享参数传递**：
   - 分享链接中包含分享者ID（tMemberId）、昵称、头像等信息
   - 分享商品时可设置分享折扣比例（最高50%）

2. **佣金计算规则**：
   - 被分享用户购买商品后，分享者可获得一定比例的助力值（佣金）
   - 佣金分为品牌馆佣金、生活馆佣金和创业馆佣金三种类型

3. **团队关系**：
   - 通过分享建立的用户关系会形成团队关系
   - 团队成员的消费会为团队长贡献业绩和佣金

## 2. 技术实现流程

### 2.1 分享功能实现

#### 2.1.1 分享参数构建

项目中通过 `getDefaultParam` 函数构建基础分享参数：

```javascript
function getDefaultParam(shareType = 1) {
  return {
    TmemberName: userStore().userInfo.nickName || '', // 推广人用户名
    TmemberHeadPortrait: userStore().userInfo.avatarUrl || '', // 推广人用户头像
    phone: userStore().userInfo.phone || '', // 推广人用户手机号
    tMemberId: userStore().userInfo.id || '', // 推广人用户id
    sysUserId: setSaleChannelStoreInfo().sysUserId || 
              setSaleChannelStoreInfo().intoStoreForSysUserId || 
              userStore().userInfo.sysUserId || '', // 绑定店铺
    shareType, // 分享类型 1微信分享 2二维码分享
  };
}
```

#### 2.1.2 分享方法封装

项目使用 `shareAbout` 函数封装分享逻辑，支持微信分享和海报分享：

```mermaid
graph TD
    A[调用shareAbout函数] --> B[构建分享参数]
    B --> C[处理分享图片和标题]
    C --> D[注册onShareAppMessage钩子]
    D --> E[返回分享对象]
    A --> F[提供getQrCode方法]
    F --> G[调用后端接口生成小程序码]
    G --> H[返回小程序码URL]
```

#### 2.1.3 分享海报生成

商品分享海报生成流程：

1. 用户点击"保存海报"按钮
2. 调用 `getQrCode()` 方法请求后端生成小程序码
3. 使用 `l-painter` 组件绘制海报（包含商品图片、小程序码、用户头像等）
4. 将海报保存到相册

### 2.2 分享参数处理与关系绑定

#### 2.2.1 分享参数接收与处理

当用户通过分享链接进入小程序时，系统会处理URL参数或场景值：

```mermaid
graph TD
    A[用户进入页面] --> B{检查URL参数}
    B -->|有scene参数| C[解析scene值]
    C --> D[调用findSysSmallcodeById接口]
    D --> E[解析param参数]
    B -->|有shareType参数| F[直接处理URL参数]
    E --> G[提取分享者信息]
    F --> G
    G --> H[存储到Pinia]
    H --> I[页面正常加载]
```

#### 2.2.2 分享关系存储

分享关系通过 Pinia store 进行临时存储：

```javascript
// 存储分享信息到 Pinia
getAllBeShared().setInfo(obj);
// 存储分享折扣比例
getAllBeShared().setShareDiscountRatio(param.shareDiscountRatio);
```

#### 2.2.3 请求拦截器中的参数传递

在请求拦截器中，会将分享关系信息添加到请求头：

```javascript
http.interceptors.request.use(config => {
  config.header = {
    // ...其他头信息
    sysUserId: getAllBeSharedStore.info?.sysUserId || 
              setSaleChannelStoreInfoStore.sysUserId ||
              setSaleChannelStoreInfoStore.intoStoreForSysUserId,
    tMemberId: getAllBeSharedStore.info?.tMemberId || '',
    shareDiscountRatio: getAllBeSharedStore.shareDiscountRatio || '',
    // ...其他头信息
  }
  return config;
})
```

### 2.3 佣金与团队管理

#### 2.3.1 佣金查询接口

项目提供了多个与佣金和团队相关的接口：

- `distributionCommission`: 获取分销佣金统计
- `businessPerformance`: 获取创业业绩统计
- `teamDetail`: 获取团队明细列表
- `distributionOrders`: 获取分销订单列表
- `findMemberRechargeRecordProtomerList`: 查看佣金明细

#### 2.3.2 团队管理页面

团队管理页面展示了团队成员信息和佣金统计：

```mermaid
graph TD
    A[进入我的团队页面] --> B[选择时间范围]
    B --> C[请求佣金统计数据]
    C --> D[请求业绩统计数据]
    D --> E[请求团队成员列表]
    E --> F[展示数据]
    F --> G[用户可查看详情]
    G --> H[跳转到佣金明细页面]
```

## 3. 关键接口说明

### 3.1 分享相关接口

| 接口名称 | 接口路径 | 功能描述 | 参数说明 |
|---------|---------|---------|---------|
| 获取页面二维码 | after/weixin/getQrCodeByPage | 生成带参数的小程序码 | page: 页面路径<br>param: 分享参数JSON字符串 |
| 解析小程序码 | front/sysSmallcode/findSysSmallcodeById | 根据scene值获取完整参数 | id: scene值 |
| 查看海报 | after/member/findMemberPromoter | 获取用户专属海报信息 | 无需特殊参数 |

### 3.2 佣金与团队相关接口

| 接口名称 | 接口路径 | 功能描述 | 参数说明 |
|---------|---------|---------|---------|
| 获取分销佣金统计 | after/member/distributionCommission | 获取用户分销佣金统计 | dateRange: 时间范围<br>startDate: 开始日期<br>endDate: 结束日期 |
| 获取创业业绩统计 | after/member/businessPerformance | 获取用户创业业绩统计 | 同上 |
| 获取团队明细列表 | after/member/teamDetail | 获取团队成员列表 | 同上 |
| 获取分销订单列表 | after/member/distributionOrders | 获取分销订单列表 | 同上 |
| 查看佣金明细 | after/memberRechargeRecord/findMemberRechargeRecordProtomerList | 获取佣金明细 | pattern: 状态筛选 |

## 4. 前后端交互流程

### 4.1 分享商品流程

```mermaid
sequenceDiagram
    participant User as 用户
    participant Frontend as 前端
    participant Backend as 后端
    
    User->>Frontend: 点击商品分享按钮
    Frontend->>Frontend: 打开分享弹窗
    User->>Frontend: 输入分享折扣比例
    User->>Frontend: 点击"保存海报"
    Frontend->>Backend: 请求生成小程序码(getQrCodeByPage)
    Backend->>Backend: 生成小程序码并保存参数
    Backend->>Frontend: 返回小程序码URL
    Frontend->>Frontend: 使用l-painter绘制海报
    Frontend->>User: 保存海报到相册
    User->>User: 分享海报给好友
```

### 4.2 通过分享链接进入小程序流程

```mermaid
sequenceDiagram
    participant Friend as 好友
    participant Frontend as 前端
    participant Backend as 后端
    participant Pinia as Pinia Store
    
    Friend->>Frontend: 点击分享链接进入小程序
    Frontend->>Frontend: 解析URL参数或scene值
    alt 有scene参数
        Frontend->>Backend: 请求解析scene(findSysSmallcodeById)
        Backend->>Frontend: 返回完整参数
    end
    Frontend->>Pinia: 存储分享者信息
    Frontend->>Backend: 请求商品详情(带分享信息)
    Backend->>Backend: 记录分享关系
    Backend->>Frontend: 返回商品详情
    Frontend->>Friend: 展示商品详情页面
```

### 4.3 分享购买后佣金计算流程

```mermaid
sequenceDiagram
    participant Friend as 好友
    participant Frontend as 前端
    participant Backend as 后端
    participant Sharer as 分享者
    
    Friend->>Frontend: 下单购买商品
    Frontend->>Backend: 提交订单(带分享信息)
    Backend->>Backend: 创建订单并记录分享关系
    Friend->>Frontend: 支付订单
    Frontend->>Backend: 支付请求
    Backend->>Backend: 处理支付并计算佣金
    Backend->>Backend: 为分享者增加佣金
    Backend->>Sharer: 佣金到账通知
```

## 5. 总结与建议

### 5.1 现有功能总结

心福商城小程序的分享与邀请功能主要包括：

1. **商品分享**：支持微信分享和海报分享，可设置分享折扣
2. **个人海报**：用户可生成专属海报进行推广
3. **团队管理**：可查看团队成员和业绩统计
4. **佣金系统**：分享者可获得被分享用户购买产生的佣金

### 5.2 优化建议

1. **分享体验优化**：
   - 简化分享流程，减少操作步骤
   - 提供更多分享内容模板，增加吸引力

2. **数据分析增强**：
   - 增加分享转化率分析
   - 提供更详细的团队业绩分析

3. **奖励机制优化**：
   - 考虑增加阶梯式奖励机制
   - 增加特殊活动期间的额外奖励

4. **安全性提升**：
   - 增加分享参数签名机制，防止篡改
   - 完善防刷单机制，避免恶意刷佣金

### 5.3 技术实现建议

1. **代码优化**：
   - 将分享逻辑进一步模块化，提高复用性
   - 优化海报生成性能，减少绘制时间

2. **功能扩展**：
   - 增加分享到朋友圈功能（基于微信小程序新能力）
   - 支持更多分享渠道，如微信视频号等
