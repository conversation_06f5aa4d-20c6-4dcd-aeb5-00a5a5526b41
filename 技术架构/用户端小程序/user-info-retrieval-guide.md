# 用户信息获取指南

## 1. 用户信息存储机制

在心福商城小程序中，用户信息通过以下机制进行存储和管理：

### 1.1 登录流程与信息存储

当用户成功登录后，系统会：

1. 将 Token 存储到本地
2. 获取用户详细信息并存储到 Pinia store 中

```javascript
// 登录成功回调函数
export async function loginSuccessCallBack(data) {
  const recordFullPagePathStore = recordFullPagePath();
  const users = userStore();
  // 存储 Token 到 Pinia store 和本地存储
  users.setToken(data.result['X-AUTH-TOKEN']);
  // 获取用户信息
  getUserInfos();
  
  // 页面跳转逻辑...
}
```

### 1.2 用户信息存储结构

用户信息通过 Pinia store 进行管理，主要包含以下结构：

```javascript
// 用户信息 store 结构
userStore: {
  userInfo: {
    id: '123456',                                // 用户ID
    nickName: '张三',                            // 用户昵称
    phone: '138****1234',                       // 手机号（脱敏）
    avatarUrl: 'https://example.com/avatar.png', // 头像URL
    sex: '1',                                    // 性别
    areaAddr: '北京',                            // 所在地区
    balance: 100,                                // 助力值
    isLoveAmbassador: 0,                         // 是否为助梦家
    totalRechargeBalance: 500,                   // 总充值金额
    totalPerformance: 1000,                      // 创业业绩
    // 其他用户信息...
  },
  token: 'xxxxxxxx'  // 用户令牌
}
```

## 2. 获取用户信息的方法

### 2.1 基础获取方法

项目中提供了统一的用户信息获取方法 `getUserInfos()`，位于 `hooks/index.js` 中：

```javascript
// 获取用户信息
export async function getUserInfos() {
  if (userStore().token) {
    let { data } = await uni.http.get(uni.api.getMemberInfo);
    userStore().setUserInfo(data.result);
  } else {
    userStore().clearUserInfo();
  }
}
```

### 2.2 调用时机

应在以下场景调用 `getUserInfos()` 方法：

- 登录成功后（已在 `loginSuccessCallBack` 中调用）
- 应用启动时（在 `App.vue` 的 `onLaunch` 生命周期中）
- 用户页面显示时（在用户相关页面的 `onShow` 生命周期中）
- 需要刷新用户信息时（如修改用户资料后）

## 3. 在页面中使用用户信息

### 3.1 引入 userStore

在需要使用用户信息的页面中，首先引入 userStore：

```javascript
import { userStore } from '@/store/index.js';

// 在 setup 中获取 store 实例
const users = userStore();
```

### 3.2 访问用户信息

直接通过 `users.userInfo` 访问用户信息：

```javascript
// 示例：获取用户昵称
const userName = users.userInfo.nickName || '未登录';

// 示例：获取用户头像
const avatarUrl = users.userInfo.avatarUrl || '/static/missing-face.png';

// 示例：判断用户是否登录
const isLoggedIn = !!users.token;
```

### 3.3 页面示例

以下是在页面中使用用户信息的完整示例：

```javascript
<template>
  <view class="user-info">
    <view v-if="users.token">
      <image :src="users.userInfo.avatarUrl || '/static/missing-face.png'"></image>
      <text>{{ users.userInfo.nickName }}</text>
      <text>{{ users.userInfo.phone }}</text>
    </view>
    <view v-else>
      <text>请登录</text>
    </view>
  </view>
</template>

<script setup>
import { onShow } from '@dcloudio/uni-app';
import { userStore } from '@/store/index.js';
import { getUserInfos } from '@/hooks';

const users = userStore();

onShow(() => {
  // 页面显示时刷新用户信息
  getUserInfos();
});
</script>
```

## 4. 最佳实践

### 4.1 登录状态检查

在需要登录才能访问的页面或功能中，使用 `navToBeforeLogin` 函数进行登录状态检查：

```javascript
// 跳转前检查登录状态
import { navToBeforeLogin } from '@/hooks';

// 示例：跳转到需要登录的页面
function goToUserProfile() {
  navToBeforeLogin('/pages/userProfile/userProfile');
}
```

### 4.2 用户信息更新

在修改用户信息后，应立即调用 `getUserInfos()` 刷新用户信息：

```javascript
// 示例：修改用户资料后刷新信息
async function updateUserProfile(profileData) {
  await uni.http.post(uni.api.completeInformation, profileData);
  // 更新后刷新用户信息
  getUserInfos();
}
```

### 4.3 避免重复请求

为避免频繁请求用户信息，可以在合适的时机调用 `getUserInfos()`，而不是在每个页面都调用：

- 应用启动时
- 用户页面显示时
- 用户信息可能发生变化时（如登录、修改资料）

## 5. 注意事项

1. **登录状态判断**：通过 `users.token` 判断用户是否登录，而不是通过 `users.userInfo`
2. **默认值处理**：访问用户信息时应提供默认值，避免空值导致的显示问题
3. **信息安全**：敏感信息（如手机号）已在服务端进行脱敏处理
4. **Token 失效处理**：系统已在请求拦截器中处理 Token 失效情况（错误码 666）

## 6. 优化建议

1. **增加用户信息缓存**：可以考虑将用户信息也存储到本地，减少请求次数
2. **添加用户信息持久化**：目前只有 Token 进行了持久化，可以考虑也对用户信息进行持久化
3. **增加 Token 自动刷新机制**：当 Token 即将过期时自动刷新，提高用户体验
