# 首页热销商品列表接入实施记录

## 1. 需求概述

首页热销商品列表目前显示的是静态数据，需要对接后端API，实现实时显示店铺热销商品数据。要求使用商品管理中的"在售中"列表接口，并传入参数 pattern=1（按销量排序），获取前五个热销商品。

## 2. 实施方案

### 2.1 技术方案

1. 使用商品管理中的"在售中"列表接口 `/back/goodList/queryPageListNew`
2. 传入参数 pattern=1（按销量排序），获取前五个热销商品
3. 在首页下拉刷新时同时刷新热销商品数据
4. 优化商品数据展示，包括图片、价格、销量和库存

### 2.2 实施步骤

1. 引入商品相关API接口
2. 实现获取热销商品数据的方法
3. 在页面刷新时调用该方法
4. 优化商品数据展示

## 3. 实施详情

### 3.1 引入商品相关API接口

```javascript
import { queryGoodList, parseMainPicture } from '@/api/good.js';
```

### 3.2 实现获取热销商品数据的方法

```javascript
// 获取热销商品数据
const getHotProductsData = async () => {
  try {
    // 使用商品管理中的"在售中"列表接口，传入pattern=1（按销量排序）
    const params = {
      goodStatus: '1', // 在售中商品
      pageNo: 1,
      pageSize: 5,
      pattern: 1 // 按销量排序
    };
    
    console.log('获取热销商品参数:', params);
    const response = await queryGoodList(params);
    
    if (response.success && response.result && response.result.records) {
      const records = response.result.records;
      
      // 格式化商品数据
      hotProducts.value = records.map((item: any) => ({
        id: item.id,
        name: item.goodName,
        price: item.minPrice === item.maxPrice ? item.minPrice : `${item.minPrice}~${item.maxPrice}`,
        count: item.salesVolume || 0,
        stock: item.repertory || 0,
        image: parseMainPicture(item.mainPicture)
      }));
      
      console.log('热销商品数据加载成功:', hotProducts.value);
    } else {
      console.error('获取热销商品数据失败:', response.message);
      // 如果获取失败，使用默认数据
      hotProducts.value = [
        // 默认商品数据
      ];
    }
  } catch (error) {
    console.error('获取热销商品数据异常:', error);
    // 如果发生异常，使用默认数据
    hotProducts.value = [
      // 默认商品数据
    ];
  }
};
```

### 3.3 在页面刷新时调用该方法

修改页面刷新方法，添加热销商品数据刷新：

```javascript
// 刷新所有数据
const refreshAllData = async () => {
  console.log('开始刷新所有数据');
  // 显示加载提示
  uni.showLoading({
    title: '刷新中...',
    mask: true
  });
  
  try {
    // 并行请求所有数据
    await Promise.all([
      getHomeData(),
      getOrderStatusData(),
      getHotProductsData() // 添加热销商品数据刷新
    ]);
    console.log('所有数据刷新完成');
    
    // 显示刷新成功提示
    uni.showToast({
      title: '刷新成功',
      icon: 'success',
      duration: 1500
    });
  } catch (error) {
    console.error('刷新数据失败:', error);
    uni.showToast({
      title: '刷新失败，请重试',
      icon: 'none'
    });
  } finally {
    // 隐藏加载提示
    uni.hideLoading();
    // 停止下拉刷新动画
    console.log('停止下拉刷新动画');
    uni.stopPullDownRefresh();
  }
};
```

### 3.4 优化页面初始化逻辑

修改页面初始化逻辑，使用统一的刷新方法：

```javascript
onMounted(() => {
  // #ifdef MP-WEIXIN
  const systemInfo = uni.getSystemInfoSync();
  statusBarHeight.value = systemInfo.statusBarHeight;
  // #endif

  // 初始化加载所有数据
  refreshAllData();

  // 添加全局函数，供uni-app调用
  // @ts-ignore
  uni.onPullDownRefresh = function() {
    console.log('页面触发下拉刷新');
    refreshAllData();
  };
});
```

## 4. 接口说明

### 4.1 商品列表接口

- **接口URL**: `/back/goodList/queryPageListNew`
- **请求方式**: GET
- **请求参数**:
  - `goodStatus`: 商品状态，传入 '1' 表示在售中商品
  - `pageNo`: 页码，传入 1 表示第一页
  - `pageSize`: 每页条数，传入 5 表示每页5条
  - `pattern`: 排序方式，传入 1 表示按销量排序（0:综合；1：销量；2：最新;3:价格降序；4：价格升序）

### 4.2 响应参数

| 参数名 | 类型 | 描述 |
| --- | --- | --- |
| id | String | 商品ID |
| goodName | String | 商品名称 |
| mainPicture | String | 商品主图（JSON格式字符串） |
| minPrice | BigDecimal | 最低价格 |
| maxPrice | BigDecimal | 最高价格 |
| salesVolume | Integer | 累计销量 |
| repertory | Integer | 库存 |

## 5. 数据处理说明

### 5.1 商品图片处理

商品主图 `mainPicture` 返回的是JSON格式字符串，需要使用 `parseMainPicture` 方法解析：

```javascript
// 解析商品主图
export function parseMainPicture(mainPicture) {
  try {
    if (!mainPicture) return '/static/images/default-product.png';

    // 尝试解析JSON字符串
    const mainPictureObj = JSON.parse(mainPicture);

    // 获取第一张图片的相对路径
    const firstImage = mainPictureObj["0"];

    // 如果没有图片，返回默认图片
    if (!firstImage) return '/static/images/default-product.png';

    // 使用配置的IMAGE_URL作为图片基础访问路径
    const baseImageUrl = uni.env.IMAGE_URL || '';
    
    return baseImageUrl + firstImage;
  } catch (error) {
    console.error('解析商品主图出错:', error, mainPicture);
    return '/static/images/default-product.png';
  }
}
```

### 5.2 商品价格处理

当最低价格和最高价格相同时，显示单一价格；当不同时，显示价格区间：

```javascript
price: item.minPrice === item.maxPrice ? item.minPrice : `${item.minPrice}~${item.maxPrice}`
```

## 6. 优化效果

### 6.1 优化前

- 热销商品列表显示的是静态数据
- 下拉刷新时不会更新热销商品数据
- 商品数据与实际销售情况不符

### 6.2 优化后

- 热销商品列表显示的是实时数据，按销量排序
- 下拉刷新时会同时更新热销商品数据
- 商品数据与实际销售情况保持一致
- 商品价格根据实际情况显示单一价格或价格区间

## 7. 后续优化建议

1. **缓存优化**：
   - 添加本地缓存，减少频繁请求API
   - 可以设置合理的缓存过期时间，如5分钟

2. **加载状态**：
   - 添加商品列表的加载状态指示器
   - 在数据加载过程中显示骨架屏或加载动画

3. **错误处理**：
   - 完善错误处理机制，当API请求失败时显示友好的提示
   - 添加重试机制，自动重新获取数据

4. **交互优化**：
   - 添加商品点击事件，支持跳转到商品详情页
   - 添加商品标签，如"热销"、"新品"等

## 8. 总结

通过对接商品管理中的"在售中"列表接口，成功实现了首页热销商品列表的实时数据展示。这种实时数据展示方式让商家能够快速了解店铺热销商品情况，提高了工作效率。同时，通过优化商品数据展示，提升了用户体验和系统的实用性。
