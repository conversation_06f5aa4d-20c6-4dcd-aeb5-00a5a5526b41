# 首页订单状态数据字段映射说明

## 1. 数据映射关系

首页订单状态卡片与后端API返回数据的映射关系如下：

| 卡片显示 | 后端字段 | 跳转状态参数 | 说明 |
|---------|---------|------------|------|
| 待发货 | waitDeliveryCount | status=1 | 订单已支付，等待商家发货 |
| 待收货 | waitForReceivingCount | status=2 | 商家已发货，等待买家确认收货 |
| 交易成功 | dealsAreDoneCount | status=3 | 交易成功但未评价的订单数量 |
| 交易失败 | cancelCountStore | status=4 | 已关闭的订单数量 |

## 2. 字段说明

### 2.1 waitDeliveryCount

- **含义**：待发货订单数量
- **数据来源**：查询 `order_store_list` 表中符合以下条件的记录数量
  - 未删除的订单 (`del_flag = '0'`)
  - 属于当前商家的订单 (`sys_user_id = #{sysUserId}`)
  - 订单状态为待发货 (`status = '1'`)

### 2.2 waitForReceivingCount

- **含义**：待收货订单数量
- **数据来源**：查询 `order_store_list` 表中符合以下条件的记录数量
  - 未删除的订单 (`del_flag = '0'`)
  - 属于当前商家的订单 (`sys_user_id = #{sysUserId}`)
  - 订单状态为待收货 (`status = '2'`)

### 2.3 dealsAreDoneCount

- **含义**：交易成功但未评价订单数量
- **数据来源**：查询 `order_store_list` 表中符合以下条件的记录数量
  - 未删除的订单 (`del_flag = '0'`)
  - 属于当前商家的订单 (`sys_user_id = #{sysUserId}`)
  - 订单状态为交易成功 (`status = '3'`)
  - 未评价状态 (`is_evaluate = '0'`)

### 2.4 cancelCountStore

- **含义**：已关闭订单数量
- **数据来源**：查询 `order_store_list` 表中符合以下条件的记录数量
  - 未删除的订单 (`del_flag = '0'`)
  - 属于当前商家的订单 (`sys_user_id = #{sysUserId}`)
  - 订单状态为交易关闭 (`status = '4'`)

## 3. 其他可用字段

API还返回了以下字段，可根据需要在未来扩展使用：

| 字段名 | 含义 | 说明 |
|-------|------|------|
| allCountStore | 全部订单数量 | 所有未删除的订单总数 |
| obligationCount | 待付款订单数量 | 订单状态为待付款的订单数量 |
| doneCountStore | 已完成订单数量 | 订单状态为已完成的订单数量 |
| waitHandleRefundCount | 待处理退款售后数量 | 待处理的退款记录数量 |

## 4. 修改说明

### 4.1 交易成功字段变更

原先使用 `doneCountStore`（已完成订单数量）作为交易成功的数据来源，现调整为使用 `dealsAreDoneCount`（交易成功但未评价订单数量）。

**变更原因**：
- `dealsAreDoneCount` 更准确地反映了交易成功但未评价的订单数量
- 这些订单需要商家特别关注，以便提醒买家评价或进行售后服务
- 与订单列表页面的"交易成功"标签数据保持一致

**代码变更**：
```javascript
// 变更前
{
  name: '交易成功',
  count: result.doneCountStore || 0
}

// 变更后
{
  name: '交易成功',
  count: result.dealsAreDoneCount || 0
}
```

## 5. 注意事项

1. 所有数据均来自实时API请求，没有本地缓存
2. 点击订单状态卡片会跳转到对应状态的订单列表页面
3. 订单状态数据会在页面加载和下拉刷新时更新
4. 如果API返回的数据为空或请求失败，会显示默认值0
