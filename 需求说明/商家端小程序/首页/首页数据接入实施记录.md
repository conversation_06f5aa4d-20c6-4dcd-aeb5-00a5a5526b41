# 首页数据接入实施记录

## 1. 需求概述

根据业务需求，需要在首页中实现以下数据的接入：

1. 账户余额：显示商家当前可用的资金余额
2. 资金明细：显示冻结金额、提现中金额、已提现金额
3. 销售概况：显示今日销售额、今日订单数、累计业绩

## 2. 实施方案

### 2.1 技术方案

1. **数据接口**：使用 `/back/storeManage/findUseInfo` 接口获取首页所需的所有数据
2. **数据刷新**：实现下拉刷新功能，支持用户手动刷新数据
3. **数据展示**：将接口返回的数据格式化后展示在对应的UI组件中

### 2.2 实施步骤

1. 在首页组件中添加数据模型和加载状态
2. 实现数据获取和格式化函数
3. 将静态数据替换为动态数据
4. 实现下拉刷新功能
5. 添加错误处理和加载状态管理

## 3. 实施详情

### 3.1 添加数据模型和加载状态

在首页组件中添加以下数据模型：

```javascript
// 首页数据
const homeData = ref({
  balance: '0.00',           // 账户余额
  accountFrozen: '0.00',     // 冻结金额
  withdrawingAmount: '0.00', // 提现中金额
  haveWithdrawal: '0.00',    // 已提现金额
  todaySalesAmount: '0.00',  // 今日销售额
  todayOrderCount: 0,        // 今日订单数
  totalPerformance: '0.00'   // 累计业绩
});

// 加载状态
const loading = ref(false);
```

### 3.2 实现数据获取和格式化函数

添加以下函数用于获取和格式化数据：

```javascript
// 获取首页数据
const getHomeData = async () => {
  loading.value = true;
  try {
    const response: any = await uni.http.get(uni.api.getUserInfo);
    if (response.data && response.data.success && response.data.result) {
      const result = response.data.result;
      // 更新首页数据
      homeData.value = {
        balance: formatAmount(result.balance || 0),
        accountFrozen: formatAmount(result.accountFrozen || 0),
        withdrawingAmount: formatAmount(result.withdrawingAmount || 0),
        haveWithdrawal: formatAmount(result.haveWithdrawal || 0),
        todaySalesAmount: formatAmount(result.todaySalesAmount || 0),
        todayOrderCount: result.todayOrderCount || 0,
        totalPerformance: formatAmount(result.totalPerformance || 0)
      };
      console.log('首页数据加载成功:', homeData.value);
    } else {
      uni.showToast({
        title: response.data?.message || '获取数据失败',
        icon: 'none'
      });
    }
  } catch (error: any) {
    console.error('获取首页数据失败:', error);
    uni.showToast({
      title: '获取数据失败，请重试',
      icon: 'none'
    });
  } finally {
    loading.value = false;
    // 停止下拉刷新
    uni.stopPullDownRefresh();
  }
};

// 格式化金额，保留两位小数
const formatAmount = (amount: number | string): string => {
  return parseFloat(String(amount)).toFixed(2);
};
```

### 3.3 将静态数据替换为动态数据

修改模板部分，将静态数据替换为动态数据：

```html
<!-- 账户余额 -->
<text class="balance-amount">¥ {{ homeData.balance }}</text>

<!-- 资金明细 -->
<text class="amount-value">¥ {{ homeData.accountFrozen }}</text>
<text class="amount-value">¥ {{ homeData.withdrawingAmount }}</text>
<text class="amount-value">¥ {{ homeData.haveWithdrawal }}</text>

<!-- 销售概况 -->
<text class="overview-value">¥ {{ homeData.todaySalesAmount }}</text>
<text class="overview-value">{{ homeData.todayOrderCount }}</text>
<text class="overview-value">¥ {{ homeData.totalPerformance }}</text>
```

### 3.4 实现下拉刷新功能

添加下拉刷新事件处理：

```javascript
// 下拉刷新
onPullDownRefresh(() => {
  getHomeData();
});
```

并在页面加载时获取数据：

```javascript
onMounted(() => {
  // #ifdef MP-WEIXIN
  const systemInfo = uni.getSystemInfoSync();
  statusBarHeight.value = systemInfo.statusBarHeight;
  // #endif
  
  // 获取首页数据
  getHomeData();
});
```

### 3.5 添加错误处理和加载状态管理

在数据获取函数中添加了错误处理和加载状态管理：

- 在请求开始前设置 `loading.value = true`
- 在请求结束后设置 `loading.value = false`
- 添加 try-catch 块捕获并处理异常
- 使用 uni.showToast 显示错误信息
- 在 finally 块中停止下拉刷新

## 4. 接口说明

### 4.1 获取首页数据接口

- **接口URL**: `/back/storeManage/findUseInfo`
- **请求方式**: GET
- **接口描述**: 获取商家端首页展示的账户余额、销售数据等信息

### 4.2 响应参数

| 参数名 | 类型 | 描述 |
| --- | --- | --- |
| balance | BigDecimal | 账户余额 |
| accountFrozen | BigDecimal | 冻结金额 |
| withdrawingAmount | BigDecimal | 提现中金额 |
| haveWithdrawal | BigDecimal | 已提现金额 |
| todaySalesAmount | BigDecimal | 今日销售额 |
| todayOrderCount | Integer | 今日订单数量 |
| totalPerformance | BigDecimal | 累计业绩 |

## 5. 测试验证

### 5.1 测试场景

1. **首次加载**：打开首页，应自动加载数据
2. **下拉刷新**：下拉页面，应重新加载数据
3. **错误处理**：在网络异常情况下，应显示错误提示

### 5.2 测试结果

所有测试场景均通过验证，功能正常运行。

## 6. 总结

本次实施完成了首页中账户余额、资金明细和销售概况的数据接入，主要包括：

1. 实现了数据获取和格式化功能
2. 将静态数据替换为动态数据
3. 实现了下拉刷新功能
4. 添加了错误处理和加载状态管理

该功能为商家提供了实时的账户和销售数据，提高了商家的信息获取效率。

## 7. 后续优化建议

1. 添加自动刷新功能，定时更新数据
2. 优化数据加载过程中的UI展示，添加骨架屏或加载动画
3. 添加数据缓存机制，减少重复请求
4. 优化错误处理，提供更详细的错误信息和解决方案
