# 商家端订单统计接口文档

## 1. 接口基本信息

- **接口URL**: `/back/order/storeOrderCount`
- **请求方式**: GET
- **接口描述**: 获取商家端不同状态订单的数量统计，用于订单管理页面的状态标签显示

## 2. 请求参数

无需额外参数，接口通过当前登录用户的 `sysUserId` 自动获取相关数据。

## 3. 响应参数

| 参数名 | 类型 | 描述 |
| --- | --- | --- |
| allCountStore | Integer | 全部订单数量 |
| obligationCount | Integer | 待付款订单数量 |
| waitDeliveryCount | Integer | 待发货订单数量 |
| waitForReceivingCount | Integer | 待收货订单数量 |
| doneCountStore | Integer | 已完成订单数量 |
| cancelCountStore | Integer | 已关闭订单数量 |
| dealsAreDoneCount | Integer | 交易成功但未评价订单数量 |
| waitHandleRefundCount | Integer | 待处理退款售后数量 |

## 4. 响应示例

```json
{
  "success": true,
  "message": "查询成功!",
  "code": 200,
  "result": {
    "allCountStore": 125,
    "obligationCount": 15,
    "waitDeliveryCount": 30,
    "waitForReceivingCount": 25,
    "doneCountStore": 40,
    "cancelCountStore": 10,
    "dealsAreDoneCount": 5,
    "waitHandleRefundCount": 8
  }
}
```

## 5. 订单状态说明

系统中订单状态定义如下：

| 状态码 | 状态名称 | 说明 |
| --- | --- | --- |
| 0 | 待付款 | 订单已创建但尚未支付 |
| 1 | 待发货 | 订单已支付，等待商家发货 |
| 2 | 待收货 | 商家已发货，等待买家确认收货 |
| 3 | 交易成功 | 买家已确认收货，交易完成 |
| 4 | 交易关闭 | 订单已取消或交易失败 |
| 5 | 已完成 | 订单全部流程已完成 |

## 6. 业务逻辑说明

### 6.1 全部订单数量

查询条件：
- 未删除的订单 (`del_flag = '0'`)
- 属于当前商家的订单 (`sys_user_id = #{sysUserId}`)

### 6.2 待付款订单数量

查询条件：
- 未删除的订单 (`del_flag = '0'`)
- 属于当前商家的订单 (`sys_user_id = #{sysUserId}`)
- 订单状态为待付款 (`status = '0'`)

### 6.3 待发货订单数量

查询条件：
- 未删除的订单 (`del_flag = '0'`)
- 属于当前商家的订单 (`sys_user_id = #{sysUserId}`)
- 订单状态为待发货 (`status = '1'`)

### 6.4 待收货订单数量

查询条件：
- 未删除的订单 (`del_flag = '0'`)
- 属于当前商家的订单 (`sys_user_id = #{sysUserId}`)
- 订单状态为待收货 (`status = '2'`)

### 6.5 已完成订单数量

查询条件：
- 未删除的订单 (`del_flag = '0'`)
- 属于当前商家的订单 (`sys_user_id = #{sysUserId}`)
- 订单状态为已完成 (`status = '5'`)

### 6.6 已关闭订单数量

查询条件：
- 未删除的订单 (`del_flag = '0'`)
- 属于当前商家的订单 (`sys_user_id = #{sysUserId}`)
- 订单状态为交易关闭 (`status = '4'`)

### 6.7 交易成功但未评价订单数量

查询条件：
- 未删除的订单 (`del_flag = '0'`)
- 属于当前商家的订单 (`sys_user_id = #{sysUserId}`)
- 订单状态为交易成功 (`status = '3'`)
- 未评价状态 (`is_evaluate = '0'`)

### 6.8 待处理退款售后数量

查询条件：
- 未删除的退款记录 (`del_flag = '0'`)
- 属于当前商家的退款记录 (`sys_user_id = #{sysUserId}`)
- 退款状态为待处理相关状态 (`status IN ('0', '1', '2', '3')`)

## 7. 退款售后状态说明

退款售后状态定义如下：

| 状态码 | 状态名称 | 说明 |
| --- | --- | --- |
| 0 | 待商家处理 | 买家已申请退款，等待商家处理 |
| 1 | 待买家退货 | 商家同意退款，等待买家退货 |
| 2 | 待商家确认收货 | 买家已退货，等待商家确认收货 |
| 3 | 待平台处理 | 商家拒绝退款，等待平台介入处理 |
| 4 | 退款成功 | 退款已完成 |
| 5 | 退款关闭 | 退款申请已关闭 |

## 8. 使用场景

此接口主要用于商家端订单管理页面的订单状态标签显示，通过返回的各状态订单数量，前端可以在对应的标签上显示数字，方便商家快速了解各状态订单的数量。

## 9. 注意事项

1. 接口需要用户登录后才能访问
2. 只能查询当前登录用户所属店铺的订单数据
3. 订单数量统计是实时查询的，可能会随时变化
4. 如果订单量较大，可以考虑使用缓存优化接口性能
