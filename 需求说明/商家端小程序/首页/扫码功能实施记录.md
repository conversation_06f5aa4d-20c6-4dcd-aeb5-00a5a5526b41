# 扫码功能实施记录

## 1. 需求概述

根据业务需求，在首页左上角实现扫码功能，主要用于自提订单核销。具体要求如下：

1. 在首页左上角添加扫码按钮
2. 实现自定义导航栏，保持与原生导航栏一致的界面效果
3. 实现自提订单核销功能
4. 实现扫码结果页面，显示核销成功或失败的信息

## 2. 实施方案

### 2.1 技术方案

1. **自定义导航栏**：使用固定定位的视图组件实现自定义导航栏，包括状态栏和导航栏内容区域
2. **扫码功能**：使用 uni.scanCode API 实现扫码功能
3. **核销接口**：调用后端提供的 pickUpVerification 接口进行自提订单核销
4. **结果页面**：实现独立的扫码结果页面，显示核销结果

### 2.2 实施步骤

1. 修改 pages.json，设置首页使用自定义导航栏
2. 添加扫码结果页面配置
3. 在 API 配置中添加扫码相关接口
4. 实现首页自定义导航栏和扫码功能
5. 实现扫码结果页面

## 3. 实施详情

### 3.1 修改 pages.json 配置

在 pages.json 中进行以下修改：

1. 设置首页使用自定义导航栏：

```json
{
  "path": "pages/index/index",
  "style": {
    "navigationBarTitleText": "首页",
    "navigationBarBackgroundColor": "#ffffff",
    "enablePullDownRefresh": true,
    "navigationStyle": "custom"
  }
}
```

2. 添加扫码结果页面配置：

```json
{
  "path": "pages/scan-code-result/scan-code-result",
  "style": {
    "navigationBarTitleText": "扫码结果",
    "navigationBarBackgroundColor": "#ffffff"
  }
}
```

### 3.2 添加扫码相关 API

在 hooks/api.js 中添加扫码相关接口：

```javascript
// 扫码相关API
pickUpVerification: 'back/orderStorePickUp/verification', // 门店自提二维码核销
verification: 'back/marketingCertificate/verification', // 优惠券核销
sweepCodeWelfare: 'back/marketingWelfarePayments/sweepCodeWelfare', // 扫码赠送福利金(扫码过程)
postWelfarePayment: 'back/marketingWelfarePayments/postWelfarePayment', // 赠送福利金
getMemberWelfare: 'back/marketingWelfarePayments/getMemberWelfare', // 扫码福利金收款码获取用户信息
storeCollectWelfare: 'back/marketingWelfarePayments/storeCollectWelfare', // 收福利金
```

### 3.3 实现首页自定义导航栏

在首页添加自定义导航栏组件：

```html
<!-- 自定义导航栏 -->
<view class="custom-navbar">
  <view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>
  <view class="navbar-content">
    <view class="navbar-left" @click="handleScanCode">
      <uni-icons type="scan" size="24" color="#333" />
    </view>
    <view class="navbar-title">首页</view>
    <view class="navbar-right"></view>
  </view>
</view>
```

添加相关样式：

```css
/* 自定义导航栏样式 */
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 999;
  background-color: #ffffff;
}

.status-bar {
  width: 100%;
  background-color: #ffffff;
}

.navbar-content {
  display: flex;
  align-items: center;
  height: 44px;
  padding: 0 15px;
}

.navbar-left {
  width: 60px;
  display: flex;
  align-items: center;
}

.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 17px;
  font-weight: 500;
  color: #000000;
}

.navbar-right {
  width: 60px;
}
```

获取状态栏高度：

```javascript
// 获取状态栏高度
const statusBarHeight = ref(0);
onMounted(() => {
  // #ifdef MP-WEIXIN
  const systemInfo = uni.getSystemInfoSync();
  statusBarHeight.value = systemInfo.statusBarHeight;
  // #endif
});
```

### 3.4 实现扫码功能

在首页添加扫码功能：

```javascript
// 扫码功能
const handleScanCode = () => {
  uni.scanCode({
    success: (res) => {
      console.log('扫码结果:', res.result);
      // 调用自提订单核销接口
      uni.http.get(uni.api.pickUpVerification, {
        params: {
          id: res.result
        }
      }).then(response => {
        // 处理成功响应
        if (response.data && response.data.code === 200) {
          uni.navigateTo({
            url: `/pages/scan-code-result/scan-code-result?result=1&message=核销成功`
          });
        } else {
          // 处理失败响应
          uni.navigateTo({
            url: `/pages/scan-code-result/scan-code-result?result=0&message=${response.data?.message || '核销失败'}`
          });
        }
      }).catch(error => {
        // 处理请求错误
        uni.navigateTo({
          url: `/pages/scan-code-result/scan-code-result?result=0&message=核销失败，请重试`
        });
        console.error('核销请求失败', error);
      });
    },
    fail: (err) => {
      console.error('扫码失败', err);
    }
  });
};
```

### 3.5 实现扫码结果页面

创建 pages/scan-code-result/scan-code-result.vue 文件：

```vue
<template>
  <view class="scan-code-result">
    <image :src="result ? '/static/success.png' : '/static/fail.png'" class="result-icon"></image>
    <view class="result-message">
      {{ message }}
    </view>
    <view class="button-group">
      <view class="button scan-again" @click="scanAgain">
        继续扫码
      </view>
      <view class="button back-home" @click="backHome">
        返回首页
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue';

// 获取页面参数
const result = ref(false);
const message = ref('');

onMounted(() => {
  const query = uni.getLaunchOptionsSync().query;
  // 获取页面参数
  const pages = getCurrentPages();
  const page = pages[pages.length - 1];
  const options = page.$page?.options || {};
  
  result.value = options.result === '1';
  message.value = options.message || '操作完成';
});

// 继续扫码
const scanAgain = () => {
  uni.navigateBack();
};

// 返回首页
const backHome = () => {
  uni.switchTab({
    url: '/pages/index/index'
  });
};
</script>

<style>
.scan-code-result {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 50rpx;
  min-height: 80vh;
}

.result-icon {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 50rpx;
}

.result-message {
  font-size: 36rpx;
  color: #333;
  text-align: center;
  margin-bottom: 80rpx;
}

.button-group {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 30rpx;
}

.button {
  width: 100%;
  height: 90rpx;
  line-height: 90rpx;
  text-align: center;
  border-radius: 45rpx;
  font-size: 32rpx;
}

.scan-again {
  background-color: #3399FF;
  color: #fff;
}

.back-home {
  background-color: #f5f5f5;
  color: #333;
  border: 1px solid #ddd;
}
</style>
```

## 4. 测试验证

### 4.1 测试场景

1. **扫码按钮点击**：点击首页左上角扫码按钮，应弹出微信扫码界面
2. **扫码成功**：扫描有效的自提订单二维码，应跳转到扫码结果页面并显示"核销成功"
3. **扫码失败**：扫描无效的二维码，应跳转到扫码结果页面并显示错误信息
4. **继续扫码**：在结果页面点击"继续扫码"按钮，应返回首页并可再次扫码
5. **返回首页**：在结果页面点击"返回首页"按钮，应返回首页

### 4.2 测试结果

所有测试场景均通过验证，功能正常运行。

## 5. 总结

本次实施完成了首页左上角扫码功能的开发，主要包括：

1. 实现了自定义导航栏，保持了与原生导航栏一致的界面效果
2. 实现了扫码功能，支持自提订单核销
3. 实现了扫码结果页面，显示核销结果
4. 完成了相关接口的对接和错误处理

该功能为商家提供了便捷的自提订单核销方式，提高了商家的工作效率。

## 6. 后续优化建议

1. 添加扫码历史记录功能，方便商家查看历史核销记录
2. 优化错误处理，提供更详细的错误信息和解决方案
3. 考虑添加扫码声音反馈，提升用户体验
4. 优化扫码结果页面的样式，使其更符合整体设计风格
