# 扫码核销流程优化方案

## 一、业务流程概述

优化后的扫码核销流程如下：

1. 商家在首页点击扫码按钮，调用系统扫码API
2. 扫描自提订单二维码后，获取订单ID
3. 调用订单详情接口（而非核销接口），获取订单信息
4. 跳转到新增的"核销确认页面"，展示订单详情
5. 商家确认信息无误后，点击"确认核销"按钮
6. 调用核销接口，进行实际核销操作
7. 根据核销结果，跳转到现有的扫码结果页面显示成功或失败信息

## 二、页面跳转逻辑

```
首页 → 系统扫码界面 → 核销确认页面 → 扫码结果页面
```

具体跳转方式：
- 首页到系统扫码：调用 `uni.scanCode`
- 扫码到核销确认页：调用 `uni.navigateTo({ url: '/pages/scan-code-confirm/scan-code-confirm?id=xxx' })`
- 核销确认到结果页：调用 `uni.redirectTo({ url: '/pages/scan-code-result/scan-code-result' })`

## 三、异常情况处理

在扫码核销流程中，可能会遇到各种异常情况，需要进行合理处理，确保用户体验流畅且直观。

### 1. 异常情况分类

以下情况下，系统应该阻止核销操作并显示友好的错误提示：

1. **非自提订单**：快递配送订单不需要进行门店核销
2. **订单状态不符**：只有"待收货"（待核销）状态的订单才能进行核销
   - 待付款订单：尚未支付，无法核销
   - 待发货订单：尚未发货，无法核销
   - 交易成功订单：已经完成，无需重复核销
   - 交易失败订单：已被取消，无法核销
3. **订单信息不完整**：无法获取完整的订单信息
4. **无效二维码**：扫描的不是有效的订单二维码

### 2. 异常处理流程

1. 在核销确认页面加载订单详情后，立即检查订单状态和类型
2. 如果订单不符合核销条件，显示错误提示，并禁用"确认核销"按钮
3. 错误提示应包含：
   - 错误标题：简明扼要说明问题（如"非自提订单"、"订单已完成"）
   - 错误详情：详细解释原因（如"该订单为快递配送订单，无需进行门店核销"）
   - 操作建议：提示用户下一步操作（如"请扫描待核销的自提订单"）
4. 提供"返回首页"和"重新扫码"两个操作选项，方便商家继续工作

### 3. 异常情况UI设计

错误提示页面布局建议：

```
+------------------------------------------+
|                核销确认                   |  <- 导航栏
+------------------------------------------+
|                                          |
|  +--------------------------------------+|
|  |                                      ||
|  |              ⚠️ 或 ❌                 ||
|  |                                      ||
|  |           [错误标题]                  ||
|  |                                      ||
|  |           [错误详情]                  ||
|  |                                      ||
|  |           [操作建议]                  ||
|  |                                      ||
|  |  +-------------+  +---------------+  ||
|  |  |  重新扫码    |  |   返回首页     |  ||
|  |  +-------------+  +---------------+  ||
|  |                                      ||
|  +--------------------------------------+|
|                                          |
+------------------------------------------+
```

## 四、新增页面设计

### 1. 页面配置

在 `pages.json` 中添加新页面配置：

```json
{
  "path": "pages/scan-code-confirm/scan-code-confirm",
  "style": {
    "navigationBarTitleText": "核销确认",
    "navigationBarBackgroundColor": "#ffffff"
  }
}
```

### 2. UI设计建议

核销确认页面布局建议：

```
+------------------------------------------+
|                核销确认                   |  <- 导航栏
+------------------------------------------+
|                                          |
|  +--------------------------------------+|
|  |             订单信息卡片              ||
|  |  订单号：123456789                   ||
|  |  下单时间：2023-05-20 14:30:25       ||
|  |  自提门店：XX门店                     ||
|  |  订单状态：待自提                     ||
|  +--------------------------------------+|
|                                          |
|  +--------------------------------------+|
|  |             商品信息卡片              ||
|  |  +--------+                          ||
|  |  | 商品图片 | 商品名称                ||
|  |  +--------+ ¥99.00                   ||
|  |            数量：1                    ||
|  |                                      ||
|  |  [可能有多个商品]                     ||
|  +--------------------------------------+|
|                                          |
|  +--------------------------------------+|
|  |             客户信息卡片              ||
|  |  客户姓名：张三                       ||
|  |  联系电话：138****1234               ||
|  +--------------------------------------+|
|                                          |
|                                          |
|  +--------------------------------------+|
|  |           确认核销按钮                ||
|  +--------------------------------------+|
|                                          |
+------------------------------------------+
```

设计要点：
- 使用卡片式布局，清晰分隔不同类型信息
- 突出显示关键信息：订单号、商品信息、客户信息
- 底部大按钮"确认核销"，使用主题色背景
- 整体风格与现有系统保持一致

## 四、数据传递方式

### 1. 页面间数据传递

- **扫码到核销确认页**：通过URL参数传递订单ID
  ```javascript
  uni.navigateTo({
    url: `/pages/scan-code-confirm/scan-code-confirm?id=${orderId}`
  });
  ```

- **核销确认到结果页**：通过全局变量传递核销结果（复用现有机制）
  ```javascript
  // @ts-ignore
  getApp().globalData.scanCodeResult = {
    result: '1', // 或 '0'
    message: '核销成功' // 或错误信息
  };

  uni.redirectTo({
    url: '/pages/scan-code-result/scan-code-result'
  });
  ```

### 2. 接口数据处理

- 订单详情接口：获取订单基本信息、商品信息、客户信息
- 核销接口：传递订单ID，接收核销结果

## 五、接口调用时机

### 1. 订单详情接口

在核销确认页面的 `onLoad` 或 `onMounted` 生命周期中调用：

```javascript
onMounted(() => {
  const orderId = route.query.id;
  if (orderId) {
    // 调用订单详情接口
    getOrderDetail(orderId);
  }
});
```

### 2. 核销接口

在用户点击"确认核销"按钮时调用：

```javascript
const confirmVerification = () => {
  // 显示加载提示
  uni.showLoading({ title: '核销中...' });

  // 调用核销接口
  uni.http.get(uni.api.pickUpVerification, {
    params: { id: orderId.value }
  }).then(/* 处理响应 */)
    .catch(/* 处理错误 */)
    .finally(() => {
      uni.hideLoading();
    });
};
```

## 六、实施计划和工作量评估

### 1. 实施步骤

1. **准备工作** (0.5人天)
   - 确认订单详情接口规格
   - 确认核销接口规格
   - 规划页面路由

2. **页面开发** (1.5人天)
   - 创建核销确认页面
   - 实现页面UI布局
   - 实现订单详情数据展示

3. **业务逻辑开发** (1人天)
   - 实现扫码逻辑修改
   - 实现核销确认逻辑
   - 实现页面跳转逻辑

4. **测试与调整** (1人天)
   - 功能测试
   - 界面调整
   - 边界情况处理

5. **文档与上线** (0.5人天)
   - 编写操作文档
   - 部署上线

### 2. 工作量评估

总工作量：约4.5人天

- 前端开发：3人天
- 测试与调整：1人天
- 文档与上线：0.5人天

### 3. 风险评估

- **接口兼容性**：需确认订单详情接口是否返回足够的信息
- **用户体验**：增加确认步骤可能略微增加操作时间，但提高了操作安全性
- **错误处理**：需要完善各种异常情况的处理

## 七、代码实现建议

### 1. 修改现有扫码函数

```typescript
// 在首页或扫码入口处
const handleScanCode = () => {
  uni.scanCode({
    success: (res: any) => {
      console.log('扫码结果:', res.result);
      // 跳转到核销确认页面，而不是直接调用核销接口
      uni.navigateTo({
        url: `/pages/scan-code-confirm/scan-code-confirm?id=${res.result}`
      });
    },
    fail: (err: any) => {
      console.error('扫码失败', err);
      uni.showToast({
        icon: 'none',
        title: '扫码失败，请重试'
      });
    }
  });
};
```

### 2. 添加API配置

在 `hooks/api.js` 中添加订单详情接口：

```javascript
// 如果不存在，添加订单详情接口
getOrderDetail: 'back/orderStorePickUp/getOrderDetail', // 获取自提订单详情
```

## 八、总结

本方案通过增加一个核销确认页面，优化了扫码核销流程，使商家能够在核销前查看订单详情，避免误操作。方案的主要优点包括：

1. **提高操作安全性**：商家可以确认订单信息后再核销，减少误操作
2. **增强用户体验**：提供更详细的订单信息展示，便于商家核对
3. **复用现有组件**：核销结果页面复用现有页面，减少开发工作量
4. **流程清晰**：扫码 → 确认 → 结果的流程逻辑清晰，易于理解

实施本方案需要约4.5人天的工作量，主要工作集中在新增核销确认页面的开发上。通过合理的设计和复用，可以在较短时间内完成此功能的开发和上线。
