# 待结算明细页面实施记录

## 1. 需求概述

根据业务需求，需要实现"待结算明细"页面，用于展示商家当前待结算的资金明细。该页面可以通过点击首页的"冻结金额"卡片进入，主要显示尚未完成结算的交易记录，包括交易类型、时间、金额等信息，帮助商家了解待结算资金的来源和状态。

## 2. 实施方案

### 2.1 技术方案

1. **创建新页面**：在`pages/finance`目录下创建`pending-settlement.vue`页面
2. **页面配置**：在`pages.json`中添加页面配置
3. **首页跳转**：在首页的冻结金额卡片上添加点击事件，跳转到待结算明细页面
4. **数据接口**：使用`findStoreRechargeRecordInfo`接口获取待结算明细数据
5. **列表展示**：实现待结算明细列表，支持下拉刷新和上拉加载更多

### 2.2 实施步骤

1. 创建待结算明细页面文件
2. 在pages.json中添加页面配置
3. 修改首页冻结金额卡片，添加点击事件
4. 实现待结算明细页面的UI和功能

## 3. 实施详情

### 3.1 创建待结算明细页面

在`pages/finance`目录下创建`pending-settlement.vue`文件，实现待结算明细页面的UI和功能：

- 自定义导航栏
- 待结算明细列表
- 下拉刷新和上拉加载更多功能
- 空状态提示

### 3.2 添加页面配置

在`pages.json`中添加待结算明细页面的配置：

```json
{
  "path": "pages/finance/pending-settlement",
  "style": {
    "navigationBarTitleText": "待结算明细",
    "navigationBarBackgroundColor": "#ffffff",
    "enablePullDownRefresh": true,
    "navigationStyle": "custom"
  }
}
```

### 3.3 修改首页冻结金额卡片

在首页的冻结金额卡片上添加点击事件，跳转到待结算明细页面：

```html
<view class="amount-item" @click="goToPendingSettlement">
  <uni-icons :size="24" type="wallet" class="amount-icon" />
  <text class="amount-value">¥ {{ homeData.accountFrozen }}</text>
  <text class="amount-label">冻结金额</text>
</view>
```

添加跳转方法：

```javascript
// 跳转到待结算明细页面
const goToPendingSettlement = () => {
  try {
    uni.navigateTo({
      url: '/pages/finance/pending-settlement'
    });
  } catch (error) {
    console.error('跳转到待结算明细页面失败', error);
    uni.showToast({
      title: '跳转失败，请重试',
      icon: 'none'
    });
  }
};
```

### 3.4 实现待结算明细页面功能

#### 3.4.1 页面布局

页面采用垂直方向的列表布局，主要包含以下几个部分：

1. **自定义导航栏**：显示页面标题和返回按钮
2. **明细列表**：按时间倒序排列的交易记录列表
3. **空状态提示**：当没有记录时显示"暂无明细"提示
4. **加载状态**：底部显示加载状态（加载更多/没有更多）

#### 3.4.2 数据获取

使用`findStoreRechargeRecordInfo`接口获取待结算明细数据：

```javascript
// 加载待结算明细列表
async function loadSettlementList(refresh = false) {
  if (pageParams.loading) return;

  if (refresh) {
    pageParams.pageNo = 1;
    pageParams.hasMore = true;
    isRefreshing.value = true;
    settlementList.value = [];
  }

  if (!pageParams.hasMore) return;

  pageParams.loading = true;

  try {
    const params = {
      pageNo: pageParams.pageNo,
      pageSize: pageParams.pageSize,
      isPlatform: '2', // 2表示全部
      state: 2, // 2表示待结算明细
      id: storeId.value
    };

    // 使用findStoreRechargeRecordInfo接口获取待结算明细数据
    const { data } = await uni.http.get('/back/storeRechargeRecord/findStoreRechargeRecordInfo', { params });

    if (data.success && data.result) {
      const { records, total, current } = data.result;

      if (records && records.length > 0) {
        settlementList.value = [...settlementList.value, ...records];
      }

      pageParams.total = total || 0;
      pageParams.hasMore = settlementList.value.length < pageParams.total;
      pageParams.pageNo = current + 1;
    } else {
      uni.showToast({
        title: data.message || '加载失败',
        icon: 'none'
      });
    }
  } catch (error) {
    console.error('加载待结算明细失败:', error);
    uni.showToast({
      title: '加载失败，请重试',
      icon: 'none'
    });
  } finally {
    pageParams.loading = false;
    if (isRefreshing.value) {
      isRefreshing.value = false;
      uni.stopPullDownRefresh();
    }
  }
}
```



#### 3.4.3 下拉刷新和上拉加载更多

实现下拉刷新和上拉加载更多功能：

```html
<scroll-view
  scroll-y
  class="settlement-list-scroll"
  @scrolltolower="onScrollToLower"
  refresher-enabled
  :refresher-triggered="isRefreshing"
  @refresherrefresh="loadSettlementList(true)"
>
  <!-- 列表内容 -->
</scroll-view>
```

```javascript
// 滚动到底部加载更多
function onScrollToLower() {
  if (!pageParams.loading && pageParams.hasMore) {
    loadSettlementList();
  }
}
```

## 4. 接口说明

### 4.1 待结算明细接口

- **接口名称**：findStoreRechargeRecordInfo
- **接口路径**：back/storeRechargeRecord/findStoreRechargeRecordInfo
- **请求方式**：GET
- **请求参数**：
  - pageNo: 页码，默认为1
  - pageSize: 每页条数，默认为10
  - isPlatform: 收支类型：0=收入，1=支出，2=全部（默认）
  - state: 2（表示待结算明细）
  - id: 店铺ID

- **响应数据结构**：
  ```json
  {
    "success": true,
    "code": 200,
    "message": "操作成功",
    "result": {
      "records": [
        {
          "goAndCome": "0",           // 0:收入 1:支出
          "payType": "订单收入",       // 交易类型
          "createTime": "2023-04-27 15:30:45", // 创建时间
          "amount": "+100.00",        // 交易金额
          "balance": "余额 1000.00"    // 交易后余额
        }
      ],
      "total": 100,    // 总记录数
      "size": 10,      // 每页大小
      "current": 1,    // 当前页码
      "pages": 10      // 总页数
    }
  }
  ```

## 5. 测试验证

### 5.1 测试场景

1. **首页跳转**：点击首页的冻结金额卡片，应跳转到待结算明细页面
2. **数据加载**：进入页面后，应自动加载待结算明细数据
3. **下拉刷新**：下拉页面，应重新加载数据
4. **上拉加载更多**：滑动到页面底部，应加载更多数据
5. **空状态**：当没有数据时，应显示"暂无待结算明细"提示

### 5.2 测试结果

待实际测试验证。

## 6. 总结

本次实施完成了"待结算明细"页面的开发，实现了以下功能：

1. 创建了待结算明细页面，支持查看待结算的资金明细
2. 实现了下拉刷新和上拉加载更多功能
3. 在首页的冻结金额卡片上添加了点击事件，支持跳转到待结算明细页面

该页面的实现参考了需求分析文档，并遵循了当前项目的技术栈和设计风格，为商家提供了查看待结算资金明细的功能，帮助商家了解待结算资金的来源和状态。
