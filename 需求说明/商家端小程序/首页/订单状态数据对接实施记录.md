# 首页订单状态数据对接实施记录

## 1. 需求概述

首页中的订单状态卡片目前显示的是静态数据，需要对接后端API，实现实时显示各状态订单的数量，并支持点击跳转到对应状态的订单列表页面。

## 2. 实施方案

### 2.1 技术方案

1. 创建订单相关API接口文件，封装获取订单状态统计数据的方法
2. 在首页中调用API获取订单状态数据，并更新到UI
3. 添加点击事件，支持跳转到对应状态的订单列表页面

### 2.2 实施步骤

1. 创建订单API接口文件
2. 修改首页代码，添加获取订单状态数据的方法
3. 在页面加载和下拉刷新时调用该方法
4. 添加点击事件处理函数，实现跳转功能

## 3. 实施详情

### 3.1 创建订单API接口文件

在 `api/order.js` 中创建获取订单状态统计数据的方法：

```javascript
// 订单相关API接口

/**
 * 获取订单状态统计数据
 * @returns {Promise} 返回各状态订单数量
 */
export function getOrderStatusCount() {
  return new Promise((resolve, reject) => {
    uni.http.get('/back/order/storeOrderCount')
      .then(response => {
        console.log('订单状态统计API响应:', JSON.stringify(response.data));
        // 检查响应是否成功
        if (response.data && response.data.code === 200) {
          resolve({
            success: true,
            result: response.data.result,
            message: response.data.message
          });
        } else {
          // 如果响应不成功，返回错误信息
          resolve({
            success: false,
            message: response.data?.message || '获取订单状态统计失败'
          });
        }
      })
      .catch(error => {
        console.error('订单状态统计API错误:', error);
        reject(error);
      });
  });
}
```

### 3.2 修改首页代码

#### 3.2.1 引入API方法

```javascript
import { ref, onMounted } from 'vue';
import { getOrderStatusCount } from '@/api/order.js';
```

#### 3.2.2 初始化订单状态数据

```javascript
// 订单状态数据
const orderStatus = ref([
  {
    name: '待发货',
    count: 0
  },
  {
    name: '待收货',
    count: 0
  },
  {
    name: '交易成功',
    count: 0
  },
  {
    name: '交易失败',
    count: 0
  }
]);
```

#### 3.2.3 添加获取订单状态数据的方法

```javascript
// 获取订单状态统计数据
const getOrderStatusData = async () => {
  try {
    const response = await getOrderStatusCount();
    if (response.success && response.result) {
      const result = response.result;
      // 更新订单状态数据
      orderStatus.value = [
        {
          name: '待发货',
          count: result.waitDeliveryCount || 0
        },
        {
          name: '待收货',
          count: result.waitForReceivingCount || 0
        },
        {
          name: '交易成功',
          count: result.dealsAreDoneCount || 0
        },
        {
          name: '交易失败',
          count: result.cancelCountStore || 0
        }
      ];
      console.log('订单状态数据加载成功:', orderStatus.value);
    } else {
      console.error('获取订单状态数据失败:', response.message);
    }
  } catch (error) {
    console.error('获取订单状态数据异常:', error);
  }
};
```

#### 3.2.4 在页面加载和下拉刷新时调用方法

```javascript
onMounted(() => {
  // #ifdef MP-WEIXIN
  const systemInfo = uni.getSystemInfoSync();
  statusBarHeight.value = systemInfo.statusBarHeight;
  // #endif

  // 获取首页数据
  getHomeData();

  // 获取订单状态数据
  getOrderStatusData();

  // 添加全局函数，供uni-app调用
  // @ts-ignore
  uni.onPullDownRefresh = function() {
    console.log('页面触发下拉刷新');
    getHomeData();
    getOrderStatusData();
    // 停止下拉刷新动画
    setTimeout(() => {
      uni.stopPullDownRefresh();
    }, 1000);
  };
});

// 在页面加载时添加下拉刷新事件监听
uni.$on('pullDownRefresh', () => {
  getHomeData();
  getOrderStatusData();
});
```

#### 3.2.5 添加点击事件处理函数

```javascript
// 跳转到订单列表页面
const goToOrderList = (statusIndex: number) => {
  try {
    // 根据状态索引确定要跳转的状态
    let status = '';
    switch (statusIndex) {
      case 0: // 待发货
        status = '1';
        break;
      case 1: // 待收货
        status = '2';
        break;
      case 2: // 交易成功
        status = '3';
        break;
      case 3: // 交易失败
        status = '4';
        break;
      default:
        status = '';
    }

    // 跳转到订单页面并传递状态参数
    uni.navigateTo({
      url: `/pages/order/order-list?status=${status}`
    });
  } catch (error) {
    console.error('跳转订单列表失败', error);
    uni.showToast({
      title: '跳转失败，请重试',
      icon: 'none'
    });
  }
};
```

#### 3.2.6 修改模板，添加点击事件

```html
<view class="order-status">
  <view class="status-item" v-for="(item, index) in orderStatus" :key="index" @click="goToOrderList(index)">
    <text class="status-value">{{ item.count }}</text>
    <text class="status-label">{{ item.name }}</text>
  </view>
</view>
```

#### 3.2.7 优化点击效果

```css
.status-item:active {
  transform: scale(0.98);
  opacity: 0.8;
}
```

## 4. 接口说明

### 4.1 订单状态统计接口

- **接口URL**: `/back/order/storeOrderCount`
- **请求方式**: GET
- **接口描述**: 获取商家端不同状态订单的数量统计

### 4.2 响应参数

| 参数名 | 类型 | 描述 |
| --- | --- | --- |
| allCountStore | Integer | 全部订单数量 |
| obligationCount | Integer | 待付款订单数量 |
| waitDeliveryCount | Integer | 待发货订单数量 |
| waitForReceivingCount | Integer | 待收货订单数量 |
| doneCountStore | Integer | 已完成订单数量 |
| cancelCountStore | Integer | 已关闭订单数量 |
| dealsAreDoneCount | Integer | 交易成功但未评价订单数量 |
| waitHandleRefundCount | Integer | 待处理退款售后数量 |

### 4.3 订单状态说明

| 状态码 | 状态名称 | 说明 |
| --- | --- | --- |
| 0 | 待付款 | 订单已创建但尚未支付 |
| 1 | 待发货 | 订单已支付，等待商家发货 |
| 2 | 待收货 | 商家已发货，等待买家确认收货 |
| 3 | 交易成功 | 买家已确认收货，交易完成 |
| 4 | 交易关闭 | 订单已取消或交易失败 |
| 5 | 已完成 | 订单全部流程已完成 |

## 5. 优化效果

### 5.1 优化前

- 订单状态卡片显示的是静态数据
- 点击卡片无反应，不支持跳转

### 5.2 优化后

- 订单状态卡片显示的是实时数据，与后端保持同步
- 点击卡片可以跳转到对应状态的订单列表页面
- 添加了点击反馈效果，提升用户体验

## 6. 后续优化建议

1. **缓存优化**：
   - 考虑添加本地缓存，减少频繁请求API
   - 可以设置合理的缓存过期时间，如5分钟

2. **加载状态**：
   - 添加加载状态指示器，提升用户体验
   - 在数据加载过程中显示骨架屏或加载动画

3. **错误处理**：
   - 完善错误处理机制，当API请求失败时显示友好的提示
   - 添加重试机制，自动重新获取数据

4. **交互优化**：
   - 考虑添加徽标或其他视觉元素，突出显示有变化的订单状态
   - 可以添加动画效果，当数量变化时有过渡动画

## 7. 总结

通过对接后端API，成功实现了首页订单状态卡片的实时数据显示和点击跳转功能，提升了用户体验和系统的实用性。这种实时数据展示方式让商家能够快速了解各状态订单的数量，提高了工作效率。
