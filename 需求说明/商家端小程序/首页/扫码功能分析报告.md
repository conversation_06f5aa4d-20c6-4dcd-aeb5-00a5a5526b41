# 扫码功能分析报告

本报告对商家端应用的扫码功能进行分析，重点关注数据接口、传参和回参等相关信息。

## 1. 功能概述

商家端应用左上角的扫码功能是一个多功能的扫码入口，支持多种扫码场景，包括：

1. **自提订单核销**：扫描自提订单二维码进行核销
2. **福利金赠送**：扫描用户二维码进行福利金赠送
3. **福利金收款**：扫描用户二维码进行福利金收款
4. **优惠券核销**：扫描优惠券二维码进行核销

这些功能通过一个下拉选择器进行切换，用户可以根据需要选择不同的扫码功能。

## 2. 界面实现

### 2.1 扫码入口

扫码入口位于应用左上角，使用了自定义导航栏和uni-icons组件实现：

```html
<uni-nav-bar title="工作台" :status-bar="true" background="rgba(255,255,255,0)" color="#000000" fixed="true">
  <view slot="left">
    <picker :value="index" :range="array" @change="bindPickerChange">
      <uni-icons color="#000000" type="scan" size="24" />
    </picker>
  </view>
</uni-nav-bar>
```

扫码图标使用了uni-icons组件的"scan"图标，并通过picker组件实现了下拉选择不同的扫码功能。

#### 2.1.1 微信小程序导航栏扫码按钮实现方案

在微信小程序中，原生导航栏是不支持直接放置自定义按钮的。因此，项目采用了以下方案来实现导航栏中的扫码按钮：

1. **自定义导航栏方案**：
   - 使用uni-app提供的`uni-nav-bar`组件替代原生导航栏
   - 通过`fixed="true"`和`:status-bar="true"`属性使其固定在顶部并适配状态栏高度
   - 完全自定义导航栏的内容和样式

2. **插槽机制**：
   - `uni-nav-bar`组件提供了`left`、`center`和`right`三个插槽
   - 在`left`插槽中放置扫码按钮，实现左上角的扫码功能入口

3. **下拉选择器集成**：
   - 将`picker`组件与`uni-icons`组件结合使用
   - 用户点击扫码图标时，会触发picker的下拉选择功能
   - 根据用户选择的不同选项，调用不同的扫码处理函数

4. **技术实现细节**：
   - 在`pages.json`中设置`"navigationStyle": "custom"`，隐藏原生导航栏
   - 自定义导航栏的高度通常为状态栏高度 + 44px
   - 使用`uni-status-bar`组件适配不同机型的状态栏高度

这种实现方案的优点是：
- 完全自定义的UI外观和交互体验
- 可以在导航栏中放置任意复杂的组件和交互元素
- 统一跨平台的实现方式，在各端表现一致

缺点是：
- 需要自行处理状态栏高度适配
- 在某些情况下可能与系统手势冲突
- 需要自行实现返回等基础导航功能

### 2.2 扫码结果页面

扫码完成后，会跳转到扫码结果页面（`spanCodeResult.vue`），显示扫码结果：

```html
<view class='spanCodeResult'>
  <image :src="result?'/static/success.png':'/static/fail.png'"></image>
  <view class='result'>
    {{message}}
  </view>
  <view class='button span-code' @click="spanCode">
    继续扫码
  </view>
  <view class='button back' @click="back">
    返回工作台
  </view>
</view>
```

结果页面会根据扫码结果显示成功或失败的图标和消息，并提供"继续扫码"和"返回工作台"两个按钮。

## 3. 核心API接口

### 3.1 自提订单核销接口

| 接口名称 | API路径 | 功能描述 |
|---------|--------|---------|
| pickUpVerification | back/orderStorePickUp/verification | 门店自提二维码核销 |

#### 请求参数

| 参数名 | 类型 | 必填 | 描述 |
|-------|------|-----|------|
| id | String | 是 | 二维码ID |

#### 响应参数

| 参数名 | 类型 | 描述 |
|-------|------|------|
| success | Boolean | 请求是否成功 |
| message | String | 响应消息 |
| code | Number | 响应状态码 |

### 3.2 优惠券核销接口

| 接口名称 | API路径 | 功能描述 |
|---------|--------|---------|
| verification | back/marketingCertificate/verification | 优惠券核销 |

#### 请求参数

| 参数名 | 类型 | 必填 | 描述 |
|-------|------|-----|------|
| id | String | 是 | 二维码ID |

#### 响应参数

| 参数名 | 类型 | 描述 |
|-------|------|------|
| success | Boolean | 请求是否成功 |
| message | String | 响应消息 |
| code | Number | 响应状态码 |

### 3.3 福利金赠送接口

| 接口名称 | API路径 | 功能描述 |
|---------|--------|---------|
| sweepCodeWelfare | back/marketingWelfarePayments/sweepCodeWelfare | 扫码赠送福利金(扫码过程) |
| postWelfarePayment | back/marketingWelfarePayments/postWelfarePayment | 赠送福利金 |

#### sweepCodeWelfare请求参数

| 参数名 | 类型 | 必填 | 描述 |
|-------|------|-----|------|
| id | String | 是 | 二维码ID |

#### sweepCodeWelfare响应参数

| 参数名 | 类型 | 描述 |
|-------|------|------|
| success | Boolean | 请求是否成功 |
| message | String | 响应消息 |
| result | Object | 用户信息，包含phone、memberListId等 |

### 3.4 福利金收款接口

| 接口名称 | API路径 | 功能描述 |
|---------|--------|---------|
| getMemberWelfare | back/marketingWelfarePayments/getMemberWelfare | 扫码福利金收款码获取用户信息 |
| storeCollectWelfare | back/marketingWelfarePayments/storeCollectWelfare | 收福利金 |

#### getMemberWelfare请求参数

| 参数名 | 类型 | 必填 | 描述 |
|-------|------|-----|------|
| id | String | 是 | 二维码ID |

#### getMemberWelfare响应参数

| 参数名 | 类型 | 描述 |
|-------|------|------|
| success | Boolean | 请求是否成功 |
| message | String | 响应消息 |
| result | Object | 用户信息 |

## 4. 交互流程

### 4.1 自提订单核销流程

1. 用户点击左上角扫码图标，选择"自提订单核销"选项
2. 调用系统相机进行扫码
3. 获取二维码内容，调用`pickUpVerification`接口
4. 根据接口返回结果，跳转到扫码结果页面
5. 显示核销成功或失败的信息

```javascript
spanPickupCode() {
  let that = this;
  this.openOnShow = false;
  uni.scanCode({
    success(res) {
      that.$request(
        api.pickUpVerification, {
          id: res.result
        },
        'GET',
        res => {
          uni.navigateTo({
            url: `/pages2/spanCodeResult/spanCodeResult?result=1&message=核销成功`
          });
        },
        res => {
          uni.navigateTo({
            url: `/pages2/spanCodeResult/spanCodeResult?result=0&message=${res.message}`
          });
        }
      );
    },
    fail() {
      // that.$msg('券二维码不正确，请提供正确的券二维码！', false)
    }
  });
}
```

### 4.2 福利金赠送流程

1. 用户点击左上角扫码图标，选择"福利金赠送"选项
2. 调用系统相机进行扫码
3. 获取二维码内容，调用`sweepCodeWelfare`接口获取用户信息
4. 跳转到福利金赠送页面，显示用户信息
5. 用户输入赠送金额，点击确认
6. 调用`postWelfarePayment`接口完成福利金赠送

```javascript
spanWelfarePaymentCode() {
  let that = this;
  let url;
  this.openOnShow = false;
  uni.scanCode({
    success(res) {
      console.log(res);
      that.$request(
        api.sweepCodeWelfare, {
          id: res.result
        },
        'GET',
        res2 => {
          if (res2.result) {
            url = `/pages/welfarePayment/sendWelfarePayment?pageData=${JSON.stringify(res2.result)}`;
            uni.navigateTo({
              url
            });
          }
        }
      );
    },
    fail() {
      // that.$msg('二维码不正确，请提供正确的二维码！', false)
    }
  });
}
```

### 4.3 福利金收款流程

1. 用户点击左上角扫码图标，选择"福利金收款"选项
2. 调用系统相机进行扫码
3. 获取二维码内容，调用`getMemberWelfare`接口获取用户信息
4. 跳转到福利金收款页面，显示用户信息
5. 用户确认收款信息，点击确认
6. 调用`storeCollectWelfare`接口完成福利金收款

```javascript
spanWelfarePaymentReceiveCode() {
  let that = this;
  let url;
  this.openOnShow = false;
  uni.scanCode({
    success(res) {
      console.log(res);
      that.$request(
        api.getMemberWelfare, {
          id: res.result
        },
        'GET',
        res2 => {
          if (res2.result) {
            url = `/pages/welfarePayment/receiveWelfarePayment?pageData=${JSON.stringify(res2.result)}`;
            uni.navigateTo({
              url
            });
          }
        },
        res => {
          that.$msg(res.message, false);
        }
      );
    },
    fail() {
      // that.$msg('二维码不正确，请提供正确的二维码！', false)
    }
  });
}
```

### 4.4 优惠券核销流程

1. 用户点击左上角扫码图标，选择"优惠券核销"选项
2. 调用系统相机进行扫码
3. 获取二维码内容，调用`verification`接口
4. 根据接口返回结果，跳转到扫码结果页面
5. 显示核销成功或失败的信息

```javascript
spanCode() {
  let that = this;
  this.openOnShow = false;
  uni.scanCode({
    success(res) {
      that.$request(
        api.verification, {
          id: res.result
        },
        'GET',
        res => {
          uni.navigateTo({
            url: `/pages2/spanCodeResult/spanCodeResult?result=1&message=核销成功`
          });
        },
        res => {
          uni.navigateTo({
            url: `/pages2/spanCodeResult/spanCodeResult?result=0&message=${res.message}`
          });
        }
      );
    },
    fail() {
      // that.$msg('券二维码不正确，请提供正确的券二维码！', false)
    }
  });
}
```

## 5. 扫码选择器实现

扫码功能的选择是通过picker组件实现的，用户可以选择不同的扫码功能：

```javascript
data() {
  return {
    array: ['自提订单核销', '福利金赠送', '优惠券核销', '福利金收款'],
    index: 0,
    // 其他数据...
  }
},
methods: {
  bindPickerChange(e) {
    this.index = e.target.value;
    switch (e.target.value * 1) {
      case 0:
        // this.spanCode();
        this.spanPickupCode()
        break;
      case 1:
        this.spanWelfarePaymentCode();
        break;
      case 2:
        this.spanPickupCode()
        break;
      case 3:
        this.spanWelfarePaymentReceiveCode();
        break;
      default:
        break;
    }
  },
  // 其他方法...
}
```

## 6. 店铺二维码功能

除了扫码功能外，系统还提供了店铺二维码生成和保存功能，用于让顾客扫码访问店铺：

### 6.1 店铺二维码生成接口

| 接口名称 | API路径 | 功能描述 |
|---------|--------|---------|
| returnSmallcode | back/storeManage/returnSmallcode | 获取店铺二维码 |
| getQrCodeByPage | back/weixin/getQrCodeByPage | 商家端会员端码的生成 |
| getCommercialQrCodeByPage | back/weixin/getCommercialQrCodeByPage | 商家端商户端码的生成 |

### 6.2 店铺二维码生成和保存流程

1. 调用`returnSmallcode`接口获取店铺二维码
2. 将二维码图片绘制到Canvas上
3. 用户点击"保存我的二维码"按钮
4. 将Canvas转换为图片，并保存到相册

```javascript
async OnCanvas() {
  this.$loadingPop()
  this.ctx = uni.createCanvasContext('qrCode', this);
  let _imgInfo = await this.getImageInfo('myShopQrSrc'); //店铺二维码
  this.ctx.setFillStyle('#ffffff')
  this.ctx.fillRect(0, 0, 210, 210); //canvas画布大小
  this.ctx.drawImage(_imgInfo.path, 0, 0, 210, 210);
  setTimeout(() => {
    this.ctx.draw(true, (ret) => {
      this.getNewImage();
    });
  }, 200);
}

//保存图片
getNewImage(canKeep = false) {
  let _this = this;
  uni.canvasToTempFilePath({
    canvasId: 'qrCode',
    fileType: 'jpg',
    quality: 1,
    async success(res) {
      _this.canvasImgSrc = res.tempFilePath
      _this.$emit('success', res);
      if (!canKeep) return;
      _this.$loadingPop()
      let result = await _this.allAuthorizeMethods()
      if (result && result != 'fail' && result != 'foreverFalse') {
        uni.saveImageToPhotosAlbum({
          filePath: res.tempFilePath,
          success: function() {
            _this.$loadedPop();
            _this.$msg('保存成功！', true, 2)
          },
          fail() {
            _this.$loadedPop()
            _this.$msg('保存失败！', false, 2)
          }
        });
      } else {
        // 处理授权失败情况...
      }
    }
  }, this);
  _this.$loadedPop()
}
```

## 7. 导航栏扫码按钮的替代实现方案

除了上述使用自定义导航栏的实现方案外，还有以下几种可能的替代方案：

### 7.1 使用原生导航栏 + 自定义内容区域

```html
<!-- 保留原生导航栏，在页面顶部添加自定义区域 -->
<view class="custom-header">
  <view class="scan-button" @tap="showScanOptions">
    <uni-icons type="scan" size="24" color="#000000" />
  </view>
  <view class="title">工作台</view>
  <view class="placeholder"></view>
</view>
```

这种方案的优点是保留了原生导航栏的手势和行为，但需要额外处理与原生导航栏的协调问题。

### 7.2 使用胶囊按钮周围区域

微信小程序允许获取胶囊按钮的位置信息，可以在胶囊按钮旁边放置自定义按钮：

```javascript
// 获取胶囊按钮位置信息
const menuButtonInfo = uni.getMenuButtonBoundingClientRect()
// 根据胶囊位置设置扫码按钮位置
```

这种方案可以与原生导航栏共存，但布局计算较为复杂，且在不同机型上可能需要额外适配。

### 7.3 使用uView UI的u-navbar组件

uView UI提供了更加完善的导航栏组件，支持更多自定义选项：

```html
<u-navbar
  title="工作台"
  :safeAreaInsetTop="true"
  :fixed="true"
  :placeholder="true"
>
  <view slot="left">
    <picker :value="index" :range="array" @change="bindPickerChange">
      <u-icon name="scan" size="24" color="#000000"></u-icon>
    </picker>
  </view>
</u-navbar>
```

这种方案提供了更丰富的配置选项和更好的跨平台兼容性。

## 8. 总结

商家端应用的扫码功能是一个多功能的工具，支持多种业务场景，包括自提订单核销、福利金赠送和收款、优惠券核销等。这些功能通过统一的扫码入口和选择器进行管理，提供了良好的用户体验。

扫码功能的实现主要包含两个关键部分：
1. **导航栏扫码按钮**：通过自定义导航栏和插槽机制，在导航栏左侧放置扫码图标和下拉选择器
2. **扫码业务逻辑**：使用uni-app提供的`uni.scanCode`API，结合后端提供的各种业务接口，完成不同的业务流程

在微信小程序中实现导航栏扫码按钮需要特别注意以下几点：
- 原生导航栏不支持直接放置自定义按钮，需要使用自定义导航栏方案
- 自定义导航栏需要处理好状态栏高度适配和各种机型的兼容性
- 在不同平台上可能需要采用不同的实现策略

通过合理的组件设计和交互实现，扫码功能为商家提供了高效便捷的业务操作方式，大大提升了商家的使用体验。
