# 扫码核销流程优化方案 - 核销确认页面代码

## 核销确认页面完整代码

以下是核销确认页面的完整代码实现，可以直接复制使用：

```vue
<template>
  <view class="scan-code-confirm">
    <!-- 订单信息卡片 -->
    <view class="card order-info">
      <view class="card-title">订单信息</view>
      <view class="info-item">
        <text class="label">订单号：</text>
        <text class="value">{{ orderDetail.orderNo || '--' }}</text>
      </view>
      <view class="info-item">
        <text class="label">下单时间：</text>
        <text class="value">{{ orderDetail.createTime || '--' }}</text>
      </view>
      <view class="info-item">
        <text class="label">自提门店：</text>
        <text class="value">{{ orderDetail.storeName || '--' }}</text>
      </view>
      <view class="info-item">
        <text class="label">订单状态：</text>
        <text class="value">{{ orderDetail.orderStatus || '--' }}</text>
      </view>
    </view>

    <!-- 商品信息卡片 -->
    <view class="card goods-info">
      <view class="card-title">商品信息</view>
      <view class="goods-item" v-for="(item, index) in orderDetail.goodsList" :key="index">
        <image class="goods-image" :src="item.goodsImg" mode="aspectFill"></image>
        <view class="goods-detail">
          <view class="goods-name">{{ item.goodsName }}</view>
          <view class="goods-price">¥{{ item.price }}</view>
          <view class="goods-quantity">数量：{{ item.quantity }}</view>
        </view>
      </view>
    </view>

    <!-- 客户信息卡片 -->
    <view class="card customer-info">
      <view class="card-title">客户信息</view>
      <view class="info-item">
        <text class="label">客户姓名：</text>
        <text class="value">{{ orderDetail.customerName || '--' }}</text>
      </view>
      <view class="info-item">
        <text class="label">联系电话：</text>
        <text class="value">{{ orderDetail.customerPhone || '--' }}</text>
      </view>
    </view>

    <!-- 确认核销按钮 -->
    <view class="confirm-button" @click="confirmVerification">确认核销</view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useRoute } from 'vue-router';

// 获取路由参数
const route = useRoute();
const orderId = ref('');
const orderDetail = ref({
  orderNo: '',
  createTime: '',
  storeName: '',
  orderStatus: '',
  customerName: '',
  customerPhone: '',
  goodsList: []
});
const loading = ref(false);

// 页面加载时获取订单详情
onMounted(() => {
  orderId.value = route.query.id as string;
  if (orderId.value) {
    getOrderDetail(orderId.value);
  } else {
    uni.showToast({
      icon: 'none',
      title: '订单ID无效'
    });
    setTimeout(() => {
      uni.navigateBack();
    }, 1500);
  }
});

// 获取订单详情
const getOrderDetail = (id: string) => {
  loading.value = true;
  uni.showLoading({ title: '加载中...' });

  // 调用订单详情接口
  uni.http.get(uni.api.getOrderDetail, {
    params: { id }
  }).then((response: any) => {
    if (response.data && response.data.success) {
      orderDetail.value = response.data.result || {};
    } else {
      uni.showToast({
        icon: 'none',
        title: response.data?.message || '获取订单信息失败'
      });
      setTimeout(() => {
        uni.navigateBack();
      }, 1500);
    }
  }).catch((error: any) => {
    console.error('获取订单详情失败', error);
    uni.showToast({
      icon: 'none',
      title: '获取订单信息失败，请重试'
    });
    setTimeout(() => {
      uni.navigateBack();
    }, 1500);
  }).finally(() => {
    loading.value = false;
    uni.hideLoading();
  });
};

// 确认核销
const confirmVerification = () => {
  if (loading.value) return;

  uni.showModal({
    title: '确认核销',
    content: '确定要核销此订单吗？',
    success: (res) => {
      if (res.confirm) {
        performVerification();
      }
    }
  });
};

// 执行核销操作
const performVerification = () => {
  loading.value = true;
  uni.showLoading({ title: '核销中...' });

  // 调用核销接口
  uni.http.get(uni.api.pickUpVerification, {
    params: { id: orderId.value }
  }).then((response: any) => {
    // 处理响应
    console.log('核销接口响应:', response.data);

    // 初始化全局变量
    // @ts-ignore
    getApp().globalData = getApp().globalData || {};

    if (response.data && response.data.success === true) {
      // 核销成功
      // @ts-ignore
      getApp().globalData.scanCodeResult = {
        result: '1',
        message: '核销成功'
      };
    } else {
      // 处理失败情况
      const errorMessage = response.data?.message || '核销失败';
      // @ts-ignore
      getApp().globalData.scanCodeResult = {
        result: '0',
        message: errorMessage
      };
    }

    // 跳转到结果页面
    uni.redirectTo({
      url: '/pages/scan-code-result/scan-code-result'
    });
  }).catch((error: any) => {
    console.error('核销请求失败', error);

    // 处理错误情况
    let errorMessage = '';
    if (error && error.data) {
      errorMessage = error.data.message || '';
    }

    if (!errorMessage) {
      errorMessage = '核销失败';
    }

    // @ts-ignore
    getApp().globalData = getApp().globalData || {};
    // @ts-ignore
    getApp().globalData.scanCodeResult = {
      result: '0',
      message: errorMessage
    };

    // 跳转到结果页面
    uni.redirectTo({
      url: '/pages/scan-code-result/scan-code-result'
    });
  }).finally(() => {
    loading.value = false;
    uni.hideLoading();
  });
};
</script>

<style>
.scan-code-confirm {
  padding: 30rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.card {
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.card-title {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
  color: #333;
  border-bottom: 1px solid #eee;
  padding-bottom: 20rpx;
}

.info-item {
  display: flex;
  margin-bottom: 16rpx;
  font-size: 28rpx;
  line-height: 40rpx;
}

.label {
  color: #666;
  width: 180rpx;
}

.value {
  color: #333;
  flex: 1;
}

.goods-item {
  display: flex;
  padding: 20rpx 0;
  border-bottom: 1px solid #f5f5f5;
}

.goods-image {
  width: 160rpx;
  height: 160rpx;
  border-radius: 8rpx;
  margin-right: 20rpx;
  background-color: #f9f9f9;
}

.goods-detail {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.goods-name {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 10rpx;
}

.goods-price {
  font-size: 32rpx;
  color: #ff6b00;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.goods-quantity {
  font-size: 26rpx;
  color: #999;
}

.confirm-button {
  background-color: #3399FF;
  color: #fff;
  height: 90rpx;
  line-height: 90rpx;
  text-align: center;
  border-radius: 45rpx;
  font-size: 32rpx;
  margin-top: 60rpx;
}
</style>
```
