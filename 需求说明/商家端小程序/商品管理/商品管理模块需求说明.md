# 商品管理模块需求说明文档

## 1. 概述

本文档描述了"八闽助业集市"商家端应用中商品管理模块的需求和功能规范。该模块参考拼多多商家端的商品管理功能，旨在为商家提供全面的商品管理能力，包括商品的上架、下架、编辑、库存管理等功能。

### 1.1 目标用户

- 平台入驻商家：需要管理自己店铺中的商品

### 1.2 业务价值

- 为商家提供便捷的商品管理界面
- 支持商品全生命周期管理（从草稿到上架到下架）
- 提高商家运营效率

## 2. 页面结构

商品管理模块主要由以下几个部分组成：

### 2.1 顶部导航栏

- 左侧返回按钮
- 中间标题"商品管理"
- 右侧搜索按钮

### 2.2 状态分类标签页

包含六个标签页，用于区分不同状态的商品：

1. **在售中**：当前正在销售的商品
2. **已下架**：曾经上架但现在已下架的商品
3. **已售罄**：库存为0的商品
4. **发布中**：正在审核或等待上架的商品
5. **已驳回**：审核未通过的商品
6. **草稿箱**：尚未提交审核的商品草稿

每个标签页后面显示该状态下的商品数量，如"在售中(16)"。

### 2.3 筛选与排序区域

- 左侧排序下拉菜单（根据不同标签页有不同的排序选项）：
  - 在售中：最新创建在上
  - 已下架：最新下架在上
  - 已售罄：最新售罄在上
  - 发布中：无特定排序
  - 已驳回：无特定排序
  - 草稿箱：编辑时间
  
- 右侧筛选按钮
- 右侧批量操作按钮

### 2.4 通知/提示区域

- 橙色背景的通知条，显示与当前标签页相关的提示信息
- 左侧感叹号图标
- 右侧数字标记和箭头，表示有多条通知可查看

### 2.5 商品列表区域

- 每个商品项包含：
  - 商品图片（左侧）
  - 商品标题（可能会被截断）
  - 商品销售数据（累计销量、30日销量、库存）
  - 商品价格（单一价格或价格区间）
  - 操作按钮（根据商品状态不同而不同）

### 2.6 底部操作区域

- 左侧功能按钮（如"机会商品"、"回收站"等）
- 右侧主要操作按钮（"发布商品"）

### 2.7 空状态展示

当某个标签页下没有商品时，显示：
- 空状态插图（购物袋图标）
- "暂无相关商品"文字提示

## 3. 功能需求

### 3.1 通用功能

- **搜索功能**：通过点击顶部搜索图标，可以搜索商品
- **标签页切换**：点击不同标签可以切换查看不同状态的商品
- **排序功能**：可以按照不同条件对商品列表进行排序
- **筛选功能**：可以根据多种条件筛选商品
- **批量操作**：可以选择多个商品进行批量操作

### 3.2 在售中商品功能

- **查看商品**：显示所有在售中的商品
- **商品操作**：
  - 更多：查看更多操作选项
  - 下架：将商品从在售状态变为下架状态
  - 改库存：修改商品库存数量
  - 改价：修改商品价格
  - 编辑：编辑商品详情

### 3.3 已下架商品功能

- **查看商品**：显示所有已下架的商品
- **商品操作**：
  - 删除：删除下架商品
  - 改价：修改商品价格
  - 编辑：编辑商品详情
  - 上架：将商品重新上架

### 3.4 已售罄商品功能

- **查看商品**：显示所有库存为0的商品
- **空状态展示**：当没有售罄商品时，显示空状态页面

### 3.5 发布中商品功能

- **查看商品**：显示所有正在审核或等待上架的商品
- **空状态展示**：当没有发布中商品时，显示空状态页面

### 3.6 已驳回商品功能

- **查看商品**：显示所有审核未通过的商品
- **空状态展示**：当没有驳回商品时，显示空状态页面

### 3.7 草稿箱功能

- **查看商品**：显示所有尚未提交审核的商品草稿
- **空状态展示**：当没有草稿商品时，显示空状态页面

### 3.8 发布商品功能

- 点击底部"发布商品"按钮，进入商品发布流程

## 4. 交互设计

### 4.1 标签页切换

- 用户点击标签页，切换到对应的商品列表
- 当前选中的标签页下方有下划线标识
- 切换标签页时，商品列表内容和操作按钮会相应变化

### 4.2 商品操作

#### 4.2.1 在售中商品操作

- **更多按钮**：点击后可能弹出更多操作选项
- **下架按钮**：点击后弹出确认对话框，确认后商品下架
- **改库存按钮**：点击后弹出库存编辑界面，可修改库存数量
- **改价按钮**：点击后弹出价格编辑界面，可修改商品价格
- **编辑按钮**：点击后跳转到商品编辑页面

#### 4.2.2 已下架商品操作

- **删除按钮**：点击后弹出确认对话框，确认后删除商品
- **改价按钮**：点击后弹出价格编辑界面，可修改商品价格
- **编辑按钮**：点击后跳转到商品编辑页面
- **上架按钮**：点击后商品重新上架（可能需要审核）

### 4.3 筛选与排序

- **排序下拉菜单**：点击后展开排序选项，选择后按所选条件排序
- **筛选按钮**：点击后弹出筛选面板，可设置多种筛选条件
- **批量操作按钮**：点击后进入批量选择模式，可选择多个商品进行操作

### 4.4 通知/提示区域

- 点击通知条可查看详细信息或执行相关操作
- 右侧数字和箭头表示可以查看更多通知

### 4.5 底部操作区域

- **机会商品按钮**：点击后可能跳转到特殊推广商品页面
- **回收站按钮**（如有）：点击后跳转到已删除商品页面
- **发布商品按钮**：点击后进入商品发布流程

## 5. 数据结构

### 5.1 商品基本信息

```javascript
{
  id: String,                // 商品ID
  title: String,             // 商品标题
  image: String,             // 商品主图URL
  price: Number,             // 商品价格（单一价格）
  minPrice: Number,          // 最低价格（多规格商品）
  maxPrice: Number,          // 最高价格（多规格商品）
  status: String,            // 商品状态：在售/下架/售罄/发布中/已驳回/草稿
  totalSales: Number,        // 累计销量
  monthlySales: Number,      // 30日销量
  inventory: Number,         // 库存数量
  createTime: Date,          // 创建时间
  updateTime: Date,          // 更新时间
  publishTime: Date,         // 发布时间
  offShelfTime: Date,        // 下架时间
  rejectReason: String,      // 驳回原因（仅已驳回状态有此字段）
}
```

### 5.2 商品状态枚举

```javascript
const GOODS_STATUS = {
  ON_SALE: 'onSale',           // 在售中
  OFF_SHELF: 'offShelf',       // 已下架
  SOLD_OUT: 'soldOut',         // 已售罄
  PUBLISHING: 'publishing',    // 发布中
  REJECTED: 'rejected',        // 已驳回
  DRAFT: 'draft'               // 草稿箱
}
```

## 6. API接口需求

### 6.1 获取商品列表

```
GET /api/merchant/goods/list
```

**请求参数：**
- status: String (必填) - 商品状态
- page: Number (必填) - 页码，从1开始
- pageSize: Number (必填) - 每页数量
- sortBy: String (可选) - 排序字段
- sortOrder: String (可选) - 排序方式：asc/desc
- keyword: String (可选) - 搜索关键词

**响应数据：**
```javascript
{
  code: Number,           // 状态码，0表示成功
  message: String,        // 状态描述
  data: {
    total: Number,        // 总商品数
    list: Array,          // 商品列表
    page: Number,         // 当前页码
    pageSize: Number      // 每页数量
  }
}
```

### 6.2 商品上下架

```
POST /api/merchant/goods/changeStatus
```

**请求参数：**
- goodsId: String (必填) - 商品ID
- status: String (必填) - 目标状态：onSale/offShelf

**响应数据：**
```javascript
{
  code: Number,           // 状态码，0表示成功
  message: String,        // 状态描述
  data: Boolean           // 操作结果
}
```

### 6.3 修改商品价格

```
POST /api/merchant/goods/updatePrice
```

**请求参数：**
- goodsId: String (必填) - 商品ID
- price: Number (可选) - 单一价格
- skuPrices: Array (可选) - 多规格价格列表

**响应数据：**
```javascript
{
  code: Number,           // 状态码，0表示成功
  message: String,        // 状态描述
  data: Boolean           // 操作结果
}
```

### 6.4 修改商品库存

```
POST /api/merchant/goods/updateInventory
```

**请求参数：**
- goodsId: String (必填) - 商品ID
- inventory: Number (可选) - 总库存
- skuInventories: Array (可选) - 多规格库存列表

**响应数据：**
```javascript
{
  code: Number,           // 状态码，0表示成功
  message: String,        // 状态描述
  data: Boolean           // 操作结果
}
```

### 6.5 删除商品

```
POST /api/merchant/goods/delete
```

**请求参数：**
- goodsId: String (必填) - 商品ID

**响应数据：**
```javascript
{
  code: Number,           // 状态码，0表示成功
  message: String,        // 状态描述
  data: Boolean           // 操作结果
}
```

## 7. 页面布局与样式规范

### 7.1 颜色规范

- 主色调：与"八闽助业集市"平台主题一致
- 标签页文字颜色：选中状态为主题色，未选中状态为灰色
- 通知/提示区域：橙色背景 (#FFF5E5)，黑色文字
- 价格文字：红色 (#FF0000)
- 按钮颜色：
  - 主要操作按钮（如"发布商品"）：蓝色 (#4A90E2)
  - 次要操作按钮（如"编辑"、"改价"）：白底黑边
  - 危险操作按钮（如"下架"、"删除"）：白底黑边

### 7.2 字体规范

- 标题文字：18px，粗体
- 标签页文字：16px
- 商品标题：14px
- 商品数据：12px，灰色
- 商品价格：16px，红色，粗体
- 按钮文字：14px

### 7.3 间距规范

- 标签页之间间距：适当留白
- 商品项之间间距：10px
- 商品图片与文字间距：10px
- 操作按钮之间间距：5px

### 7.4 响应式设计

- 适配不同尺寸的移动设备
- 在较小屏幕上可能需要调整布局和字体大小

## 8. 开发注意事项

### 8.1 技术栈

- 前端框架：UniApp
- UI组件库：项目现有组件库
- 状态管理：Vuex/Pinia

### 8.2 性能优化

- 商品列表采用虚拟列表或分页加载，避免一次性加载过多数据
- 图片懒加载，优化首屏加载速度
- 合理使用缓存，减少不必要的网络请求

### 8.3 兼容性

- 确保在各主流小程序平台（微信、支付宝等）上正常运行
- 适配不同分辨率和屏幕尺寸的移动设备

### 8.4 安全性

- 所有API请求需要进行身份验证
- 敏感操作（如下架、删除）需要二次确认

## 9. 后续迭代计划

### 9.1 第一期（基础功能）

- 实现六个标签页的基本展示
- 实现商品列表的展示和基本操作（上下架、改价、改库存）
- 实现发布商品的基本流程

### 9.2 第二期（增强功能）

- 增加商品批量操作功能
- 增加更多筛选条件
- 优化商品编辑界面
- 增加数据统计和分析功能

### 9.3 第三期（高级功能）

- 增加商品推广功能
- 增加智能定价建议
- 增加库存预警和自动补货功能
- 增加商品复制功能

## 10. 总结

本文档详细描述了"八闽助业集市"商家端应用中商品管理模块的需求和功能规范。该模块参考拼多多商家端的商品管理功能，旨在为商家提供全面的商品管理能力。通过实现本文档中描述的功能，可以满足商家对商品全生命周期管理的需求，提高商家运营效率。
