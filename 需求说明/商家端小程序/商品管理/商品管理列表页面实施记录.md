# 商品管理列表页面实施记录

## 1. 需求概述

根据《商品管理模块需求说明.md》文档，实现商品管理列表页面的静态页面，包括以下主要功能模块：

1. 顶部导航栏
2. 状态分类标签页（在售中、已下架、已售罄、发布中、已驳回、草稿箱）
3. 筛选与排序区域
4. 通知/提示区域
5. 商品列表区域
6. 底部操作区域

参考了拼多多商家端的商品管理功能，设计了符合"八闽助业集市"商家端应用风格的商品管理页面。

## 2. 页面结构实现

### 2.1 顶部导航栏

实现了包含返回按钮、标题和搜索按钮的顶部导航栏，样式为白底黑字，与应用整体风格保持一致。

```vue
<view class="nav-bar">
  <view class="nav-bar-left" @click="goBack">
    <uni-icons type="left" size="20"></uni-icons>
  </view>
  <view class="nav-bar-title">商品管理</view>
  <view class="nav-bar-right" @click="goSearch">
    <uni-icons type="search" size="20"></uni-icons>
  </view>
</view>
```

### 2.2 状态分类标签页

实现了六个标签页，用于区分不同状态的商品：在售中、已下架、已售罄、发布中、已驳回、草稿箱。每个标签页后面显示该状态下的商品数量。

```vue
<scroll-view scroll-x class="tab-scroll" show-scrollbar="false">
  <view class="tab-container">
    <view 
      v-for="(tab, index) in tabs" 
      :key="index" 
      class="tab-item" 
      :class="{active: currentTab === index}"
      @click="changeTab(index)"
    >
      <text>{{tab.name}}({{tab.count}})</text>
      <view v-if="currentTab === index" class="tab-line"></view>
    </view>
  </view>
</scroll-view>
```

### 2.3 筛选与排序区域

实现了排序下拉菜单和筛选、批量操作按钮，用户可以通过这些功能对商品列表进行排序和筛选。

```vue
<view class="filter-sort-area">
  <view class="sort-dropdown" @click="toggleSortDropdown">
    <text>{{currentSort}}</text>
    <uni-icons :type="sortDropdownVisible ? 'top' : 'bottom'" size="14"></uni-icons>
  </view>
  <view class="action-buttons">
    <view class="filter-button" @click="openFilter">
      <uni-icons type="settings" size="18"></uni-icons>
      <text>筛选</text>
    </view>
    <view class="batch-button" @click="startBatchOperation">
      <uni-icons type="checkbox" size="18"></uni-icons>
      <text>批量操作</text>
    </view>
  </view>
</view>
```

### 2.4 通知/提示区域

实现了橙色背景的通知条，显示与当前标签页相关的提示信息，如库存预警、审核状态等。

```vue
<view class="notification-area" v-if="notifications.length > 0">
  <view class="notification-content">
    <uni-icons type="info" size="16" color="#FF9500"></uni-icons>
    <text class="notification-text">{{notifications[currentNotificationIndex].content}}</text>
  </view>
  <view class="notification-indicator" v-if="notifications.length > 1">
    <text>{{currentNotificationIndex + 1}}/{{notifications.length}}</text>
    <uni-icons type="right" size="14"></uni-icons>
  </view>
</view>
```

### 2.5 商品列表区域

实现了商品列表，每个商品项包含商品图片、标题、销售数据、价格和操作按钮。根据不同的商品状态，显示不同的操作按钮。

```vue
<view class="goods-item" v-for="(item, goodsIndex) in onSaleGoods" :key="goodsIndex">
  <view class="goods-item-left">
    <image class="goods-image" :src="item.image" mode="aspectFill"></image>
  </view>
  <view class="goods-item-right">
    <view class="goods-title">{{item.title}}</view>
    <view class="goods-data">
      <text>累计销量: {{item.totalSales}}</text>
      <text>30日销量: {{item.monthlySales}}</text>
      <text>库存: {{item.inventory}}</text>
    </view>
    <view class="goods-bottom">
      <view class="goods-price" v-if="item.minPrice === item.maxPrice">¥ {{item.price}}</view>
      <view class="goods-price" v-else>¥ {{item.minPrice}} ~ {{item.maxPrice}}</view>
      <view class="goods-actions">
        <button class="action-btn more-btn" @click="showMoreActions(item)">更多</button>
        <button class="action-btn" @click="offShelfGoods(item)">下架</button>
        <button class="action-btn" @click="changeInventory(item)">改库存</button>
        <button class="action-btn" @click="changePrice(item)">改价</button>
        <button class="action-btn" @click="editGoods(item)">编辑</button>
      </view>
    </view>
  </view>
</view>
```

### 2.6 底部操作区域

实现了底部操作区域，包含左侧功能按钮（机会商品、回收站）和右侧主要操作按钮（发布商品）。

```vue
<view class="bottom-action-area">
  <view class="left-actions">
    <view class="action-btn" @click="goToOpportunityGoods">
      <uni-icons type="star" size="16"></uni-icons>
      <text>机会商品</text>
    </view>
    <view class="action-btn" @click="goToRecycleBin">
      <uni-icons type="trash" size="16"></uni-icons>
      <text>回收站</text>
    </view>
  </view>
  <view class="right-actions">
    <view class="publish-btn" @click="publishGoods">
      <text>发布商品</text>
    </view>
  </view>
</view>
```

### 2.7 空状态展示

当某个标签页下没有商品时，使用项目中的空状态组件显示"暂无相关商品"提示。

```vue
<template v-else>
  <empty text="暂无相关商品"></empty>
</template>
```

## 3. 功能实现

### 3.1 标签页切换

实现了标签页切换功能，用户可以通过点击标签页或左右滑动切换不同状态的商品列表。

```javascript
// 切换标签页
function changeTab(index) {
  currentTab.value = index;
}

// 滑动切换标签页
function swiperChange(e) {
  currentTab.value = e.detail.current;
}
```

### 3.2 排序功能

实现了排序下拉菜单，用户可以选择不同的排序方式。

```javascript
// 切换排序下拉菜单
function toggleSortDropdown() {
  sortDropdownVisible.value = !sortDropdownVisible.value;
}

// 选择排序方式
function selectSort(index) {
  currentSortIndex.value = index;
  sortDropdownVisible.value = false;
}
```

### 3.3 商品操作

实现了商品的各种操作功能，包括下架、改库存、改价、编辑等。

```javascript
// 下架商品
function offShelfGoods(item) {
  uni.showModal({
    title: '提示',
    content: `确定要下架商品「${item.title}」吗？`,
    success: function(res) {
      if (res.confirm) {
        uni.showToast({
          title: '下架成功',
          icon: 'success'
        });
        // 这里暂时不实现实际下架逻辑，等后续对接接口
      }
    }
  });
}
```

## 4. 样式实现

### 4.1 整体布局

采用Flex布局，实现了页面的整体结构和各个区域的布局。

```scss
.goods-container {
  background-color: #f8f8f8;
  min-height: 100vh;
  position: relative;
  display: flex;
  flex-direction: column;
}
```

### 4.2 标签页样式

实现了标签页的样式，包括选中状态的下划线标识。

```scss
.tab-item {
  padding: 0 15px;
  height: 100%;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  position: relative;
  font-size: 16px;
  color: #666;
  
  &.active {
    color: #4A90E2;
    font-weight: bold;
  }
  
  .tab-line {
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 20px;
    height: 3px;
    background-color: #4A90E2;
    border-radius: 2px;
  }
}
```

### 4.3 商品列表样式

实现了商品列表的样式，包括商品项的布局、商品标题的多行省略、价格和操作按钮的样式。

```scss
.goods-item {
  background-color: #ffffff;
  border-radius: 8px;
  margin-bottom: 10px;
  padding: 10px;
  display: flex;
  
  .goods-item-left {
    width: 80px;
    height: 80px;
    margin-right: 10px;
    
    .goods-image {
      width: 100%;
      height: 100%;
      border-radius: 4px;
    }
  }
  
  .goods-item-right {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    
    .goods-title {
      font-size: 14px;
      color: #333;
      line-height: 1.3;
      height: 36px;
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      line-clamp: 2;
      -webkit-box-orient: vertical;
    }
    
    // 其他样式...
  }
}
```

## 5. 数据模拟

由于暂时不对接接口，使用模拟数据展示商品列表。

```javascript
// 模拟商品数据
const onSaleGoods = ref([
  {
    id: '1',
    title: '八闽特产武夷山大红袍茶叶礼盒装送礼高档礼品茶叶礼盒过年送礼',
    image: '/static/images/default-product.png',
    price: 128,
    minPrice: 128,
    maxPrice: 128,
    status: 'onSale',
    totalSales: 256,
    monthlySales: 68,
    inventory: 120,
    createTime: '2025-03-15',
    updateTime: '2025-04-20',
    publishTime: '2025-03-18'
  },
  // 其他商品数据...
]);
```

## 6. 后续计划

1. 对接后端接口，实现商品数据的真实加载和操作
2. 完善筛选功能，支持更多筛选条件
3. 实现批量操作功能
4. 优化商品列表的性能，支持虚拟列表或分页加载
5. 实现商品发布和编辑页面

## 7. 总结

本次实施完成了商品管理列表页面的静态页面开发，包括顶部导航栏、状态分类标签页、筛选与排序区域、通知/提示区域、商品列表区域和底部操作区域。页面布局合理，样式美观，符合需求文档的要求。

后续将继续完善功能，对接后端接口，实现商品管理的完整功能。
