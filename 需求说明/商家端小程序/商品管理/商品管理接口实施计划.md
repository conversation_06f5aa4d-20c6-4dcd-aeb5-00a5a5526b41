# 商品管理模块接口实施计划

## 文档说明

本文档详细描述商品管理模块的接口实施情况，包括已对接接口的详细说明和未对接接口的规划。

**文档更新日期**：2025-05-09

## 1. 已对接接口

### 1.1 商品状态统计接口

- **接口名称**：获取商品状态统计数据
- **接口地址**：`/back/goodList/getGoodStoreCount`
- **请求方式**：GET
- **接口说明**：获取各状态商品数量（在售中、已下架、已售罄、发布中、已驳回、草稿箱）
- **请求参数**：无
- **响应数据结构**：
  ```javascript
  {
    code: 200,                    // 状态码，200表示成功
    result: {
      goodOnSaleCount: Number,        // 在售中商品数量
      goodSoldOutCount: Number,       // 已下架商品数量
      goodSoldOutOfStockCount: Number, // 已售罄商品数量
      goodPublishingCount: Number,    // 发布中商品数量
      goodRejectedCount: Number,      // 已驳回商品数量
      goodDraftsCount: Number         // 草稿箱商品数量
    },
    message: String               // 响应消息
  }
  ```
- **前端调用位置**：
  - `loadGoodStatusCount` 函数 - 加载商品状态统计数据
  - `refreshAllData` 函数 - 刷新所有数据时调用
- **实施状态**：✅ 已完成

### 1.2 商品列表分页查询接口

- **接口名称**：获取商品列表（分页）
- **接口地址**：`/back/goodList/queryPageListNew`
- **请求方式**：GET
- **接口说明**：根据条件分页查询商品列表
- **请求参数**：
  ```javascript
  {
    goodStatus: String,  // 商品状态（1:在售中, 2:已下架, 3:已售罄, 4:发布中, 5:已驳回, 6:草稿箱）
    level: String,       // 级别（默认-1）
    typeId: String,      // 分类ID
    pageNo: Number,      // 页码，默认为1
    pageSize: Number     // 每页条数，默认为10
  }
  ```
- **响应数据结构**：
  ```javascript
  {
    code: 200,                    // 状态码，200表示成功
    result: {
      records: [                  // 商品记录数组
        {
          id: String,             // 商品ID
          goodName: String,       // 商品名称
          mainPicture: String,    // 商品主图（JSON字符串）
          minPrice: Number,       // 最低价格
          maxPrice: Number,       // 最高价格
          salesVolume: Number,    // 累计销量
          monthlySales: Number,   // 30日销量
          repertory: Number,      // 库存数量
          auditStatus: String,    // 审核状态
          frameStatus: String,    // 上架状态
          status: String,         // 商品状态
          delFlag: String         // 删除标记
        }
      ],
      total: Number,              // 总记录数
      current: Number,            // 当前页码
      size: Number                // 每页大小
    },
    message: String               // 响应消息
  }
  ```
- **前端调用位置**：
  - `loadGoodsList` 函数 - 加载商品列表数据
  - `loadTabData` 函数 - 切换标签页时加载数据
- **实施状态**：✅ 已完成

### 1.3 商品图片解析工具

- **工具名称**：解析商品主图
- **功能说明**：解析商品主图JSON字符串，返回第一张图片的完整URL
- **参数**：
  ```javascript
  mainPicture: String  // 商品主图JSON字符串
  ```
- **返回值**：
  ```javascript
  String  // 第一张图片的完整URL
  ```
- **前端调用位置**：
  - `formatGoodItem` 函数 - 格式化商品数据时处理图片URL
- **实施状态**：✅ 已完成

## 2. 接口实施状态

### 2.1 商品操作类接口

#### 2.1.1 商品删除/下架接口

- **接口名称**：商品删除/下架
- **接口说明**：将商品下架或删除
- **前端功能**：`takeOffShelf` 函数
- **UI位置**：各状态标签页中的"下架"或"删除"按钮
- **实施状态**：✅ 已完成
- **实施时间**：2025-05-10
- **实施说明**：
  - 已对接 `/back/goodList/delete` 和 `/back/goodList/updateFrameStatus` 接口
  - 根据当前标签页状态执行不同操作（在售中执行下架，其他执行删除）
  - 添加了加载状态提示和错误处理
  - 操作成功后自动刷新数据

#### 2.1.2 商品上架接口

- **接口名称**：商品上架
- **接口说明**：将已下架的商品重新上架
- **前端功能**：`putOnShelf` 函数
- **UI位置**：已下架标签页中的"上架"按钮
- **实施状态**：✅ 已完成
- **实施时间**：2025-05-10
- **实施说明**：
  - 已对接 `/back/goodList/updateFrameStatus` 接口
  - 添加了加载状态提示和错误处理
  - 操作成功后自动刷新数据

#### 2.1.3 修改库存接口

- **接口名称**：修改商品库存
- **接口说明**：修改商品的库存数量
- **前端功能**：`changeInventory` 函数
- **UI位置**：在售中和已售罄标签页中的"改库存"按钮
- **实施状态**：✅ 已完成
- **实施时间**：2025-05-12
- **实施说明**：
  - 已对接 `/back/goodList/getGoodPriceAndRepertory` 和 `/back/goodList/updateGoodPriceAndRepertory` 接口
  - 实现了单规格和多规格商品的库存修改
  - 支持批量设置多规格商品的库存
  - 添加了库存变更验证和错误处理
  - 操作成功后自动刷新数据

#### 2.1.4 修改价格接口

- **接口名称**：修改商品价格
- **接口说明**：修改商品的价格
- **前端功能**：`changePrice` 函数
- **UI位置**：在售中、已下架和已售罄标签页中的"改价"按钮
- **实施状态**：✅ 已完成
- **实施时间**：2025-05-15
- **实施说明**：
  - 已对接 `/back/goodList/getGoodPriceAndRepertory` 和 `/back/goodList/updateGoodPriceAndRepertory` 接口
  - 创建 `goods-price.vue` 页面，实现价格修改功能
  - 支持单规格和多规格商品的价格修改
  - 添加价格输入验证和错误处理
  - 操作成功后自动刷新数据

#### 2.1.5 编辑商品接口

- **接口名称**：编辑商品
- **接口说明**：编辑商品的详细信息
- **前端功能**：`editGoods` 函数
- **UI位置**：各状态标签页中的"编辑"按钮（已隐藏）
- **实施状态**：❌ 未完成（仅有UI交互，未对接后端接口）
- **预计实施时间**：待定

#### 2.1.6 发布商品接口

- **接口名称**：发布商品
- **接口说明**：发布新商品或发布草稿箱中的商品
- **前端功能**：`publishGoods` 和 `publishItem` 函数
- **UI位置**：
  - 底部的"发布商品"按钮（已隐藏）
  - 已驳回和草稿箱标签页中的"发布"按钮
- **实施状态**：❌ 未完成（仅有UI交互，未对接后端接口）
- **预计实施时间**：待定

### 2.2 页面交互类接口

#### 2.2.1 商品搜索接口

- **接口名称**：商品搜索
- **接口说明**：根据关键词搜索商品
- **前端功能**：`goSearch` 函数
- **UI位置**：筛选与排序区域的"搜索"按钮
- **实施状态**：❌ 未完成（已有页面跳转，未对接搜索接口）
- **预计实施时间**：待定

#### 2.2.2 商品筛选接口

- **接口名称**：商品筛选
- **接口说明**：根据多种条件筛选商品
- **前端功能**：`openFilter` 函数
- **UI位置**：筛选与排序区域的"筛选"按钮
- **实施状态**：❌ 未完成（仅有UI交互，未对接筛选接口）
- **预计实施时间**：待定

#### 2.2.3 批量操作接口

- **接口名称**：批量操作
- **接口说明**：批量处理多个商品
- **前端功能**：`startBatchOperation` 函数
- **UI位置**：筛选与排序区域的"批量"按钮
- **实施状态**：❌ 未完成（仅有UI交互，未对接批量操作接口）
- **预计实施时间**：待定

#### 2.2.4 机会商品接口

- **接口名称**：机会商品
- **接口说明**：特殊推广商品功能
- **前端功能**：`goToOpportunityGoods` 函数
- **UI位置**：底部操作区域的"机会商品"按钮（已隐藏）
- **实施状态**：❌ 未完成（仅有UI交互，未对接相关接口）
- **预计实施时间**：待定

#### 2.2.5 回收站接口

- **接口名称**：回收站
- **接口说明**：查看已删除商品
- **前端功能**：`goToRecycleBin` 函数
- **UI位置**：底部操作区域的"回收站"按钮（已隐藏）
- **实施状态**：❌ 未完成（仅有UI交互，未对接相关接口）
- **预计实施时间**：待定

### 2.3 排序功能

- **功能名称**：商品排序
- **功能说明**：根据不同条件排序商品列表
- **前端功能**：`selectSort` 函数
- **UI位置**：筛选与排序区域的排序下拉菜单
- **实施状态**：⚠️ 部分完成（已有UI和本地排序切换，但排序参数未传递到后端接口）
- **预计实施时间**：待定

## 3. 接口实施优先级建议

根据业务需求和用户体验，建议按以下优先级实施未完成的接口：

1. **高优先级**
   - ✅ 商品上架接口（已完成）
   - ✅ 商品下架接口（已完成）
   - ✅ 修改库存接口（已完成）
   - ✅ 修改价格接口（已完成）

2. **中优先级**
   - 商品搜索接口
   - 商品筛选接口
   - 排序功能完善

3. **低优先级**
   - 编辑商品接口
   - 发布商品接口
   - 批量操作接口
   - 机会商品接口
   - 回收站接口

## 4. 后续工作计划

1. ✅ 与后端开发人员确认各接口的具体参数和返回值（已完成商品上下架和删除接口）
2. 按优先级逐步实施未完成的接口
   - ✅ 商品删除/下架接口（已完成）
   - ✅ 商品上架接口（已完成）
   - ✅ 修改库存接口（已完成）
   - ✅ 修改价格接口（已完成）
3. 对已实现的接口进行测试和优化
4. 完善错误处理和异常情况的用户提示
5. 优化页面性能和用户体验

## 5. 备注

- 当前页面已完成基本的数据展示功能，包括商品列表和状态数量统计
- 部分UI按钮（如"发布商品"、"编辑"、"机会商品"、"回收站"）已按需求隐藏
- 已完成商品删除/下架、商品上架、修改库存和修改价格功能的接口对接
- 已下架商品页签增加了"改库存"按钮，放在"改价"按钮前面
- 其他未完成接口的前端交互逻辑已实现，只需对接实际后端接口即可完成功能

## 6. 更新日志

### 2025-05-10

- 完成商品删除/下架接口实施
  - 添加 `deleteGood` API函数
  - 更新 `takeOffShelf` 函数，根据当前标签页状态执行不同操作
  - 添加加载状态和错误处理
  - 操作成功后自动刷新数据

- 完成商品上架接口实施
  - 添加 `updateGoodFrameStatus` API函数
  - 实现 `putOnShelf` 函数调用上架API
  - 添加加载状态和错误处理
  - 操作成功后自动刷新数据

### 2025-05-12

- 完成修改库存接口实施
  - 添加 `getGoodPriceAndRepertory` 和 `updateGoodPriceAndRepertory` API函数
  - 创建 `goods-inventory.vue` 页面，实现库存修改功能
  - 支持单规格和多规格商品的库存修改
  - 实现批量设置多规格商品库存功能
  - 更新 `changeInventory` 函数，实现跳转到库存修改页面
  - 添加加载状态、数据验证和错误处理
  - 操作成功后自动刷新数据

### 2025-05-15

- 完成修改价格接口实施
  - 复用 `getGoodPriceAndRepertory` 和 `updateGoodPriceAndRepertory` API函数
  - 创建 `goods-price.vue` 页面，实现价格修改功能
  - 支持单规格和多规格商品的价格修改
  - 优化价格输入框UI，提供更好的用户体验
  - 更新 `changePrice` 函数，实现跳转到价格修改页面
  - 添加价格输入验证和错误处理
  - 操作成功后自动刷新数据
