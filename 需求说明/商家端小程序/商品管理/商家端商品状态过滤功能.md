# 商家端商品状态过滤功能

## 需求背景

商家端需要对商品列表进行状态过滤，以便商家能够更方便地管理不同状态的商品。

## 功能描述

在商家端商品列表页面，添加商品状态过滤功能，支持以下状态过滤：

1. 全部商品
2. 在售中商品（已上架且库存大于0）
3. 已下架商品
4. 已售罄商品（已上架但库存为0）
5. 发布中商品（审核中）
6. 已驳回商品
7. 草稿箱商品
8. 回收站商品

每个状态标签后显示对应状态的商品数量。

## 技术实现

### 后端实现

1. 在商品主表 `good_store_list` 中添加库存字段 `repertory`，用于存储商品的总库存。

2. 修改 `GoodStoreListMapper.xml` 文件，添加各状态的查询条件：
   - 在售中：已上架且库存大于0
   - 已下架：已下架
   - 已售罄：已上架但库存为0或为NULL
   - 发布中：审核中
   - 已驳回：已驳回
   - 草稿箱：草稿状态
   - 回收站：已删除

3. 在 `IGoodStoreListService` 接口中添加 `getGoodStoreListCount` 方法，用于获取各状态的商品数量。

4. 在 `GoodStoreListServiceImpl` 类中实现 `getGoodStoreListCount` 方法，根据不同的状态参数返回对应状态的商品数量。

5. 修改 `BackGoodListController` 中的 `getGoodStoreListMapList` 方法，添加各状态商品数量的统计，并在返回结果中包含这些数量。

### 前端实现

1. 创建 `GoodStatusFilter.vue` 组件，用于显示状态过滤标签，并支持点击切换状态。

2. 创建 `goodList.js` API文件，封装与商品列表相关的API请求。

3. 创建 `GoodListPage.vue` 页面，集成状态过滤组件和商品列表展示。

## 状态定义

| 状态值 | 状态名称 | 查询条件 |
| ------ | -------- | -------- |
| 0 | 全部商品 | del_flag = 0 AND audit_status != 0 |
| 1 | 在售中 | del_flag = 0 AND audit_status = 2 AND status = 1 AND frame_status = 1 AND repertory > 0 |
| 2 | 已下架 | del_flag = 0 AND audit_status = 2 AND status = 1 AND frame_status = 0 |
| 3 | 已售罄 | del_flag = 0 AND audit_status = 2 AND status = 1 AND frame_status = 1 AND (repertory = 0 OR repertory IS NULL) |
| 4 | 发布中 | del_flag = 0 AND audit_status = 1 AND status = 1 |
| 5 | 已驳回 | del_flag = 0 AND audit_status = 3 AND status = 1 |
| 6 | 草稿箱 | del_flag = 0 AND audit_status = 0 |
| 7 | 回收站 | del_flag = 1 |

## 接口说明

### 商品列表接口（新）

**请求路径**：`/good/backGoodList/queryPageListNew`

**请求方式**：GET

**请求参数**：

| 参数名 | 类型 | 必填 | 描述 |
| ------ | ---- | ---- | ---- |
| goodStatus | String | 否 | 商品状态，默认为0（全部） |
| level | String | 否 | 级别，默认为-1 |
| typeId | String | 否 | 分类ID |
| pageNo | Integer | 否 | 页码，默认为1 |
| pageSize | Integer | 否 | 每页条数，默认为10 |

**返回参数**：

```json
{
  "success": true,
  "message": "操作成功!",
  "code": 200,
  "result": {
    "records": [
      {
        "id": "1001",
        "goodName": "八闽特产武夷山大红袍茶叶礼盒装送礼...",
        "mainPicture": "https://example.com/images/tea.jpg",
        "salesVolume": 256,             // 累计销量
        "monthlySales": 68,             // 30日销量
        "repertory": 120,               // 库存
        "minPrice": 128,                // 最低价格
        "maxPrice": 128,                // 最高价格（与最低价格相同时，前端只显示一个价格）
        "auditStatus": "2",             // 审核状态：0=草稿，1=发布中，2=审核通过，3=已驳回
        "frameStatus": "1",             // 上下架状态：0=下架，1=上架
        "status": "1",                  // 状态：0=停用，1=启用
        "delFlag": "0",                 // 删除状态：0=正常，1=已删除
        "isSpecification": "0",         // 是否有规格：0=无规格，1=有规格
        "shopInfo": "[{\"salesPrice\":128,\"vipPrice\":118,\"repertory\":120}]", // 商品信息
        "specificationsDecribes": "[]"  // 规格描述
      },
      {
        "id": "1002",
        "goodName": "福建特产古田银耳干货特级野生白木耳...",
        "mainPicture": "https://example.com/images/mushroom.jpg",
        "salesVolume": 1024,            // 累计销量
        "monthlySales": 320,            // 30日销量
        "repertory": 500,               // 库存
        "minPrice": 39.9,               // 最低价格
        "maxPrice": 59.9,               // 最高价格（与最低价格不同时，前端显示价格区间）
        "auditStatus": "2",             // 审核状态
        "frameStatus": "1",             // 上下架状态
        "status": "1",                  // 状态
        "delFlag": "0",                 // 删除状态
        "isSpecification": "1",         // 有规格
        "shopInfo": "[{\"salesPrice\":\"39.9-59.9\",\"vipPrice\":\"35.9-55.9\",\"repertory\":500}]", // 商品信息
        "specificationsDecribes": "[{\"pName\":\"100g装\",\"salesPrice\":39.9,\"repertory\":200},{\"pName\":\"200g装\",\"salesPrice\":59.9,\"repertory\":300}]" // 规格描述
      }
    ],
    "total": 100,      // 总记录数
    "size": 10,        // 每页大小
    "current": 1,      // 当前页
    "pages": 10        // 总页数
  }
}
```

**字段说明**：

| 字段名 | 类型 | 描述 |
| ------ | ---- | ---- |
| id | String | 商品ID |
| goodName | String | 商品名称 |
| mainPicture | String | 商品主图，JSON格式字符串，如：`{"0":"IMG_0421_1737020017601.JPG","1":"IMG_0422_1737020017679.PNG"}` |
| salesVolume | Integer | 累计销量 |
| monthlySales | Integer | 30日销量（新增字段） |
| repertory | Integer | 库存 |
| minPrice | BigDecimal | 最低价格 |
| maxPrice | BigDecimal | 最高价格 |
| auditStatus | String | 审核状态：0=草稿，1=发布中，2=审核通过，3=已驳回 |
| frameStatus | String | 上下架状态：0=下架，1=上架 |
| status | String | 状态：0=停用，1=启用 |
| delFlag | String | 删除状态：0=正常，1=已删除 |
| isSpecification | String | 是否有规格：0=无规格，1=有规格 |
| shopInfo | String | 商品信息（JSON字符串） |
| specificationsDecribes | String | 规格描述（JSON字符串） |

### 商品状态统计接口

**请求路径**：`/good/backGoodList/getGoodStoreCount`

**请求方式**：GET

**请求参数**：无

**返回参数**：

```json
{
  "success": true,
  "message": "请求成功!",
  "code": 200,
  "result": {
    "goodSumCount": 100,                // 0: 全部商品数量
    "goodOnSaleCount": 16,              // 1: 在售中商品数量 - 显示为"在售中(16)"
    "goodSoldOutCount": 5,              // 2: 已下架商品数量 - 显示为"已下架(5)"
    "goodSoldOutOfStockCount": 3,       // 3: 已售罄商品数量 - 显示为"已售罄(3)"
    "goodPublishingCount": 2,           // 4: 发布中商品数量 - 显示为"发布中(2)"
    "goodRejectedCount": 1,             // 5: 已驳回商品数量 - 显示为"已驳回(1)"
    "goodDraftsCount": 4,               // 6: 草稿箱商品数量 - 显示为"草稿箱(4)"
    "goodRecycleBinCount": 2,           // 7: 回收站商品数量 - 显示为"回收站(2)"

    // 兼容旧版前端的字段
    "goodSumSaleCount": 16,             // 在售商品数（与在售中商品相同）
    "goodAuditCount": 3                 // 审核中商品数（发布中+已驳回）
  }
}
```

**字段说明**：

| 字段名 | 类型 | 描述 |
| ------ | ---- | ---- |
| goodSumCount | Integer | 全部商品数量 |
| goodOnSaleCount | Integer | 在售中商品数量 |
| goodSoldOutCount | Integer | 已下架商品数量 |
| goodSoldOutOfStockCount | Integer | 已售罄商品数量 |
| goodPublishingCount | Integer | 发布中商品数量 |
| goodRejectedCount | Integer | 已驳回商品数量 |
| goodDraftsCount | Integer | 草稿箱商品数量 |
| goodRecycleBinCount | Integer | 回收站商品数量 |
| goodSumSaleCount | Integer | 在售商品数（与在售中商品相同，兼容旧版） |
| goodAuditCount | Integer | 审核中商品数（发布中+已驳回，兼容旧版） |

## 使用说明

### 前端展示说明

1. **状态标签展示**：
   - 使用 `getGoodStoreCount` 接口获取各状态的商品数量
   - 在状态标签上显示对应的数量，如"在售中(16)"、"已下架(5)"等
   - 状态标签包括：全部、在售中、已下架、已售罄、发布中、已驳回、草稿箱、回收站

2. **商品列表展示**：
   - 使用 `queryPageListNew` 接口获取商品列表
   - 商品名称：显示 `goodName`
   - 商品图片：
     - `mainPicture` 返回的是JSON格式字符串，如：`{"0":"IMG_0421_1737020017601.JPG","1":"IMG_0422_1737020017679.PNG"}`
     - 需要取第一张图片（即"0"对应的值）进行展示
     - 返回的是相对路径，需要自己拼接图片基础访问路径，如：`baseImageUrl + mainPictureObj["0"]`
   - 累计销量：显示 `salesVolume`
   - 30日销量：显示 `monthlySales`
   - 库存：显示 `repertory`
   - 价格：
     - 当 `minPrice` 和 `maxPrice` 相同时：显示单一价格，如"¥ 128"
     - 当 `minPrice` 和 `maxPrice` 不同时：显示价格区间，如"¥ 39.9 ~ 59.9"
   - 操作按钮：根据商品状态显示不同的操作按钮
     - 在售中商品：显示"下架"、"改库存"、"改价"、"编辑"按钮
     - 已下架商品：显示"上架"、"改库存"、"改价"、"编辑"按钮
     - 其他状态：根据业务需求显示相应按钮

3. **状态过滤**：
   - 点击不同的状态标签，传递不同的 `goodStatus` 参数给 `queryPageListNew` 接口
   - 接口返回对应状态的商品列表

4. **搜索和筛选**：
   - 可以通过搜索框搜索商品名称或商品描述
   - 可以通过商品分类筛选商品

## 注意事项

1. 商品状态的判断逻辑需要与后端保持一致。
2. 在前端展示时，需要根据商品的状态显示不同的状态标签和操作按钮。
3. 商品数量统计可能会影响接口性能，需要考虑优化方案，如缓存等。
4. 由于商品主表原本没有库存字段，需要执行SQL脚本添加库存字段，并从规格表中汇总库存数据。
5. 在添加或修改商品时，需要同步更新商品主表的库存字段。
6. 处理商品图片时需要注意：
   - `mainPicture` 字段返回的是JSON格式字符串，包含多张图片的相对路径
   - 前端需要解析JSON字符串，取第一张图片（"0"对应的值）进行展示
   - 图片路径是相对路径，需要拼接图片基础访问路径才能正确显示
   - 示例代码：
     ```javascript
     // 假设baseImageUrl是图片服务器的基础URL
     const baseImageUrl = 'https://example.com/images/';
     // 解析mainPicture字段
     const mainPictureObj = JSON.parse(item.mainPicture);
     // 获取第一张图片的完整URL
     const imageUrl = baseImageUrl + mainPictureObj["0"];
     ```

## 数据库变更

需要执行以下SQL脚本，为商品主表添加库存字段：

```sql
-- 添加库存字段到商品主表
ALTER TABLE good_store_list ADD COLUMN repertory decimal(9) DEFAULT 0 NULL COMMENT '库存';

-- 更新商品主表的库存字段，从规格表中汇总
UPDATE good_store_list gsl SET repertory = (
    SELECT IFNULL(SUM(repertory), 0)
    FROM good_store_specification gss
    WHERE gss.good_store_list_id = gsl.id AND gss.del_flag = '0'
);
```
