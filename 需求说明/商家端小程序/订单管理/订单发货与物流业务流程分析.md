# 订单发货与物流业务流程分析

## 一、订单状态流转

### 1. 订单状态定义

根据系统设计，订单状态定义如下：
- **0**：待付款 - 买家已下单但尚未支付
- **1**：待发货 - 买家已付款，等待商家发货
- **2**：待收货 - 商家已发货，等待买家收货
- **3**：交易成功 - 买家已确认收货，订单完成
- **4**：交易失败 - 订单已取消或退款

### 2. 状态流转路径

订单状态的正常流转路径：
1. 买家下单 → 状态变为"待付款(0)"
2. 买家付款 → 状态变为"待发货(1)"
3. 商家发货 → 状态变为"待收货(2)"
4. 买家确认收货 → 状态变为"交易成功(3)"

异常流转路径：
1. 待付款状态下，超时未付款 → 状态变为"交易失败(4)"
2. 待发货状态下，商家取消订单并退款 → 状态变为"交易失败(4)"
3. 待收货状态下，买家申请退款退货 → 可能变为"交易失败(4)"

### 3. 发货操作对状态的影响

商家执行发货操作后：
- 订单状态从"待发货(1)"变为"待收货(2)"
- 系统记录发货时间、物流公司和快递单号
- 系统可能会向买家发送发货通知

## 二、发货业务流程

### 1. 发货前置条件

商家只能对满足以下条件的订单进行发货操作：
- 订单状态为"待发货(1)"
- 订单已完成支付
- 订单未被标记为异常状态

### 2. 发货操作流程

完整的发货操作流程如下：

1. **发货入口**：
   - 商家在订单列表页面找到"待发货"状态的订单
   - 点击订单卡片上的"发货"按钮
   - 或者进入订单详情页面，点击"立即发货"按钮

2. **准备发货信息**：
   - 系统跳转到发货页面
   - 系统自动加载订单信息和商品信息
   - 系统加载物流公司列表

3. **填写发货信息**：
   - 商家选择要发货的商品（可以全部发货或部分发货）
   - 商家选择物流公司
   - 商家填写快递单号
   - 如有多个包裹，可以添加多个包裹并分别填写信息

4. **提交发货**：
   - 商家点击"发货"按钮
   - 系统验证发货信息（必须选择商品、物流公司和填写快递单号）
   - 系统向后端发送发货请求

5. **发货结果处理**：
   - 发货成功：显示成功提示，返回订单列表或详情页面
   - 发货失败：显示错误提示，允许商家修改信息重新提交

### 3. 多包裹发货

系统支持多包裹发货，流程如下：

1. 商家点击"添加包裹"按钮，创建新的包裹表单
2. 为每个包裹分别选择商品、物流公司和填写快递单号
3. 系统将所有包裹信息合并为一个发货请求
4. 发货成功后，系统记录每个包裹的物流信息

### 4. 发货数据处理

发货请求的数据结构和处理流程：

1. **构建发货数据**：
```javascript
let listParcel = [];
for (let item of this.checkedList) {
  let results = {
    id: count <= 0 ? that.datas.orderStoreSubListDTOs[0].id : '',
    name: `第${count}个包裹`,
    orderProviderGoodRecordInfo: item.list.map(i => ({
      id: i.id,
      name: i.goodName
    })),
    logisticsCompany: that.array[item.indexex]?.value || that.array[item.indexex],
    trackingNumber: item.trackingNumber
  };
  listParcel.push(results);
  count += 1;
}

let obj = {
  listMap: [{
    providerAddressFa: '无',
    providerAddressTui: '无',
    sysUserName: that.datas.orderStoreSubListDTOs[0].sysUserName,
    orderNo: that.datas.orderNo,
    goodsInfo: that.datas.orderStoreSubListDTOs[0].orderStoreGoodRecords,
    listParcel
  }]
};
```

2. **发送发货请求**：
```javascript
shipOrder(obj).then(response => {
  if (response.success) {
    uni.showToast({
      title: '发货成功',
      icon: 'success'
    });
    setTimeout(() => {
      uni.navigateBack();
    }, 1000);
  }
});
```

## 三、物流查询业务流程

### 1. 物流查询前置条件

只有满足以下条件的订单才能查询物流信息：
- 订单状态为"待收货(2)"或"交易成功(3)"
- 订单已经完成发货操作
- 订单使用的是快递配送方式（非自提或其他配送方式）

### 2. 物流查询流程

完整的物流查询流程如下：

1. **查询入口**：
   - 商家在订单列表页面找到"待收货"或"交易成功"状态的订单
   - 点击订单卡片上的"查看物流"按钮
   - 或者进入订单详情页面，点击"查看物流"按钮

2. **加载物流信息**：
   - 系统跳转到物流信息页面
   - 系统根据订单ID调用物流查询接口
   - 系统解析并展示物流信息

3. **查看物流详情**：
   - 商家可以查看物流基本信息（物流公司、快递单号等）
   - 商家可以查看物流跟踪记录（物流轨迹）
   - 如有多个包裹，商家可以切换查看不同包裹的物流信息

4. **刷新物流信息**：
   - 商家可以下拉页面刷新物流信息
   - 系统重新调用物流查询接口获取最新信息

### 3. 物流数据处理

物流查询的数据处理流程：

1. **请求物流数据**：
```javascript
getLogisticsInfo(options).then(response => {
  if (response.success && response.result) {
    let rests = response.result;
    this.datas = rests;
    // 处理物流信息...
  }
});
```

2. **解析物流跟踪信息**：
```javascript
if (orderProviderList.logisticsTracking) {
  try {
    const parsedData = JSON.parse(orderProviderList.logisticsTracking);
    this.logisticsTrackingList = parsedData.result?.list || [];
  } catch (error) {
    console.error('解析物流跟踪信息错误:', error);
    this.logisticsTrackingList = [];
  }
}
```

## 四、API接口详细分析

### 1. 订单发货接口

- **接口路径**: `/back/order/ordereDlivery`
- **请求方式**: POST
- **请求参数**:
  ```json
  {
    "listMap": [
      {
        "providerAddressFa": "发货地址",
        "providerAddressTui": "退货地址",
        "sysUserName": "商家名称",
        "orderNo": "订单编号",
        "goodsInfo": [商品信息数组],
        "listParcel": [
          {
            "id": "包裹ID",
            "name": "包裹名称",
            "orderProviderGoodRecordInfo": [
              {
                "id": "商品ID",
                "name": "商品名称"
              }
            ],
            "logisticsCompany": "物流公司代码",
            "trackingNumber": "快递单号"
          }
        ]
      }
    ]
  }
  ```
- **响应数据**:
  ```json
  {
    "success": true,
    "code": 200,
    "message": "发货成功",
    "result": null
  }
  ```

### 2. 物流查询接口

- **接口路径**: `/back/order/parcelInformation`
- **请求方式**: GET
- **请求参数**:
  | 参数名 | 类型 | 必填 | 描述 |
  | ------ | ---- | ---- | ---- |
  | orderListId | String | 是 | 订单ID |
- **响应数据**:
  ```json
  {
    "success": true,
    "code": 200,
    "message": "操作成功",
    "result": [
      {
        "sysUserName": "商家名称",
        "trackingNumber": "快递单号",
        "logisticsCompany": "物流公司",
        "providerAddressDTOFa": {
          "contactName": "联系人",
          "contactPhone": "联系电话",
          "detailedAddress": "详细地址"
        },
        "providerAddressDTOTui": {
          "contactName": "联系人",
          "contactPhone": "联系电话",
          "detailedAddress": "详细地址"
        },
        "logisticsTracking": "物流跟踪JSON字符串"
      }
    ]
  }
  ```

### 3. 物流公司查询接口

- **接口路径**: `/front/sysDict/getDicts`
- **请求方式**: GET
- **请求参数**:
  | 参数名 | 类型 | 必填 | 描述 |
  | ------ | ---- | ---- | ---- |
  | code | String | 是 | 字典代码，固定为'logistics_company' |
- **响应数据**:
  ```json
  {
    "success": true,
    "code": 200,
    "message": "操作成功",
    "result": [
      {
        "text": "顺丰速运",
        "value": "SF"
      },
      {
        "text": "圆通快递",
        "value": "YTO"
      },
      // 更多物流公司...
    ]
  }
  ```

## 五、业务规则与限制

### 1. 发货业务规则

1. **发货时机**：
   - 只能对"待发货"状态的订单进行发货操作
   - 订单必须已完成支付

2. **商品选择规则**：
   - 可以选择全部商品一次性发货
   - 也可以选择部分商品分批发货
   - 每个包裹必须至少包含一个商品

3. **物流信息规则**：
   - 物流公司必须从系统提供的列表中选择
   - 快递单号必须填写，且不能为空
   - 系统不会验证快递单号的有效性，由商家自行确保正确

4. **多包裹规则**：
   - 系统支持添加多个包裹
   - 每个包裹可以选择不同的物流公司和快递单号
   - 至少要有一个包裹（默认包裹不能删除）

### 2. 物流查询业务规则

1. **查询条件**：
   - 只能查询已发货的订单的物流信息
   - 订单状态必须是"待收货"或"交易成功"

2. **物流数据更新**：
   - 物流信息可能不是实时的，需要手动刷新获取最新状态
   - 物流跟踪记录按时间倒序排列，最新的记录在最上方

3. **多包裹查询**：
   - 如果订单包含多个包裹，可以在物流信息页面切换查看
   - 每个包裹的物流信息是独立的

### 3. 特殊情况处理

1. **发货失败处理**：
   - 如果发货请求失败，系统会显示错误提示
   - 商家可以修改信息后重新提交
   - 常见失败原因：网络问题、参数错误、服务器异常等

2. **无物流信息处理**：
   - 如果订单刚发货，可能还没有物流跟踪记录
   - 系统会显示提示："您的快件还未发货，请耐心等待..."
   - 商家可以稍后再查询

3. **物流异常处理**：
   - 如果物流公司返回的数据格式异常，系统会尝试解析并显示可用信息
   - 如果解析失败，会显示空的物流跟踪列表

## 六、业务优化建议

### 1. 发货流程优化

1. **批量发货功能**：
   - 添加批量发货功能，允许商家一次性处理多个订单
   - 对于使用相同物流公司的订单，可以批量填写物流信息

2. **快递单号验证**：
   - 添加快递单号格式验证，减少错误输入
   - 可以根据选择的物流公司，自动匹配对应的单号格式

3. **物流模板**：
   - 添加常用物流信息模板功能，方便商家快速填写
   - 记住商家最近使用的物流公司，优先显示

4. **智能发货建议**：
   - 根据订单商品、收货地址等信息，智能推荐合适的物流公司
   - 提供物流费用估算，帮助商家选择最优方案

### 2. 物流查询优化

1. **物流状态推送**：
   - 添加物流状态变更推送功能，当物流状态更新时通知商家
   - 特别是异常状态（如派送失败、退回等）及时通知

2. **物流地图可视化**：
   - 添加物流轨迹地图可视化功能，直观展示物流路径
   - 显示当前包裹位置和预计到达时间

3. **物流异常预警**：
   - 添加物流异常预警功能，自动检测长时间未更新或异常状态的物流
   - 提醒商家关注并处理可能的问题

4. **物流统计分析**：
   - 添加物流数据统计分析功能，帮助商家了解各物流公司的服务质量
   - 提供平均配送时间、异常率等指标，辅助决策
