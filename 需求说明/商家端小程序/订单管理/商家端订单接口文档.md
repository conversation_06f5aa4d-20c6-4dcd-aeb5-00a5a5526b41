# 商家端订单接口文档

本文档详细说明了商家端订单相关的接口，包括订单查询、订单管理和售后处理等功能。

## 目录

1. [接口基本信息](#接口基本信息)
2. [订单查询接口](#订单查询接口)
   - [查询店铺不同状态的订单数量](#查询店铺不同状态的订单数量)
   - [订单详情](#订单详情)
   - [店铺全部订单列表](#店铺全部订单列表)
   - [待付款订单列表](#待付款订单列表)
   - [待发货订单列表](#待发货订单列表)
   - [待收货订单列表](#待收货订单列表)
   - [交易成功订单列表](#交易成功订单列表)
   - [包裹信息](#包裹信息)
   - [待支付订单计时器](#待支付订单计时器)
3. [订单管理接口](#订单管理接口)
   - [取消并退款](#取消并退款)
   - [删除订单](#删除订单)
   - [修改收货地址](#修改收货地址)
   - [订单发货](#订单发货)
4. [售后管理接口](#售后管理接口)
   - [售后分页列表查询](#售后分页列表查询)
   - [售后详情](#售后详情)
   - [拒绝售后申请](#拒绝售后申请)
   - [通过售后申请](#通过售后申请)
   - [确认收货并退款](#确认收货并退款)
   - [换货商家发货](#换货商家发货)

## 接口基本信息

- **接口前缀**: `/back/order`
- **请求方式**: 主要为 GET 和 POST
- **认证方式**: 通过请求头或请求属性中的 `sysUserId` 进行身份验证
- **响应格式**: 统一使用 `Result<T>` 格式返回，包含 code、message 和 result 字段

## 订单查询接口

### 查询店铺不同状态的订单数量

获取店铺各种状态订单的数量统计。

- **接口路径**: `/back/order/storeOrderCount`
- **请求方式**: GET
- **请求参数**: 无需额外参数，从请求中获取 `sysUserId`
- **响应数据**:
  ```json
  {
    "success": true,
    "code": 200,
    "message": "查询成功!",
    "result": {
      "allCountStore": 100,           // 全部订单数量
      "obligationCount": 10,          // 待付款订单数量
      "waitDeliveryCount": 20,        // 待发货订单数量
      "waitForReceivingCount": 15,    // 待收货订单数量
      "doneCountStore": 40,           // 已完成订单数量
      "cancelCountStore": 5,          // 已关闭订单数量
      "dealsAreDoneCount": 10,        // 交易成功订单数量
      "waitHandleRefundCount": 5      // 待处理退款售后数量
    }
  }
  ```

### 订单详情

获取订单的详细信息。

- **接口路径**: `/back/order/getOrderDetail`
- **请求方式**: GET
- **请求参数**:
  | 参数名 | 类型 | 必填 | 描述 |
  | ------ | ---- | ---- | ---- |
  | orderNo | String | 是 | 订单编号 |
- **响应数据**:
  ```json
  {
    "success": true,
    "code": 200,
    "message": "操作成功",
    "result": {
      "id": "订单ID",
      "orderNo": "订单编号",
      "status": "订单状态",
      "status_dictText": "订单状态文本",
      "totalPrice": 100.00,
      "payPrice": 95.00,
      "consignee": "收件人",
      "contactNumber": "联系电话",
      "shippingAddress": "收货地址",
      "createTime": "2023-08-24 10:00:00",
      "payTime": "2023-08-24 10:05:00",
      "orderStoreSubList": [
        {
          "id": "子订单ID",
          "goodsName": "商品名称",
          "goodsImg": "商品图片",
          "goodsPrice": 50.00,
          "goodsNum": 2,
          "specInfo": "规格信息"
        }
      ]
    }
  }
  ```

### 店铺全部订单列表

分页查询店铺的所有订单。

- **接口路径**: `/back/order/list`
- **请求方式**: GET
- **请求参数**:
  | 参数名 | 类型 | 必填 | 描述 |
  | ------ | ---- | ---- | ---- |
  | pageNo | Integer | 否 | 页码，默认1 |
  | pageSize | Integer | 否 | 每页条数，默认10 |
  | orderListVO | OrderStoreListVO | 否 | 查询条件对象 |
- **响应数据**:
  ```json
  {
    "success": true,
    "code": 200,
    "message": "操作成功",
    "result": {
      "records": [
        {
          "id": "订单ID",
          "orderNo": "订单编号",
          "status": "订单状态",
          "status_dictText": "订单状态文本",
          "totalPrice": 100.00,
          "payPrice": 95.00,
          "consignee": "收件人",
          "contactNumber": "联系电话",
          "shippingAddress": "收货地址",
          "createTime": "2023-08-24 10:00:00",
          "payTime": "2023-08-24 10:05:00",
          "orderStoreSubList": [
            {
              "id": "子订单ID",
              "goodsName": "商品名称",
              "goodsImg": "商品图片",
              "goodsPrice": 50.00,
              "goodsNum": 2,
              "specInfo": "规格信息"
            }
          ]
        }
      ],
      "total": 100,
      "size": 10,
      "current": 1,
      "pages": 10
    }
  }
  ```

### 待付款订单列表

分页查询店铺的待付款订单。

- **接口路径**: `/back/order/queryPageListObligation`
- **请求方式**: GET
- **请求参数**:
  | 参数名 | 类型 | 必填 | 描述 |
  | ------ | ---- | ---- | ---- |
  | pageNo | Integer | 否 | 页码，默认1 |
  | pageSize | Integer | 否 | 每页条数，默认10 |
  | orderStoreListVO | OrderStoreListVO | 否 | 查询条件对象 |
- **响应数据**: 与[店铺全部订单列表](#店铺全部订单列表)相同，但只返回状态为"待付款"的订单

### 待发货订单列表

分页查询店铺的待发货订单。

- **接口路径**: `/back/order/queryPageListToSendTheGoods`
- **请求方式**: GET
- **请求参数**:
  | 参数名 | 类型 | 必填 | 描述 |
  | ------ | ---- | ---- | ---- |
  | pageNo | Integer | 否 | 页码，默认1 |
  | pageSize | Integer | 否 | 每页条数，默认10 |
  | orderStoreListVO | OrderStoreListVO | 否 | 查询条件对象 |
- **响应数据**: 与[店铺全部订单列表](#店铺全部订单列表)相同，但只返回状态为"待发货"的订单

### 待收货订单列表

分页查询店铺的待收货订单。

- **接口路径**: `/back/order/queryPageListWaitForReceiving`
- **请求方式**: GET
- **请求参数**:
  | 参数名 | 类型 | 必填 | 描述 |
  | ------ | ---- | ---- | ---- |
  | pageNo | Integer | 否 | 页码，默认1 |
  | pageSize | Integer | 否 | 每页条数，默认10 |
  | orderStoreListVO | OrderStoreListVO | 否 | 查询条件对象 |
- **响应数据**: 与[店铺全部订单列表](#店铺全部订单列表)相同，但只返回状态为"待收货"的订单

### 交易成功订单列表

分页查询店铺的交易成功订单。

- **接口路径**: `/back/order/queryPageListDealsAreDone`
- **请求方式**: GET
- **请求参数**:
  | 参数名 | 类型 | 必填 | 描述 |
  | ------ | ---- | ---- | ---- |
  | pageNo | Integer | 否 | 页码，默认1 |
  | pageSize | Integer | 否 | 每页条数，默认10 |
  | orderStoreListVO | OrderStoreListVO | 否 | 查询条件对象 |
- **响应数据**: 与[店铺全部订单列表](#店铺全部订单列表)相同，但只返回状态为"交易成功"的订单

### 包裹信息

获取订单的包裹信息。

- **接口路径**: `/back/order/parcelInformation`
- **请求方式**: GET
- **请求参数**:
  | 参数名 | 类型 | 必填 | 描述 |
  | ------ | ---- | ---- | ---- |
  | orderListId | String | 是 | 订单ID |
- **响应数据**:
  ```json
  {
    "success": true,
    "code": 200,
    "message": "操作成功",
    "result": [
      {
        "id": "子订单ID",
        "orderListId": "订单ID",
        "logisticsCompany": "物流公司代码",
        "logisticsCompany_dictText": "物流公司名称",
        "trackingNumber": "物流单号",
        "deliveryTime": "发货时间",
        "goodsList": [
          {
            "id": "商品ID",
            "goodsName": "商品名称",
            "goodsImg": "商品图片",
            "goodsPrice": 50.00,
            "goodsNum": 2,
            "specInfo": "规格信息"
          }
        ]
      }
    ]
  }
  ```

### 待支付订单计时器

获取待支付订单的剩余支付时间。

- **接口路径**: `/back/order/prepaidOrderTimer`
- **请求方式**: GET
- **请求参数**:
  | 参数名 | 类型 | 必填 | 描述 |
  | ------ | ---- | ---- | ---- |
  | orderId | String | 是 | 订单ID |
  | isPlatform | Integer | 否 | 是否平台类型，默认0 |
- **响应数据**:
  ```json
  {
    "success": true,
    "code": 200,
    "message": "请求成功",
    "result": {
      "timer": "00:30:00" // 剩余支付时间，格式为HH:mm:ss
    }
  }
  ```

## 订单管理接口

### 取消并退款

取消订单并退款。

- **接口路径**: `/back/order/refundAndAbrogateOrder`
- **请求方式**: POST
- **请求参数**:
  | 参数名 | 类型 | 必填 | 描述 |
  | ------ | ---- | ---- | ---- |
  | id | String | 是 | 订单ID |
  | closeExplain | String | 是 | 取消原因 |
- **响应数据**:
  ```json
  {
    "success": true,
    "code": 200,
    "message": "操作成功"
  }
  ```

### 删除订单

删除订单记录。

- **接口路径**: `/back/order/delete`
- **请求方式**: DELETE
- **请求参数**:
  | 参数名 | 类型 | 必填 | 描述 |
  | ------ | ---- | ---- | ---- |
  | id | String | 是 | 订单ID |
- **响应数据**:
  ```json
  {
    "success": true,
    "code": 200,
    "message": "删除成功!"
  }
  ```

### 修改收货地址

修改订单的收货地址信息。

- **接口路径**: `/back/order/updateReceiveAddress`
- **请求方式**: POST
- **请求参数**:
  | 参数名 | 类型 | 必填 | 描述 |
  | ------ | ---- | ---- | ---- |
  | orderId | String | 是 | 订单ID |
  | consignee | String | 是 | 收件人名字 |
  | contactNumber | String | 是 | 收件人电话 |
  | shippingAddress | String | 是 | 收件人地址 |
- **响应数据**:
  ```json
  {
    "success": true,
    "code": 200,
    "message": "修改成功"
  }
  ```

### 订单发货

商家发货处理。

- **接口路径**: `/back/order/ordereDlivery`
- **请求方式**: POST
- **请求参数**:
  | 参数名 | 类型 | 必填 | 描述 |
  | ------ | ---- | ---- | ---- |
  | listMap | String | 是 | 发货信息JSON字符串 |
- **请求参数示例**:
  ```json
  {
    "orderListId": "订单ID",
    "logisticsCompany": "物流公司代码",
    "trackingNumber": "物流单号",
    "goodsIds": ["商品ID1", "商品ID2"]
  }
  ```
- **响应数据**:
  ```json
  {
    "success": true,
    "code": 200,
    "message": "发货成功"
  }
  ```

## 售后管理接口

### 售后分页列表查询

分页查询店铺的售后申请列表。

- **接口路径**: `/back/order/storeRefundList`
- **请求方式**: GET
- **请求参数**:
  | 参数名 | 类型 | 必填 | 描述 |
  | ------ | ---- | ---- | ---- |
  | pageNo | Integer | 否 | 页码，默认1 |
  | pageSize | Integer | 否 | 每页条数，默认10 |
  | orderRefundList | OrderRefundList | 否 | 查询条件对象 |
- **响应数据**:
  ```json
  {
    "success": true,
    "code": 200,
    "message": "操作成功",
    "result": {
      "records": [
        {
          "id": "售后单ID",
          "orderListId": "订单ID",
          "memberId": "会员ID",
          "refundReason": "退款原因代码",
          "refundReason_dictText": "退款原因文本",
          "refundReasonLabel": "退款原因标签",
          "refundAmount": 95.00,
          "status": "售后状态",
          "createTime": "2023-08-24 10:00:00",
          "memberList": {
            "id": "会员ID",
            "nickname": "会员昵称",
            "headImg": "会员头像"
          },
          "orderStoreList": {
            "id": "订单ID",
            "orderNo": "订单编号",
            "totalPrice": 100.00
          }
        }
      ],
      "total": 50,
      "size": 10,
      "current": 1,
      "pages": 5
    }
  }
  ```

### 售后详情

获取售后申请的详细信息。

- **接口路径**: `/back/order/queryById`
- **请求方式**: GET
- **请求参数**:
  | 参数名 | 类型 | 必填 | 描述 |
  | ------ | ---- | ---- | ---- |
  | id | String | 是 | 售后单ID |
- **响应数据**:
  ```json
  {
    "success": true,
    "code": 200,
    "message": "操作成功",
    "result": {
      "id": "售后单ID",
      "orderListId": "订单ID",
      "memberId": "会员ID",
      "refundReason": "退款原因代码",
      "refundReason_dictText": "退款原因文本",
      "refundReasonLabel": "退款原因标签",
      "refundAmount": 95.00,
      "status": "售后状态",
      "createTime": "2023-08-24 10:00:00",
      "refundType": "退款类型",
      "refundType_dictText": "退款类型文本",
      "refundExplain": "退款说明",
      "refundImg": "退款凭证图片",
      "memberLogisticsCompany": "买家物流公司",
      "memberLogisticsCompany_dictText": "买家物流公司名称",
      "memberTrackingNumber": "买家物流单号",
      "merchantLogisticsCompany": "商家物流公司",
      "merchantLogisticsCompany_dictText": "商家物流公司名称",
      "merchantTrackingNumber": "商家物流单号",
      "merchantConsigneeName": "商家收件人姓名",
      "merchantConsigneeAddress": "商家收件地址",
      "merchantConsigneePhone": "商家收件手机号",
      "orderStoreList": {
        "id": "订单ID",
        "orderNo": "订单编号",
        "totalPrice": 100.00,
        "consignee": "收件人",
        "contactNumber": "联系电话",
        "shippingAddress": "收货地址"
      },
      "memberList": {
        "id": "会员ID",
        "nickname": "会员昵称",
        "headImg": "会员头像"
      }
    }
  }
  ```

### 拒绝售后申请

拒绝用户的售后申请。

- **接口路径**: `/back/order/refused`
- **请求方式**: POST
- **请求参数**:
  | 参数名 | 类型 | 必填 | 描述 |
  | ------ | ---- | ---- | ---- |
  | id | String | 是 | 售后单ID |
  | refusedExplain | String | 是 | 拒绝原因 |
- **响应数据**:
  ```json
  {
    "success": true,
    "code": 200,
    "message": "操作成功"
  }
  ```

### 通过售后申请

通过用户的售后申请，根据售后类型执行不同操作：
- 仅退款：填写退款金额
- 退款退货：填写商家收货地址
- 换货：填写商家收货地址

- **接口路径**: `/back/order/pass`
- **请求方式**: POST
- **请求参数**:
  | 参数名 | 类型 | 必填 | 描述 |
  | ------ | ---- | ---- | ---- |
  | id | String | 是 | 售后单ID |
  | actualRefundPrice | BigDecimal | 否 | 退款金额（微信） |
  | actualRefundBalance | BigDecimal | 否 | 退款余额 |
  | refundChannel | String | 否 | 退款渠道，0=微信 1=余额，多选用逗号分隔 |
  | merchantConsigneeName | String | 否 | 商家收件人姓名 |
  | merchantConsigneeAddress | String | 否 | 商家收件地址 |
  | merchantConsigneePhone | String | 否 | 商家收件手机号 |
  | merchantConsigneeProvinceId | String | 否 | 省ID |
  | merchantConsigneeCityId | String | 否 | 市ID |
  | merchantConsigneeAreaId | String | 否 | 区ID |
- **响应数据**:
  ```json
  {
    "success": true,
    "code": 200,
    "message": "操作成功"
  }
  ```

### 确认收货并退款

退款退货流程中，商家确认收到退货后进行退款。

- **接口路径**: `/back/order/confirm`
- **请求方式**: POST
- **请求参数**:
  | 参数名 | 类型 | 必填 | 描述 |
  | ------ | ---- | ---- | ---- |
  | id | String | 是 | 售后单ID |
  | refundChannel | String | 否 | 退款渠道，0=微信 1=余额，多选用逗号分隔 |
  | actualRefundPrice | BigDecimal | 否 | 退款金额（微信） |
  | actualRefundBalance | BigDecimal | 否 | 退款余额 |
- **响应数据**:
  ```json
  {
    "success": true,
    "code": 200,
    "message": "操作成功"
  }
  ```

### 换货商家发货

换货流程中，商家发货处理。

- **接口路径**: `/back/order/editLogisticsInfo`
- **请求方式**: POST
- **请求参数**:
  | 参数名 | 类型 | 必填 | 描述 |
  | ------ | ---- | ---- | ---- |
  | id | String | 是 | 售后单ID |
  | merchantLogisticsCompany | String | 是 | 商家物流公司代码 |
  | merchantTrackingNumber | String | 是 | 商家快递单号 |
- **响应数据**:
  ```json
  {
    "success": true,
    "code": 200,
    "message": "操作成功"
  }
  ```

## 数据字典说明

### 订单状态

- 0：待付款
- 1：待发货（已付款、部分发货）
- 2：待收货（已发货）
- 3：交易成功
- 4：交易失败
- 5：已完成

### 售后状态

- 0：待商家处理
- 1：商家已同意
- 2：商家已拒绝
- 3：买家已发货
- 4：商家已收货
- 5：已退款
- 6：商家已发货
- 7：买家已收货
- 8：换货完成

### 退款类型

- 0：仅退款
- 1：退款退货
- 2：换货

### 物流公司

- 0：顺丰速运
- 1：圆通快递
- 2：申通快递
- 3：中通快递
- 4：韵达快递
- 5：天天快递
- 6：中国邮政
- 7：EMS邮政特快专递
- 8：德邦快递

### 退款渠道

- 0：微信
- 1：余额