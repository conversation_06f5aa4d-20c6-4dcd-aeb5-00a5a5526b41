# 订单列表页面接口调用迁移方案

本文档详细分析了八闽助业集市商家端小程序中订单列表页面的接口调用实现逻辑，并提供了将其迁移到新项目的详细方案。

## 1. 接口调用分析

### 1.1 接口定义

订单列表页面使用的核心API接口定义在 `static/api/api.js` 文件中：

```javascript
// 查询店铺不同状态的数量
storeOrderCount: "/back/order/storeOrderCount",
// 全部订单
AllOrder: "/back/order/list",
// 待付款订单
queryPageListObligation: "/back/order/queryPageListObligation",
// 待发货订单
queryPageListToSendTheGoods: "/back/order/queryPageListToSendTheGoods",
// 待收货订单
queryPageListWaitForReceiving: "/back/order/queryPageListWaitForReceiving",
// 交易成功
queryPageListDealsAreDone: "/back/order/queryPageListDealsAreDone",
// 订单详情
getOrderDetail: "/back/order/getOrderDetail",
// 订单发货
ordereDlivery: "/back/order/ordereDlivery",
// 物流字典
logisticsCompany: '/front/sysDict/getDicts',
// 包裹信息
parcelInformation: 'back/order/parcelInformation',
// 取消并退款
backOrder: "back/order/refundAndAbrogateOrder",
// 删除订单
deleteOrder: "back/order/delete"
```

### 1.2 接口调用方式

在原项目中，接口调用使用了自定义的 `$request` 方法，该方法封装了 uni-app 的网络请求 API。以下是几个核心接口的调用示例：

#### 1.2.1 获取全部订单列表

```javascript
getAllOrderList() {
  const that = this
  that.stauts = false;
  this.loadMore = 'loading';
  this.$request(api.AllOrder, {
    goodName: that.goodName,
    pageNo: that.pageNo,
    pageSize: that.pageSize
  }, 'GET', res => {
    if (res.result.total <= res.result.current * res.result.size) {
      that.stauts = false;
      this.loadMore = 'noMore'
    } else {
      this.loadMore = 'more'
      that.stauts = true;
    }
    if (res.result.records.length === 0) {
      that.OrderList = res.result.records
    } else {
      if (that.pageNo === 1) {
        that.OrderList = res.result.records
      } else {
        res.result.records.forEach(item => {
          that.OrderList.push(item)
        });
      }
    }
  })
}
```

#### 1.2.2 获取待发货订单列表

```javascript
getqueryPageListToSendTheGoods() {
  this.$request(api.queryPageListToSendTheGoods, {}, 'Get', res => {
    this.queryPageListToSendTheGoodsList = res.result.records
  })
}
```

#### 1.2.3 取消并退款

```javascript
dialogInputConfirm(val) {
  uni.showLoading({
    mask: true
  })
  this.qxtkReason = val
  this.$request(api.backOrder, {
    id: this.qxtkId,
    closeExplain: val
  }, 'post', res => {
    uni.hideLoading()
    uni.showToast({
      title: '操作成功',
      icon: 'none'
    })
    this.qxtkId = ''
    // 关闭窗口后，恢复默认内容
    this.$refs.inputDialog.close()
    this.getAllOrderList()
    this.getqueryPageListObligation()
    this.getqueryPageListToSendTheGoods()
    this.getqueryPageListWaitForReceiving()
    this.getqueryPageListDealsAreDone()
    this.getFiveList()
  })
}
```

#### 1.2.4 删除订单

```javascript
dele(item) {
  const that = this;
  uni.showModal({
    title: '温馨提示',
    content: '您确定要删除该订单吗',
    success: function(res) {
      if (res.confirm) {
        uni.showLoading({
          mask: true
        })
        that.$request(api.deleteOrder, {
          id: item.id
        }, 'delete', res => {
          uni.hideLoading()
          uni.showToast({
            title: '操作成功',
            icon: 'none'
          })
          that.getAllOrderList()
          that.getqueryPageListObligation()
          that.getqueryPageListToSendTheGoods()
          that.getqueryPageListWaitForReceiving()
          that.getqueryPageListDealsAreDone()
          that.getFiveList()
        })
      }
    }
  })
}
```

### 1.3 请求参数和响应数据结构

#### 1.3.1 获取全部订单列表

**请求参数**：
```javascript
{
  goodName: string,  // 商品名称（可选，用于搜索）
  pageNo: number,    // 页码
  pageSize: number,  // 每页大小
  status: number     // 订单状态（可选）
}
```

**响应数据结构**：
```javascript
{
  success: boolean,
  code: number,
  message: string,
  result: {
    records: [
      {
        id: string,           // 订单ID
        orderNo: string,      // 订单编号
        status: string,       // 订单状态：0待付款，1待发货，2待收货，3交易成功，4交易失败
        createTime: string,   // 创建时间
        consignee: string,    // 收货人
        distribution: number, // 配送方式：0快递，1自提，2配送
        memberList: {
          headPortrait: string, // 买家头像
          phone: string,        // 买家手机号
          nickName: string      // 买家昵称
        },
        orderStoreSubListDTOs: [
          {
            orderStoreGoodRecords: [
              {
                goodName: string,    // 商品名称
                mainPicture: string, // 商品图片（JSON字符串）
                num: number,         // 商品数量
                price: number        // 商品价格
              }
            ]
          }
        ]
      }
    ],
    total: number,    // 总记录数
    size: number,     // 每页大小
    current: number,  // 当前页码
    pages: number     // 总页数
  }
}
```

#### 1.3.2 取消并退款

**请求参数**：
```javascript
{
  id: string,         // 订单ID
  closeExplain: string // 取消原因
}
```

**响应数据结构**：
```javascript
{
  success: boolean,
  code: number,
  message: string,
  result: null
}
```

#### 1.3.3 删除订单

**请求参数**：
```javascript
{
  id: string  // 订单ID
}
```

**响应数据结构**：
```javascript
{
  success: boolean,
  code: number,
  message: string,
  result: null
}
```

## 2. 接口调用迁移方案

### 2.1 接口定义迁移

在新项目中，需要创建对应的API接口定义文件：

```javascript
// api/order.js
export default {
  // 查询店铺不同状态的订单数量
  storeOrderCount: "/back/order/storeOrderCount",
  
  // 订单列表相关接口
  allOrder: "/back/order/list",
  queryPageListObligation: "/back/order/queryPageListObligation",
  queryPageListToSendTheGoods: "/back/order/queryPageListToSendTheGoods",
  queryPageListWaitForReceiving: "/back/order/queryPageListWaitForReceiving",
  queryPageListDealsAreDone: "/back/order/queryPageListDealsAreDone",
  
  // 订单详情
  getOrderDetail: "/back/order/getOrderDetail",
  
  // 订单操作
  ordereDlivery: "/back/order/ordereDlivery",
  backOrder: "/back/order/refundAndAbrogateOrder",
  deleteOrder: "/back/order/delete",
  
  // 物流相关
  logisticsCompany: '/front/sysDict/getDicts',
  parcelInformation: 'back/order/parcelInformation'
}
```

### 2.2 接口调用适配

根据新项目的网络请求方式，需要适配接口调用代码。以下是几种常见的适配方案：

#### 2.2.1 方案一：创建适配层

如果新项目使用不同的网络请求方式，可以创建一个适配层来统一接口调用：

```javascript
// utils/request-adapter.js
import request from '@/utils/request'  // 新项目的请求方法
import orderApi from '@/api/order'

export const orderService = {
  // 获取全部订单列表
  getAllOrders(params) {
    return request.get(orderApi.allOrder, params)
  },
  
  // 获取待发货订单列表
  getPendingShipmentOrders(params) {
    return request.get(orderApi.queryPageListToSendTheGoods, params)
  },
  
  // 取消并退款
  cancelAndRefund(params) {
    return request.post(orderApi.backOrder, params)
  },
  
  // 删除订单
  deleteOrder(params) {
    return request.delete(orderApi.deleteOrder, params)
  }
}
```

然后在组件中使用适配层：

```javascript
import { orderService } from '@/utils/request-adapter'

// 获取全部订单列表
getAllOrderList() {
  const params = {
    goodName: this.goodName,
    pageNo: this.pageNo,
    pageSize: this.pageSize
  }
  
  this.loadMore = 'loading'
  orderService.getAllOrders(params).then(res => {
    // 处理响应数据...
    if (res.result.total <= res.result.current * res.result.size) {
      this.stauts = false
      this.loadMore = 'noMore'
    } else {
      this.loadMore = 'more'
      this.stauts = true
    }
    
    if (res.result.records.length === 0) {
      this.OrderList = res.result.records
    } else {
      if (this.pageNo === 1) {
        this.OrderList = res.result.records
      } else {
        this.OrderList = [...this.OrderList, ...res.result.records]
      }
    }
  }).catch(err => {
    // 错误处理...
    console.error(err)
    this.loadMore = 'more'
  })
}
```

#### 2.2.2 方案二：封装 $request 方法

如果希望保持原有代码结构，可以在新项目中封装一个与原项目相同的 $request 方法：

```javascript
// utils/request.js
import request from '@/utils/request'  // 新项目的请求方法

// 封装与原项目相同的 $request 方法
export function $request(url, data, method, callback, errorCallback) {
  const requestMethod = method.toLowerCase()
  
  const requestConfig = {
    url,
    method: requestMethod,
    [requestMethod === 'get' ? 'params' : 'data']: data
  }
  
  return request(requestConfig)
    .then(res => {
      if (res.success) {
        callback && callback(res)
      } else {
        errorCallback ? errorCallback(res) : console.error(res.message)
      }
    })
    .catch(err => {
      errorCallback ? errorCallback(err) : console.error(err)
    })
}

// 在 Vue 实例上挂载 $request 方法
export function setupRequest(app) {
  app.config.globalProperties.$request = $request
}
```

然后在 main.js 中注册：

```javascript
import { createApp } from 'vue'
import App from './App.vue'
import { setupRequest } from './utils/request'

const app = createApp(App)
setupRequest(app)
app.mount('#app')
```

这样就可以在组件中直接使用 this.$request 方法，保持与原项目相同的调用方式。

#### 2.2.3 方案三：使用 Composition API 重构

如果新项目使用 Vue 3 的 Composition API，可以将接口调用逻辑抽取为可复用的组合式函数：

```javascript
// composables/useOrderList.js
import { ref, reactive } from 'vue'
import request from '@/utils/request'
import orderApi from '@/api/order'

export function useOrderList() {
  const orderList = ref([])
  const pendingShipmentOrders = ref([])
  const pendingReceiptOrders = ref([])
  const completedOrders = ref([])
  
  const pagination = reactive({
    pageNo: 1,
    pageSize: 10,
    total: 0,
    hasMore: true
  })
  
  const loading = ref(false)
  const loadingStatus = ref('more')
  
  // 获取全部订单列表
  const getAllOrders = (params = {}) => {
    loading.value = true
    loadingStatus.value = 'loading'
    
    return request.get(orderApi.allOrder, {
      pageNo: pagination.pageNo,
      pageSize: pagination.pageSize,
      ...params
    }).then(res => {
      const { records, total, current, size } = res.result
      
      pagination.total = total
      pagination.hasMore = total > current * size
      loadingStatus.value = pagination.hasMore ? 'more' : 'noMore'
      
      if (pagination.pageNo === 1) {
        orderList.value = records
      } else {
        orderList.value = [...orderList.value, ...records]
      }
      
      return res
    }).finally(() => {
      loading.value = false
    })
  }
  
  // 获取待发货订单列表
  const getPendingShipmentOrders = () => {
    return request.get(orderApi.queryPageListToSendTheGoods).then(res => {
      pendingShipmentOrders.value = res.result.records
      return res
    })
  }
  
  // 加载更多
  const loadMore = () => {
    if (!pagination.hasMore || loading.value) return
    
    pagination.pageNo++
    return getAllOrders()
  }
  
  // 取消并退款
  const cancelAndRefund = (id, reason) => {
    return request.post(orderApi.backOrder, {
      id,
      closeExplain: reason
    })
  }
  
  // 删除订单
  const deleteOrder = (id) => {
    return request.delete(orderApi.deleteOrder, { id })
  }
  
  return {
    orderList,
    pendingShipmentOrders,
    pendingReceiptOrders,
    completedOrders,
    pagination,
    loading,
    loadingStatus,
    getAllOrders,
    getPendingShipmentOrders,
    loadMore,
    cancelAndRefund,
    deleteOrder
  }
}
```

然后在组件中使用：

```vue
<script setup>
import { ref, onMounted } from 'vue'
import { useOrderList } from '@/composables/useOrderList'

const {
  orderList,
  pendingShipmentOrders,
  loadingStatus,
  getAllOrders,
  getPendingShipmentOrders,
  loadMore,
  cancelAndRefund,
  deleteOrder
} = useOrderList()

const tabIndex = ref(0)
const searchKeyword = ref('')

onMounted(() => {
  loadAllData()
})

const loadAllData = () => {
  getAllOrders({ goodName: searchKeyword.value })
  getPendingShipmentOrders()
  // 加载其他状态订单...
}

const handleSearch = (value) => {
  searchKeyword.value = value
  getAllOrders({ goodName: value })
}

const handleCancelOrder = async (id, reason) => {
  try {
    await cancelAndRefund(id, reason)
    uni.showToast({ title: '操作成功', icon: 'none' })
    loadAllData()
  } catch (error) {
    console.error(error)
  }
}

const handleDeleteOrder = async (id) => {
  try {
    await deleteOrder(id)
    uni.showToast({ title: '操作成功', icon: 'none' })
    loadAllData()
  } catch (error) {
    console.error(error)
  }
}
</script>
```

### 2.3 错误处理适配

原项目中的错误处理逻辑可能与新项目不同，需要进行适配：

#### 2.3.1 全局错误处理

```javascript
// utils/request.js
import axios from 'axios'

const request = axios.create({
  baseURL: process.env.VUE_APP_API_BASE_URL,
  timeout: 10000
})

// 响应拦截器
request.interceptors.response.use(
  response => {
    const res = response.data
    
    if (res.success) {
      return res
    } else {
      // 统一错误提示
      uni.showToast({
        title: res.message || '请求失败',
        icon: 'none'
      })
      
      return Promise.reject(res)
    }
  },
  error => {
    // 网络错误处理
    uni.showToast({
      title: '网络异常，请稍后重试',
      icon: 'none'
    })
    
    return Promise.reject(error)
  }
)

export default request
```

#### 2.3.2 组件级错误处理

```javascript
// 取消并退款
cancelOrder(item) {
  this.$refs.inputDialog.open()
  this.qxtkId = item.id
},

dialogInputConfirm(val) {
  uni.showLoading({ mask: true })
  
  orderService.cancelAndRefund({
    id: this.qxtkId,
    closeExplain: val
  }).then(res => {
    uni.hideLoading()
    uni.showToast({
      title: '操作成功',
      icon: 'none'
    })
    this.qxtkId = ''
    this.$refs.inputDialog.close()
    this.loadAllData()
  }).catch(err => {
    uni.hideLoading()
    // 错误已在全局拦截器中处理
  })
}
```

## 3. 接口调用迁移示例

### 3.1 订单列表页面（AllOrder.vue）

以下是订单列表页面的接口调用迁移示例：

```vue
<template>
  <!-- 模板部分保持不变 -->
</template>

<script>
import orderApi from '@/api/order'

export default {
  data() {
    return {
      goodName: '',
      tab: 0,
      OrderList: [],
      queryPageListObligationList: [],
      queryPageListToSendTheGoodsList: [],
      queryPageListWaitForReceivingList: [],
      queryPageListDealsAreDoneList: [],
      FiveList: [],
      loadMore: 'more',
      ImgDomain: this.$ImgDomain,
      pageSize: 10,
      pageNo: 1,
      stauts: true,
      qxtkReason: '',
      qxtkId: ''
    }
  },
  
  onLoad(option) {
    this.tab = option.tab || 0
  },
  
  onShow() {
    this.loadAllData()
  },
  
  methods: {
    // 加载所有数据
    loadAllData() {
      this.getAllOrderList()
      this.getqueryPageListObligation()
      this.getqueryPageListToSendTheGoods()
      this.getqueryPageListWaitForReceiving()
      this.getqueryPageListDealsAreDone()
      this.getFiveList()
    },
    
    // 获取全部订单列表
    getAllOrderList() {
      const that = this
      that.stauts = false
      this.loadMore = 'loading'
      
      // 使用新项目的请求方法
      this.$http.get(orderApi.allOrder, {
        params: {
          goodName: that.goodName,
          pageNo: that.pageNo,
          pageSize: that.pageSize
        }
      }).then(res => {
        if (res.result.total <= res.result.current * res.result.size) {
          that.stauts = false
          this.loadMore = 'noMore'
        } else {
          this.loadMore = 'more'
          that.stauts = true
        }
        
        if (res.result.records.length === 0) {
          that.OrderList = res.result.records
        } else {
          if (that.pageNo === 1) {
            that.OrderList = res.result.records
          } else {
            res.result.records.forEach(item => {
              that.OrderList.push(item)
            })
          }
        }
      }).catch(err => {
        this.loadMore = 'more'
        console.error(err)
      })
    },
    
    // 获取待发货订单列表
    getqueryPageListToSendTheGoods() {
      this.$http.get(orderApi.queryPageListToSendTheGoods).then(res => {
        this.queryPageListToSendTheGoodsList = res.result.records
      })
    },
    
    // 其他获取订单列表的方法...
    
    // 取消并退款
    inputDialogToggle(item) {
      this.qxtkId = item.id
      this.$refs.inputDialog.open()
    },
    
    dialogInputConfirm(val) {
      uni.showLoading({ mask: true })
      
      this.$http.post(orderApi.backOrder, {
        id: this.qxtkId,
        closeExplain: val
      }).then(res => {
        uni.hideLoading()
        uni.showToast({
          title: '操作成功',
          icon: 'none'
        })
        this.qxtkId = ''
        this.$refs.inputDialog.close()
        this.loadAllData()
      }).catch(err => {
        uni.hideLoading()
      })
    },
    
    // 删除订单
    dele(item) {
      const that = this
      uni.showModal({
        title: '温馨提示',
        content: '您确定要删除该订单吗',
        success: function(res) {
          if (res.confirm) {
            uni.showLoading({ mask: true })
            
            that.$http.delete(orderApi.deleteOrder, {
              params: { id: item.id }
            }).then(res => {
              uni.hideLoading()
              uni.showToast({
                title: '操作成功',
                icon: 'none'
              })
              that.loadAllData()
            }).catch(err => {
              uni.hideLoading()
            })
          }
        }
      })
    },
    
    // 其他方法...
  }
}
</script>

<style lang="scss">
/* 样式部分保持不变 */
</style>
```

### 3.2 订单搜索结果页面（SearchOeder.vue）

```vue
<template>
  <!-- 模板部分保持不变 -->
</template>

<script>
import orderApi from '@/api/order'

export default {
  data() {
    return {
      loadMore: 'more',
      ImgDomain: this.$ImgDomain,
      pageSize: 10,
      pageNo: 1,
      stauts: true,
      searchData: null,
      OrderList: []
    }
  },
  
  onLoad(option) {
    this.searchData = JSON.parse(option.searchData)
  },
  
  onShow() {
    this.getAllOrderList()
  },
  
  methods: {
    getAllOrderList() {
      const that = this
      that.stauts = false
      this.loadMore = 'loading'
      
      const requestParams = {
        pageNo: that.pageNo,
        pageSize: that.pageSize,
        ...that.searchData
      }
      
      this.$http.get(orderApi.allOrder, {
        params: requestParams
      }).then(res => {
        if (res.result.total <= res.result.current * res.result.size) {
          that.stauts = false
          this.loadMore = 'noMore'
        } else {
          this.loadMore = 'more'
          that.stauts = true
        }
        
        if (res.result.records.length === 0) {
          that.OrderList = res.result.records
        } else {
          if (that.pageNo === 1) {
            that.OrderList = res.result.records
          } else {
            res.result.records.forEach(item => {
              that.OrderList.push(item)
            })
          }
        }
      }).catch(err => {
        this.loadMore = 'more'
        console.error(err)
      })
    },
    
    scrolltoBottom() {
      if (this.stauts) {
        this.pageNo++
        this.getAllOrderList()
      }
    }
  }
}
</script>

<style lang="scss">
/* 样式部分保持不变 */
</style>
```

## 4. 接口调用迁移注意事项

### 4.1 请求方法适配

1. **请求方法名称**：确保新项目中的请求方法名称与原项目一致，或者进行适当的映射
2. **请求参数格式**：注意请求参数的传递方式，如 GET 请求使用 params，POST 请求使用 data
3. **响应数据处理**：确保对响应数据的处理逻辑与原项目一致

### 4.2 错误处理适配

1. **全局错误处理**：确保新项目中有全局的错误处理机制
2. **组件级错误处理**：在组件中添加必要的错误处理逻辑
3. **加载状态管理**：确保在请求开始和结束时正确设置加载状态

### 4.3 接口参数验证

1. **必填参数检查**：确保所有必填参数都已正确传递
2. **参数类型转换**：注意数字类型参数的转换，如 `item.id * 1` 或 `Number(item.id)`
3. **日期格式化**：确保日期参数的格式符合接口要求

### 4.4 分页逻辑适配

1. **分页参数**：确保分页参数名称与接口要求一致（pageNo/page, pageSize/size 等）
2. **加载更多逻辑**：确保上拉加载更多的逻辑正确实现
3. **数据合并**：确保新加载的数据正确合并到现有列表中

### 4.5 接口响应适配

1. **响应结构**：确保新项目中对接口响应结构的处理与原项目一致
2. **成功/失败判断**：确保正确判断接口调用的成功与失败
3. **数据转换**：必要时对接口返回的数据进行转换，以适应页面展示需求

## 5. 总结

本文档详细分析了订单列表页面的接口调用实现逻辑，并提供了将其迁移到新项目的详细方案。通过适当的接口调用适配，可以确保在新项目中复用现有的后端接口，同时保持页面功能的完整性和一致性。

在迁移过程中，需要特别注意请求方法适配、错误处理适配、接口参数验证、分页逻辑适配和接口响应适配等方面，以确保迁移后的页面能够正常工作。
