# 订单发货与物流流程分析

## 一、订单列表页面分析

### 1. 页面结构与布局

订单列表页面（`pages/order/AllOrder.vue`）主要包含以下几个部分：

1. **顶部导航栏**：显示"订单管理"标题
2. **状态标签栏**：包含"全部订单"、"待发货"、"已发货"、"已收货"等标签
3. **订单列表区域**：显示符合当前选中状态的订单卡片
4. **订单卡片**：每个订单卡片包含以下信息：
   - 订单号
   - 订单状态（待付款、待发货、已发货、交易成功等）
   - 商品信息（图片、名称、价格等）
   - 操作按钮（根据订单状态显示不同的按钮）

### 2. 数据加载流程

1. 页面初始化时，调用`getOrderStatusCount`获取各状态订单数量
2. 根据选中的标签页（tab），调用对应的API获取订单列表：
   - 全部订单：`getAllOrders`
   - 待发货订单：`getPendingShipmentOrders`
   - 已发货订单：`getPendingReceiptOrders`
   - 已收货订单：`getCompletedOrders`
3. 支持下拉刷新和上拉加载更多功能

### 3. 发货按钮显示逻辑

在订单列表页面，"发货"按钮仅在订单状态为"待发货"（status=1）时显示：

```html
<view class="o_bottom" v-if="item.status == 1">
  <view class="b_right" @click="toFH(item)">发货</view>
</view>
```

## 二、订单详情页面分析

### 1. 页面结构与布局

订单详情页面（`pages/order/OrderDetail.vue`）主要包含以下几个部分：

1. **顶部状态区域**：
   - 显示订单状态（待付款、待发货、已发货、交易成功等）
   - 订单号
   - 操作按钮（根据订单状态显示不同的按钮）
2. **商品信息区域**：显示订单中的商品详情
3. **订单信息区域**：显示订单的详细信息，如收货地址、支付方式、下单时间等

### 2. 数据加载流程

1. 页面初始化时，通过URL参数获取订单号（orderNo）
2. 调用`getOrderDetail`接口获取订单详情数据
3. 根据订单状态，显示不同的操作按钮和页面样式

### 3. 发货按钮显示逻辑

在订单详情页面，"立即发货"按钮仅在订单状态为"待发货"（status=1）时显示：

```html
<template v-if="orderData.status==='1'">
  <view class="h_right" @click="toFH">立即发货</view>
</template>
```

### 4. 查看物流按钮显示逻辑

在订单详情页面，"查看物流"按钮在订单状态为"已发货"（status=2）或"交易成功"（status=3）时显示：

```html
<template v-else-if="orderData.status==='3' || orderData.status==='2'">
  <view class="h_right" @click="wl">查看物流</view>
</template>
```

## 三、发货页面分析

### 1. 页面结构与布局

发货页面（`pages/order/ProductShipment.vue`）主要包含以下几个部分：

1. **物流服务选择区域**：
   - 包裹列表，支持添加多个包裹
   - 每个包裹可以选择商品、物流公司和填写快递单号
2. **买家信息确认区域**：显示买家账号、昵称等信息
3. **底部操作按钮**：取消和发货按钮

### 2. 数据流转过程

1. 从订单列表或详情页点击"发货"按钮时，会将订单数据存储到本地：
```javascript
async toFH(item) {
  const memberList = item.memberList
  let obj = {
    ...item,
    phone: memberList?.phone,
    nickName: memberList?.nickName,
    memberType: memberList?.memberType,
    orderStoreGoodRecords: item.orderStoreSubListDTOs,
    distribution: item.distribution == 1 ? '自提' : item.distribution == 2 ? '配送' : '快递'
  }

  await uni.setStorageSync('ProductShipmentData', obj)
  this.navTo(`/pages/order/ProductShipment`)
}
```

2. 发货页面初始化时，从本地存储获取订单数据：
```javascript
onLoad(o) {
  this.datas = uni.getStorageSync('ProductShipmentData');
  // 获取物流公司列表
  this.loadLogisticsCompanies();
}
```

3. 加载物流公司列表：
```javascript
loadLogisticsCompanies() {
  getLogisticsCompanies({
    code: 'logistics_company'
  }).then(response => {
    if (response.success) {
      this.array = response.result;
    }
  });
}
```

### 3. 发货操作流程

1. 用户选择商品、物流公司和填写快递单号
2. 点击"发货"按钮时，进行表单验证：
   - 检查是否选择了商品
   - 检查是否填写了快递单号
3. 构建发货请求参数：
```javascript
let listParcel = [];
for (let item of this.checkedList) {
  let results = {
    id: count <= 0 ? that.datas.orderStoreSubListDTOs[0].id : '',
    name: `第${count}个包裹`,
    orderProviderGoodRecordInfo: item.list.map(i => ({
      id: i.id,
      name: i.goodName
    })),
    logisticsCompany: that.array[item.indexex]?.value || that.array[item.indexex],
    trackingNumber: item.trackingNumber
  };
  listParcel.push(results);
  count += 1;
}

let obj = {
  listMap: [{
    providerAddressFa: '无',
    providerAddressTui: '无',
    sysUserName: that.datas.orderStoreSubListDTOs[0].sysUserName,
    orderNo: that.datas.orderNo,
    goodsInfo: that.datas.orderStoreSubListDTOs[0].orderStoreGoodRecords,
    listParcel
  }]
};
```

4. 调用发货接口：
```javascript
shipOrder(obj).then(response => {
  if (response.success) {
    uni.showToast({
      title: '发货成功',
      icon: 'success'
    });
    setTimeout(() => {
      uni.navigateBack();
    }, 1000);
  }
});
```

## 四、物流信息页面分析

### 1. 页面结构与布局

物流信息页面（`pages/order/LogisticsInformation.vue`）主要包含以下几个部分：

1. **包裹选择器**：当订单包含多个包裹时，可以切换查看不同包裹的物流信息
2. **物流基本信息**：显示供应商、运单号码、物流公司、收货信息等
3. **物流跟踪详情**：以时间线形式展示物流跟踪记录

### 2. 数据加载流程

1. 页面初始化时，从URL参数获取订单ID（orderListId）
2. 调用`getLogisticsInfo`接口获取物流信息：
```javascript
loadLogisticsInfo(options) {
  getLogisticsInfo(options).then(response => {
    if (response.success && response.result) {
      let rests = response.result;
      this.datas = rests;
      
      // 处理物流信息...
      
      // 解析物流跟踪信息
      if (orderProviderList.logisticsTracking) {
        try {
          const parsedData = JSON.parse(orderProviderList.logisticsTracking);
          this.logisticsTrackingList = parsedData.result?.list || [];
        } catch (error) {
          this.logisticsTrackingList = [];
        }
      }
    }
  });
}
```

### 3. 多包裹切换逻辑

物流信息页面支持多包裹切换查看：

```html
<view class="package-selector">
  <view
    class="package-item"
    :class="{active: active==index}"
    v-for="(item,index) in datas"
    @click="changePackage(index)"
    :key="index"
  >
    包裹 {{index + 1}}
  </view>
</view>
```

```javascript
changePackage(i) {
  this.active = i;
}
```

## 五、API接口分析

### 1. 订单列表相关接口

| 接口名称 | 路径 | 方法 | 描述 |
|---------|-----|------|------|
| getAllOrders | /back/order/list | GET | 获取全部订单列表 |
| getPendingShipmentOrders | /back/order/queryPageListToSendTheGoods | GET | 获取待发货订单列表 |
| getPendingReceiptOrders | /back/order/queryPageListWaitForReceiving | GET | 获取待收货订单列表 |
| getCompletedOrders | /back/order/queryPageListDealsAreDone | GET | 获取交易成功订单列表 |

### 2. 订单详情接口

| 接口名称 | 路径 | 方法 | 描述 |
|---------|-----|------|------|
| getOrderDetail | /back/order/getOrderDetail | GET | 获取订单详情 |

### 3. 发货相关接口

| 接口名称 | 路径 | 方法 | 描述 |
|---------|-----|------|------|
| getLogisticsCompanies | /front/sysDict/getDicts | GET | 获取物流公司列表 |
| shipOrder | /back/order/ordereDlivery | POST | 订单发货 |

### 4. 物流相关接口

| 接口名称 | 路径 | 方法 | 描述 |
|---------|-----|------|------|
| getLogisticsInfo | /back/order/parcelInformation | GET | 获取物流信息 |

## 六、数据结构分析

### 1. 订单列表数据结构

订单列表API返回的数据结构包含分页信息和订单记录：

```json
{
  "success": true,
  "result": {
    "records": [订单记录数组],
    "total": 总记录数,
    "size": 每页大小,
    "current": 当前页码
  }
}
```

每个订单记录包含以下主要字段：
- id: 订单ID
- orderNo: 订单编号
- status: 订单状态（0-待付款，1-待发货，2-已发货，3-交易成功，4-交易失败）
- orderStoreSubListDTOs: 订单商品信息
- memberList: 买家信息

### 2. 发货请求数据结构

发货请求的数据结构如下：

```json
{
  "listMap": [
    {
      "providerAddressFa": "发货地址",
      "providerAddressTui": "退货地址",
      "sysUserName": "商家名称",
      "orderNo": "订单编号",
      "goodsInfo": [商品信息数组],
      "listParcel": [
        {
          "id": "包裹ID",
          "name": "包裹名称",
          "orderProviderGoodRecordInfo": [
            {
              "id": "商品ID",
              "name": "商品名称"
            }
          ],
          "logisticsCompany": "物流公司代码",
          "trackingNumber": "快递单号"
        }
      ]
    }
  ]
}
```

### 3. 物流信息数据结构

物流信息API返回的数据结构包含包裹信息和物流跟踪记录：

```json
{
  "success": true,
  "result": [
    {
      "sysUserName": "商家名称",
      "trackingNumber": "快递单号",
      "logisticsCompany": "物流公司",
      "providerAddressDTOFa": {
        "contactName": "联系人",
        "contactPhone": "联系电话",
        "detailedAddress": "详细地址"
      },
      "providerAddressDTOTui": {
        "contactName": "联系人",
        "contactPhone": "联系电话",
        "detailedAddress": "详细地址"
      },
      "logisticsTracking": "物流跟踪JSON字符串"
    }
  ]
}
```

物流跟踪JSON字符串解析后的结构：

```json
{
  "result": {
    "list": [
      {
        "time": "时间",
        "status": "状态描述"
      }
    ]
  }
}
```
