# 登录认证与状态管理实施记录

本文档记录了商家端小程序登录认证与状态管理的实施过程，包括需求分析、实施方案、具体实施步骤以及注意事项。

## 一、需求分析

根据《登录认证流程与状态管理分析.md》文档和当前项目代码，我们需要实现以下功能：

1. 账号密码登录功能
2. 登录状态管理（Token存储与验证）
3. 自动登录机制
4. 请求拦截器（添加Token和处理Token失效）
5. 页面访问权限控制
6. 登出功能

## 二、实施方案

### 1. API接口调整

添加商家端登录相关API：

```javascript
// hooks/api.js 中添加商家端登录相关API
const api = {
  // 商家端登录相关API
  login: 'before/sysUser/login', // 账号密码登录
  loginByCode: 'before/sysUser/loginByCode', // 微信授权登录
  logout: 'back/skysUser/logout', // 登出
  getUserInfo: 'back/storeManage/findUseInfo', // 获取商家信息

  // 其他API...
};
```

### 2. 登录页面实现

修改登录页面，实现账号密码登录功能：

```javascript
// pages/login/login.vue
const handleLogin = async () => {
  if (!validatePhone() || !validatePassword()) {
    return;
  }

  isLoading.value = true;
  try {
    // 实际登录请求
    const { data } = await uni.http.post(uni.api.login, {
      username: phone.value,
      password: password.value,
      rememberMe: rememberMe.value
    });

    // 如果记住账号，保存账号信息
    if (rememberMe.value) {
      uni.setStorageSync('rememberedAccount', {
        username: phone.value,
        timestamp: Date.now()
      });
    } else {
      uni.removeStorageSync('rememberedAccount');
    }

    // 登录成功处理
    loginSuccessCallBack(data);
  } catch (error) {
    uni.showToast({
      title: error.data?.message || '登录失败',
      icon: 'none'
    });
  } finally {
    isLoading.value = false;
  }
};
```

### 3. 登录状态管理实现

修改getUserInfos函数，使用新的getUserInfo API：

```javascript
// hooks/index.js
export async function getUserInfos() {
  if (userStore().token) {
    try {
      let { data } = await uni.http.get(uni.api.getUserInfo);
      userStore().setUserInfo(data.result);
    } catch (error) {
      console.error('获取用户信息失败', error);
      userStore().clearUserInfo();
    }
  } else {
    userStore().clearUserInfo();
  }
}
```

### 4. 登出功能实现

添加登出功能：

```javascript
// hooks/index.js
export async function logout() {
  try {
    // 调用登出接口
    await uni.http.post(uni.api.logout);
  } catch (error) {
    console.error('登出失败', error);
  } finally {
    // 无论接口是否调用成功，都清除本地登录信息
    userStore().logOut();

    // 跳转到登录页
    uni.reLaunch({
      url: '/pages/login/login'
    });
  }
}
```

### 5. 自动登录机制实现

在App.vue中实现自动登录机制：

```javascript
// App.vue
export default {
  // 其他配置...

  onLaunch: async function() {
    // 检查登录状态
    this.checkLoginStatus();

    // 其他初始化...
  },

  methods: {
    // 检查登录状态
    checkLoginStatus() {
      // 获取用户信息（如果有Token会自动获取）
      getUserInfos();
    }
  }
}
```

## 三、实施步骤

### 1. 修改API配置

在hooks/api.js中添加商家端登录相关API：

```javascript
// 商家端登录相关API
login: 'before/sysUser/login', // 账号密码登录
loginByCode: 'before/sysUser/loginByCode', // 微信授权登录
logout: 'back/skysUser/logout', // 登出
getUserInfo: 'back/storeManage/findUseInfo', // 获取商家信息
```

### 2. 完善登录页面

修改pages/login/login.vue，实现以下功能：

1. 引入必要的依赖：
   ```javascript
   import { ref, onMounted } from 'vue';
   import { loginSuccessCallBack } from '@/hooks';
   import { userStore } from '@/store';
   ```

2. 实现登录处理函数：
   ```javascript
   const handleLogin = async () => {
     if (!validatePhone() || !validatePassword()) {
       return;
     }

     isLoading.value = true;
     try {
       // 实际登录请求
       const { data } = await uni.http.post(uni.api.login, {
         username: phone.value,
         password: password.value,
         rememberMe: rememberMe.value
       });

       // 如果记住账号，保存账号信息
       if (rememberMe.value) {
         uni.setStorageSync('rememberedAccount', {
           username: phone.value,
           timestamp: Date.now()
         });
       } else {
         uni.removeStorageSync('rememberedAccount');
       }

       // 登录成功处理
       loginSuccessCallBack(data);
     } catch (error) {
       uni.showToast({
         title: error.data?.message || '登录失败',
         icon: 'none'
       });
     } finally {
       isLoading.value = false;
     }
   };
   ```

3. 添加记住账号功能：
   ```javascript
   onMounted(() => {
     const rememberedAccount = uni.getStorageSync('rememberedAccount');
     if (rememberedAccount && rememberedAccount.username) {
       phone.value = rememberedAccount.username;
       rememberMe.value = true;
     }
   });
   ```

### 3. 修改getUserInfos函数

在hooks/index.js中修改getUserInfos函数，使用新的getUserInfo API：

```javascript
export async function getUserInfos() {
  if (userStore().token) {
    try {
      let { data } = await uni.http.get(uni.api.getUserInfo);
      userStore().setUserInfo(data.result);
    } catch (error) {
      console.error('获取用户信息失败', error);
      userStore().clearUserInfo();
    }
  } else {
    userStore().clearUserInfo();
  }
}
```

### 4. 添加登出功能

在hooks/index.js中添加logout函数：

```javascript
export async function logout() {
  try {
    // 调用登出接口
    await uni.http.post(uni.api.logout);
  } catch (error) {
    console.error('登出失败', error);
  } finally {
    // 无论接口是否调用成功，都清除本地登录信息
    userStore().logOut();

    // 跳转到登录页
    uni.reLaunch({
      url: '/pages/login/login'
    });
  }
}
```

### 5. 修改App.vue

在App.vue中添加自动登录机制：

1. 修改onLaunch方法：
   ```javascript
   onLaunch: async function() {
     // 检查登录状态
     this.checkLoginStatus();

     //程序设置相关
     let { data } = await uni.http.get(uni.api.findMarketingDistributionSetting);
     frontSetting().setSetting(data.result)
   },
   ```

2. 添加checkLoginStatus方法：
   ```javascript
   methods: {
     // 检查登录状态
     checkLoginStatus() {
       // 检查是否有Token
       const token = uni.getStorageSync('token');
       if (!token) {
         // 没有Token，跳转到登录页
         uni.reLaunch({
           url: '/pages/login/login'
         });
         return;
       }

       // 有Token，获取用户信息
       getUserInfos();
     }
   }
   ```

## 四、注意事项

1. **Token存储安全**：
   - 目前Token是明文存储的，可以考虑加密存储
   - 可以设置Token的有效期，超过有效期自动清除

2. **记住账号功能**：
   - 只存储账号，不存储密码，保证安全性
   - 设置时间戳，可以实现记住账号的有效期

3. **错误处理**：
   - 登录失败时显示具体的错误信息
   - 获取用户信息失败时自动清除用户信息

4. **自动登录机制**：
   - 应用启动时自动检查登录状态
   - 如果有Token，自动获取用户信息

5. **登出功能**：
   - 调用登出接口
   - 无论接口是否调用成功，都清除本地登录信息
   - 跳转到登录页

## 五、后续优化方向

1. **Token加密存储**：
   - 使用加密算法对Token进行加密存储，提高安全性

2. **Token自动刷新**：
   - 实现Token自动刷新机制，延长用户登录状态
   - 可以在Token即将过期时自动刷新

3. **统一错误处理**：
   - 创建统一的错误处理函数，提高代码可维护性
   - 根据错误码进行不同的处理

4. **登录状态检查优化**：
   - 减少不必要的请求，提高性能
   - 可以在本地存储Token的过期时间，过期自动清除

5. **记住账号功能优化**：
   - 可以添加自动登录选项，提高用户体验
   - 设置记住账号的有效期，超过有效期自动清除

## 六、问题修复

在实施过程中，我们发现并修复了以下问题：

### 1. 默认打开首页而非登录页的问题

**问题描述**：
在初次实施后，我们发现项目默认打开了首页，而没有进行登录状态检查。这是因为我们在App.vue中的checkLoginStatus方法只是调用了getUserInfos函数，没有处理未登录时的跳转逻辑。

**修复方案**：
修改App.vue中的checkLoginStatus方法，添加未登录时跳转到登录页的逻辑：

```javascript
// 检查登录状态
checkLoginStatus() {
  // 检查是否有Token
  const token = uni.getStorageSync('token');
  if (!token) {
    // 没有Token，跳转到登录页
    uni.reLaunch({
      url: '/pages/login/login'
    });
    return;
  }

  // 有Token，获取用户信息
  getUserInfos();
}
```

**修复效果**：
现在，当用户首次打开应用或未登录时，会自动跳转到登录页面，而不是直接进入首页。这样确保了用户必须登录后才能使用应用的功能。

### 2. 登录接口的用户名参数名称格式问题

**问题描述**：
在实施登录功能时，我们使用了`username`作为用户名的参数名称，但实际上服务端接口需要的参数名称是`userName`。

**修复方案**：
修改登录请求中的用户名参数名称：

```javascript
// 修改前
const { data } = await uni.http.post(uni.api.login, {
  username: phone.value,
  password: password.value,
  rememberMe: rememberMe.value
});

// 修改后
const { data } = await uni.http.post(uni.api.login, {
  userName: phone.value,
  password: password.value,
  rememberMe: rememberMe.value
});
```

同时，我们也修改了记住账号功能中的用户名参数名称：

```javascript
// 修改前
uni.setStorageSync('rememberedAccount', {
  username: phone.value,
  timestamp: Date.now()
});

// 修改后
uni.setStorageSync('rememberedAccount', {
  userName: phone.value,
  timestamp: Date.now()
});
```

以及检查记住账号的逻辑：

```javascript
// 修改前
if (rememberedAccount && rememberedAccount.username) {
  phone.value = rememberedAccount.username;
  rememberMe.value = true;
}

// 修改后
if (rememberedAccount && rememberedAccount.userName) {
  phone.value = rememberedAccount.userName;
  rememberMe.value = true;
}
```

**修复效果**：
现在，登录请求会使用正确的参数名称`userName`，确保了登录接口能够正确处理请求。同时，记住账号功能也能正常工作。

## 七、总结

通过本次实施，我们完成了商家端小程序的登录认证与状态管理功能，包括账号密码登录、登录状态管理、自动登录机制、请求拦截器、页面访问权限控制和登出功能。这些功能的实现，为用户提供了更好的登录体验，同时也保证了系统的安全性。

后续可以根据实际使用情况，进一步优化登录认证与状态管理功能，提高系统的安全性和用户体验。
