# 个人页面与退出登录功能实施记录

本文档记录了添加个人页面和退出登录功能的实施过程。

## 一、需求分析

需要添加一个"个人"页面，配置在tabbar最后一个，页面中添加一个"退出登录"按钮并实现退出登录的功能，主要用于方便调试登录功能。

## 二、实施方案

### 1. 创建个人页面

创建`pages/profile/profile.vue`文件，实现一个简单的个人中心页面，包含退出登录按钮。

### 2. 配置tabbar

修改`pages.json`文件，将个人页面添加到tabbar列表中，作为最后一个选项。

### 3. 实现退出登录功能

使用之前实现的`logout`函数，实现退出登录功能。

## 三、实施步骤

### 1. 创建个人页面

创建`pages/profile/profile.vue`文件，内容如下：

```vue
<template>
  <view class="profile-container">
    <view class="header">
      <text class="title">个人中心</text>
    </view>

    <view class="content">
      <!-- 这里可以添加个人信息展示 -->
    </view>

    <view class="footer">
      <button class="logout-btn" @click="handleLogout">退出登录</button>
    </view>
  </view>
</template>

<script setup>
import { ref } from 'vue';
import { logout } from '@/hooks';

// 处理退出登录
const handleLogout = () => {
  uni.showModal({
    title: '提示',
    content: '确定要退出登录吗？',
    success: (res) => {
      if (res.confirm) {
        // 调用登出方法
        logout();
      }
    }
  });
};
</script>

<style>
.profile-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
  padding: 0 30rpx;
}

.header {
  padding: 40rpx 0;
  display: flex;
  justify-content: center;
  align-items: center;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.content {
  flex: 1;
}

.footer {
  padding: 40rpx 0;
}

.logout-btn {
  width: 100%;
  height: 88rpx;
  line-height: 88rpx;
  background-color: #ff4d4f;
  color: #ffffff;
  font-size: 32rpx;
  border-radius: 44rpx;
}
</style>
```

### 2. 修改pages.json

在`pages.json`文件中添加个人页面的配置：

```json
{
  "path": "pages/profile/profile",
  "style": {
    "navigationBarTitleText": "个人中心",
    "navigationBarBackgroundColor": "#ffffff"
  }
}
```

在`tabBar.list`中添加个人页面的配置：

```json
{
  "pagePath": "pages/profile/profile",
  "iconPath": "static/tabbar/user_btn.png",
  "selectedIconPath": "static/tabbar/user_btn_sel.png",
  "text": "个人"
}
```

## 四、实施效果

1. 在tabbar中添加了"个人"选项，点击可以进入个人中心页面
2. 个人中心页面中添加了"退出登录"按钮，点击后会弹出确认对话框
3. 确认退出登录后，会调用`logout`函数，清除登录信息并跳转到登录页面

## 五、问题修复

### 1. 退出登录接口地址错误

**问题描述**：
在实施退出登录功能时，我们使用了`back/skysUser/logout`作为退出登录的接口地址，但实际上正确的接口地址应该是`back/sysUser/logout`。这导致退出登录功能无法正常工作。

**修复方案**：
修改`hooks/api.js`文件中的退出登录接口地址：

```javascript
// 修改前
logout: 'back/skysUser/logout', // 登出

// 修改后
logout: 'back/sysUser/logout', // 登出
```

**修复效果**：
现在，退出登录功能可以正常工作，用户点击“退出登录”按钮后，可以成功调用退出登录接口，清除登录信息并跳转到登录页面。

## 六、后续优化方向

1. **完善个人信息展示**：
   - 添加用户头像、昵称、手机号等基本信息展示
   - 添加用户等级、积分等信息展示

2. **添加更多功能**：
   - 添加个人信息编辑功能
   - 添加订单管理功能
   - 添加收货地址管理功能
   - 添加账号安全设置功能

3. **优化页面样式**：
   - 优化页面布局，提高用户体验
   - 添加动画效果，提升页面交互体验

4. **添加数据缓存**：
   - 缓存个人信息，减少请求次数
   - 添加数据刷新机制，保证数据的及时性

## 七、总结

通过本次实施，我们完成了个人页面的创建和退出登录功能的实现，为用户提供了一个简单的个人中心页面，方便用户管理个人信息和退出登录。这是个人中心页面的第一个版本，后续可以根据需求进行进一步的完善和优化。
