# "我的"页面实施记录

本文档记录了"我的"页面的实施过程，包括需求分析、设计方案、实施步骤和测试结果。

## 一、需求分析

根据需求分析文档，"我的"页面是八闽助业集市商家端小程序的个人中心页面，主要展示商家的基本信息、认证状态，并提供账号安全和退出登录等功能入口。

### 主要功能需求

1. **顶部商家信息区域**：
   - 展示商家头像、店铺名称、认证状态、账号信息和VIP标识
   - 点击头像可导航至店铺信息页面

2. **功能菜单区域**：
   - 账号安全：点击导航至账号安全页面
   - 退出登录：点击弹出确认对话框，确认后执行退出登录操作

3. **底部标签栏**：
   - 配置在tabbar中，作为最后一个选项
   - 图标使用用户图标，文本为"我的"

## 二、设计方案

### 1. 页面结构设计

页面整体分为三个主要部分：
1. 顶部商家信息区域
2. 功能菜单区域
3. 底部标签栏（由系统tabbar提供）

### 2. 数据交互设计

1. **获取商家信息**：
   - 调用`/back/storeManage/getStoreManage`接口获取商家基本信息
   - 根据返回的数据动态显示商家头像、店铺名称、认证状态等信息

2. **退出登录**：
   - 调用`/back/sysUser/logout`接口执行退出登录操作
   - 清除本地登录信息并跳转到登录页面

### 3. 交互设计

1. **点击头像**：导航至店铺信息页面
2. **点击账号安全**：导航至账号安全页面
3. **点击退出登录**：弹出确认对话框，确认后执行退出登录操作

## 三、实施步骤

### 1. 创建"我的"页面

创建`pages/mine/mine.vue`文件，实现"我的"页面的基本结构和样式：

```vue
<template>
  <view class="mine-container">
    <!-- 顶部商家信息区域 -->
    <view class="merchant-info-area">
      <!-- 商家信息内容 -->
    </view>

    <!-- 功能菜单区域 -->
    <view class="menu-area">
      <!-- 功能菜单项 -->
    </view>

    <!-- 退出登录确认弹窗 -->
    <uni-popup ref="logoutPopup" type="dialog">
      <!-- 弹窗内容 -->
    </uni-popup>
  </view>
</template>
```

### 2. 实现顶部商家信息区域

```vue
<view class="merchant-info-area">
  <view class="merchant-info">
    <view class="avatar-container" @click="goToStoreInfo">
      <image class="avatar" :src="headImg" mode="aspectFill"></image>
    </view>
    <view class="info-container">
      <view class="store-name-container">
        <text class="store-name">{{ storeInfo.storeName || '未设置店铺名称' }}</text>
        <image class="status-icon" :src="statusIconUrl" mode="aspectFill"></image>
      </view>
      <view class="account-info">
        <text class="account-label">账号：</text>
        <text class="account-value">{{ storeInfo.username || '未登录' }}</text>
      </view>
      <view class="vip-info" v-if="vip">
        <image class="vip-icon" src="/static/vipLogos.png" mode="aspectFill"></image>
        <text class="vip-text">{{ vip }}</text>
      </view>
    </view>
  </view>
</view>
```

### 3. 实现功能菜单区域

```vue
<view class="menu-area">
  <view class="menu-item" @click="goToAccountSecurity">
    <text class="menu-text">账号安全</text>
    <image class="arrow-icon" src="/static/right-arrow-black.png" mode="aspectFill"></image>
  </view>
  <view class="menu-item" @click="showLogoutConfirm">
    <text class="menu-text">退出登录</text>
    <image class="arrow-icon" src="/static/right-arrow-black.png" mode="aspectFill"></image>
  </view>
</view>
```

### 4. 实现退出登录确认弹窗

```vue
<uni-popup ref="logoutPopup" type="dialog">
  <uni-popup-dialog
    type="info"
    cancelText="取消"
    confirmText="确认"
    title="提示"
    content="确定要退出登录吗？"
    @confirm="handleLogout"
  ></uni-popup-dialog>
</uni-popup>
```

### 5. 实现页面逻辑

```javascript
import { ref, onMounted, computed } from 'vue';
import { userStore } from '@/store';
import { getUserInfos, logout } from '@/hooks';

// 商家信息
const storeInfo = ref({});
// 头像图片
const headImg = ref('/static/defaultHead.png');
// 认证状态图标
const statusIconUrl = computed(() => {
  const status = storeInfo.value.attestationStatus;
  if (status === '-1') return '/static/authStatus-1.png'; // 未认证
  if (status === '0') return '/static/authStatus0.png'; // 认证中
  if (status === '1') return '/static/authStatus1.png'; // 已认证
  if (status === '2') return '/static/authStatus4.png'; // 认证失败
  return '/static/authStatus-1.png'; // 默认未认证
});
// VIP信息
const vip = ref('');

// 退出登录弹窗引用
const logoutPopup = ref(null);

// 获取商家信息
async function getStoreInfo() {
  try {
    const response = await uni.http.get('/back/storeManage/getStoreManage');
    if (response.data && response.data.success) {
      storeInfo.value = response.data.result || {};
      
      // 处理头像
      if (storeInfo.value.logoAddr) {
        headImg.value = uni.env.IMAGE_URL + storeInfo.value.logoAddr;
      }
      
      // 处理VIP信息
      if (storeInfo.value.endTime === null) {
        vip.value = '终生会员';
      } else if (storeInfo.value.endTime) {
        vip.value = `会员到期时间：${storeInfo.value.endTime}`;
      }
      
      // 更新用户信息
      userStore().setUserInfo(storeInfo.value);
    }
  } catch (error) {
    console.error('获取商家信息失败', error);
  }
}

// 页面加载时获取商家信息
onMounted(() => {
  getStoreInfo();
});
```

### 6. 配置页面路由和tabbar

在`pages.json`文件中添加"我的"页面的配置：

```json
{
  "path": "pages/mine/mine",
  "style": {
    "navigationBarTitleText": "我的",
    "navigationBarBackgroundColor": "#ffffff"
  }
}
```

在`tabBar.list`中更新"个人"选项卡的配置：

```json
{
  "pagePath": "pages/mine/mine",
  "iconPath": "static/tabbar/user_btn.png",
  "selectedIconPath": "static/tabbar/user_btn_sel.png",
  "text": "我的"
}
```

## 四、测试结果

1. **页面加载测试**：
   - 页面能够正常加载，顶部显示商家信息，底部显示功能菜单
   - 商家信息根据接口返回的数据动态显示

2. **功能测试**：
   - 点击头像能够正常导航至店铺信息页面
   - 点击账号安全能够正常导航至账号安全页面
   - 点击退出登录能够正常弹出确认对话框
   - 确认退出登录后能够正常执行退出登录操作并跳转到登录页面

3. **样式测试**：
   - 页面样式符合设计要求，布局合理，显示正常
   - 在不同尺寸的设备上显示正常

## 五、问题与解决方案

### 1. 图片路径问题

**问题描述**：
在实施过程中，发现部分图标资源的路径与需求分析文档中的路径不一致，导致图标无法正常显示。

**解决方案**：
通过查找项目中的图标资源，找到了正确的图标路径，并在代码中使用正确的路径。

### 2. 接口调用问题

**问题描述**：
在测试退出登录功能时，发现退出登录接口调用失败。

**解决方案**：
检查接口路径，确认正确的退出登录接口路径为`/back/sysUser/logout`，修改接口调用代码。

## 六、后续优化方向

1. **完善商家信息展示**：
   - 添加更多商家信息，如店铺评分、店铺等级等
   - 优化商家信息的布局和样式

2. **添加更多功能菜单**：
   - 添加店铺设置、消息通知等功能菜单
   - 根据商家权限动态显示不同的功能菜单

3. **优化页面交互体验**：
   - 添加页面加载动画
   - 优化页面切换效果

4. **添加数据缓存机制**：
   - 缓存商家信息，减少接口调用次数
   - 添加数据刷新机制，确保数据的及时性

## 七、总结

通过本次实施，我们完成了"我的"页面的开发，实现了商家信息展示、账号安全和退出登录等功能。页面设计符合需求分析文档的要求，功能实现完整，交互体验良好。后续可以根据用户反馈和业务需求进行进一步的优化和完善。
