# "我的"页面需求分析文档

## 1. 页面概述

"我的"页面是八闽助业集市商家端小程序的个人中心页面，主要展示商家的基本信息、认证状态，并提供账号安全和退出登录等功能入口。该页面作为小程序的主要导航页面之一，通过底部标签栏可以快速访问。

## 2. 页面布局与交互

### 2.1 整体布局

页面整体分为三个主要部分：
1. 顶部商家信息区域
2. 功能菜单区域
3. 底部标签栏

### 2.2 顶部商家信息区域

**布局结构：**
- 左侧为商家头像（可点击进入店铺信息页面）
- 右侧为商家信息，包括：
  - 店铺名称
  - 认证状态图标
  - 账号信息
  - VIP标识和有效期

**交互逻辑：**
- 点击头像可导航至店铺信息页面（`/pages/storeInfo/storeInfo`）
- 认证状态图标根据店铺认证状态动态显示不同图标

**数据字段：**
- `headImg`：商家头像图片路径
- `pageData.storeName`：店铺名称
- `statusIconUrl`：认证状态图标路径
- `pageData.username`：账号信息
- `vip`：VIP有效期信息

### 2.3 功能菜单区域

**布局结构：**
- 白色背景的圆角卡片
- 包含多个功能菜单项，每项包括：
  - 左侧功能名称
  - 右侧箭头图标

**当前包含的功能菜单：**
1. 账号安全
2. 退出登录

**交互逻辑：**
- 点击"账号安全"导航至账号安全页面（`/pages/userSecurity/userSecurity`）
- 点击"退出登录"弹出确认对话框，确认后执行退出登录操作

### 2.4 底部标签栏

**布局结构：**
- 使用uView的`u-tabbar`组件
- 包含两个标签项：首页和我的

**交互逻辑：**
- 点击"首页"标签导航至首页（`/pages/index/index`）
- 点击"我的"标签导航至我的页面（`/pages/mine/mine`）
- 当前页面高亮显示"我的"标签

### 2.5 弹出确认框

**布局结构：**
- 使用自定义的`pop-up`组件
- 显示确认退出登录的提示文本
- 底部包含"取消"和"确认"按钮

**交互逻辑：**
- 点击"取消"关闭弹窗
- 点击"确认"执行退出登录操作，并显示退出成功提示

## 3. 接口说明

### 3.1 获取商家信息接口

**接口名称：** `getStoreManage`

**接口路径：** `back/storeManage/getStoreManage`

**请求方式：** GET

**请求参数：** 无特殊参数，使用通用请求头

**响应数据结构：**
```javascript
{
  "success": true,
  "code": 200,
  "message": "操作成功",
  "result": {
    "storeName": "店铺名称",
    "username": "账号名称",
    "attestationStatus": "认证状态", // -1:未认证 0:认证中 1:已认证 2:认证失败
    "logoAddr": "店铺logo地址",
    "endTime": "会员到期时间", // null表示终生会员
    "openType": "会员类型"
  }
}
```

### 3.2 退出登录接口

**接口名称：** `logout`

**接口路径：** `back/skysUser/logout`

**请求方式：** POST

**请求参数：** 无特殊参数，使用通用请求头

**响应数据结构：**
```javascript
{
  "success": true,
  "code": 200,
  "message": "操作成功",
  "result": null
}
```

## 4. 数据处理逻辑

### 4.1 商家信息处理

1. 页面加载时，通过`loadPageApiName`属性指定调用`getStoreManage`接口获取商家信息
2. 接口返回数据存储在`pageData`对象中
3. 通过watch监听`pageData`变化，处理以下逻辑：
   - 根据`attestationStatus`设置认证状态图标
   - 如果有`logoAddr`，设置头像图片并更新Vuex中的商家信息
   - 根据`endTime`判断会员类型，设置VIP显示文本

### 4.2 退出登录处理

1. 点击"退出登录"按钮，调用`logout2`方法打开确认弹窗
2. 确认退出后，调用Vuex的`logout`和`clearRelationship`方法清除登录状态和关联信息
3. 显示退出成功提示
4. 系统会自动跳转到首页（在Vuex的logout方法中处理）

## 5. 页面样式

### 5.1 整体样式

- 页面背景色：`#F4F6FA`
- 内容区域左右内边距：32rpx

### 5.2 顶部商家信息区域样式

- 头像尺寸：120rpx × 120rpx
- 头像圆角：50%（圆形）
- 店铺名称字体：加粗
- 账号信息字体大小：24rpx
- VIP标识背景色：线性渐变，从`#FFCF7F`到`#FF8400`
- VIP标识圆角：24rpx
- VIP标识内边距：左右16rpx，上下6rpx

### 5.3 功能菜单区域样式

- 背景色：白色
- 圆角：16rpx
- 内边距：左右24rpx
- 菜单项高度：100rpx
- 菜单项边框：底部2rpx实线，颜色`#EEEEEE`
- 菜单项字体大小：28rpx
- 箭头图标尺寸：32rpx × 32rpx

### 5.4 底部标签栏样式

- 激活颜色：`#ff0000`
- 标签图标尺寸：55rpx × 55rpx

## 6. 使用的图标资源

1. **头像默认图标**：`/static/defaultHead.png`
2. **认证状态图标**：
   - 未认证：`/static/authStatus-1.png`
   - 认证中：`/static/authStatus0.png`
   - 已认证：`/static/authStatus1.png`
   - 认证失败：`/static/authStatus2.png`
3. **VIP标识图标**：`/static/vipLogos.png`
4. **箭头图标**：`@/static/arrowR.png`
5. **底部标签栏图标**：
   - 首页（未选中）：`@/static/tabbar/home.png`
   - 首页（选中）：`@/static/tabbar/home_l.png`
   - 我的（未选中）：`@/static/tabbar/mine.png`
   - 我的（选中）：`@/static/tabbar/mine_l.png`

## 7. 组件依赖

1. **弹出确认框组件**：`@/components/popUp/pop-up.vue`
   - 用于显示退出登录确认对话框
   - 提供`open`、`close`方法和`sure`、`cancel`事件

2. **底部标签栏组件**：`u-tabbar`和`u-tabbar-item`（uView UI组件）
   - 用于显示底部导航
   - 配置项：
     - `value`：当前选中标签值
     - `fixed`：是否固定在底部
     - `placeholder`：是否生成占位元素
     - `safeAreaInsetBottom`：是否适配底部安全区
     - `activeColor`：选中颜色

## 8. 复刻实施建议

1. **保持接口一致性**：
   - 新项目需要实现相同的`back/storeManage/getStoreManage`接口
   - 确保接口返回数据结构与原项目一致

2. **图标资源处理**：
   - 从原项目复制所有需要的图标资源到新项目
   - 保持图标路径一致，或更新引用路径

3. **组件迁移**：
   - 迁移`popUp`组件到新项目
   - 如使用不同的UI框架，需要替换uView的tabbar组件

4. **样式适配**：
   - 根据新项目的设计规范调整样式
   - 保持页面布局结构不变

5. **状态管理**：
   - 实现相同的Vuex状态管理逻辑，特别是登录状态管理
   - 确保`logout`和`clearRelationship`方法在新项目中正确实现

6. **路由导航**：
   - 确保新项目中存在对应的路由页面
   - 保持导航逻辑一致

## 9. 总结

"我的"页面是一个相对简单但功能明确的个人中心页面，主要展示商家基本信息并提供账号安全和退出登录功能。在复刻过程中，需要特别注意接口一致性、状态管理逻辑和UI组件的迁移，以确保功能正常运行。
