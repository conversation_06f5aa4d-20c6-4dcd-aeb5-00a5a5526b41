# 系统运营人员字段重构说明

## 重构目标

将系统运营人员的标识从字典配置改为会员表的字段，简化查询逻辑，提高代码可维护性。

## 修改内容

### 1. 数据库修改

在 `member_list` 表中添加 `is_system_operation` 字段，用于标识是否为系统运营人员：

```sql
-- 添加是否系统运营人员字段
ALTER TABLE member_list ADD COLUMN is_system_operation varchar(2) DEFAULT '0' COMMENT '是否系统运营人员：0否1是';

-- 更新系统运营人员标识
UPDATE member_list SET is_system_operation = '1' WHERE phone = (
    SELECT item_text FROM sys_dict_item WHERE dict_id = (
        SELECT id FROM sys_dict WHERE dict_code = 'system_operation_phone'
    ) AND item_value = 'system_operation_phone'
);
```

### 2. 实体类修改

在 `MemberList` 实体类中添加 `isSystemOperation` 字段：

```java
/**
 * 是否系统运营人员：0否1是
 */
@Excel(name = "是否系统运营人员", width = 15)
@ApiModelProperty(value = "是否系统运营人员：0否1是")
private String isSystemOperation;
```

### 3. 代码修改

#### 3.1 OrderStoreListServiceImpl.java

修改了 `submitOrderStoreGoods` 方法中获取系统运营人员的代码：

```java
// 修改前
String systemOperationPhone = iSysDictService.queryTableDictTextByKey("sys_dict_item", "item_value", "item_text", "system_operation_phone");
MemberList systemOperation = iMemberListService.getOne(new LambdaQueryWrapper<MemberList>()
        .eq(MemberList::getPhone, systemOperationPhone),false);

// 修改后
MemberList systemOperation = iMemberListService.getOne(new LambdaQueryWrapper<MemberList>()
        .eq(MemberList::getIsSystemOperation, "1"),false);
```

#### 3.2 MemberListServiceImpl.java

修改了 `setLoginRegister` 方法中获取系统运营人员的代码：

```java
// 修改前
String systemOperationPhone = iSysDictService.queryTableDictTextByKey("sys_dict_item", "item_value", "item_text", "system_operation_phone");
MemberList systemOperation = getOne(new LambdaQueryWrapper<MemberList>()
        .eq(MemberList::getPhone, systemOperationPhone),false);

// 修改后
MemberList systemOperation = getOne(new LambdaQueryWrapper<MemberList>()
        .eq(MemberList::getIsSystemOperation, "1"),false);
```

#### 3.3 MemberListController.java

修改了 `buildMemberTreeNode` 方法中判断是否为系统运营人员的代码：

```java
// 修改前
String systemOperationPhone = iSysDictService.queryTableDictTextByKey("sys_dict_item", "item_value", "item_text", "system_operation_phone");
treeNode.put("isSystemOperation", member.getPhone().equals(systemOperationPhone) ? "1" : "0"); //是否平台运营账号

// 修改后
treeNode.put("isSystemOperation", member.getIsSystemOperation()); //是否平台运营账号
```

## 优势

1. **简化查询逻辑**：直接通过字段查询，无需再通过字典表获取系统运营人员的手机号
2. **提高性能**：减少了跨表查询，提高查询效率
3. **提高可维护性**：字段含义更加明确，代码更易理解
4. **支持多个系统运营人员**：虽然正常情况下只会配置一个系统运营人员，但新的设计也支持多个系统运营人员的情况

## 注意事项

1. 在部署此修改前，需要先执行数据库脚本，添加字段并更新数据
2. 修改后，系统运营人员的标识将不再依赖于字典配置，如需修改系统运营人员，直接修改会员表中的 `is_system_operation` 字段即可
