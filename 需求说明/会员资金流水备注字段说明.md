# 会员资金流水备注字段说明

## 功能概述

在会员资金流水表中新增"备注"字段，用于在系统补发和系统扣减操作时记录备注信息。

## 数据库变更

在`member_account_capital`表中新增了`remarks`字段，类型为`varchar(255)`，用于存储备注信息。

```sql
ALTER TABLE `member_account_capital` ADD COLUMN `remarks` varchar(255) DEFAULT NULL COMMENT '备注';
```

## 实体类变更

在`MemberAccountCapital`实体类中新增了`remarks`字段：

```java
/**
 * 备注
 */
@Excel(name = "备注", width = 15)
@ApiModelProperty(value = "备注")
private String remarks;
```

## 接口变更

### 1. 系统补发接口

接口路径：`memberList/memberList/systemTopUp`

新增参数：
- `remarks`：备注信息，类型为String

示例：
```
POST /memberList/memberList/systemTopUp
参数：
memberId=123456
balance=100.00
remarks=系统补发测试
```

### 2. 系统扣减接口

接口路径：`memberList/memberList/systemDeduction`

新增参数：
- `remarks`：备注信息，类型为String

示例：
```
POST /memberList/memberList/systemDeduction
参数：
memberId=123456
balance=50.00
remarks=系统扣减测试
```

## 使用说明

1. 在调用系统补发或系统扣减接口时，可以传入`remarks`参数，用于记录操作的备注信息
2. 备注信息将存储在会员资金流水表的`remarks`字段中
3. 在查询会员资金流水时，可以看到对应的备注信息

## 注意事项

1. `remarks`参数为可选参数，如果不传入，则不会记录备注信息
2. 建议在进行系统补发或系统扣减操作时，填写清晰的备注信息，方便后续查询和核对
