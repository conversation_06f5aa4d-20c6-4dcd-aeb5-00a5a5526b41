# 店铺首页接口返回licenseForEnterprise字段说明

## 功能概述

在店铺首页接口`/front/storeManage/index`中新增返回"营业执照"字段`licenseForEnterprise`，用于前端展示店铺的营业执照信息。

## 接口变更

### 1. 接口路径

`/front/storeManage/index`

### 2. 返回字段变更

在接口返回的数据中新增了`licenseForEnterprise`字段，用于展示店铺营业执照图片地址。

### 3. 相关实现变更

在`StoreManageMapper.xml`的`getStoreManageById`方法中，直接在SQL查询中添加了`licenseForEnterprise`字段：

```xml
<select id="getStoreManageById" resultType="map">
    SELECT sm.id,
           ...
           sm.store_propaganda_images                as storePropagandaImages,
           sm.straight                               as storeStraight,
           sm.license_for_enterprise                 as licenseForEnterprise
    FROM `store_manage` sm
    WHERE sm.id = #{storeManageId}
      AND sm.del_flag = '0'
</select>
```

这种方式直接在SQL查询中返回字段，避免了额外的数据库查询，提高了性能。

## 使用说明

1. 前端在调用`/front/storeManage/index`接口获取店铺首页信息时，可以从返回数据中获取`licenseForEnterprise`字段
2. 可以根据`licenseForEnterprise`字段的值展示店铺的营业执照图片

## 示例

### 接口返回示例

```json
{
  "success": true,
  "message": "操作成功！",
  "code": 200,
  "result": {
    "id": "**********",
    "storeName": "测试店铺",
    "logoAddr": "http://example.com/logo.jpg",
    "storeAddress": "测试地址",
    "comprehensiveEvaluation": 4.5,
    "longitude": 116.123456,
    "latitude": 39.123456,
    "sysUserId": "user123",
    "status": "1",
    "storePropagandaImages": "http://example.com/image1.jpg,http://example.com/image2.jpg",
    "storeStraight": "1",
    "licenseForEnterprise": "http://example.com/license.jpg",
    "storeColumn": [
      {
        "name": "商品",
        "storeCode": 1
      },
      {
        "name": "优惠券",
        "storeCode": 2
      }
    ]
  },
  "timestamp": 1683000000000
}
```

### 前端使用示例

前端可以根据`licenseForEnterprise`字段的值展示店铺的营业执照图片：

```javascript
// 获取店铺营业执照图片地址
const licenseForEnterprise = response.result.licenseForEnterprise;

// 如果有营业执照图片，则展示
if (licenseForEnterprise) {
  // 展示营业执照图片
  document.getElementById('license-image').src = licenseForEnterprise;
  document.getElementById('license-container').style.display = 'block';
} else {
  // 隐藏营业执照图片容器
  document.getElementById('license-container').style.display = 'none';
}
```

## 注意事项

1. `licenseForEnterprise`字段可能为空，前端在使用时需要做好空值处理
2. 营业执照图片可能涉及到隐私信息，前端在展示时需要考虑权限控制
3. 如果需要展示高清图片，可能需要考虑图片加载性能问题
