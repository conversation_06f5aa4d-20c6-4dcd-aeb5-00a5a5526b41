### 商家数据面板核心需求（AI设计版）

#### 一、核心模块
1. **实时概览**
- 指标卡：销售额/订单量/客单价（今日+环比）
- 趋势图：近7天销售曲线（双轴：总销售额vs净收入）

2. **订单管理**
- 状态分布饼图（6种状态）
- 换货订单列表（带原因标签）

3. **商品分析**
- 热销TOP5（销量+销售额+类目）
- 库存预警表（≤10件标红）

4. **分佣管理**
- 合作企业家看板（含分佣比例/推广商品数）
- 分佣明细表（可筛选导出）

5. **资金账户**
- 余额卡片（可用+冻结）
- 提现记录（带状态标签）

#### 二、关键交互
1. **分佣比例设置**
```mermaid
flowchart LR
A[设置比例1-20%]-->B[勾选商品]-->C[生成链接]
```

2. **提现审核流程**  
```mermaid
flowchart TB
  A[商家申请提现] --> B{金额≤余额?}
  B -->|是| C[生成待审核记录]
  B -->|否| D[提示余额不足]
  C --> E[管理员审核]

#### 三、数据规则
1. **分佣计算**
- 冻结：订单状态1-2
- 结算：状态3（交易成功）
- 公式：订单金额×店铺比例

2. **提现限制**
- 单日≤5万
- 手续费自动扣减

#### 四、可视化要求
1. **状态标签**
- 待审核（黄）
- 待打款（蓝）
- 已付款（绿）
- 无效（灰）

2. **图表类型**
- 趋势：折线图
- 分布：饼图/环形图
- 排名：条形图

#### 五、异常处理
1. 分佣金额突增200%→触发审核
2. 提现失败→自动解冻金额
3. 库存预警→显示补货按钮