### **商家端数据面板需求文档（完整优化版）**

---

#### **1. 核心模块与功能**
| **模块**         | **子功能**                | **数据维度**                                                                 | **交互需求**                     |
|------------------|--------------------------|-----------------------------------------------------------------------------|----------------------------------|
| **实时概览**     | 核心指标卡片              | 当日销售额、订单量、客单价、实时访客数                                        | 时间筛选（今日/7天/30天/自定义）  |
|                  | 销售趋势图表              | 近7天/30天销售额（总销售额 vs 总业绩(实际入账金额)）                                       | 双轴折线图，支持环比对比          |
| **订单管理**     | 订单状态分布              | 待发货(1)、待收货(2)、交易成功(3)、交易失败(4)、交易完成(5)        | 直观查看各状态订单数量                    |
|                  | 换货订单追踪              | 换货原因分类（尺码/质量问题等）、关联商品                                     | 标记高频换货商品                  |
| **商品分析**     | 热销商品TOP5             | 商品名称、销量、销售额、类目占比                                             | 跳转至商品详情页                  |
|                  | 库存预警                  | 库存量≤10件的商品、滞销商品（30天无销售）                                     | 红色标记，快捷补货                |
| **企业家分佣**   | 合作企业家列表            | 企业家名称、企业家手机号、推广订单数、累计推广业绩、累计分佣金额、当前分佣比例（店铺维度），按累计推广业绩金额降序排序。              | 修改比例需企业家确认              |
|                  | 分佣明细                  | 分佣订单号、分佣金额（订单金额×店铺比例）、结算状态             | 按商品/企业家筛选+导出            |
| **资金账户**     | 实时余额与流水            | 可用余额、冻结金额、收支类型（收入/支出）、关联订单号                         | 点击余额跳转流水明细              |
|                  | 提现管理                  | 提现金额、手续费、实际到账金额、状态（待审核/待打款/已付款/无效）、银行卡信息  | 状态标签颜色化，支持原因查看      |

---

#### **22. 状态与规则定义**

**2.1订单状态与分佣规则**
```markdown
| **状态码** | **状态名称** | **分佣触发节点**               | **分佣资金状态**          |
|------------|--------------|--------------------------------|--------------------------|
| 0          | 待付款       | 不分佣                         | -                        |
| 1          | 待发货       | 分佣待结算（冻结）             | 冻结金额=订单金额×比例   |
| 2          | 待收货       | 分佣待结算（冻结）             | 维持冻结                 |
| 3          | 交易成功     | 分佣已结算（可提现）           | 转入企业家可提现余额     |
| 4          | 交易失败     | 取消分佣                       | 解冻金额                 |
| 5          | 交易完成     | 数据归档                       | -                        |
```

**2.2 提现状态机**  
```mermaid
stateDiagram-v2
    [*] --> 待审核: 提交申请
    待审核 --> 待打款: 审核通过
    待审核 --> 无效: 审核拒绝
    待打款 --> 已付款: 打款成功
    待打款 --> 无效: 打款失败
```

---

#### **3. 企业家分佣模块详解**
**3.1 分佣规则设计**
```mermaid
flowchart LR
  A[商家设置店铺分佣比例] --> B(范围1%-20%)
  B --> C[勾选参与分佣的商品]
  C --> D[生成推广链接]
  D --> E[分佣计算=订单金额×店铺比例]
```

**3.2 关键特性**  
- **全店统一比例**：所有参与分佣的商品共用同一比例，商家后台集中管理  
- **商品级参与控制**：可随时调整参与分佣的商品清单，实时生效  
- **分佣透明度**：订单详情页展示分佣计算明细（如：`¥100×10%=¥10`）

**3.3 数据展示优化**  
| **指标**           | **说明**                                  | **计算逻辑**                              |
|--------------------|------------------------------------------|------------------------------------------|
| 分佣商品贡献榜     | 参与分佣商品的销量与分佣金额排名          | 分佣金额=商品销售额×店铺分佣比例          |
| 企业家推广效率     | 每位企业家的订单转化率（点击→成交）       | 结合推广链接的UV-订单转化数据             |

---

#### **4. 交互与权限控制**
1. **分佣比例修改流程**  
   ```mermaid
   sequenceDiagram
     商家->>系统: 请求修改分佣比例
     系统->>企业家: 推送确认通知
     企业家-->>系统: 同意/拒绝
     系统->>商家: 返回结果并生效
   ```

2. **权限分级**  
   - **店主**：可修改分佣比例、管理参与商品  
   - **运营**：仅查看分佣数据，不可修改比例  
   - **财务**：导出分佣结算报表  

---

#### **5. 异常处理与风控**
| **场景**                 | **解决方案**                                                                 |
|--------------------------|-----------------------------------------------------------------------------|
| 分佣比例争议             | 提供历史修改日志，记录操作人与时间                                          |
| 突发大额分佣订单         | 单日分佣金额超过均值200%时，自动触发人工审核                                |
| 商品退出分佣             | 已生成链接失效，但历史订单正常结算                                          |

---

#### **6. 可视化示例**
**企业家分佣看板**  
```mermaid
pie
    title 分佣商品占比
    "服饰类" : 45
    "食品类" : 30
    "日用品" : 25
```

**分佣状态追踪**  
```mermaid
gantt
    title 分佣结算周期
    dateFormat  YYYY-MM-DD
    section 订单流程
    待结算      :a1, 2023-11-01, 2d
    已结算      :a2, after a1, 1d
    section 资金状态
    冻结        :crit, 2023-11-01, 3d
    可提现      :active, 2023-11-04, 1d
```
**资金流动看板**  
```mermaid
pie
    title 当月资金构成
    "商品收入" : 65
    "分佣结算" : 20
    "其他" : 15
```

**提现审核流程**  
```mermaid
flowchart TB
  A[商家申请提现] --> B{金额≤余额?}
  B -->|是| C[生成待审核记录]
  B -->|否| D[提示余额不足]
  C --> E[管理员审核]
```