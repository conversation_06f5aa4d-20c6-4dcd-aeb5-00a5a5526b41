# 店铺商品返回最少可获得分享佣金金额字段说明

## 功能概述

在店铺商品接口中新增返回"最少可获得分享佣金金额"字段`minShareCommission`，该字段根据最低商品规格的销售价格和商品分享佣金比例计算得出，用于前端展示商品分享可获得的最少佣金金额。

## 接口变更

### 1. 影响的接口

以下接口将返回`minShareCommission`字段：

- 商品列表相关接口：
  - `/front/goodList/findGoodListByGoodType`（根据类型查询商品列表）
  - `/front/goodList/searchGoodList`（搜索商品列表）
  - `/front/goodList/findGoodListBySysUserId`（根据店铺ID查询商品）
  - 其他使用`goodInfo` SQL片段的接口

- 商品详情接口：
  - `/front/goodList/findGoodListByGoodId`（根据商品ID获取商品详情）

### 2. 返回字段说明

新增字段`minShareCommission`：
- 类型：数值型（保留2位小数）
- 含义：最少可获得的分享佣金金额
- 计算方式：最低商品规格销售价格 × 商品分享佣金比例 ÷ 100

## 相关实现变更

### 1. 商品列表接口

在`GoodStoreListMapper.xml`的`goodInfo` SQL片段中添加了计算`minShareCommission`的代码：

```xml
<sql id="goodInfo">
    gsl.id AS id,
    gsl.main_picture AS mainPicture,
    gsl.share_picture as sharePicture,
    0 AS isPlatform,
    gsl.good_name AS goodName,
    gsl.sys_user_id as  sysUserId,
    gsl.share_commission_rate as shareCommissionRate,
    ( SELECT price FROM good_store_specification WHERE good_store_list_id = gsl.id AND del_flag = '0' ORDER BY price ASC LIMIT 1 ) AS smallPrice,
    ( SELECT vip_price FROM good_store_specification WHERE good_store_list_id = gsl.id AND del_flag = '0' ORDER BY price ASC LIMIT 1 ) AS smallVipPrice,
    ( SELECT SUM( sales_volume ) FROM good_store_specification WHERE good_store_list_id = gsl.id AND del_flag = '0' ) AS salesVolume,
    gsl.market_price AS marketPrice,
    gsl.total_performance as salesPerformance,
    ROUND(( SELECT price FROM good_store_specification WHERE good_store_list_id = gsl.id AND del_flag = '0' ORDER BY price ASC LIMIT 1 ) * gsl.share_commission_rate / 100, 2) AS minShareCommission,
    ...
</sql>
```

### 2. 商品详情接口

在`findGoodListByGoodId`方法中添加了`minShareCommission`字段的计算：

```xml
<select id="findGoodListByGoodId" resultType="map">
    SELECT
        id AS id,
        main_picture AS mainPicture,
        share_picture as sharePicture,
        0 AS isPlatform,
        good_name AS goodName,
        ( SELECT price FROM good_store_specification WHERE good_store_list_id = gsl.id AND del_flag = '0' ORDER BY price ASC LIMIT 1 ) AS smallPrice,
        good_describe AS goodDescribe,
        market_price AS marketPrice,
        ( SELECT vip_price FROM good_store_specification WHERE good_store_list_id = gsl.id AND del_flag = '0' ORDER BY price ASC LIMIT 1 ) AS smallVipPrice,
        specification AS specification,
        good_video AS goodVideo,
        details_goods AS detailsGoods,
        ( SELECT SUM( repertory ) FROM good_store_specification WHERE good_store_list_id = gsl.id AND del_flag = '0' ) AS repertory,
        frame_status AS frameStatus,
        share_commission_rate AS shareCommissionRate,
        ROUND(( SELECT price FROM good_store_specification WHERE good_store_list_id = gsl.id AND del_flag = '0' ORDER BY price ASC LIMIT 1 ) * share_commission_rate / 100, 2) AS minShareCommission,
        ...
</select>
```

## 使用说明

前端在调用商品列表或商品详情接口时，可以从返回数据中获取`minShareCommission`字段，用于展示商品分享可获得的最少佣金金额。

## 示例

### 商品列表接口返回示例

```json
{
  "success": true,
  "message": "操作成功！",
  "code": 200,
  "result": {
    "records": [
      {
        "id": "**********",
        "mainPicture": "http://example.com/image1.jpg",
        "sharePicture": "http://example.com/share1.jpg",
        "isPlatform": 0,
        "goodName": "测试商品1",
        "sysUserId": "store123",
        "shareCommissionRate": 5.00,
        "smallPrice": 100.00,
        "smallVipPrice": 90.00,
        "salesVolume": 50,
        "marketPrice": 120.00,
        "salesPerformance": 5000.00,
        "minShareCommission": 5.00,
        "storeName": "测试店铺(分店)"
      },
      {
        "id": "**********",
        "mainPicture": "http://example.com/image2.jpg",
        "sharePicture": "http://example.com/share2.jpg",
        "isPlatform": 0,
        "goodName": "测试商品2",
        "sysUserId": "store456",
        "shareCommissionRate": 10.00,
        "smallPrice": 200.00,
        "smallVipPrice": 180.00,
        "salesVolume": 30,
        "marketPrice": 240.00,
        "salesPerformance": 6000.00,
        "minShareCommission": 20.00,
        "storeName": "测试店铺2(分店)"
      }
    ],
    "total": 2,
    "size": 10,
    "current": 1,
    "orders": [],
    "optimizeCountSql": true,
    "hitCount": false,
    "searchCount": true,
    "pages": 1
  },
  "timestamp": 1683000000000
}
```

### 商品详情接口返回示例

```json
{
  "success": true,
  "message": "操作成功！",
  "code": 200,
  "result": {
    "goodinfo": {
      "id": "**********",
      "mainPicture": "http://example.com/image1.jpg",
      "sharePicture": "http://example.com/share1.jpg",
      "isPlatform": 0,
      "goodName": "测试商品1",
      "smallPrice": 100.00,
      "goodDescribe": "这是一个测试商品",
      "marketPrice": 120.00,
      "smallVipPrice": 90.00,
      "specification": "规格1,规格2",
      "goodVideo": "http://example.com/video.mp4",
      "detailsGoods": "<p>商品详情</p>",
      "repertory": 100,
      "frameStatus": "1",
      "shareCommissionRate": 5.00,
      "minShareCommission": 5.00,
      "goodTypeId": "type123",
      "specifications": [
        {
          "id": "spec123",
          "price": 100.00,
          "vipPrice": 90.00,
          "specification": "规格1",
          "repertory": 50
        },
        {
          "id": "spec456",
          "price": 110.00,
          "vipPrice": 99.00,
          "specification": "规格2",
          "repertory": 50
        }
      ]
    }
  },
  "timestamp": 1683000000000
}
```

### 前端使用示例

前端可以根据`minShareCommission`字段的值展示商品分享可获得的最少佣金金额：

```javascript
// 商品列表中展示最少可获得的分享佣金金额
function renderGoodsList(goodsList) {
  return goodsList.map(good => `
    <div class="good-item">
      <img src="${good.mainPicture}" alt="${good.goodName}">
      <h3>${good.goodName}</h3>
      <div class="price">¥${good.smallPrice}</div>
      <div class="commission">分享佣金: ¥${good.minShareCommission}</div>
    </div>
  `).join('');
}

// 商品详情中展示最少可获得的分享佣金金额
function renderGoodDetail(good) {
  return `
    <div class="good-detail">
      <img src="${good.mainPicture}" alt="${good.goodName}">
      <h2>${good.goodName}</h2>
      <div class="price">¥${good.smallPrice}</div>
      <div class="commission">分享佣金: ¥${good.minShareCommission}</div>
      <div class="description">${good.goodDescribe}</div>
    </div>
  `;
}
```

## 注意事项

1. `minShareCommission`字段的计算基于最低商品规格的销售价格，如果商品没有规格或者规格价格为0，则该字段可能为0或null
2. 前端在使用时需要做好空值处理
3. 分享佣金比例`shareCommissionRate`是百分比值，计算时需要除以100
