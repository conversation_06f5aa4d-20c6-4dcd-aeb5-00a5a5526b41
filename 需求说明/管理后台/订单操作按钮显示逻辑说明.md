# 八闽助业集市平台 - 订单操作按钮显示逻辑说明

## 1. 概述

本文档详细说明「全部订单」页面中订单操作按钮的显示逻辑，包括不同订单状态下可执行的操作及其业务规则。

## 2. 订单状态定义

系统中订单状态定义如下：

| 状态值 | 状态名称 | 说明 |
|-------|---------|------|
| 0 | 待付款 | 订单已创建但买家尚未支付 |
| 1 | 待发货 | 买家已付款，等待商家发货 |
| 2 | 待收货 | 商家已发货，等待买家确认收货 |
| 3 | 交易成功 | 买家已确认收货，交易完成 |
| 4 | 交易关闭 | 订单已取消或交易失败 |
| 5 | 交易完成 | 交易成功且售后期已过 |

## 3. 配送方式定义

系统支持多种配送方式：

| 配送方式值 | 配送方式名称 | 说明 |
|-----------|------------|------|
| 0 | 快递 | 通过物流公司配送 |
| 1 | 自提（核销） | 买家到店自提，需要核销码 |
| 2 | 配送 | 商家自行配送 |

## 4. 订单操作按钮显示逻辑

### 4.1 待付款订单 (status == 0)

显示的操作按钮：
- **取消**：取消未支付的订单
- **修改地址**：修改收货地址信息
- **详情**：查看订单详细信息

```javascript
<div v-if="record.status == '0'">
  <a @click="showModalCancelInformation(record.id)">取消</a>
  <a-divider type="vertical" />
  <a @click="showModalAddressInformation(...)">修改地址</a>
  <a-divider type="vertical" />
  <a @click="particulars(record, 2)">详情</a>
</div>
```

### 4.2 待发货订单 (status == 1)

显示的操作按钮：
- **取消并退款**：取消订单并将款项退回给买家
- **发货**：商家发货操作
- **详情**：查看订单详细信息
- **查看物流**：查看物流信息

```javascript
<div v-if="record.status == '1'">
  <a @click="refundAndAbrogateOrderStoreClick(record.id)">取消并退款</a>
  <a-divider type="vertical" />
  <a @click="deliverGoods(record)">发货</a>
  <a-divider type="vertical" />
  <a @click="particulars(record, 3)">详情</a>
  <a-divider type="vertical" />
  <a @click="checkLogistics(record, 3)">查看物流</a>
</div>
```

### 4.3 待收货订单 (status == 2)

显示的操作按钮：
- **取消并退款**：取消订单并将款项退回给买家
- **查看物流**：查看物流信息
- **详情**：查看订单详细信息

对于核销订单（record.distribution == 1），在详情页会显示：
- 核销状态（未核销、已核销、已失效）
- 核销码
- 查看核销码按钮（仅当状态为未核销时显示）

```javascript
<div v-if="record.status == '2'">
  <a @click="refundAndAbrogateOrderStoreClick(record.id)">取消并退款</a>
  <a-divider type="vertical" />
  <a @click="checkLogistics(record, 4)">查看物流</a>
  <a-divider type="vertical" />
  <a @click="particulars(record, 4)">详情</a>
</div>
```

### 4.4 交易成功订单 (status == 3)

显示的操作按钮：
- **取消并退款**：取消订单并将款项退回给买家
- **审批**：审批评价（仅当有评价时显示）
- **查看评价**：查看买家评价（仅当有评价时显示）
- **查看物流**：查看物流信息
- **详情**：查看订单详细信息

```javascript
<div v-if="record.status == '3'">
  <a @click="refundAndAbrogateOrderStoreClick(record.id)">取消并退款</a>
  <a-divider type="vertical" />
  <a @click="showAudiModal(record.id)" v-if="record.isEvaluate == 1">审批</a>
  <a-divider type="vertical" v-if="record.isEvaluate == 1" />
  <a @click="showEvaluateModal(record.id)" v-if="record.isEvaluate == 1">查看评价</a>
  <a-divider type="vertical" v-if="record.isEvaluate == 1" />
  <a @click="checkLogistics(record, 5)">查看物流</a>
  <a-divider type="vertical" />
  <a @click="particulars(record, 5)">详情</a>
</div>
```

### 4.5 交易关闭订单 (status == 4)

显示的操作按钮：
- **详情**：查看订单详细信息
- **删除**：删除订单记录

```javascript
<div v-if="record.status == '4'">
  <a @click="particulars(record, 1)">详情</a>
  <a-divider type="vertical" />
  <a @click="showModalDeletess(record.id)">删除</a>
</div>
```

### 4.6 交易完成订单 (status == 5)

显示的操作按钮：
- **取消并退款**：取消订单并将款项退回给买家
- **审批**：审批评价（仅当有评价时显示）
- **查看评价**：查看买家评价（仅当有评价时显示）
- **查看物流**：查看物流信息
- **详情**：查看订单详细信息

```javascript
<div v-if="record.status == '5'">
  <a @click="refundAndAbrogateOrderStoreClick(record.id)">取消并退款</a>
  <a-divider type="vertical" />
  <a @click="showAudiModal(record.id)" v-if="record.isEvaluate == 1">审批</a>
  <a-divider type="vertical" v-if="record.isEvaluate == 1" />
  <a @click="showEvaluateModal(record.id)" v-if="record.isEvaluate == 1">查看评价</a>
  <a-divider type="vertical" v-if="record.isEvaluate == 1" />
  <a @click="checkLogistics(record, 5)">查看物流</a>
  <a-divider type="vertical" />
  <a @click="particulars(record, 5)">详情</a>
</div>
```

## 5. 核销订单特殊处理

对于核销订单（配送方式 distribution == 1），系统有特殊处理：

1. 在订单详情页面，当订单状态为待收货（status == 2）且配送方式为自提（distribution == 1）时，会显示核销相关信息：
   - 核销状态（pickUpVerificationStatus）：
     - 0：未核销
     - 1：已核销
     - 2：已失效
   - 核销码（pickUpQrCode）
   - 查看核销码按钮（仅当状态为未核销时显示）

2. 点击"查看核销码"按钮会显示二维码，买家可以将此二维码出示给店员扫码核销。

```javascript
<!-- 核销订单 -->
<div v-if="reusePage == 4 && record && record.distribution == 1">
  <div style="float: left">
    <div class="cnt-title">
      待核销
    </div>
    <div>
      <div class="self-pickup-status">
        <span class="status-label">核销状态：</span>
        <span class="status-value" v-if="record.pickUpVerificationStatus == '0'">
          <a-tag color="orange">未核销</a-tag>
        </span>
        <span class="status-value" v-else-if="record.pickUpVerificationStatus == '1'">
          <a-tag color="green">已核销</a-tag>
        </span>
        <span class="status-value" v-else-if="record.pickUpVerificationStatus == '2'">
          <a-tag color="red">已失效</a-tag>
        </span>
        <span class="status-value" v-else>
          <a-tag color="blue">未知状态</a-tag>
        </span>
      </div>
      <div class="self-pickup-code" v-if="record.pickUpQrCode">
        <span class="code-label">核销码：</span>
        <span class="code-value">{{ record.pickUpQrCode }}</span>
      </div>
    </div>
  </div>
  <div style="float: right">
    <a-button
      type="primary"
      v-if="record.pickUpQrAddr && record.pickUpVerificationStatus == '0'"
      @click="showQrCode(record.pickUpQrAddr)">
      查看核销码
    </a-button>
  </div>
</div>
```

## 6. 操作方法说明

### 6.1 取消订单
- 方法：`showModalCancelInformation(id)`
- 说明：取消未支付的订单

### 6.2 修改地址
- 方法：`showModalAddressInformation(goodListId, memberListId, consignee, contactNumber, shippingAddress)`
- 说明：修改订单收货地址信息

### 6.3 查看详情
- 方法：`particulars(record, reusePage)`
- 说明：查看订单详细信息，reusePage参数表示不同状态的订单页面
  - reusePage=1：已取消订单
  - reusePage=2：待付款订单
  - reusePage=3：待发货订单
  - reusePage=4：待收货订单
  - reusePage=5：交易成功/完成订单

### 6.4 发货
- 方法：`deliverGoods(record)`
- 说明：商家发货操作，跳转到发货页面

### 6.5 查看物流
- 方法：`checkLogistics(record, reusePage)`
- 说明：查看订单物流信息

### 6.6 取消并退款
- 方法：`refundAndAbrogateOrderStoreClick(id)`
- 说明：取消订单并将款项退回给买家

### 6.7 审批评价
- 方法：`showAudiModal(id)`
- 说明：审批买家评价

### 6.8 查看评价
- 方法：`showEvaluateModal(id)`
- 说明：查看买家评价内容

### 6.9 删除订单
- 方法：`showModalDeletess(id)`
- 说明：删除已关闭的订单记录

### 6.10 查看核销码
- 方法：`showQrCode(pickUpQrAddr)`
- 说明：显示核销二维码，供买家到店出示给店员扫码核销

## 7. 注意事项

1. 不同状态的订单显示不同的操作按钮，确保用户只能执行当前状态下允许的操作。

2. 对于核销订单，需要特别关注核销状态和核销码的显示逻辑。

3. 评价相关的操作（审批、查看评价）仅在订单有评价时（isEvaluate == 1）才显示。

4. 删除操作仅适用于已关闭的订单，且删除后不可恢复，应谨慎操作。

5. 发货操作提交后无法修改，系统会提示确认信息的正确性。