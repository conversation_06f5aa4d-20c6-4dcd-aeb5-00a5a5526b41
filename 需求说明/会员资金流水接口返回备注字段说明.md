# 会员资金流水接口返回备注字段说明

## 功能概述

在会员资金流水接口`memberAccountCapital/memberAccountCapital/queryPageListByMemberId`中新增返回"备注"字段，用于展示系统补发和系统扣减操作时记录的备注信息。

## 接口变更

### 1. 接口路径

`memberAccountCapital/memberAccountCapital/queryPageListByMemberId`

### 2. 返回字段变更

在接口返回的数据中新增了`remarks`字段，用于展示备注信息。

### 3. 相关实现变更

1. 在`MemberAccountCapitalDTO`类中添加了`remarks`字段：
   ```java
   /**
    * 备注
    */
   @ApiModelProperty(value = "备注")
   private String remarks;
   ```

2. 在`MemberAccountCapitalMapper.xml`的`getMemberAccountCapitalList`方法中添加了`remarks`字段的查询：
   ```xml
   SELECT mac.id,
          mac.member_list_id,
          mac.pay_type,
          mac.go_and_come,
          mac.amount,
          mac.order_no,
          mac.balance,
          ml.phone     AS phone,
          ml.nick_name AS nickName,
          mac.create_time,
          mac.trade_no AS tradeNo,
          mac.sys_user_id AS sysUserId,
          mac.remarks AS remarks
   FROM
   ...
   ```

3. 在`findAccountCapitalByMemberId`方法中也添加了`remarks`字段的查询：
   ```xml
   SELECT ...
          mac.order_no                            as orderNo,
          mac.remarks                             as remarks
   FROM member_account_capital mac
   ...
   ```

4. 在`findMemberAccountCapitalByMemberId`方法中也添加了`remarks`字段的查询：
   ```xml
   SELECT amount                                     AS amount,
          go_and_come                                AS goAndCome,
          pay_type                                   AS payType,
          DATE_FORMAT(create_time, '%Y-%m-%d %H:%i') AS createTime,
          remarks                                    AS remarks
   FROM `member_account_capital`
   ...
   ```

## 使用说明

1. 前端在调用`memberAccountCapital/memberAccountCapital/queryPageListByMemberId`接口获取会员资金流水列表时，可以从返回数据中获取`remarks`字段
2. 可以在列表中展示备注信息，方便用户了解系统补发和系统扣减操作的原因

## 示例

### 接口返回示例

```json
{
  "success": true,
  "message": "操作成功！",
  "code": 200,
  "result": {
    "records": [
      {
        "id": "**********",
        "memberListId": "abcdefg",
        "payType": "24",
        "goAndCome": "0",
        "amount": 100.00,
        "orderNo": "ORDER123456",
        "balance": 500.00,
        "phone": "***********",
        "nickName": "测试用户",
        "createTime": "2023-05-01 10:00:00",
        "tradeNo": "TRADE123456",
        "sysUserId": "STORE001",
        "remarks": "系统补发测试"
      }
    ],
    "total": 1,
    "size": 10,
    "current": 1,
    "orders": [],
    "optimizeCountSql": true,
    "hitCount": false,
    "searchCount": true,
    "pages": 1
  },
  "timestamp": 1683000000000
}
```

### 前端展示示例

在资金流水列表中，可以添加一列"备注"，展示`remarks`字段的内容。

## 注意事项

1. 只有通过系统补发和系统扣减接口操作的资金流水才会有备注信息
2. 如果`remarks`字段为空，前端可以不显示或显示为"无"
