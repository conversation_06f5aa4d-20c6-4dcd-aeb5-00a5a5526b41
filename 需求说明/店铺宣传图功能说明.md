# 店铺宣传图功能说明

## 功能概述

在店铺管理模块中，新增了"店铺宣传图"字段，支持存储多张图片，用于展示店铺的宣传图片。

## 数据库变更

在`store_manage`表中新增了`store_propaganda_images`字段，类型为`text`，用于存储店铺宣传图片的路径。

```sql
ALTER TABLE `store_manage` ADD COLUMN `store_propaganda_images` text COMMENT '店铺宣传图';
```

## 实体类变更

在`StoreManage`实体类中新增了`storePropagandaImages`字段：

```java
/**
 * 店铺宣传图
 */
@Excel(name = "店铺宣传图", width = 15)
@ApiModelProperty(value = "店铺宣传图")
private String storePropagandaImages;
```

## 使用说明

### 图片上传

1. 店铺宣传图支持上传多张图片
2. 上传的图片路径将以逗号分隔的字符串形式存储在`storePropagandaImages`字段中
3. 可以使用系统提供的文件上传接口进行图片上传

### 前端实现建议

1. 在店铺新增和编辑页面中，添加图片上传组件，支持多图片上传
2. 上传成功后，将图片路径以逗号分隔的字符串形式提交到后端
3. 在展示时，将字符串分割为数组，遍历展示多张图片

### 示例代码

#### 前端上传组件示例

```vue
<j-upload
  v-model="form.storePropagandaImages"
  :fileMax="9"
  :multiple="true"
  @change="handleUploadChange"
></j-upload>
```

#### 后端处理示例

```java
// 获取店铺宣传图
String storePropagandaImages = storeManage.getStorePropagandaImages();

// 分割图片路径
if (StringUtils.isNotBlank(storePropagandaImages)) {
    String[] imageArray = storePropagandaImages.split(",");
    // 处理图片数组...
}
```

## 注意事项

1. 上传图片时，建议控制图片大小和格式，推荐使用JPG、PNG格式
2. 建议限制上传图片的数量，避免数据过大影响性能
3. 在展示时，可以使用轮播图或图片列表的形式展示多张宣传图
