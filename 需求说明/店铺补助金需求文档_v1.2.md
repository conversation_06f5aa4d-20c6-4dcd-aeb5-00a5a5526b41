# 店铺补助金功能模块需求说明文档 (最终确认版)

**版本**: 1.2  
**日期**: 2025-05-22 (最终确认)

## 1.0 引言

### 1.1 功能概述
店铺补助金（以下简称"补助金"）是平台为提升用户活跃度、促进特定店铺销售而设计的一种营销工具。补助金以特定金额形式发放给会员，具有指定店铺使用、指定有效期、可部分消耗等特点。用户在指定店铺消费时，可使用补助金抵扣订单金额。

### 1.2 设计目标
1. 为平台提供一种灵活的定向营销手段。
2. 激励用户在特定店铺进行消费。
3. 提升用户体验，增加用户粘性。

### 1.3 核心原则
1. **特定资金类型**：补助金作为一种特殊的资金记录（通过 `pay_type` 区分）存储在会员资金流水表中，其可用金额通过 `remaining_amount` 字段追踪。它不直接增加用户的通用账户余额 (`member_account_capital` 表中其他流水的 `balance` 累计值所代表的通用余额)。
2. **清晰展示**：用户端需清晰、准确地展示可用补助金情况及其使用规则。
3. **优先抵扣**：在符合条件的订单支付时，优先消耗适用的补助金的 `remaining_amount`。

## 2.0 功能范围

### 2.1 核心功能
1. 管理后台：补助金发放（即创建特定类型的资金流水记录）、会员补助金查询。
2. 小程序用户端：补助金查看、购物车/结算页补助金展示与自动抵扣、订单中使用补助金。
3. 系统逻辑：补助金 `remaining_amount` 核销、有效期管理、自动过期处理、并发控制。

### 2.2 暂不包含范围
1. 用户间补助金转赠。
2. 补助金的 `remaining_amount` 直接提现。
3. 除指定店铺外的其他任何场景使用补助金。
4. 补助金使用门槛（例如：满X元可用）。

## 3.0 功能需求详述

### 3.1 管理后台 (Admin Portal)

#### 3.1.1 店铺补助金发放管理
1. **发放操作** (即创建新的 `member_account_capital` 记录)：
    - **`member_list_id` (会员ID)**：指定接收补助金的会员。
    - **`pay_type` (交易类型)**：固定为"店铺补助金发放"（例如，后台设定值为 '50'）。
    - **`go_and_come` (收入/支出)**：对于发放补助金，应为"收入"（'0'）。
    - **`subsidy_store_id` (补助店铺)**：选择补助金适用的唯一店铺ID。
    - **`expire_time` (过期时间)**：设置该笔补助金的失效日期和时间。
    - **`amount` (交易额/初始面额)**：输入发放的补助金总面额（例如：300.00）。
    - **`remaining_amount` (剩余可用金额)**：发放时，此值等于 `amount`。
    - **`store_subsidy_status` (店铺补助金状态)**：发放时，默认为"可用"（'0'）。
    - **`balance` (账户余额)**：此字段在此类补助金发放记录中，**不应**包含此次发放的补助金金额。其值应为发放操作发生**前**该会员的通用账户余额，或发放操作发生**后**该会员的通用账户余额（通用余额本身不因补助金发放而增加）。
    - **`remarks` (备注)**：可填写发放原因或活动说明。

2. **发放记录查看**：
    - 管理员应能查询所有 `pay_type` 为"店铺补助金发放"的记录。

#### 3.1.2 会员补助金明细查询
1. 管理员可根据会员ID/手机号等，筛选 `member_account_capital` 表中 `pay_type` 为"店铺补助金发放"的记录。
2. 列表展示：会员信息、补助金ID (`id` of the capital record)、适用店铺名称 (通过 `subsidy_store_id` 关联查询)、初始金额 (`amount`)、剩余可用金额 (`remaining_amount`)、过期时间 (`expire_time`)、状态 (`store_subsidy_status`)、发放时间 (`create_time`)。

### 3.2 小程序用户端 (Mini-Program User End)

#### 3.2.1 补助金信息展示 (采用方案A)
1. **个人中心/我的钱包页面**：
    - 通用账户余额的展示（通常来自会员主表或最新的非补助金类资金流水记录的 `balance` 字段）不包含任何店铺补助金的 `remaining_amount`。
    - 在该余额下方或单独模块，增加"店铺补助金"入口/摘要信息：
        - 文字提示如："可用店铺补助金总额 ¥X"。（X 为该用户所有 `pay_type`='50', `store_subsidy_status`='0', `expire_time` > now, 且 `remaining_amount` > 0 的补助金记录的 `remaining_amount` 之和）。
        - 点击该区域可跳转至"我的店铺补助金"列表页面。

2. **"我的店铺补助金"列表页面**：
    - 顶部Tab切换："可用"、"已用尽"、"已过期"。
    - **"可用"列表** (指 `pay_type`='50', `store_subsidy_status`='0', `expire_time` > now, `remaining_amount` > 0 的记录)：
        - 卡片式展示每条补助金记录。
        - 卡片信息：适用店铺名称、**剩余可用金额 ( `remaining_amount` )**、有效期（`expire_time`）、"去店铺使用"按钮（点击跳转至对应店铺首页）。
        - 按有效期远近排序（优先展示即将过期的）。
    - **"已用尽"列表** (指 `pay_type`='50', `store_subsidy_status`='2', `expire_time` > now, `remaining_amount` = 0 的记录)：
        - 卡片信息：适用店铺名称、初始金额 (`amount`)、发放时间、用尽时间 (可从关联的消费流水获取)。
    - **"已过期"列表** (指 `pay_type`='50', `store_subsidy_status`='1' 的记录)：
        - 卡片信息：适用店铺名称、初始金额 (`amount`)、发放时的剩余/已用金额、原有效期。

3. **特定店铺首页/商品详情页**：
    - 当用户进入某店铺首页或该店铺的商品详情页时，若用户持有该店铺可用的补助金，应在页面显著位置进行提示。
    - 提示信息："您在本店有 ¥X 补助金可用，有效期至 YYYY-MM-DD"。（X 为该用户在该店铺所有 `pay_type`='50', `store_subsidy_status`='0', `expire_time` > now, 且 `remaining_amount` > 0 的补助金记录的 `remaining_amount` 之和）。

#### 3.2.2 购物车与结算页面补助金抵扣 (采用多店铺妥协方案1)
1. **购物车页面**：
    - 按原逻辑展示商品。
2. **订单确认/结算页面**：
    - **商品列表按店铺分组显示**。
    - 在每个店铺的商品分组下方，单独计算该店铺的费用与抵扣：
        - "【XX店铺】商品小计：¥A"
        - "可用【XX店铺】补助金：- ¥B" (系统自动选择该店铺下 `pay_type`='50', `store_subsidy_status`='0', `expire_time` > now, `remaining_amount` > 0 的补助金记录，按**优先消耗最早过期 (`expire_time` ASC)**的策略，累计抵扣。B为实际抵扣金额，B ≤ A，且B ≤ 该店铺可用补助金的`remaining_amount`总和)。
        - "该店铺补助金后支付：¥C (C = A - B)"
    - 若用户在该店铺无可用补助金，则不显示"可用【XX店铺】补助金"行。
    - **页面底部费用汇总区域**：
        - "商品总金额：¥Total_Original"
        - "店铺补助金抵扣总额：- ¥Total_Subsidy_Applied"
        - "账户余额支付：- ¥Balance_Used"
        - "仍需支付/在线支付：¥Final_Payable"
        - "您的账户余额：¥User_Current_Balance" (指通用账户余额)
    - 清晰显示最终支付构成。

#### 3.2.3 支付逻辑
1. **判断订单适用补助金**：
    - 遍历订单中每个店铺的商品。
    - 查询用户持有该店铺所有 `pay_type`='50', `store_subsidy_status`='0', `expire_time` > now, 且 `remaining_amount` > 0 的补助金记录。
    - **消耗策略**：若有多条可用补助金记录，**优先消耗 `expire_time` 最早的记录的 `remaining_amount`**。如果最早过期的用完仍可抵扣（即该店铺子订单金额未完全被抵扣完），则继续消耗次早过期的，以此类推，直至抵扣完该店铺子订单金额或所有可用补助金耗尽。
2. **扣款顺序**：
    - **店铺补助金**：针对每个店铺的子订单金额，按上述消耗策略扣减对应补助金发放记录的 `remaining_amount`。同时，在 `member_account_capital` 表中为会员生成一条对应的**支出型**流水记录（详见3.3.2）。
    - **通用账户余额**：抵扣完所有适用店铺补助金后，若订单仍有未支付金额，则从用户通用账户余额中扣除（同样需生成相应资金流水）。
    - **其他支付方式**（如微信支付）：剩余部分由用户选择其他支付方式。
3. **补助金状态更新**：支付成功后，对于被消耗的补助金发放记录：
    - 更新其 `remaining_amount`。
    - 如果 `remaining_amount` 变为 0，将其 `store_subsidy_status` 更新为 '2' (已用尽)。

#### 3.2.4 订单详情页面
- 清晰展示该订单实际使用的店铺补助金抵扣金额。可考虑展示关联的补助金消耗流水记录ID或原补助金发放记录ID。

#### 3.2.5 订单取消/售后退款
1. **补助金处理规则**：
    - 当订单发生退款（用户实际支付部分退回，包括通用余额和在线支付部分）时，其原先使用的店铺补助金部分：
        - **按相应抵扣金额恢复（增加）到原补助金发放记录的 `remaining_amount` 中**。
        - **条件**：原补助金发放记录本身未过期 (`expire_time` > now) 且其 `store_subsidy_status` 非 '1' (已过期)。
        - 如果恢复后 `remaining_amount` > 0 且原补助金发放记录的 `store_subsidy_status` 为 '2' (已用尽)，则将其状态改回 '0' (可用)。
        - 需生成一条对应的补助金"退回"或"恢复"的资金流水记录（收入型，`pay_type`可设为'52'-店铺补助金退回/恢复）。
        - 之前生成的"店铺补助金消费/抵扣" (`pay_type`='51') 流水记录，在逻辑上被此"退回/恢复"流水对冲。

### 3.3 系统逻辑

#### 3.3.1 补助金数据管理
1. 所有补助金相关操作均在 `member_account_capital` 表中进行，通过 `pay_type`, `subsidy_store_id`, `expire_time`, `remaining_amount`, `store_subsidy_status` 等字段进行识别和管理。
2. **`balance` 字段隔离**：`member_account_capital` 表中的 `balance` 字段，在记录类型为"店铺补助金发放" (`pay_type`='50', `go_and_come`='0') 时，其 `balance` 字段的值**不应**将此笔补助金的 `amount` 计入用户的通用可支配余额的累计值。该 `balance` 字段应反映此交易发生**前**用户的通用余额，或者此笔补助金发放交易发生**后**用户的通用余额（但通用余额本身不因补助金发放而增加）。

#### 3.3.2 补助金消耗流水记录
1. 当补助金在订单中被使用抵扣时，在 `member_account_capital` 表中为会员生成一条独立的**支出型**流水记录：
    - `pay_type`：'51' (店铺补助金消费/抵扣)。
    - `go_and_come`：'1' (支出)。
    - `amount`：本次实际抵扣的补助金金额。
    - `order_no`：关联的订单号。
    - `subsidy_store_id`：对应的店铺ID。
    - `remarks`：可备注"订单使用XX店铺补助金抵扣，原补助金记录ID: [原发放记录id]"。
    - `balance`：此流水发生**后**用户的通用账户余额（通用余额未因此次补助金抵扣而改变）。
    - `trade_no`：新的交易流水号。

#### 3.3.3 自动过期处理
1. 定时任务（例如每日凌晨执行）。
2. 扫描 `member_account_capital` 表中所有 `pay_type`='50', (`store_subsidy_status`='0' (可用) OR `store_subsidy_status`='2' (已用尽，但仍需检查是否过期，以防状态未及时更新或有特殊逻辑)), 且 `expire_time` < 当前时间的记录。主要针对`store_subsidy_status`='0'的记录。
3. 将这些记录的 `store_subsidy_status` 更新为 '1' (已过期)。

#### 3.3.4 并发消耗控制
1. 在对补助金记录的 `remaining_amount` 进行扣减操作时，必须考虑并发场景，确保数据一致性。
2. **建议方案**：在 `member_account_capital` 表中增加一个 `version` 字段（乐观锁版本号，数据类型为int或bigint，默认值为0或1）。每次更新 `remaining_amount` 时，`UPDATE`语句的`WHERE` 条件需包含预期的 `version` 值（即读取出来的值），并在 `SET` 子句中将 `version` 加1。若更新影响行数为0，则表示发生并发冲突，需进行重试或提示用户稍后操作。
3. 例如: `UPDATE member_account_capital SET remaining_amount = remaining_amount - X, version = version + 1 WHERE id = 'subsidy_id' AND version = 'current_version_read_from_db' AND remaining_amount >= X;`

## 4.0 用户界面 (UI) 与用户体验 (UX) 注意事项

1. **清晰明确**：所有关于补助金的金额、适用店铺、有效期等信息必须清晰、无歧义地展示给用户。
2. **操作引导**：在适当的场景（如店铺页、结算页）主动提示用户可用补助金，并引导其使用。
3. **状态透明**：用户能方便地查看补助金的可用、已用尽、已过期状态。
4. **及时反馈**：补助金的使用、过期等状态变更应有及时反馈。
5. **购物车/结算页的易用性**：即使多店铺信息展示略复杂，也应力求布局合理，让用户能快速理解费用构成和补助金抵扣情况。

## 5.0 数据模型 (`member_account_capital` 表关键补充)

1. `pay_type` 数据字典新增：
    - '50': 店铺补助金发放 (收入型)
    - '51': 店铺补助金消费/抵扣 (支出型，对应原补助金记录的`remaining_amount`减少)
    - '52': 店铺补助金退回/恢复 (收入型，对应原补助金记录的`remaining_amount` )
2. `store_subsidy_status` 数据字典 (仅对`pay_type`='50'的记录有效)：
    - '0': 可用 (`remaining_amount` > 0 且未过期)
    - '1': 已过期
    - '2': 已用尽 (`remaining_amount` = 0 且未过期)
3. (建议新增) `version` (int/bigint, DEFAULT 0): 乐观锁版本号，用于并发控制 `remaining_amount` 的更新。

## 6.0 待确认与讨论点 (已确认)

1. (已确认) 6.1 `balance` 字段在补助金发放流水中的隔离。
2. (已确认) 6.2 `store_subsidy_status` 状态完善：增加"已用尽"('2')。
3. (已确认) 6.3 补助金消耗流水的记录方式：生成独立支出型流水 (`pay_type`='51')。
4. (已确认) 6.4 订单退款时补助金恢复逻辑：按金额恢复至原补助金记录 `remaining_amount`（若未过期），并生成对应恢复流水 (`pay_type`='52')。
5. (已确认) 6.5 使用门槛字段：暂不需要。
6. (已确认) 6.6 并发消耗控制：需要考虑并设计（建议乐观锁）。