# 产品需求文档 (PRD): 八闽助业集市商城 - 同规格商品换货功能 (MVP)

**版本**: 1.0
**最后更新日期**: 2025-05-11
**负责人**: (填写负责人名称)

## 1. 引言

(Source 1) 本文档旨在详细定义《八闽助业集市商城》（以下简称“商城”）中“同规格商品换货功能”MVP (Minimum Viable Product - 最小可行产品) 版本的功能需求、用户体验、以及相关非功能性需求。此功能的目的是为用户提供一个在购买的商品出现质量问题、瑕疵或损坏时，申请更换同一商品、同一规格良品的便捷渠道，从而提升用户满意度和完善商城的售后服务体系。**本功能严格限定为同商品同规格换货，不涉及任何差价计算和处理。**

## 2. 目标与背景

### 2.1. 项目目标

(Source 2) *参考来源: `需求简报_八闽助业集市商城_同规格换货.md` - 章节 2. 愿景与目标*

* **用户端换货申请与跟踪**: 用户能够针对“已收货”订单中的特定商品（可指定换货数量）发起同规格换货申请，上传凭证，并在商家同意后填写退货物流信息，全程跟踪换货状态。
* **商家端换货处理与管理**: 商家能够审核用户的换货申请（包括凭证），确认库存，进行同意（并提供退货地址）或拒绝的操作；在收到用户退货后进行验货，并记录发出新换货商品的物流信息。
* **流程自动化与通知**: 换货流程中的关键状态变更能自动流转，并向用户和商家发送必要的系统通知。

### 2.2. 可衡量的成果 (成功标准)

(Source 3) *参考来源: `需求简报_八闽助业集市商城_同规格换货.md` - 章节 2. 成功指标*

* 换货申请处理平均时长（从用户申请到商家首次有效响应）控制在 X 小时内。
* 换货请求一次性通过率（商家首次审核即同意的比例）达到 Y% 以上。
* 换货周期平均时长（从用户申请到用户收到新商品的平均天数）控制在 Z 天内。
* 用户对换货流程的满意度评分达到 N 分以上（满分5分）。
* 功能上线后，因“商品质量/瑕疵”导致的“仅退款”或“退货退款”率相较于上线前降低 M%。

### 2.3. 关键绩效指标 (KPIs)

* 每日/每周/每月换货申请数量
* 各状态停留平均时长（如：待审核平均时长、待用户发货平均时长等）
* 用户放弃换货申请的比例（在各环节）
* 商家拒绝换货申请的比例及主要原因分布
* 用户上传凭证的有效性（需结合人工抽检）

## 3. 用户画像与用户故事

### 3.1. 用户画像

* **消费者 (买家) - 小敏**
    * **背景**: 28岁，办公室职员，经常在《八闽助业集市商城》购买生活用品和服饰。注重商品质量和购物体验。
    * **需求**: 当收到有瑕疵或损坏的商品时，希望能方便快捷地更换一个同款同规格的完好商品，而不是麻烦地退货再重新购买。希望整个换货过程透明，能随时了解进度。
    * **痛点**: 以前遇到小瑕疵商品，如果退货流程繁琐或运费自理，可能会选择将就使用或放弃维权，导致购物体验下降。

* **商家 (卖家) - 老王**
    * **背景**: 45岁，在《八闽助业集市商城》经营一家特色农产品店铺。希望维护好店铺信誉，减少差评和不必要的退款损失。
    * **需求**: 需要一个标准化的流程来处理用户的换货请求，能够清晰地看到用户的换货理由和凭证，方便判断和操作。希望系统能帮助管理换货库存和物流信息。
    * **痛点**: 缺乏统一的换货工具，处理用户换货请求时，沟通成本高，容易出错，也难以追踪管理。

### 3.2. 用户故事

* **买家 (小敏)**:
    1.  **US1.1**: 作为小敏，我希望能在我购买的订单中，为有质量问题的商品方便地发起换货申请，以便我能得到一个完好的同样商品。
    2.  **US1.2**: 作为小敏，我希望在申请换货时，能上传图片或视频作为商品问题的证明，以便商家能快速了解情况。
    3.  **US1.3**: 作为小敏，我希望在商家同意我的换货申请后，能清晰地看到退货地址和退货要求，并能方便地填写我寄回商品的物流信息。
    4.  **US1.4**: 作为小敏，我希望能实时查看我的换货申请处理到了哪一步，例如商家是否收到我退回的商品，新的商品是否已发出。
    5.  **US1.5**: 作为小敏，我希望在收到新的换货商品后，能在系统中确认收货，完成这次换货。

* **卖家 (老王)**:
    1.  **US2.1**: 作为老王，我希望能及时收到用户发起的换货申请通知，并能在一个集中的地方查看和管理所有换货请求。
    2.  **US2.2**: 作为老王，我希望能方便地查看用户换货申请的详情，包括商品信息、换货理由和用户上传的凭证，以便我做出准确的判断。
    3.  **US2.3**: 作为老王，我希望在审核换货申请时，能方便地操作同意或拒绝，并在同意时让系统自动告知用户我的退货地址。
    4.  **US2.4**: 作为老王，我希望在收到用户退回的商品后，能在系统中记录已收货并进行验货，再决定是否寄出新商品。
    5.  **US2.5**: 作为老王，我希望在寄出新的换货商品后，能在系统中记录发出的物流信息，以便用户能追踪。
    6.  **US2.6**: 作为老王，我希望系统能帮助我管理换货相关的库存，例如退回的瑕疵品如何处理，发出的良品如何扣减。

## 4. 功能需求 (MVP / 当前版本)

(Source 4) *参考来源: `需求简报_八闽助业集市商城_同规格换货.md` - 章节 4. 核心功能 / 范围 (MVP)*

### 4.1. 换货流程状态定义

1.  **待审核**: 用户已提交换货申请，等待商家处理。
2.  **待用户寄回**: 商家已同意换货申请，等待用户寄回商品并填写物流信息。
3.  **用户已寄回 (待商家收货)**: 用户已填写退货物流信息，等待商家确认收到退货。
4.  **商家已收货 (待验货/待发新货)**: 商家已确认收到退货，正在验货或准备发新货。
5.  **商家已发新货**: 商家已将新的换货商品发出，并填写了新货的物流信息，等待用户确认收货。
6.  **换货完成**: 用户已确认收到新货，或系统超时自动确认，流程结束。
7.  **已拒绝**: 商家审核拒绝了用户的换货申请，流程结束。
8.  **已取消 (用户)**: 用户在特定阶段主动取消了换货申请，流程结束。
9.  **已关闭 (系统/商家)**: 因特定原因（如用户超时未寄回、商家验货不通过且无法协商一致）导致流程关闭。

### 4.2. 用户端 (买家) 功能

#### 4.2.1. 发起换货申请
* **入口**: “我的订单”列表 -> “已完成”/“已收货”状态的订单详情页 -> 订单内商品列表 -> 商品旁显示“申请售后”按钮。
* **选择售后类型**: 点击“申请售后”后，弹出选项，选择“换货”。
* **选择换货商品与数量**:
    * **界面元素**:
        * 展示订单中可申请换货的商品列表（含商品图片、名称、原购买规格、原购买单价、原购买数量）。
        * 每个商品旁提供“选择”操作。
        * 用户选择一个商品后，系统自动锁定该商品的原规格（不可更改）。
        * 数量输入框/选择器：允许用户输入希望换货的数量，最大不超过该商品在该订单中的可售后数量（原购买数量 - 已发起且未关闭的售后数量）。
    * **操作逻辑**:
        * 用户选择要换货的商品。
        * 用户输入/选择要换货的数量。
    * **系统反馈**: 实时校验数量是否合法。
* **填写换货信息**:
    * **界面元素**:
        * 换货理由：下拉选择（如：商品质量问题、商品损坏/少发、与描述不符-但仍要原规格等）+ 其他文本框补充。
        * 问题描述：多行文本框，用户详细描述问题。
        * 上传凭证：图片/视频上传控件（支持多张图片，建议限制图片大小和数量，视频时长和大小）。**此项为必填。**
        * 联系电话：默认显示用户在订单中的联系电话，可修改。
    * **操作逻辑**: 用户填写各项信息并上传凭证。
* **提交申请**:
    * **界面元素**: “提交申请”按钮。
    * **操作逻辑**:
        * 点击提交前进行表单校验（必填项是否填写，凭证是否上传）。
        * 提交成功后，系统生成换货单，状态为“待审核”。
    * **系统反馈**: 提示“换货申请提交成功！”，并跳转到换货详情页。

#### 4.2.2. 查看换货详情与状态
* **入口**: “我的” -> “退换/售后”列表 -> 点击具体换货单进入详情。
* **界面元素**:
    * 换货单号、申请时间。
    * 当前状态及状态变更时间轴/列表。
    * 原商品信息（图片、名称、规格、换货数量）。
    * 换货理由、问题描述、用户上传的凭证图片/视频。
    * 商家处理意见（如拒绝理由）。
    * 退货地址信息（商家同意后显示）：收件人、联系电话、详细地址。
    * 退货物流信息（用户填写后显示）：快递公司、物流单号、物流轨迹（若对接）。
    * 新发货物流信息（商家发货后显示）：快递公司、物流单号、物流轨迹（若对接）。
    * 操作按钮（根据当前状态显示）：如“取消申请”、“填写退货物流”、“确认收到新货”。
* **操作逻辑**: 用户查看换货进度和详情。

#### 4.2.3. 取消换货申请
* **条件**: 换货状态为“待审核”时，用户可取消。
* **界面元素**: 换货详情页显示“取消申请”按钮。
* **操作逻辑**: 用户点击“取消申请”，二次确认后，换货单状态变为“已取消 (用户)”。
* **系统反馈**: 提示“申请已取消”，详情页状态更新。

#### 4.2.4. 填写退货物流信息
* **条件**: 换货状态为“待用户寄回”（即商家已同意换货）。
* **界面元素**: 换货详情页显示“填写退货物流”按钮。点击后弹出/跳转到填写页面：
    * 快递公司：下拉选择或手动输入。
    * 物流单号：文本输入框。
    * （可选）发货备注：文本框。
    * “提交”按钮。
* **操作逻辑**:
    * 用户寄出商品后，填写并提交物流信息。
    * 提交后，换货单状态变为“用户已寄回 (待商家收货)”。
* **系统反馈**: 提示“物流信息提交成功”，详情页更新物流信息和状态。

#### 4.2.5. 催促商家处理 (可选)
* **条件**: 若商家在规定时限内未处理（如未审核、未确认收货等）。
* **界面元素**: 详情页显示“催促处理”按钮。
* **操作逻辑**: 用户点击后，系统向商家发送提醒。每日限催促N次。
* **系统反馈**: 提示“已提醒商家尽快处理”。

#### 4.2.6. 确认收到新货
* **条件**: 换货状态为“商家已发新货”。
* **界面元素**: 换货详情页显示“确认收到新货”按钮。
* **操作逻辑**:
    * 用户收到新商品并确认无误后，点击该按钮。
    * 二次确认后，换货单状态变为“换货完成”。
* **系统反馈**: 提示“换货已完成”，详情页状态更新。若有评价功能，可引导评价。
* **自动确认**: 若用户在商家发货后M天内未确认收货，且物流信息显示已签收超过N天，系统可自动将状态更新为“换货完成”。

### 4.3. 商家端 (卖家) 功能 (通常在PC端后台或商家版App)

#### 4.3.1. 查看换货申请列表
* **入口**: 商家后台 -> “售后管理” -> “换货管理”。
* **界面元素**:
    * 列表展示换货单：申请时间、换货单号、用户昵称、商品名称、换货数量、当前状态。
    * 筛选条件：按状态、申请时间范围、用户昵称/订单号搜索。
    * 操作：每条记录后有“查看详情”按钮/链接。
    * 新申请提醒标记。
* **操作逻辑**: 商家查看和筛选换货申请。

#### 4.3.2. 查看换货申请详情
* **界面元素**:
    * 同用户端看到的申请信息：换货单号、申请时间、当前状态。
    * 用户信息：用户昵称、联系电话。
    * 订单信息：关联的原订单号（可跳转查看原订单详情）。
    * 原商品信息：图片、名称、规格、换货数量、原商品单价、原商品总价（换货部分）。
    * 用户填写的换货理由、问题描述、上传的凭证（可点击放大查看）。
    * 若已进行，显示退货物流信息、新发货物流信息。
    * 处理操作区域（根据状态显示）。
* **操作逻辑**: 商家全面了解换货申请的细节。

#### 4.3.3. 审核换货申请
* **条件**: 换货状态为“待审核”。
* **界面元素**: 详情页底部显示处理操作按钮/区域：
    * “同意换货”按钮。
    * “拒绝换货”按钮。
    * 退货地址显示/确认：默认显示商家设置的退货地址，可本次临时修改（不建议MVP阶段做临时修改，应引导商家维护好默认地址）。
* **操作逻辑 - 同意换货**:
    1.  商家检查库存，确认有同规格商品可供换货。
    2.  点击“同意换货”。
    3.  系统（或商家再次确认后）将换货单状态更新为“待用户寄回”。
    4.  系统通知用户申请已通过，并展示退货地址。
* **操作逻辑 - 拒绝换货**:
    1.  点击“拒绝换货”。
    2.  弹出窗口，要求商家填写拒绝理由（必填，可提供常用理由模板供选择，并支持手动输入）。
    3.  确认提交后，换货单状态更新为“已拒绝”。
    4.  系统通知用户申请被拒绝及拒绝理由。
* **系统反馈**: 操作成功提示，详情页状态和信息更新。

#### 4.3.4. 确认收到退货与验货
* **条件**: 换货状态为“用户已寄回 (待商家收货)”。用户已填写退货物流单号。
* **界面元素**: 详情页显示：
    * 用户填写的退货快递公司和物流单号（可点击查询物流轨迹）。
    * “确认收到退货”按钮。
* **操作逻辑 - 确认收到退货**:
    1.  商家实际收到用户寄回的包裹后，点击“确认收到退货”。
    2.  系统将换货单状态更新为“商家已收货 (待验货/待发新货)”。
* **验货 (线下操作，系统记录结果)**:
    * 商家对退回的商品进行检查：数量是否正确、规格是否为原规格、用户描述的问题是否属实、有无其他人为损坏等。
    * **验货通过**: 商家准备发新货。
    * **验货不通过**:
        * **界面元素 (在详情页提供入口)**: “验货不通过处理”或类似按钮/操作。
        * **操作逻辑**:
            1.  商家点击该按钮，填写验货不通过的原因、上传商品当前状况的图片/视频凭证。
            2.  系统将此信息记录，并可能暂时挂起换货单，或提供选项给商家联系用户协商（如：用户承担部分责任后换货、直接拒绝并将原商品退回用户 - 运费由谁承担需平台规则定义）。
            3.  **MVP简化处理**: 若验货不通过，商家可直接再次操作“拒绝换货”（理由为验货不符），流程引导至“已关闭 (系统/商家)”。此环节可能需要客服介入机制（未来迭代）。
* **系统反馈**: 操作成功提示，详情页状态更新。

#### 4.3.5. 发新货并录入物流
* **条件**: 换货状态为“商家已收货 (待验货/待发新货)”且验货通过（或MVP中商家在确认收货后直接准备发新货）。
* **界面元素**: 详情页显示“发新货”按钮或区域：
    * 新发货快递公司：下拉选择或手动输入。
    * 新发货物刘单号：文本输入框。
    * （可选）发货备注。
    * “确认发货”按钮。
* **操作逻辑**:
    1.  商家将新的同规格商品打包发出后，在此填写物流信息。
    2.  点击“确认发货”。
    3.  系统将换货单状态更新为“商家已发新货”。
    4.  系统通知用户新商品已发出及物流信息。
    5.  系统应在此环节扣减对应商品的实际库存。
* **系统反馈**: 操作成功提示，详情页更新物流信息和状态。

#### 4.3.6. 查看换货历史
* **界面元素**: “换货管理”列表页提供已完成、已关闭、已拒绝等所有状态的换货单查看和筛选。
* **操作逻辑**: 商家可查阅历史换货记录。

### 4.4. 系统层面功能

* **状态自动流转**: 根据用户和商家的操作，系统自动更新换货单的生命周期状态。
* **消息通知 (小程序内消息/服务通知，短信可选)**:
    * **通知用户**:
        * 申请提交成功。
        * 商家同意换货（含退货地址）。
        * 商家拒绝换货（含拒绝理由）。
        * 商家确认收到退货。
        * 商家已发出新货（含物流信息）。
        * 换货完成。
        * （可选）超时提醒：如商家长时间未审核，用户长时间未寄回。
    * **通知商家**:
        * 收到新的换货申请。
        * 用户已填写退货物流。
        * 用户已确认收到新货。
        * （可选）超时提醒：如用户寄回商品物流异常，或需要商家处理的环节超时。
* **超时机制 (可选，建议MVP后迭代)**:
    * 商家审核超时：若商家在X小时内未审核，系统可自动提醒商家，或特定条件下自动同意（需谨慎设计）。
    * 用户退货超时：若用户在商家同意后Y天内未填写退货物流，系统可自动取消/关闭换货申请。
* **退货地址管理**:
    * 商家后台提供设置和管理默认退货地址的功能（收件人、电话、省市区、详细地址）。
    * 在商家同意换货时，可默认带出此地址。
* **库存联动**:
    * 换货申请提交时，不强制预占库存（因为是同规格换，通常是质量问题，默认商家有良品可换）。
    * 商家发出新货时，需从实际库存中扣减对应商品规格的数量。
    * 退回的瑕疵品，需有机制让商家标记其状态（如：待销毁、返厂维修、可作为二手品处理等），不应自动回流入正品可售库存。MVP阶段可简化为不自动处理退回商品的库存，由商家线下管理，仅扣减发出新品的库存。
* **运费处理逻辑 (MVP阶段明确规则，暂不线上处理)**:
    * 明确因商品质量问题导致的同规格换货，来回运费由谁承担。
    * MVP阶段，系统层面不直接处理运费的线上支付或补贴，由买卖双方根据平台规则线下协商或通过其他方式处理。后续可迭代运费补贴功能。

## 5. 界面原型草图/线框图建议

*(由于文本格式限制，此处仅作文字描述性建议。实际PRD中应附带图片或链接到设计工具)*

### 5.1. 用户端 (小程序)

* **订单详情页**:
    * 在商品项旁增加“申请售后”按钮。
* **售后类型选择弹窗/页面**:
    * 清晰的“换货”、“退货退款”等选项。
* **换货申请填写页**:
    * 顶部显示订单信息和选定换货的商品信息（图、名、规格、数量）。
    * 表单区域：换货数量选择、换货理由选择、问题描述输入框、凭证上传入口。
    * 底部“提交申请”按钮。
* **换货详情页**:
    * 顶部显示换货单号和当前状态。
    * 物流式时间轴展示各处理节点和时间。
    * 商品信息区域。
    * 申请信息区域（理由、描述、凭证预览）。
    * 商家反馈区域（如拒绝理由）。
    * 退货地址显示区域。
    * 物流信息填写/展示区域（用户退货物流、商家发新货物流）。
    * 底部根据状态显示操作按钮。
* **售后列表页**:
    * 卡片式展示各售后单（含换货单），显示商品图、名称、状态、申请时间。

### 5.2. 商家端 (PC后台)

* **换货管理列表页**:
    * 表格形式展示，包含关键信息列和操作列。
    * 强大的筛选和搜索功能。
* **换货详情页**:
    * 多栏布局，清晰展示申请详情、用户信息、订单信息、商品信息、凭证预览、处理记录。
    * 底部或右侧为处理操作区域。
    * 物流信息输入表单。

## 6. 非功能性需求

### 6.1. 易用性
* **用户端**: 流程引导清晰，操作步骤简洁，用户无需过多思考即可完成申请和信息填写。错误提示友好明确。
* **商家端**: 信息展示全面，操作便捷高效，易于商家快速上手处理换货请求。
* 所有用户交互界面应符合小程序的设计规范和用户使用习惯。

### 6.2. 性能
* 页面加载速度：列表页、详情页加载时间应在3秒以内。
* 操作响应时间：用户提交申请、商家审核等操作的后端响应时间应在2秒以内。
* 图片/视频上传：支持后台异步上传，避免长时间阻塞用户界面；上传过程中有明确进度提示。

### 6.3. 安全性
* 用户敏感信息（如联系方式、地址）需妥善存储和展示，符合隐私政策。
* 防止恶意提交、重复提交等滥用行为（如通过接口频率限制）。
* 商家后台操作需有权限控制。
* 所有数据传输使用HTTPS加密。

### 6.4. 可靠性/可用性
* 系统应保证高可用性，核心换货流程在99.9%的时间内可用。
* 数据一致性：确保换货状态、库存信息等在用户端、商家端和数据库中保持一致。
* 错误处理：对可预见的异常情况（如网络错误、库存不足的临界情况、无效输入）有优雅的错误处理和提示。

### 6.5. 可维护性
* 代码结构清晰，模块化设计，易于后期功能迭代和维护。
* 关键业务逻辑有清晰的注释和文档。
* 配置化：超时时限、部分提示语等可后台配置。

### 6.6. 兼容性
* 用户端小程序需兼容主流手机操作系统（iOS, Android）的最新几个版本及主流机型。
* 商家端后台需兼容主流PC浏览器（Chrome, Firefox, Edge等）的最新版本。

## 7. 集成需求 (高层)

* **订单管理模块**: 读取订单信息，更新订单关联的售后状态。
* **商品管理模块**: 读取商品信息，校验商品是否支持换货。
* **库存管理模块**: 商家发新货时扣减实际库存。退回商品不自动回流至可售库存（MVP）。
* **用户消息通知模块**: 发送小程序模板消息或短信通知。
* **物流查询服务 (可选)**: 对接第三方物流服务接口，展示物流轨迹。

## 8. 风险与约束回顾

*参考来源: `需求简报_八闽助业集市商城_同规格换货.md` - 章节 5. 已知技术约束或偏好*

* **核心简化**: 再次强调，MVP版本严格限定为同商品同规格换货，不处理差价。
* **用户理解**: 需在用户申请入口和流程中清晰告知“仅限同规格换货”的规则。
* **运费**: MVP阶段运费承担规则需明确，但系统不直接介入线上结算。
* **恶意行为**: 需要关注上线后的数据，为后续的风险控制策略迭代（如限制用户换货频率、建立商家申诉机制等）收集依据。

## 9. 未来迭代方向 (Post-MVP)

* 支持换不同规格商品（涉及差价处理、更复杂的库存管理）。
* 线上运费补贴/支付功能。
* 更智能的超时提醒与自动处理机制。
* 商家端更完善的退货商品管理（如入库残次品库、与供应商协同等）。
* 用户与商家在换货单内的沟通/留言功能。
* 平台客服介入处理争议换货单的功能。

## 10. 变更日志

| 版本 | 日期       |修订人 | 修订描述             |
|------|------------|--------|----------------------|
| 1.0  | 2025-05-11 | (AI)   | 基于项目简报创建初始PRD |
|      |            |        |                      |