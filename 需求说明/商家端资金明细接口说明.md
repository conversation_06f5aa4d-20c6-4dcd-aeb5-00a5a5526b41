# 商家端资金明细接口说明

## 接口概述

该接口用于获取商家端的资金流水明细，支持分页查询和收支类型筛选，返回交易记录列表。

## 接口详情

### 请求路径

```
/back/storeAccountCapital/findStoreAccountCapitalInfo
```

### 请求方式

GET

### 请求参数

| 参数名 | 类型 | 必填 | 描述 |
| ------ | ---- | ---- | ---- |
| pageNo | Integer | 否 | 页码，默认为1 |
| pageSize | Integer | 否 | 每页条数，默认为10 |
| isPlatform | String | 否 | 收支类型：0=收入，1=支出，2=全部（默认） |
| id | String | 是 | 店铺ID（store_manage_id） |

### 返回参数

```json
{
  "success": true,
  "message": "返回可用余额明细成功!",
  "code": 200,
  "result": {
    "records": [
      {
        "goAndCome": "0",                  // 收支类型：0=收入，1=支出
        "payType": "订单交易",              // 交易类型文本
        "createTime": "2025-04-13 14:29:17", // 交易时间
        "amount": "+98.60",                // 交易金额（带正负号）
        "balance": "余额 123.25"            // 交易后余额
      },
      {
        "goAndCome": "0",
        "payType": "订单交易",
        "createTime": "2025-04-12 22:56:53",
        "amount": "+24.65",
        "balance": "余额 24.65"
      }
    ],
    "total": 100,      // 总记录数
    "size": 10,        // 每页大小
    "current": 1,      // 当前页
    "pages": 10        // 总页数
  }
}
```

### 字段说明

| 字段名 | 类型 | 描述 |
| ------ | ---- | ---- |
| goAndCome | String | 收支类型：0=收入，1=支出 |
| payType | String | 交易类型文本，如：订单交易、余额提现、订单退款等 |
| createTime | String | 交易时间，格式：yyyy-MM-dd HH:mm:ss |
| amount | String | 交易金额，带正负号，如：+98.60、-24.65 |
| balance | String | 交易后余额，格式：余额 xxx.xx |

## 交易类型说明

交易类型（payType）对应的字典值：

| 字典值 | 文本描述 |
| ------ | -------- |
| 0 | 订单交易 |
| 1 | 余额提现 |
| 2 | 订单退款 |
| 3 | 福利金赠送 |
| 4 | 余额充值 |
| 5 | 归属店铺奖励 |
| 6 | 渠道销售奖励 |

## 前端展示说明

### 列表展示

1. 根据 `goAndCome` 字段区分收入和支出：
   - 收入（0）：金额显示为绿色，前缀为"+"
   - 支出（1）：金额显示为红色，前缀为"-"

2. 交易时间格式化为 "yyyy-MM-dd HH:mm:ss"

3. 余额直接显示 `balance` 字段内容

### 筛选功能

1. 通过 `isPlatform` 参数筛选收支类型：
   - 0：只显示收入记录
   - 1：只显示支出记录
   - 2：显示全部记录（默认）

2. 分页功能：
   - 使用 `pageNo` 和 `pageSize` 参数进行分页查询
   - 默认每页显示10条记录

## 使用示例

### 请求示例

```
GET /back/storeAccountCapital/findStoreAccountCapitalInfo?pageNo=1&pageSize=10&isPlatform=2&id=1001
```

### 响应示例

```json
{
  "success": true,
  "message": "返回可用余额明细成功!",
  "code": 200,
  "result": {
    "records": [
      {
        "goAndCome": "0",
        "payType": "订单交易",
        "createTime": "2025-04-13 14:29:17",
        "amount": "+98.60",
        "balance": "余额 123.25"
      },
      {
        "goAndCome": "0",
        "payType": "订单交易",
        "createTime": "2025-04-12 22:56:53",
        "amount": "+24.65",
        "balance": "余额 24.65"
      }
    ],
    "total": 100,
    "size": 10,
    "current": 1,
    "pages": 10
  }
}
```

## 注意事项

1. 接口需要用户登录后才能访问
2. 只能查询当前登录用户所属店铺的资金明细
3. 金额字段 `amount` 已经包含了正负号，前端直接显示即可
4. 余额字段 `balance` 已经包含了"余额"前缀，前端直接显示即可
