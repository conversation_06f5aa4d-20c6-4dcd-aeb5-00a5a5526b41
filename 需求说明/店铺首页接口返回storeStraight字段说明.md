# 店铺首页接口返回storeStraight字段说明

## 功能概述

在店铺首页接口`/front/storeManage/index`中新增返回"店铺主体类型"字段`storeStraight`，用于前端展示店铺的主体类型信息。

## 接口变更

### 1. 接口路径

`/front/storeManage/index`

### 2. 返回字段变更

在接口返回的数据中新增了`storeStraight`字段，用于展示店铺主体类型信息。

### 3. 相关实现变更

在`StoreManageMapper.xml`的`getStoreManageById`方法中，直接在SQL查询中添加了`storeStraight`字段：

```xml
<select id="getStoreManageById" resultType="map">
    SELECT sm.id,
           ...
           sm.introduce                              as introduce,
           sm.total_performance                      as totalPerformance,
           sm.store_propaganda_images                as storePropagandaImages,
           sm.straight                               as storeStraight
    FROM `store_manage` sm
    WHERE sm.id = #{storeManageId}
      AND sm.del_flag = '0'
</select>
```

这种方式直接在SQL查询中返回字段，避免了额外的数据库查询，提高了性能。

## 使用说明

1. 前端在调用`/front/storeManage/index`接口获取店铺首页信息时，可以从返回数据中获取`storeStraight`字段
2. 可以根据`storeStraight`字段的值展示不同的店铺主体类型信息

## 示例

### 接口返回示例

```json
{
  "success": true,
  "message": "操作成功！",
  "code": 200,
  "result": {
    "id": "1234567890",
    "storeName": "测试店铺",
    "logoAddr": "http://example.com/logo.jpg",
    "storeAddress": "测试地址",
    "comprehensiveEvaluation": 4.5,
    "longitude": 116.123456,
    "latitude": 39.123456,
    "sysUserId": "user123",
    "status": "1",
    "storePropagandaImages": "http://example.com/image1.jpg,http://example.com/image2.jpg",
    "storeStraight": "个体工商户",
    "storeColumn": [
      {
        "name": "商品",
        "storeCode": 1
      },
      {
        "name": "优惠券",
        "storeCode": 2
      }
    ]
  },
  "timestamp": 1683000000000
}
```

### 前端使用示例

前端可以根据`storeStraight`字段的值展示不同的店铺主体类型信息：

```javascript
// 获取店铺主体类型
const storeStraight = response.result.storeStraight;

// 根据不同的主体类型展示不同的信息
if (storeStraight === '个体工商户') {
  // 展示个体工商户相关信息
} else if (storeStraight === '企业') {
  // 展示企业相关信息
} else {
  // 展示其他类型相关信息
}
```

## 注意事项

1. `storeStraight`字段可能为空，前端在使用时需要做好空值处理
2. 不同的店铺主体类型可能需要不同的展示方式，前端需要根据业务需求进行适配
