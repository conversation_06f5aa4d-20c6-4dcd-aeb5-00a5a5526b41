## 产品需求文档：店铺等级升降级策略

**版本**: 1.0
**日期**: 2025年5月25日
**负责人**: AI产品经理 (八闽助业集市)

### 1. 引言

#### 1.1 文档目的
本文档旨在详细描述“八闽助业集市”平台关于店铺等级自动升降级策略的功能需求。它将作为产品设计、开发、测试及后续迭代的依据。

#### 1.2 项目背景
为提升平台商家的活跃度和经营业绩，激励商家拓展合作资源，特引入一套动态的店铺等级评定与激励机制。通过自动化评估店铺的月度表现，实现等级的动态调整及相应奖励，从而促进平台生态的健康发展。

#### 1.3 目标用户
* **平台管理员**: 配置等级规则、监控执行情况、处理异常。
* **平台商家**: 了解自身等级、关注等级变化、获取等级奖励。

#### 1.4 名词解释
* **店铺等级 (Store Level)**: 根据店铺业绩和合作企业家数量评定的级别，存储于 `store_manage.store_level`。
* **上月业绩 (Previous Month's Performance)**: 店铺在上一个自然月内，状态为“交易成功”（status='3'）或“交易完成”（status='5'），且以“确认收货时间”（`delivery_time`）为准的订单的“店铺实收金额”（`actually_received_amount`）的总和。数据来源于 `order_store_list` 表。
* **合作企业家数量 (Cooperative Entrepreneur Count)**: 在评定日当天，与指定店铺（`store_cashier_routing.store_manage_id`）在 `store_cashier_routing` 表中关联的、非空的 `member_list_id` 的去重数量。
* **店铺等级评定配置表 (`store_level_configurations`)**: 存储各店铺等级的达成标准（业绩、企业家数量）和月度奖励金额的配置表。
* **店铺余额 (`store_manage.balance`)**: 商家在平台的可提现余额账户，等级奖励将发放到此账户。
* **逐级升/降级**: 等级调整时，每次只能上升或下降一个级别。

### 2. 功能概述

#### 2.1 功能简介
系统将每月初自动执行一次店铺等级评定任务。根据各店铺上一个月的业绩表现和合作企业家数量，对照【店铺等级评定配置表】中的标准，对店铺等级进行维持、升级或降级处理。符合特定高级别标准的店铺将获得月度店铺余额奖励。

#### 2.2 核心价值
* **激励商家**: 通过明确的等级和奖励机制，激励商家提升销售业绩和合作网络。
* **自动化管理**: 减少人工干预，实现店铺等级评定的自动化和规范化。
* **精细化运营**: 为后续基于店铺等级的差异化赋能、活动参与等运营策略提供数据基础。
* **提升平台活跃度**: 促进商家更积极地参与平台经营活动。

### 3. 功能详述

#### 3.1 店铺等级配置 (`store_level_configurations` 表)

* **3.1.1 表结构设计**
    ```sql
    CREATE TABLE `store_level_configurations` (
      -- === 标准字段 ===
      `id` varchar(32) NOT NULL COMMENT '主键ID',
      `create_by` varchar(32) DEFAULT NULL COMMENT '创建人',
      `create_time` datetime DEFAULT (now()) NOT NULL COMMENT '创建时间',
      `update_by` varchar(32) DEFAULT NULL COMMENT '更新人',
      `update_time` datetime DEFAULT NULL COMMENT '更新时间',
      `del_flag` varchar(1) DEFAULT '0' COMMENT '删除状态 0=未删除 1=已删除',
      -- === 业务字段 ===
      `level_value` varchar(20) NOT NULL COMMENT '店铺等级值，例如 "0", "1", "2"。唯一标识一个等级。',
      `level_name` varchar(100) NOT NULL COMMENT '店铺等级名称，例如 "新手店铺", "一级认证店铺"。用于后台展示。',
      `min_monthly_sales` decimal(20,2) DEFAULT '0.00' COMMENT '达成/维持此等级的最低月业绩要求（大于等于）。',
      `min_entrepreneur_count` int DEFAULT '0' COMMENT '达成/维持此等级的最低关联企业家数量要求（大于等于）。',
      `monthly_reward_balance_amount` decimal(10,2) DEFAULT '0.00' COMMENT '达到此等级后，每月发放的店铺余额奖励金额。',
      `description` text COMMENT '等级说明，描述该等级的特性或备注信息。',
      `sort_order` int DEFAULT '0' COMMENT '排序字段，用于后台展示等级配置列表的顺序，值越小越靠前。',
      `is_active` tinyint(1) DEFAULT '1' COMMENT '是否启用此等级配置: 0=未启用, 1=已启用。字典: common_active_status。未启用的配置不参与自动评级。',
      PRIMARY KEY (`id`),
      UNIQUE KEY `uk_store_level_value` (`level_value`, `del_flag`) COMMENT '同一删除状态下，等级值唯一',
      KEY `idx_is_active` (`is_active`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='店铺等级评定配置表';
    ```
    * 对应的 `common_active_status` 字典需提前定义或使用现有通用状态字典。

* **3.1.2 配置管理 (后台管理功能)**
    * 平台管理员应能通过后台管理界面对 `store_level_configurations` 表进行增、删、改、查操作。
    * **新增/编辑时**：
        * `level_value`: 必填，校验唯一性（结合`del_flag`）。
        * `level_name`: 必填。
        * `min_monthly_sales`: 必填，非负数。
        * `min_entrepreneur_count`: 必填，非负整数。
        * `monthly_reward_balance_amount`: 必填，非负数。
        * `is_active`: 默认为“启用”。
    * **查询时**: 可按 `level_name`, `level_value`, `is_active` 等条件查询，列表按 `sort_order` 排序。

#### 3.2 自动化等级评定引擎

* **3.2.1 执行周期与评估对象**
    * **执行时间**: 每个月1号的凌晨（例如 02:00 AM）。
    * **评估对象**: 所有 `store_manage` 表中 `status = '1'` 且 `del_flag = '0'` 的店铺。

* **3.2.2 上月业绩数据统计规则**
    * 针对每个评估店铺，统计其上一个自然月（以 `order_store_list.delivery_time` 为准）的有效订单业绩总和。
    * 有效订单定义：`order_store_list.status` 为 '3' (交易成功) 或 '5' (交易完成)。
    * 业绩金额：取 `order_store_list.actually_received_amount` 字段。
    * SQL示意:
        ```sql
        SELECT SUM(osl.actually_received_amount)
        FROM order_store_list osl
        WHERE osl.sys_user_id = [current_store_id]
          AND osl.status IN ('3', '5')
          AND osl.delivery_time >= DATE_FORMAT(CURDATE() - INTERVAL 1 MONTH, '%Y-%m-01 00:00:00')
          AND osl.delivery_time < DATE_FORMAT(CURDATE(), '%Y-%m-01 00:00:00');
        ```

* **3.2.3 合作企业家数量统计规则**
    * 针对每个评估店铺（`store_manage.id`），在评定日当天，统计其在 `store_cashier_routing` 表中，以 `store_cashier_routing.store_manage_id` 关联的、非空的 `member_list_id` 的去重（DISTINCT）数量。
    * SQL示意:
        ```sql
        SELECT COUNT(DISTINCT scr.member_list_id)
        FROM store_cashier_routing scr
        WHERE scr.store_manage_id = [current_store_id]
          AND scr.member_list_id IS NOT NULL
          AND scr.del_flag = '0'; -- 假设 store_cashier_routing 也有 del_flag
        ```

* **3.2.4 等级评定流程**
    * 对每个店铺，首先获取其当前等级 `current_level` (来自 `store_manage.store_level`)。
    * 获取其上月业绩 `actual_sales` 和当前合作企业家数量 `actual_entrepreneurs`。
    * **步骤A：维持当前等级评估**
        * 查询 `store_level_configurations` 表获取 `current_level` 对应的 `min_monthly_sales` 和 `min_entrepreneur_count` 标准。
        * **IF** `actual_sales >= min_monthly_sales_for_current_level` AND `actual_entrepreneurs >= min_entrepreneur_count_for_current_level`：
            * 店铺维持当前等级。进入 **步骤B：晋升下一等级评估**。
        * **ELSE**:
            * 店铺不满足当前等级维持标准。执行 **3.2.6 降级规则**。本轮该店铺评定结束。
    * **步骤B：晋升下一等级评估** (仅当店铺维持当前等级时进行)
        * 设 `next_level` 为 `current_level + 1` (或按 `level_value` 逻辑上的下一级)。
        * 查询 `store_level_configurations` 表获取 `next_level` 对应的 `min_monthly_sales` 和 `min_entrepreneur_count` 标准。
        * **IF** 存在 `next_level` 配置且 `actual_sales >= min_monthly_sales_for_next_level` AND `actual_entrepreneurs >= min_entrepreneur_count_for_next_level`:
            * 店铺升级到 `next_level`。执行 **3.2.5 升级规则**。
        * **ELSE**:
            * 店铺不满足晋升条件，等级保持为 `current_level`。
        * 本轮该店铺评定结束。

* **3.2.5 升级规则**
    * 店铺等级 (`store_manage.store_level`) 更新为新的、更高的等级。
    * 升级是逐级的，即一次评定周期内最多升一级。
    * 记录等级变更日志 (见 3.3.1)。
    * 根据新等级，在当月发放对应奖励 (见 3.2.7)。

* **3.2.6 降级规则**
    * 店铺等级 (`store_manage.store_level`) 更新为当前等级的下一级（即 `current_level - 1`），最低降至 '0' 级。
    * 降级是逐级的，即一次评定周期内最多降一级。
    * 记录等级变更日志 (见 3.3.1)。
    * 若降级后的新等级无奖励或奖励金额低于原等级，则原等级的月度奖励停止发放或按新标准发放。

* **3.2.7 奖励发放规则**
    * 在等级评定完成后，如果店铺的最终等级在 `store_level_configurations` 表中配置了 `monthly_reward_balance_amount > 0`，则将该金额增加到店铺的 `store_manage.balance` 字段。
    * 需记录奖励发放流水，可与等级变更日志结合或单独记录。

* **3.2.8 首次执行逻辑**
    * 功能上线后首次执行等级评定任务时，所有符合评估对象的店铺（包括老店铺，无论其当前 `store_level` 是否为 '0'）都将基于其当前的 `store_level`，并根据上一个月的业绩表现和合作企业家数量，进行上述的维持/升级/降级评估流程。

* **3.2.9 等级初始化**
    * 新注册并通过审核的店铺，其 `store_manage.store_level` 默认值为 '0'。

#### 3.3 数据记录与日志

* **3.3.1 店铺等级变更日志表 (`store_level_change_logs`)**
    * 建议表结构 (遵循数据库设计规范)：
        ```sql
        CREATE TABLE `store_level_change_logs` (
          -- === 标准字段 ===
          `id` varchar(32) NOT NULL COMMENT '主键ID',
          `create_by` varchar(32) DEFAULT NULL COMMENT '创建人 (可为 system)',
          `create_time` datetime DEFAULT (now()) NOT NULL COMMENT '创建时间 (评定时间)',
          `update_by` varchar(32) DEFAULT NULL COMMENT '更新人',
          `update_time` datetime DEFAULT NULL COMMENT '更新时间',
          `del_flag` varchar(1) DEFAULT '0' COMMENT '删除状态 0=未删除 1=已删除',
          -- === 业务字段 ===
          `store_manage_id` varchar(32) NOT NULL COMMENT '店铺ID (关联 store_manage.id)',
          `assessment_period` varchar(7) NOT NULL COMMENT '评定业绩周期，格式 YYYY-MM (例如 2025-04)',
          `previous_level` varchar(20) NOT NULL COMMENT '变更前等级',
          `current_level` varchar(20) NOT NULL COMMENT '变更后等级',
          `assessed_sales` decimal(20,2) DEFAULT '0.00' COMMENT '评定使用的上月业绩金额',
          `assessed_entrepreneur_count` int DEFAULT '0' COMMENT '评定使用的合作企业家数量',
          `change_type` varchar(10) NOT NULL COMMENT '变更类型: UPGRADE, DOWNGRADE, MAINTAIN。字典: store_level_change_type',
          `reward_balance_amount_given` decimal(10,2) DEFAULT '0.00' COMMENT '因此次评级发放的店铺余额奖励金额',
          `remarks` varchar(255) DEFAULT NULL COMMENT '备注信息',
          PRIMARY KEY (`id`),
          KEY `idx_store_manage_id` (`store_manage_id`),
          KEY `idx_assessment_period` (`assessment_period`),
          KEY `idx_change_type` (`change_type`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='店铺等级变更及奖励日志表';
        ```
    * 需定义 `store_level_change_type` 字典。

#### 3.4 平台管理端界面与操作

* **3.4.1 等级配置管理**
    * 提供对 `store_level_configurations` 表的可视化管理界面，实现增删改查。
    * 界面应清晰展示各等级的条件和奖励。
* **3.4.2 店铺等级与日志查看**
    * 可在店铺列表或店铺详情中展示当前店铺等级。
    * 提供界面查看 `store_level_change_logs`，支持按店铺ID、评定周期、变更类型等筛选。
* **3.4.3 手动调整与触发 (可选，需谨慎评估)**
    * **手动调整等级**: 为特殊情况提供修改单个店铺等级的功能，操作需记录详细日志（操作人、时间、原因、原等级、新等级）。
    * **手动触发评定**: 为测试或补偿执行，提供手动触发单个或符合条件的店铺进行等级评定的功能。

#### 3.5 商家端展示与通知

* **3.5.1 等级显示**
    * 在商家端小程序（如“我的店铺”、“个人中心”等位置）清晰展示店铺当前等级名称或标识。
* **3.5.2 变更与奖励通知**
    * 当店铺等级发生变化（升级/降级）时，应通过系统消息（如商家端站内信、短信等）通知商家。
    * 若获得等级奖励，通知内容应包含奖励金额，并提示商家可在店铺余额中查看。

### 4. 数据需求

#### 4.1 依赖数据表
* `store_manage`: 读取店铺当前等级、状态，更新店铺等级、余额。
* `order_store_list`: 读取上月有效订单数据用于计算业绩。
* `store_cashier_routing`: 读取合作企业家数据。
* `store_level_configurations`: 读取各等级的评定标准和奖励。
* `sys_dict` / `sys_dict_item`: (若使用) 为相关状态字段提供字典支持。

#### 4.2 关键数据字段
* `store_manage.store_level`
* `store_manage.balance`
* `order_store_list.delivery_time`, `order_store_list.status`, `order_store_list.actually_received_amount`, `order_store_list.sys_user_id`
* `store_cashier_routing.store_manage_id`, `store_cashier_routing.member_list_id`
* `store_level_configurations` 表中的所有业务字段。

### 5. 非功能性需求

* **5.1 性能**
    * 每月1号的批量评定任务需在合理时间内完成，避免长时间占用数据库资源或影响其他系统服务。
    * 涉及的查询（尤其是订单业绩汇总和企业家数量统计）需要进行优化。考虑是否需要月度业绩快照表。
* **5.2 可靠性**
    * 评定任务需保证稳定运行，具备失败重试机制（部分或全部）。
    * 确保数据计算和等级更新的事务性，避免部分成功导致的数据不一致。
* **5.3 可配置性**
    * 店铺等级的各项标准（业绩、企业家数）和奖励金额应能通过 `store_level_configurations` 表灵活配置。
* **5.4 数据准确性**
    * 业绩统计、企业家数量统计、等级判断逻辑必须准确无误。
* **5.5 可维护性**
    * 代码逻辑清晰，日志记录完善，便于问题排查和后续维护。

### 6. 待定与未来扩展 (Open Issues & Future Considerations)

* **企业家定义细化**: 当前“合作企业家”的定义来源于 `store_cashier_routing`，未来若有更复杂的企业家角色或合作模式，可能需要调整统计逻辑。
* **等级权益**: 当前仅涉及余额奖励，未来可基于店铺等级扩展更多差异化权益（如更高的分佣比例、平台流量扶持、专属活动参与权等）。
* **缓冲/警告机制**: 当前明确无缓冲期，未来可考虑为即将降级的店铺提供预警。
* **业绩快照**: 若订单数据量巨大，直接从订单表实时汇总月度业绩性能压力较大，可考虑在月末生成店铺月度业绩快照表，供次月初评级使用。

### 7. 附录

* **7.1 `store_level_configurations` 表SQL**: (见 3.1.1)
* **7.2 `store_level_change_logs` 表SQL**: (见 3.3.1)
* **7.3 `store_manage` 表相关字段**: `id`, `store_level`, `balance`, `status`, `del_flag`。 (参考用户提供)
* **7.4 `order_store_list` 表相关字段**: `sys_user_id`, `delivery_time`, `status`, `actually_received_amount`。 (参考用户提供)
* **7.5 `store_cashier_routing` 表相关字段**: `store_manage_id`, `member_list_id`, `del_flag`。 (参考用户提供)