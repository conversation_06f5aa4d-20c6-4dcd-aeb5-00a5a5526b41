{"mcpServers": {"heartful_mall_mysql": {"command": "/usr/local/n/versions/node/23.11.0/bin/node", "args": ["/Users/<USER>/Dev/Tools/mcp_tools/mcp-server-mysql/dist/index.js"], "env": {"MYSQL_HOST": "**************", "MYSQL_PORT": "13308", "MYSQL_USER": "root", "MYSQL_PASS": "wwX5FhezC0BVUtzlFh", "MYSQL_DB": "heartful-mall", "PATH": "/usr/local/n/versions/node/23.11.0/bin:/usr/bin:/bin", "NODE_PATH": "/usr/local/n/versions/node/23.11.0/lib/node_modules", "ALLOW_INSERT_OPERATION": "true", "ALLOW_UPDATE_OPERATION": "true", "ALLOW_DELETE_OPERATION": "true", "ALLOW_DDL_OPERATION": "true", "MYSQL_POOL_SIZE": "20", "MYSQL_QUERY_TIMEOUT": "300000", "MYSQL_CACHE_TTL": "600000", "MYSQL_RATE_LIMIT": "1000", "MYSQL_MAX_QUERY_COMPLEXITY": "10000", "MYSQL_SSL": "true", "MYSQL_ENABLE_LOGGING": "true", "MYSQL_LOG_LEVEL": "info", "MYSQL_METRICS_ENABLED": "true"}}}}