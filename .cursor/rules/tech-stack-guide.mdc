---
description: Uniapp 微信小程序技术栈规范
globs: 
alwaysApply: false
---
# 📝 Uniapp 微信小程序技术栈规范

本文档概述了基于 uniapp 开发微信小程序所需遵循的技术栈和开发约定。遵守此规范将确保代码库的一致性和可维护性。

## 1. 核心框架 🏗️

* **主要框架**：**Vue.js**
    * **版本**：`3.x` (依据 `manifest.json` 中的 `vueVersion: "3"`)
* **开发框架**：**uni-app**
    * **编译器版本**：`3.x` (依据 `manifest.json` 中的 `compilerVersion: 3`)
* **项目创建**：项目应通过 **HBuilderX** 创建和管理。 (依据无根目录 `package.json` 判断)

---

## 2. UI 框架 🎨

* **主要 UI 库**：**uView UI**
    * **版本**：`2.x`
    * **使用**：利用如 `uv-badge`、`uv-icon`、`uv-sticky`、`uv-tabs` 等组件。
* **次要 UI 库**：**uni-ui**
    * **版本**：最新可用版
    * **使用**：整合官方组件，如 `uni-badge`、`uni-countdown`、`uni-popup` 等。
* **自定义组件**：根据独特的 UI/UX 需求，开发和使用项目特定的自定义组件（例如 `activityBanner`、`cancelOrder`）。

---

## 3. 状态管理 📊

* **解决方案**：**Pinia**
    * **版本**：最新稳定版
* **Store 模块结构**：按功能或领域组织 Store。现有 Store 模块可作为参考，包括：
    * `userStore`：用户信息相关
    * `frontSetting`：程序设置相关
    * `countDownStore`：倒计时相关
    * `locationInfo`：地理位置相关
    * `getAllBeShared`：分享信息相关
    * `setSaleChannelStoreInfo`：销售渠道信息
    * `recordFullPagePath`：页面路径记录
    * `recordIndexPopUp`：首页弹窗状态

---

## 4. 网络请求 🌐

* **库**：**luch-request**
    * **版本**：`v3.0.8`
* **实现细节**：
    * 实现**请求和响应拦截器**进行全局处理。
    * 建立**统一的错误处理**机制。
    * 确保认证请求**自动携带 Token**。
    * 使用条件编译实现**跨端适配**。
    * 自动处理**登录失效**（例如，收到 code `666` 时）。
    * 该库应全局挂载为 `uni.http`。

---

## 5. 微信小程序原生能力 📱

* **目标平台**：主要是**微信小程序**。
* **AppID**：`wx8bb02b064cee39b4` (微信小程序)
* **原生 API 使用**：
    * 必要时使用微信原生 API，例如：
        * `wx.getMenuButtonBoundingClientRect()` 用于 UI 调整。
        * `wx.chooseMessageFile` (例如，在 `uni-file-picker` 组件内) 用于文件选择。
    * 在适用的情况下，使用条件编译 (`// #ifdef MP-WEIXIN`) 包装原生 API 调用，以确保跨平台兼容性。

---

## 6. 条件编译 🔄

* **目的**：管理特定平台的代码并确保跨平台兼容性。
* **主要使用的标记**：
    * `// #ifdef MP-WEIXIN`：微信小程序专用代码
    * `// #ifdef H5`：H5 平台专用代码
    * `// #ifdef APP-PLUS`：App 专用代码
    * `// #ifdef VUE3`：Vue3 专用代码
    * `// #ifdef MP`：所有小程序平台
    * `// #ifdef APP-NVUE`：App nvue 页面专用

---

## 7. 跨平台兼容性指南 🌍

* **请求适配**：
    * 在网络请求中使用条件编译设置特定平台的参数。例如 `softModel` 参数：
        ```javascript
        // #ifdef MP-WEIXIN
        'softModel': 0, //
        // #endif
        // #ifdef H5
        'softModel': 3, //
        // #endif
        ```
* **UI 适配**：
    * 在 `<style>` 标签内或 JavaScript 逻辑中使用条件编译应用特定平台的样式。
        ```css
        /* #ifdef H5 */
        /* H5专用样式 */ /* */
        /* #endif */
        ```
* **API 兼容性**：
    * 使用条件编译调用特定平台的 API 或提供备用方案。
        ```javascript
        // #ifdef MP-WEIXIN
        safeBottom = `calc(${screenHeight - safeArea.bottom}px + ${bottomMore}rpx)` //
        // #endif
        // #ifndef MP-WEIXIN
        safeBottom = `calc(${safeAreaInsets.bottom}px + ${bottomMore}rpx)` //
        // #endif
        ```
* **Vue 版本兼容性**：
    * 尽管项目是 Vue 3 (依据 `vueVersion: "3"`)，但如果需要任何旧版 Vue 2 代码或兼容性，请使用条件编译。(注意：主要目标是 Vue 3)
        ```javascript
        // #ifndef VUE3
        import Vue from 'vue' //
        // Vue2 代码
        // #endif

        // #ifdef VUE3
        import { createSSRApp } from 'vue' //
        // Vue3 代码
        // #endif
        ```