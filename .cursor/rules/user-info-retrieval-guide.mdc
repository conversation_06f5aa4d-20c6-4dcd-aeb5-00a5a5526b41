---
description: 用户信息获取与使用规范指南
globs: 
alwaysApply: false
---
# 📄 用户信息获取与使用规范指南

## 1. 引言

本文档旨在规范在心福商城小程序中获取和使用登录用户信息的方法和最佳实践。所有需要访问用户信息的开发工作应遵循此指南，以确保数据的一致性、准确性和应用的健壮性。

## 2. 用户信息存储机制

### 2.1 核心存储方案
用户信息主要通过 **Pinia store (`userStore`)** 进行状态管理。

### 2.2 登录与信息同步流程
用户成功登录后，系统将执行以下操作：
1.  **Token 存储**：将获取到的 `X-AUTH-TOKEN` 同时存入 Pinia store (`users.setToken(data.result['X-AUTH-TOKEN'])`) 和本地持久化存储。
2.  **用户信息获取与存储**：调用 `getUserInfos()` 方法从后端获取用户详细信息，并将其存储到 `userStore` 的 `userInfo` 对象中 (`userStore().setUserInfo(data.result)`)。

   ```javascript
   // 登录成功回调示例
   export async function loginSuccessCallBack(data) { //
     const recordFullPagePathStore = recordFullPagePath(); //
     const users = userStore(); //
     // 存储 Token 到 Pinia store 和本地存储
     users.setToken(data.result['X-AUTH-TOKEN']); //
     // 获取用户信息
     getUserInfos(); //
     
     // 页面跳转逻辑... //
   }
   ```

### 2.3 `userStore` 结构参考
`userStore` 大致包含以下结构：

```javascript
// 用户信息 store 结构
userStore: { //
  userInfo: { //
    id: '123456',                                // 用户ID //
    nickName: '张三',                            // 用户昵称 //
    phone: '138****1234',                       // 手机号（脱敏） //
    avatarUrl: '[https://example.com/avatar.png](https://example.com/avatar.png)', // 头像URL //
    sex: '1',                                    // 性别 //
    areaAddr: '北京',                            // 所在地区 //
    balance: 100,                                // 助力值 //
    isLoveAmbassador: 0,                         // 是否为助梦家 //
    totalRechargeBalance: 500,                   // 总充值金额 //
    totalPerformance: 1000,                      // 创业业绩 //
    // 其他用户信息... //
  },
  token: 'xxxxxxxx'  // 用户令牌 //
}
```

## 3. 获取用户信息标准方法

### 3.1 主要获取函数
项目提供统一的用户信息获取函数 **`getUserInfos()`**，该函数位于 `hooks/index.js`。

```javascript
// 获取用户信息
export async function getUserInfos() { //
  if (userStore().token) { //
    let { data } = await uni.http.get(uni.api.getMemberInfo); //
    userStore().setUserInfo(data.result); //
  } else { //
    userStore().clearUserInfo(); //
  }
}
```

### 3.2 `getUserInfos()` 调用时机规范
为确保用户信息的及时性和准确性，应在以下关键时机调用 `getUserInfos()`：
-   **登录成功后**：此操作已内建于 `loginSuccessCallBack` 函数中。
-   **应用启动时**：建议在 `App.vue` 的 `onLaunch` 生命周期函数中调用，以保证应用加载初始用户信息。
-   **用户相关页面显示时**：在涉及展示用户信息的页面的 `onShow` 生命周期函数中调用，确保数据为最新。
-   **用户信息变更后**：例如，用户修改了个人资料并成功保存后，应立即调用以刷新本地状态。

## 4. 在页面及组件中使用用户信息

### 4.1 引入并实例化 `userStore`
在 Vue 组件的 `<script setup>` 中：

```javascript
import { userStore } from '@/store/index.js'; //

// 在 setup 中获取 store 实例
const users = userStore(); //
```

### 4.2 访问用户信息和登录状态
-   **访问用户信息**：通过 `users.userInfo` 对象访问，务必处理属性可能不存在或为空的情况，提供默认值。
    ```javascript
    // 示例：获取用户昵称
    const userName = users.userInfo.nickName || '未登录'; //

    // 示例：获取用户头像
    const avatarUrl = users.userInfo.avatarUrl || '/static/missing-face.png'; //
    ```
-   **判断登录状态**：严格通过 `users.token` 是否存在来判断。
    ```javascript
    // 示例：判断用户是否登录
    const isLoggedIn = !!users.token; //
    ```

### 4.3 页面集成示例
以下是在页面中使用用户信息的完整示例：

```vue
<template>
  <view class="user-info">
    <view v-if="users.token"> <image :src="users.userInfo.avatarUrl || '/static/missing-face.png'"></image> <text>{{ users.userInfo.nickName }}</text> <text>{{ users.userInfo.phone }}</text> </view>
    <view v-else> <text>请登录</text> </view>
  </view>
</template>

<script setup>
import { onShow } from '@dcloudio/uni-app'; //
import { userStore } from '@/store/index.js'; //
import { getUserInfos } from '@/hooks'; //

const users = userStore(); //

onShow(() => { //
  // 页面显示时刷新用户信息
  getUserInfos(); //
});
</script>
```

## 5. 核心开发实践规范

### 5.1 登录前置检查
对于需要用户登录才能访问的页面或执行的操作，必须使用项目提供的 `navToBeforeLogin` 函数进行前置检查和自动跳转。

```javascript
// 跳转前检查登录状态
import { navToBeforeLogin } from '@/hooks'; //

// 示例：跳转到需要登录的页面
function goToUserProfile() { //
  navToBeforeLogin('/pages/userProfile/userProfile'); //
}
```

### 5.2 用户信息实时更新
当用户资料（如昵称、头像等）发生变更并成功提交到后端后，前端必须立即调用 `getUserInfos()` 函数，以同步最新的用户信息到 `userStore`。

```javascript
// 示例：修改用户资料后刷新信息
async function updateUserProfile(profileData) { //
  await uni.http.post(uni.api.completeInformation, profileData); //
  // 更新后刷新用户信息
  getUserInfos(); //
}
```

### 5.3 避免用户信息重复请求
`getUserInfos()` 的调用应遵循“按需刷新”原则，避免在每个组件或页面的 `onLoad` 或 `created` 中无差别调用。优先选择全局（如 `App.vue` 的 `onLaunch`）、特定页面显示时（`onShow`）或信息实际发生变化时（如登录、修改资料）进行调用。

## 6. 重要注意事项

1.  **登录状态的权威判断**：必须以 `users.token` (或 `userStore().token`) 是否有效作为用户是否登录的唯一判断标准，而非 `users.userInfo` 对象或其任何属性。
2.  **界面健壮性**：在模板中渲染 `users.userInfo` 的任何属性时，务必提供默认值或使用可选链 (`?.`)，以防止因数据尚未加载完成或用户未登录导致的渲染错误或页面白屏。
3.  **敏感信息处理**：已知用户敏感信息（如手机号）已在服务端进行了脱敏处理。前端展示时直接使用即可。
4.  **Token 失效的全局处理**：项目的网络请求拦截器已包含对 Token 失效（如后端返回错误码 666）的统一处理逻辑，会自动清理登录状态并引导用户重新登录。业务代码层面无需重复处理此类情况。

## 7. 潜在优化方向 (参考)

以下建议源自原始文档，可作为未来迭代的参考：
1.  **增加用户信息缓存**：可以考虑将用户信息也存储到本地 (`uni.setStorageSync` / `uni.getStorageSync`)，减少请求次数。
2.  **添加用户信息持久化**：目前只有 Token 进行了持久化，可以考虑也对用户信息进行持久化，以改善冷启动时的用户体验。
3.  **增加 Token 自动刷新机制**：当 Token 即将过期时自动刷新，提高用户体验。