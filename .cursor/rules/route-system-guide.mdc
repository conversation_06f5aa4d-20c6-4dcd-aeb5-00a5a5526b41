---
description: uniapp 路由系统 AI 开发指导指南
globs: 
alwaysApply: false
---
# 🧭 uniapp 路由系统 AI 开发指导指南

## 1. 引言

本文档旨在为 AI 开发者提供在当前 uniapp 项目中进行路由相关开发的规范和指导。遵循此指南有助于保持路由系统的一致性、可维护性，并确保用户导航体验的流畅性。

## 2. 页面注册与配置 (`pages.json`)

### 2.1 主要页面路径
AI 在进行页面跳转或路由逻辑处理时，应参考以下已定义的主要页面路径：
-   **首页**: `pages/index/index` (应用启动页)
-   **分类**: `pages/heartPool/heartPool`
-   **个人中心**: `pages/user/user`
-   **商品详情**: `pages/productInfo/productInfo`
-   **确认订单**: `pages/confirmOrder/confirmOrder`
-   **登录**: `pages/login/login`
-   **验证码登录**: `pages/phoneLogin/phoneLogin`

### 2.2 页面配置规范
在新增或修改页面配置时，应注意以下项目特点：
-   **自定义导航栏**: 若页面需要特殊导航栏样式，应配置 `"navigationStyle": "custom"`。
-   **下拉刷新**: 若页面需要下拉刷新功能，应配置 `"enablePullDownRefresh": true"`。
-   **全局导航栏背景色**: 默认为 `#f5f5f5`。

## 3. TabBar 配置

项目的 TabBar 当前包含三个标签页。AI 在涉及 TabBar 相关的跳转或逻辑时，需遵循以下配置：

```json
"tabBar": {
    "color": "#7A7E83",
    "selectedColor": "#000000",
    "borderStyle": "white",
    "backgroundColor": "#ffffff",
    "iconWidth": "30px",
    "list": [
        {
            "pagePath": "pages/index/index",
            "iconPath": "static/tabbar/index_btn.png",
            "selectedIconPath": "static/tabbar/index_btn_sel.png",
            "text": "首页"
        },
        {
            "pagePath": "pages/heartPool/heartPool",
            "iconPath": "static/tabbar/heartPool_btn.png",
            "selectedIconPath": "static/tabbar/heartPool_btn_sel.png",
            "text": "分类"
        },
        {
            "pagePath": "pages/user/user",
            "iconPath": "static/tabbar/user_btn.png",
            "selectedIconPath": "static/tabbar/user_btn_sel.png",
            "text": "个人"
        }
    ]
}
```
**注意**：购物车标签页 (`pages/cart/cart`) 已被注释掉，当前仅首页、分类、个人三个标签页生效。

## 4. 导航方法使用规范

项目已封装多种导航方法，AI 开发时应优先使用这些封装方法以简化操作并统一处理逻辑。

### 4.1 标准导航方法
-   **普通导航 (`navTo`)**: 用于跳转到应用内的非 TabBar 页面。
    ```javascript
    navTo(url, callback) // url: 页面路径, callback: 成功回调
    ```
-   **重定向 (`redTo`)**: 关闭当前页面，跳转到应用内的某个页面。
    ```javascript
    redTo(url) // url: 页面路径
    ```
-   **Tab切换 (`switchTo`)**: 用于跳转到 TabBar 页面，并关闭其他所有非 TabBar 页面。
    ```javascript
    switchTo(url) // url: TabBar 页面路径
    ```

### 4.2 带拦截逻辑的导航
-   **登录拦截导航 (`navToBeforeLogin`)**: 跳转前检查登录状态。
    ```javascript
    navToBeforeLogin(url = '', shoudRecord = false) // url: 目标路径, shoudRecord: 是否记录当前页以便登录后返回
    ```
    具体逻辑见下方“路由拦截与控制”部分。

-   **手机验证拦截 (`authPhone`)**: (根据名称推断，用于需要手机号验证的场景)
    ```javascript
    authPhone(type) // type: 可能用于区分不同的验证场景
    ```

### 4.3 特定业务场景导航
-   **商品详情导航 (`navToGoodDetail`)**: 用于跳转到商品详情页。
    ```javascript
    navToGoodDetail(item) // item: 通常为商品对象，包含ID等信息
    ```
-   **店铺首页导航 (`navToStoreIndex`)**: 用于跳转到店铺首页。
    ```javascript
    navToStoreIndex(item) // item: 通常为店铺对象，包含ID等信息
    ```

### 4.4 页面返回控制
-   **智能返回上一页 (`toUpPage`)**:
    ```javascript
    toUpPage() // 如果存在上一页则 uni.navigateBack()，否则 uni.switchTab({ url: '/pages/index/index' })
    ```
    此方法应作为标准的返回操作被使用。

## 5. 路由拦截与控制

AI 开发需理解并正确使用项目中已实现的路由拦截机制。

### 5.1 登录拦截 (`navToBeforeLogin`)
此函数位于 `hooks/index.js`，核心逻辑如下：
1.  检查 `userStore().token` 是否存在。
2.  **未登录**:
    * 如果 `shoudRecord` 为 `true`，且当前页面非登录相关页面，则获取当前页面完整路径并存入 `recordFullPagePathStore`。
    * 使用 `uni.reLaunch({ url: '/pages/login/login' })` 跳转到登录页。
3.  **已登录**:
    * 如果 `url` 参数有效，则调用 `navTo(url)` 进行跳转。

### 5.2 登录后返回原页面 (`toLoginAndBackPage`)
此函数用于实现“跳转登录，成功后返回原访问页面”的逻辑：
1.  获取当前页面完整路径。
2.  如果当前路径非登录相关页面，则将该路径存入 `recordFullPagePathStore`。
3.  使用 `uni.reLaunch({ url: '/pages/login/login' })` 跳转到登录页。
    (AI 需知晓：登录成功后，系统应从 `recordFullPagePathStore` 读取路径并执行返回操作。)

### 5.3 全局 Promise 结果拦截
项目在 `uni.promisify.adaptor.js` 中通过 `uni.addInterceptor` 添加了全局 Promise 返回值拦截器。
-   该拦截器主要处理 `uni` API 调用返回的 Promise，使其符合 `res[0]` 为错误对象、`res[1]` 为成功结果的约定。
-   AI 在直接使用 `uni` 的异步 API 时，应预期此种返回结构，或通过已封装的方法间接使用。

## 6. 状态保持与路由
-   项目通过 Pinia 状态管理（如 `recordFullPagePathStore`）记录页面路径，以支持登录后返回原页面等功能。AI 在开发类似需跨页面状态的导航功能时，可参考此模式。

## 7. 页面预加载策略
-   当前项目中未发现明确的、全局性的页面内容预加载策略（如 `onLoadPreload`）。
-   仅在 `zebra-swiper` 组件中存在图片预加载配置 (`preloadImages: true`)。
-   AI 在开发时，除非有新的全局策略引入，否则不应假设页面有预加载行为。

## 8. 总结性规范
1.  **路由结构**: 严格遵循 `pages.json` 的 uni-app 标准路由配置方式。
2.  **导航栏**: 根据页面需求，合理选择使用默认导航栏或自定义导航栏 (`"navigationStyle": "custom"`)。
3.  **拦截机制**: 充分利用已有的登录拦截 (`navToBeforeLogin`) 和 Promise 结果拦截机制。
4.  **导航封装**: 优先调用项目 `hooks` 中封装的导航方法，而不是直接使用 `uni.navigateTo` 等原生 API，以保证逻辑统一和代码简洁。
5.  **状态保持**: 对于需要登录后返回或有类似跨页面记忆需求的场景，应使用 Pinia store (如 `recordFullPagePath`) 进行路径或其他必要信息的记录和恢复。