---
description: 网络请求指导规范
globs: 
alwaysApply: false
---
# 🌐 网络请求指导规范文档

## 1. 引言

本文档旨在规范项目中的网络请求行为，确保代码的一致性、可维护性及健壮性。所有网络请求相关开发均应遵循此规范。

## 2. 核心请求库

项目统一使用 **`luch-request`** 作为基础 HTTP 请求库。这是一个专为 uni-app 设计的、类似 axios 的请求库。

## 3. 关键文件结构

网络请求的核心逻辑主要分布在以下文件中：

-   `hooks/request.js`: 主要的请求封装、拦截器配置等。
-   `hooks/api.js`: API 接口路径的统一定义文件。
-   `js_sdk/luch-request/`: `luch-request` 库的源码。

## 4. 请求配置

### 4.1. 基础配置

所有请求配置应在 `hooks/request.js` 中通过 `http.setConfig` 进行初始化。

-   **基础 URL (`baseURL`)**: 应通过 `uni.env.REQUEST_URL` 从环境配置中动态获取。
    ```javascript
    // hooks/request.js
    http.setConfig(config => {
      config.baseURL = uni.env.REQUEST_URL; //
      return config;
    });
    ```
-   **默认请求头 (`header`)**:
    -   `content-type`: 默认为 `application/x-www-form-urlencoded`。
    ```javascript
    // hooks/request.js
    http.setConfig(config => {
      // ...其他配置
      config.header = {
        'content-type': 'application/x-www-form-urlencoded' //
      };
      return config;
    });
    ```
-   **默认超时时间 (`timeout`)**: `60000` 毫秒 (60秒)。
-   **默认数据类型 (`dataType`)**: `json`。
-   **默认响应类型 (`responseType`)**: `text`。

### 4.2. 环境配置

API 的基础 URL 等环境变量通过 `uni.env` 全局对象访问，其源配置通常位于 `hooks/config.js` (根据分析报告推断，实际文件名可能按项目约定)。
例如，生产环境 API 地址: REQUEST_URL: 'https://www.zehuaiit.top/heartful-mall-api/' //

## 5. 拦截器

### 5.1. 请求拦截器 (`http.interceptors.request`)

必须配置请求拦截器，用于在请求发送前进行统一处理。

-   **添加平台标识**: 使用条件编译为不同平台添加 `softModel` 请求头。
    ```javascript
    // #ifdef MP-WEIXIN
    'softModel': 0, //
    // #endif
    // #ifdef H5
    'softModel': 3, //
    // #endif
    ```
-   **添加通用业务参数**: 如 `sysUserId`, `tMemberId`, `longitude`, `latitude`, `shareDiscountRatio` 等，根据实际业务逻辑从状态管理 (Pinia stores) 中获取。
-   **添加认证凭证**: `X-AUTH-TOKEN` 必须从 `uni.getStorageSync('token')` 获取并添加到请求头中。
    ```javascript
    // hooks/request.js
    http.interceptors.request.use(config => {
      config.header = {
        // #ifdef MP-WEIXIN
        'softModel': 0, //
        // #endif
        // #ifdef H5
        'softModel': 3, //
        // #endif
        sysUserId: getAllBeSharedStore.info?.sysUserId || setSaleChannelStoreInfoStore.sysUserId ||
          setSaleChannelStoreInfoStore.intoStoreForSysUserId, //
        tMemberId: getAllBeSharedStore.info?.tMemberId || '', //
        longitude: locationStore.info.longitude, //
        latitude: locationStore.info.latitude, //
        shareDiscountRatio: getAllBeSharedStore.shareDiscountRatio || '', //
        "X-AUTH-TOKEN": uni.getStorageSync('token') || '', //
        ...config.header,
      };
      return config;
    }, config => {
      return Promise.reject(config);
    });
    ```

### 5.2. 响应拦截器 (`http.interceptors.response`)

必须配置响应拦截器，用于在接收到响应后进行统一处理。

-   **Token 失效处理 (code `666`)**:
    1.  调用 `store.clearUserInfo()` 清除用户信息。
    2.  调用 `store.clearToken()` 清除 Token。
    3.  调用 `toLoginAndBackPage()` 跳转到登录页并携带当前页面路径。
    4.  延时隐藏加载提示 `uni.hideLoading()`。
    5.  必须 `return Promise.reject(response)` 中断后续操作。
    ```javascript
    if (response.data?.code == 666) { //
      store.clearUserInfo(); //
      store.clearToken(); //
      toLoginAndBackPage(); //
      setTimeout(() => {
        uni.hideLoading(); //
      }, 500);
      return Promise.reject(response); //
    }
    ```
-   **服务器内部错误处理 (code `500`)**:
    1.  使用 `uni.showToast` 显示错误信息，优先使用 `response.data?.message`，若无则显示通用提示 "网络异常，请稍后再试"。
    2.  必须 `return Promise.reject(response)`。
-   **其他请求失败处理 (`response.statusCode != 200`)**:
    1.  使用 `uni.showToast` 显示错误信息。
    2.  必须 `return Promise.reject(response)`。
-   **成功响应**: 若无特定错误码，直接 `return response`。

## 6. API 接口管理

-   **集中管理**: 所有 API 接口路径必须在 `hooks/api.js` 文件中以对象形式集中定义和管理。
    ```javascript
    // hooks/api.js
    const api = {
      loginByCode: '/front/weixin/loginByCode', //
      indexNew: '/front/index/indexNew', //
      // ... 更多 API
    };
    ```
-   **全局挂载**: API 对象应挂载到 `uni` 对象上，以便全局通过 `uni.api.接口名` 的方式调用。
    ```javascript
    uni.api = api; //
    ```
-   **模块化建议**: 对于大型项目，建议将 API 按功能模块进行分类，以提高可维护性。

## 7. 请求调用方式

-   **全局实例**: 所有 HTTP 请求必须通过全局挂载的 `uni.http` 实例发起。
-   **Promise 风格**: 遵循 `luch-request` 的 Promise API 风格。
-   **`async/await`**: 推荐并优先使用 `async/await` 语法糖处理异步请求，使代码更简洁易读。

    **GET 请求示例:**
    ```javascript
    // hooks/index.js 或页面/组件中
    async function getUserInfos() {
      if (userStore().token) {
        try {
          let { data } = await uni.http.get(uni.api.getMemberInfo); //
          userStore().setUserInfo(data.result); //
        } catch (error) {
          // 处理错误
          console.error(error);
        }
      } else {
        userStore().clearUserInfo();
      }
    }
    ```
    **POST 请求示例:**
    ```javascript
    // pages/login/login.vue
    async function wxLogin(e) {
      uni.showLoading({
        title: '登录中...',
        mask: true
      });
      try {
        let { data } = await uni.http.post(uni.api.loginByCode, { //
          ...e.detail,
          code: code.value
        });
        uni.hideLoading();
        loginSuccessCallBack(data); //
      } catch (e) {
        getCode();
        // 处理错误
      }
    }
    ```
-   **支持的方法**: 包括 `get`, `post`, `put`, `delete`, `upload`, `download` 等。

## 8. 平台差异化处理

-   对于请求相关的平台差异，如请求头中的 `softModel`，必须使用条件编译 (`// #ifdef MP-WEIXIN`, `// #ifdef H5` 等) 进行处理。

## 9. 错误处理与用户提示

-   对于用户可见的错误提示，应统一使用 `uni.showToast({ icon: 'none', title: '错误信息' })`。
-   错误信息应尽量友好，优先使用后端返回的 `message` 字段。

## 10. 安全规范

-   **HTTPS**: 生产环境必须使用 HTTPS 协议进行数据传输。
-   **敏感信息**: 确保不在请求中明文传输敏感信息。
-   **Token 刷新机制**: 建议规划并实现 Token 的自动刷新机制，以提升用户体验和安全性。

## 11. 其他开发建议

根据项目分析报告中的建议，以下方面可作为未来优化的方向：

-   **请求取消机制**: 考虑实现请求取消功能，避免因页面快速切换等原因导致的无效请求或潜在冲突。
-   **请求重试机制**: 对于网络抖动或特定可重试错误，可考虑加入请求自动重试逻辑。
-   **Mock 数据**: 为便于前端独立开发和测试，建议引入 Mock 数据方案。
-   **请求日志**: 可考虑添加更完善的请求日志记录机制，方便开发调试和线上问题排查。