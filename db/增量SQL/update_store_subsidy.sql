-- 为member_account_capital表添加店铺补助金相关字段
ALTER TABLE `member_account_capital` ADD COLUMN `expire_time` datetime DEFAULT NULL COMMENT '补助金过期时间';
ALTER TABLE `member_account_capital` ADD COLUMN `subsidy_store_id` varchar(50) DEFAULT NULL COMMENT '接收补助的店铺ID';
ALTER TABLE `member_account_capital` ADD COLUMN `remaining_amount` decimal(10,2) DEFAULT NULL COMMENT '剩余可用金额';

-- 更新member_deal_type数据字典，添加"50"对应"店铺补助金"
INSERT INTO `sys_dict_item` (`id`,`dict_id`,`item_text`,`item_value`,`description`,`sort_order`,`status`,`create_by`,`create_time`,`update_by`,`update_time`) 
SELECT UUID(), id, '店铺补助金', '50', '店铺补助金', 50, 1, 'admin', NOW(), NULL, NULL FROM `sys_dict` WHERE `dict_code` = 'member_deal_type' LIMIT 1; 