-- 为member_account_capital表添加店铺补助金状态字段
ALTER TABLE `member_account_capital` ADD COLUMN `store_subsidy_status` varchar(2) DEFAULT '0' COMMENT '店铺补助金状态；0=可用；1=已过期';

-- 更新已有记录的状态
-- 将过期时间小于当前时间的记录状态设置为已过期(1)
UPDATE `member_account_capital` SET `store_subsidy_status` = '1' 
WHERE `pay_type` = '50' AND `expire_time` < NOW();

-- 将过期时间大于等于当前时间或过期时间为空的记录状态设置为可用(0)
UPDATE `member_account_capital` SET `store_subsidy_status` = '0' 
WHERE `pay_type` = '50' AND (`expire_time` >= NOW() OR `expire_time` IS NULL); 