<template>
	<view class="quick-checkout-bar" v-if="show">
		<view class="quick-checkout-content">
			<!-- 左侧购物车图标和数量 -->
			<view class="cart-icon-section">
				<view class="cart-icon-wrapper">
					<image src="/static/cart/cart.png" class="cart-icon"></image>
					<view class="cart-badge" v-if="totalCount > 0">
						<text class="cart-badge-text">{{ totalCount > 99 ? '99+' : totalCount }}</text>
					</view>
				</view>
			</view>
			
			<!-- 中间总金额显示 -->
			<view class="total-amount-section">
				<view class="amount-label">合计</view>
				<view class="amount-value">
					<image src="@/static/index/i_1.png" class="price-icon"></image>
					<text class="amount-text">{{ totalAmount.toFixed(2) }}</text>
				</view>
			</view>
			
			<!-- 右侧去兑换按钮 -->
			<view class="checkout-button" @click="handleCheckout">
				<text class="checkout-text">去兑换</text>
			</view>
		</view>
	</view>
</template>

<script setup>
	import { computed } from 'vue'
	
	const props = defineProps({
		// 是否显示快捷结算栏
		show: {
			type: Boolean,
			default: false
		},
		// 总数量
		totalCount: {
			type: Number,
			default: 0
		},
		// 总金额
		totalAmount: {
			type: Number,
			default: 0
		},
		// 当前页面添加的商品信息
		currentPageItems: {
			type: Object,
			default: () => ({})
		}
	})
	
	const emit = defineEmits(['checkout'])
	
	// 处理去兑换点击
	const handleCheckout = () => {
		if (props.totalCount <= 0) {
			uni.showToast({
				title: '购物车为空',
				icon: 'none'
			})
			return
		}
		
		// 将当前页面的商品信息传递给父组件
		const selectedItems = Object.entries(props.currentPageItems).map(([goodId, item]) => ({
			goodId,
			cartItemId: item.cartItemId,
			quantity: item.quantity,
			price: item.price
		}))
		
		emit('checkout', selectedItems)
	}
</script>

<style lang="scss" scoped>
	.quick-checkout-bar {
		position: fixed;
		bottom: 0;
		left: 0;
		right: 0;
		z-index: 999;
		background: #ffffff;
		border-top: 2rpx solid #f0f0f0;
		box-shadow: 0 -4rpx 12rpx rgba(0, 0, 0, 0.1);
		padding-bottom: constant(safe-area-inset-bottom);
		padding-bottom: env(safe-area-inset-bottom);
		
		.quick-checkout-content {
			display: flex;
			align-items: center;
			padding: 24rpx 32rpx;
			height: 120rpx;
			
			.cart-icon-section {
				flex-shrink: 0;
				margin-right: 32rpx;
				
				.cart-icon-wrapper {
					position: relative;
					width: 72rpx;
					height: 72rpx;
					display: flex;
					align-items: center;
					justify-content: center;
					
					.cart-icon {
						width: 48rpx;
						height: 48rpx;
					}
					
					.cart-badge {
						position: absolute;
						top: -8rpx;
						right: -8rpx;
						min-width: 32rpx;
						height: 32rpx;
						background: linear-gradient(135deg, #FF6B35 0%, #FF8F6B 100%);
						border-radius: 16rpx;
						display: flex;
						align-items: center;
						justify-content: center;
						padding: 0 8rpx;
						box-shadow: 0 2rpx 6rpx rgba(255, 107, 53, 0.3);
						
						.cart-badge-text {
							font-size: 20rpx;
							color: #ffffff;
							font-weight: 600;
							line-height: 1;
						}
					}
				}
			}
			
			.total-amount-section {
				flex: 1;
				display: flex;
				flex-direction: column;
				align-items: flex-start;
				
				.amount-label {
					font-size: 24rpx;
					color: #999999;
					margin-bottom: 8rpx;
				}
				
				.amount-value {
					display: flex;
					align-items: center;
					
					.price-icon {
						width: 28rpx;
						height: 28rpx;
						margin-right: 8rpx;
					}
					
					.amount-text {
						font-size: 36rpx;
						color: #FF6B35;
						font-weight: 600;
					}
				}
			}
			
			.checkout-button {
				flex-shrink: 0;
				background: linear-gradient(135deg, #22A3FF 0%, #4FC3F7 100%);
				border-radius: 44rpx;
				padding: 0 48rpx;
				height: 88rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				box-shadow: 0 4rpx 12rpx rgba(34, 163, 255, 0.3);
				transition: all 0.2s ease;
				
				&:active {
					transform: scale(0.95);
					opacity: 0.9;
				}
				
				.checkout-text {
					font-size: 32rpx;
					color: #ffffff;
					font-weight: 600;
				}
			}
		}
	}
</style> 