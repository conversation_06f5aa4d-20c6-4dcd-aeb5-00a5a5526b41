<template>
	<view class="specificationWrap">
		<view class="specificationWrap-proTitle">
			{{resultInfo.goodName}}
		</view>
		<view class="specificationWrap-top">
			<image
				:src="imgUrl + (resultInfo.specificationPicture || (resultInfo.mainPicture && resultInfo.mainPicture.length > 0 && resultInfo.mainPicture[0]))"
				mode=""></image>
			<view>

				<view>
					<image src="@/static/index/i_1.png" mode=""></image>
					{{resultInfo.price || resultInfo.smallPrice}}
				</view>
				<view>
					已选择：<text v-for="item in selectSpecificationList" :key="item"
						style="margin-right: 15rpx;">{{item}}</text>
				</view>
			</view>
		</view>
		<!-- <scroll-view scroll-y="true" style="width: 100%;height: 500rpx;" v-if="showSpecSelect">
			<view>
				<view class="specificationWrap-title">
					规格
				</view>
				<view class="specificationWrap-selectedWrap">
					<view v-for="(it,id) in specificationList" :key='it' @click="selectSpecification(0,it)"
						:class="{selected:selectSpecificationList[0] == it.specification}">
						{{it.specification}}
						<view class="specificationWrap-selectedWrap-qh" v-if="!it.repertory || it.repertory <= 0">
							缺货
						</view>
					</view>
				</view>
			</view>
		</scroll-view> -->

		<scroll-view scroll-y="true" style="width: 100%;height: 500rpx;" v-if="showSpecSelect">
			<view v-for="(it,id) in specificationList" :key='it.CommodityStyle'>
				<view class="specificationWrap-title">
					{{it.CommodityStyle}}
				</view>
				<view class="specificationWrap-selectedWrap">
					<view v-for="(item) in it.classification" :key='item+it'
						@click="getSpecStock(item.value) > 0 ? selectSpecification(id,item.value) : null"
						:class="{
							selected: selectSpecificationList[id] == item.value,
							'no-stock': getSpecStock(item.value) <= 0,
							'low-stock': getSpecStock(item.value) > 0 && getSpecStock(item.value) < 10
						}"
						class="spec-option">
						<view class="spec-option-name">{{item.value}}</view>
						<view class="spec-option-info">
							<view class="spec-option-price" v-if="getSpecPrice(item.value)">
								<image src="@/static/index/i_1.png" class="price-icon"></image>
								{{getSpecPrice(item.value)}}
							</view>
							<view class="spec-option-stock" 
								:class="{ 
									'stock-low': getSpecStock(item.value) > 0 && getSpecStock(item.value) < 10,
									'stock-zero': getSpecStock(item.value) <= 0 
								}">
								库存 {{ getSpecStock(item.value) }}
							</view>
						</view>
						<view class="specificationWrap-selectedWrap-qh" v-if="getSpecStock(item.value) <= 0">
							缺货
						</view>
					</view>
				</view>
			</view>
		</scroll-view>


		<view class="specificationWrap-num" v-if="props.showCountIpt">
			<view class="specificationWrap-title" style='margin:0;margin-right: 30rpx;'>
				数量
			</view>
			<view>
				<numberInput v-model="quality" :disabled="inputDisabled" :min="1" :max="resultInfo.repertory || 999">
				</numberInput>
			</view>
		</view>
	</view>
</template>

<script setup>
	import {
		computed,
		ref,
		watch
	} from "vue";
	import {
		clone,
		parseImgurl
	} from "@/utils";
	const imgUrl = uni.env.IMAGE_URL
	const props = defineProps({
		info: {
			type: Object,
			default () {
				return {}
			}
		},
		//是否需要在请求接口 findGoodListByGoodId 即商品详情接口
		hasRequest: {
			type: Boolean,
			default: true
		},
		//输入框是否可输入 false 可输入 true 不可
		inputDisabled: {
			type: Boolean,
			default: false
		},
		//是否显示输入框购买数量
		showCountIpt: {
			type: Boolean,
			default: true
		}
	})
	//原始商品信息
	let info = ref({});
	//省id 默认福建 用作运费
	let areaIds = ref(1160);
	//规格列表
	let specificationList = ref([]);
	//选择的规格(数组内容代表规格名,所在位置索引即为大分类的索引)
	let selectSpecificationList = ref([])

	//数量
	let quality = ref(1);
	//当前选中规格对应的数据
	let selectSpceDatas = computed(() => {
		console.log('计算selectSpceDatas:', {
			specifications: info.value?.specifications,
			selectSpecificationList: selectSpecificationList.value
		})
		
		if (info.value?.specifications?.length && selectSpecificationList.value?.length && selectSpecificationList.value.every(i => i)) {
			const st = selectSpecificationList.value.join(',')
			console.log('查找规格:', st)
			const found = info.value.specifications.find(i => i.specification === st)
			console.log('找到的规格数据:', found)
			return found || {}
		}
		
		// 如果没有选择规格，但是有规格数据，返回第一个规格作为默认
		if (info.value?.specifications?.length > 0) {
			console.log('使用第一个规格作为默认:', info.value.specifications[0])
			return info.value.specifications[0]
		}
		
		return {}
	})
	//loading加载
	let loading = ref(false)
	//判断是否显示规格选择
	const showSpecSelect = computed(() => {
		if (specificationList.value.length <= 0) return false
		if (specificationList.value.length == 1 && specificationList.value[0]?.specification == '无') return false;
		return true;
	})


	//提交接口的specification字段值
	const specification = computed(() => {
		return selectSpecificationList.value.length > 0 ? selectSpecificationList.value
			.join(',') : '无'
	})
	//提交接口的specificationId字段值
	const specificationId = computed(() => {
		return selectSpceDatas.value?.id || ''
	})
	//最终的商品信息
	const resultInfo = computed(() => {
		return {
			...info.value,
			...selectSpceDatas.value
		}
	})
	const requestInfo = computed(() => {
		const propsInfo = props.info
		return {
			goodId: propsInfo.goodListId || propsInfo.goodId || propsInfo.id,
			isPlatform: propsInfo.isPlatform || '0',
			areaIds: areaIds.value
		}
	})

	defineExpose({
		requestInfo,
		quality,
		specification,
		specificationId,
		loading
	})
	watch(() => {
		return {
			...props.info
		}
	}, async () => {
		const propsInfo = props.info
		if (!propsInfo.id) return;
		quality.value = 1
		if (propsInfo.isWholesale + '' === '1') {
			quality.value = propsInfo.minWholesaleNum || propsInfo.speMinWholesaleNum || 1;
		}

		let goodsInfo = propsInfo
		if (props.hasRequest) {
			loading.value = true
			//商品详情
			let {
				data: storeInfo
			} = await uni.http.get(uni.api.findGoodListByGoodId, {
				params: {
					...requestInfo.value,
					isTransition: 1
				}

			})
			goodsInfo = storeInfo.result.goodinfo
			loading.value = false
		}
		//规格数据处理
		if (goodsInfo.specification) {
			if (goodsInfo.specification == '无') {
				specificationList.value = []
			} else {
				try {
					let result = JSON.parse(goodsInfo.specification);
					result.forEach((i, x) => {
						selectSpecificationList.value[x] = ''
					})
					specificationList.value = result;
				} catch (e) {
					console.error('解析规格问题' + e)
				}
			}
		} else {
			specificationList.value = []
		}
		console.log('specificationList:', specificationList.value)
		console.log('goodsInfo.specifications:', goodsInfo.specifications)

		//图片处理
		if (goodsInfo.mainPicture) {
			goodsInfo.mainPicture = parseImgurl(goodsInfo.mainPicture)
		}
		
		info.value = goodsInfo;
		
		// 规格选择逻辑优化：如果有规格明细数据，默认选择第一个规格
		if (goodsInfo.specifications && goodsInfo.specifications.length > 0) {
			// 如果有最小价格规格，优先选择
			if (goodsInfo.smallSpecification) {
				let smaSz = goodsInfo.smallSpecification.split(',');
				selectSpecificationList.value = smaSz;
				console.log('使用smallSpecification:', smaSz)
			} else {
				// 否则默认选择第一个规格
				const firstSpec = goodsInfo.specifications[0];
				if (firstSpec && firstSpec.specification) {
					const specParts = firstSpec.specification.split(',');
					selectSpecificationList.value = specParts;
					console.log('默认选择第一个规格:', specParts)
				}
			}
		} else {
			selectSpecificationList.value = []
		}

	}, {
		immediate: true,
		deep: true
	})

	async function quantityChange(count, item) {
		quality.value = count
	}

	//选择规格  firIdx:大分类坐标  it小分类名称
	async function selectSpecification(firIdx, it) {
		selectSpecificationList.value[firIdx] = it
	}

	// 根据规格名称获取对应的价格
	function getSpecPrice(specName) {
		if (!info.value?.specifications?.length) {
			return null
		}
		
		// 查找包含该规格名称的规格明细
		const matchingSpecs = info.value.specifications.filter(spec => {
			return spec.specification && spec.specification.includes(specName)
		})
		
		console.log('查找规格价格:', specName, matchingSpecs)
		
		if (matchingSpecs.length === 0) {
			return null
		}
		
		if (matchingSpecs.length === 1) {
			return matchingSpecs[0].price
		}
		
		// 如果有多个匹配的规格，显示价格范围
		const prices = matchingSpecs.map(spec => parseFloat(spec.price)).sort((a, b) => a - b)
		const minPrice = prices[0]
		const maxPrice = prices[prices.length - 1]
		
		if (minPrice === maxPrice) {
			return minPrice.toFixed(2)
		} else {
			return `${minPrice.toFixed(2)}-${maxPrice.toFixed(2)}`
		}
	}

	// 根据规格名称获取对应的库存
	function getSpecStock(specName) {
		if (!info.value?.specifications?.length) {
			return 0
		}
		
		// 查找包含该规格名称的规格明细
		const matchingSpecs = info.value.specifications.filter(spec => {
			return spec.specification && spec.specification.includes(specName)
		})
		
		if (matchingSpecs.length === 0) {
			return 0
		}
		
		// 返回所有匹配规格的库存总和
		return matchingSpecs.reduce((total, spec) => {
			return total + (parseInt(spec.stock) || 0)
		}, 0)
	}
</script>

<style lang="scss">
	.specificationWrap {
		width: 750rpx;
		border-radius: 32rpx 32rpx 0 0;
		background-color: white;
		padding: 32rpx;

		>view {
			margin-bottom: 30rpx;
		}



		&-proTitle {
			color: #000000;
			font-size: 28rpx;
			text-align: center;
		}

		&-wholeSaleWrap {
			color: #999999;
			font-size: 27rpx;
			margin-left: 15rpx;
		}

		&-num {
			display: flex;
			align-items: center;
			justify-content: flex-end;
			width: 100%;

		}

		&-selectedWrap {
			display: flex;
			flex-wrap: wrap;

			>view {
				min-width: 116rpx;
				margin-right: 20rpx;
				margin-bottom: 40rpx;
				height: 61rpx;
				font-size: 26rpx;
				color: #666666;
				line-height: 0;
				padding: 0 32rpx;
				background: #F5F7F8;
				display: flex;
				align-items: center;
				justify-content: center;
				border-radius: 30rpx;
				position: relative;
			}

			.spec-option {
				flex-direction: column;
				height: auto !important;
				padding: 16rpx 24rpx !important;
				min-height: 100rpx;
				line-height: 1.2 !important;
				transition: all 0.2s ease;

				.spec-option-name {
					font-size: 26rpx;
					color: #666666;
					margin-bottom: 8rpx;
					text-align: center;
				}

				.spec-option-info {
					display: flex;
					flex-direction: column;
					align-items: center;
					gap: 4rpx;
				}

				.spec-option-price {
					display: flex;
					align-items: center;
					font-size: 22rpx;
					color: #FF6B35;
					font-weight: 600;

					.price-icon {
						width: 20rpx;
						height: 20rpx;
						margin-right: 4rpx;
					}
				}

				.spec-option-stock {
					font-size: 20rpx;
					color: #22A3FF;
					font-weight: 500;

					&.stock-low {
						color: #FF8F00;
					}

					&.stock-zero {
						color: #FF4757;
					}
				}

				&.selected {
					.spec-option-name {
						color: #1A69D1;
					}
					
					.spec-option-price {
						color: #1A69D1;
					}

					.spec-option-stock {
						color: #1A69D1;

						&.stock-low {
							color: #FF8F00;
						}

						&.stock-zero {
							color: #FF4757;
						}
					}
				}

				&.no-stock {
					opacity: 0.5;
					pointer-events: none;
					background-color: #F0F0F0 !important;

					.spec-option-name {
						color: #999999;
					}

					.spec-option-price {
						color: #999999;
					}
				}

				&.low-stock {
					border: 2rpx solid #FF8F00;
				}
			}

			.selected {
				background: #E9F3FF;
				border: 1rpx solid #1A69D1;
				color: #1A69D1;
			}

			&-qh {
				position: absolute;
				width: 100%;
				height: 100%;
				top: 0;
				left: 0;
				background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(240, 240, 240, 0.9) 100%);
				display: flex;
				align-items: center;
				justify-content: center;
				font-size: 24rpx;
				color: #FF4757;
				font-weight: 600;
				border-radius: 30rpx;
				backdrop-filter: blur(2rpx);
				z-index: 3;
				
				&::before {
					content: "⛔ ";
					margin-right: 4rpx;
					font-size: 20rpx;
				}
				
				&::after {
					content: "";
					position: absolute;
					top: 50%;
					left: 50%;
					width: 2rpx;
					height: 80%;
					background: #FF4757;
					transform: translate(-50%, -50%) rotate(45deg);
					opacity: 0.3;
				}
			}
		}

		&-title {
			font-weight: bold;
			font-size: 28rpx;
			color: #333333;
			text-align: justify;
			margin-bottom: 16rpx;
		}

		&-top {
			width: 100%;
			display: flex;
			align-items: center;
			height: 185rpx;

			>image:nth-child(1) {
				width: 185rpx;
				height: 185rpx;
				border-radius: 14rpx;
				margin-right: 16rpx;
			}

			>view:nth-child(2) {
				flex: 1;
				height: 80%;
				display: flex;
				flex-direction: column;
				justify-content: space-between;

				>view:nth-child(1) {
					display: flex;
					align-items: center;
					color: #E6A600;
					font-size: 28rpx;

					>image {
						width: 40rpx;
						height: 40rpx;
						margin-right: 10rpx;

					}
				}

				>view:nth-child(2) {
					font-size: 24rpx;
					color: #666666;
					line-height: 24rpx;
				}


			}
		}
	}
</style>