# 八闽助业集市商家端小程序用户手册

## 前言

（请在此处根据您的需求填写前言内容）

## 目录

1.  入门指南
    * 1.1 小程序获取与安装
    * 1.2 账号登录
    * 1.3 界面导航与基本操作
    * 1.4 店铺信息设置
2.  首页与数据概览
    * 2.1 首页布局与功能介绍
    * 2.2 账户余额与资金概览
    * 2.3 销售数据与业绩统计
    * 2.4 扫码功能使用
3.  商品管理
    * 3.1 商品列表查看与筛选
    * 3.2 商品上架与下架
    * 3.3 商品库存与价格调整
    * 3.4 商品搜索与批量操作
4.  订单管理
    * 4.1 订单列表与状态分类
    * 4.2 订单详情查看
    * 4.3 订单发货操作
    * 4.4 物流信息管理
    * 4.5 订单搜索与筛选
5.  资金管理
    * 5.1 账户余额查看
    * 5.2 资金明细查询
    * 5.3 提现操作指南
    * 5.4 账单与结算记录
6.  个人中心
    * 6.1 个人信息查看与修改
    * 6.2 店铺信息管理
    * 6.3 安全设置
    * 6.4 退出登录操作 (注：此部分在 "八闽助业集市商家端小程序用户手册实施计划.md" 中提及，但在 "6.个人中心.md" 文件中未明确详细步骤，请根据实际功能补充)
7.  常见问题与故障排除
    * (注：此模块在 "八闽助业集市商家端小程序用户手册实施计划.md" 中规划，请根据实际情况补充详细内容)
    * 7.1 登录问题解决
    * 7.2 商品管理常见问题
    * 7.3 订单处理常见问题
    * 7.4 资金相关常见问题
    * 7.5 系统错误处理指南

附录：常见问题汇总
(注：此部分在 "八闽助业集市商家端小程序用户手册实施计划.md" 中规划，请根据实际情况补充详细内容)

---

## 1. 入门指南

### 1.1 小程序获取与安装

#### 功能概述
八闽助业集市商家端小程序是专为商家用户设计的管理工具，通过该小程序，商家可以随时随地管理店铺、处理订单、查看销售数据和资金明细。本节将介绍如何获取和安装该小程序。

#### 操作步骤
1.  **打开微信**：在手机上打开微信应用。
2.  **搜索小程序**：点击微信首页右上角的"搜索"图标，在搜索框中输入"八闽助业集市商家端"。
    ![搜索小程序](images/入门指南/小程序获取-1.png)
3.  **进入小程序**：在搜索结果中找到"八闽助业集市商家端"小程序，点击进入。
    ![进入小程序](images/入门指南/小程序获取-2.png)
4.  **添加到我的小程序**：首次使用时，建议将小程序添加到"我的小程序"列表中，方便下次快速访问。
    * 点击小程序右上角的"..."按钮
    * 选择"添加到我的小程序"选项
    ![添加到我的小程序](images/入门指南/小程序获取-3.png)
5.  **创建桌面快捷方式**（可选）：
    * 点击小程序右上角的"..."按钮
    * 选择"添加到桌面"选项
    * 按照提示完成操作
    ![创建桌面快捷方式](images/入门指南/小程序获取-4.png)

#### 注意事项
* 确保您的微信版本为最新版本，以获得最佳使用体验
* 小程序无需单独安装，直接在微信中搜索使用即可
* 如遇到搜索不到小程序的情况，可以联系平台客服获取小程序码

### 1.2 账号登录

#### 功能概述
八闽助业集市商家端小程序采用账号密码登录方式，商家需要使用平台分配的账号进行登录。本节将介绍如何登录商家端小程序。

#### 重要说明
**商家端小程序没有注册入口，所有商家账号均由平台管理员在后台预先创建，系统会根据商家提供的手机号自动分配商家管理员账号。** 如需开通商家账号，请联系平台客服或运营人员。

#### 操作步骤
1.  **打开登录页面**：进入小程序后，系统会自动跳转到登录页面。
    ![登录页面](images/入门指南/账号登录-1.png)
2.  **输入账号**：在账号输入框中输入平台分配的账号（通常为手机号）。
    ![输入账号](images/入门指南/账号登录-2.png)
3.  **输入密码**：在密码输入框中输入账号对应的密码。
    * 如果是首次登录，请使用平台分配的初始密码
    * 可以点击密码框右侧的眼睛图标切换密码显示/隐藏状态
    ![输入密码](images/入门指南/账号登录-3.png)
4.  **记住账号**（可选）：如果希望下次登录时自动填充账号，可以勾选"记住账号"选项。
    ![记住账号](images/入门指南/账号登录-4.png)
5.  **点击登录**：填写完账号密码后，点击"安全登录"按钮进行登录。
    ![点击登录](images/入门指南/账号登录-5.png)
6.  **登录成功**：登录成功后，系统会自动跳转到首页。

#### 注意事项
* 如果忘记密码，请联系平台客服或运营人员进行重置
* 为保障账号安全，建议定期修改密码
* 请勿将账号密码泄露给他人，以免造成不必要的损失
* 如遇到登录异常情况，可尝试清除微信缓存后重新登录

### 1.3 界面导航与基本操作

#### 功能概述
八闽助业集市商家端小程序采用底部标签栏导航设计，方便商家快速切换不同功能模块。本节将介绍小程序的基本界面结构和导航操作。

#### 底部标签栏导航
小程序底部设有5个主要功能入口，分别是：

1.  **首页**：展示账户余额、销售概况、订单状态和热销商品等信息。
    ![首页](images/入门指南/界面导航-1.png)
2.  **资金**：查看资金明细、提现记录等财务相关信息。
    ![资金](images/入门指南/界面导航-2.png)
3.  **商品**：管理店铺商品，包括上架、下架、修改价格和库存等操作。
    ![商品](images/入门指南/界面导航-3.png)
4.  **订单**：查看和处理各种状态的订单，包括待发货、待收货等。
    ![订单](images/入门指南/界面导航-4.png)
5.  **我的**：查看和管理个人账号信息，包括店铺信息、账号安全设置等。
    ![我的](images/入门指南/界面导航-5.png)

#### 基本操作手势
在使用小程序过程中，您可以使用以下手势进行操作：

1.  **点击**：选择功能、确认操作等。
2.  **下拉刷新**：在首页、订单列表等页面，下拉页面可刷新数据。
    ![下拉刷新](images/入门指南/界面导航-6.png)
3.  **左右滑动**：在某些列表页面，可通过左右滑动切换不同标签。
    ![左右滑动](images/入门指南/界面导航-7.png)
4.  **返回上一页**：点击页面左上角的返回按钮可返回上一页面。
    ![返回上一页](images/入门指南/界面导航-8.png)

#### 常用功能入口
除了底部标签栏外，小程序还提供了一些常用功能的快捷入口：

1.  **扫码功能**：在首页顶部导航栏左侧有扫码图标，点击可进行扫码操作，用于核销自提订单。
    ![扫码功能](images/入门指南/界面导航-9.png)
2.  **提现入口**：在首页账户余额卡片上有"提现"按钮，点击可快速进入提现页面。
    ![提现入口](images/入门指南/界面导航-10.png)
3.  **查看更多**：在首页的热销商品区域有"查看更多"按钮，点击可进入商品管理页面。
    ![查看更多](images/入门指南/界面导航-11.png)

#### 注意事项
* 首次使用时，建议先熟悉各个功能模块的位置和用途
* 部分功能可能需要特定权限，如无法访问，请联系平台管理员
* 在网络不稳定的环境下，可能会出现数据加载缓慢的情况，请耐心等待

### 1.4 店铺信息设置

#### 功能概述
店铺信息是商家在平台上的基本资料，包括店铺名称、联系方式等重要信息。正确设置店铺信息有助于提升店铺形象，方便买家了解和联系商家。本节将介绍如何查看和设置店铺信息。

#### 操作路径
1.  点击底部导航栏的"我的"选项
2.  在个人中心页面点击头像或店铺名称区域

#### 操作步骤
1.  **进入个人中心**：点击底部导航栏的"我的"选项，进入个人中心页面。
    ![进入个人中心](images/入门指南/店铺信息-1.png)
2.  **进入店铺信息页面**：点击头像或店铺名称区域，进入店铺信息页面。
    ![进入店铺信息页面](images/入门指南/店铺信息-2.png)
3.  **查看店铺基本信息**：在店铺信息页面可以查看店铺的基本信息，包括：
    * 店铺名称
    * 店铺logo
    * 联系电话
    * 店铺地址
    * 经营类目
    * 认证状态
    ![查看店铺基本信息](images/入门指南/店铺信息-3.png)
4.  **修改店铺信息**：如需修改店铺信息，请联系平台客服或运营人员进行操作。目前商家端小程序暂不支持直接修改店铺基本信息。

#### 注意事项
* 店铺信息对买家可见，请确保信息真实有效
* 如店铺信息有误或需要更新，请及时联系平台客服或运营人员
* 店铺认证状态会影响部分功能的使用，请确保完成店铺认证

---

## 2. 首页与数据概览

### 2.1 首页布局与功能介绍

#### 功能概述
八闽助业集市商家端小程序的首页是商家日常运营的信息中心，集中展示了账户余额、销售数据、订单状态和热销商品等核心信息，帮助商家快速了解店铺经营状况。本节将介绍首页的整体布局和各功能区域。

#### 首页整体布局
首页主要分为以下几个功能区域：

1.  **顶部导航栏**：包含扫码功能入口和页面标题。
    ![顶部导航栏](images/首页与数据概览/首页布局-1.png)
2.  **账户余额卡片**：显示当前账户余额和提现入口。
    ![账户余额卡片](images/首页与数据概览/首页布局-2.png)
3.  **资金明细区域**：展示冻结金额、提现中金额和已提现金额。
    ![资金明细区域](images/首页与数据概览/首页布局-3.png)
4.  **销售概况区域**：展示今日销售额、今日订单数和累计业绩。
    ![销售概况区域](images/首页与数据概览/首页布局-4.png)
5.  **订单状态区域**：展示各状态订单数量，包括全部、待发货、待收货和已收货。
    ![订单状态区域](images/首页与数据概览/首页布局-5.png)
6.  **热销商品区域**：展示店铺热销商品列表。
    ![热销商品区域](images/首页与数据概览/首页布局-6.png)
7.  **底部标签栏**：提供首页、资金、商品、订单和我的等主要功能入口。
    ![底部标签栏](images/首页与数据概览/首页布局-7.png)

#### 功能说明

##### 顶部导航栏
* **扫码功能**：点击左侧扫码图标，可调用微信扫码功能，用于核销自提订单。
* **页面标题**：显示当前页面名称"首页"。

##### 账户余额卡片
* **账户余额**：显示当前可提现的账户余额。
* **刷新按钮**：点击右侧刷新图标，可刷新首页所有数据。
* **提现按钮**：点击"提现"按钮，可跳转至提现申请页面。

##### 资金明细区域
* **冻结金额**：显示当前被冻结的资金金额，点击可查看待结算明细。
* **提现中**：显示正在提现处理中的金额，点击可查看提现中的记录。
* **已提现**：显示已成功提现的累计金额，点击可查看已提现的记录。

##### 销售概况区域
* **今日销售额**：显示当天的销售总金额。
* **今日订单数**：显示当天的订单总数量。
* **累计业绩**：显示店铺开业以来的销售总金额。

##### 订单状态区域
* **全部**：显示所有状态订单的总数量，点击可查看全部订单列表。
* **待发货**：显示待发货订单数量，点击可查看待发货订单列表。
* **待收货**：显示待收货订单数量，点击可查看待收货订单列表。
* **已收货**：显示已收货订单数量，点击可查看已收货订单列表。

##### 热销商品区域
* **商品列表**：展示店铺销量排名靠前的商品，包含商品图片、名称、价格、销量和库存信息。
* **查看更多**：点击右上角"查看更多"，可跳转至商品管理页面。

#### 操作说明

##### 页面刷新
首页支持两种刷新方式：
1.  **下拉刷新**：在首页向下拉动页面，触发下拉刷新功能，刷新所有数据。
    ![下拉刷新](images/首页与数据概览/首页布局-8.png)
2.  **点击刷新按钮**：点击账户余额卡片右上角的刷新图标，刷新所有数据。
    ![点击刷新按钮](images/首页与数据概览/首页布局-9.png)

##### 功能跳转
首页提供多个快捷跳转入口：
1.  **点击账户余额卡片**：跳转至资金明细页面。
2.  **点击提现按钮**：跳转至提现申请页面。
3.  **点击资金明细项**：跳转至对应的资金明细页面。
4.  **点击订单状态项**：跳转至对应状态的订单列表页面。
5.  **点击查看更多**：跳转至商品管理页面。

#### 注意事项
* 首页数据每次进入页面时会自动刷新，也可通过下拉刷新或点击刷新按钮手动更新
* 销售数据统计截止时间为当前查询时间，今日数据会实时更新
* 热销商品默认按销量排序，展示销量排名靠前的商品
* 如遇数据加载失败，可尝试手动刷新页面

### 2.2 账户余额与资金概览

#### 功能概述
账户余额与资金概览功能为商家提供了店铺资金状况的直观展示，包括可提现余额、冻结金额、提现中金额和已提现金额等关键财务数据。通过这些数据，商家可以实时了解店铺的资金流向和结算情况。

#### 操作路径
账户余额与资金概览信息位于首页顶部区域，无需额外操作即可查看。

#### 账户余额卡片详解
账户余额卡片是首页最醒目的区域，展示了商家当前可提现的资金金额。
![账户余额卡片](images/首页与数据概览/账户余额-1.png)

卡片包含以下元素：
1.  **余额标题**：显示"账户余额"文字。
2.  **刷新按钮**：点击可刷新首页所有数据。
3.  **余额金额**：显示当前可提现的账户余额，精确到分。
4.  **提现按钮**：点击可进入提现申请页面。

#### 资金明细区域详解
资金明细区域位于账户余额卡片下方，展示了资金的不同状态分类。
![资金明细区域](images/首页与数据概览/账户余额-2.png)

该区域包含三个资金项目：

1.  **冻结金额**
    * 显示当前被冻结的资金总额
    * 冻结金额通常是指订单完成后，按平台规则需要冻结一定时间才能结算的资金
    * 点击可查看待结算明细，了解每笔冻结资金的来源和预计解冻时间
    ![冻结金额详情](images/首页与数据概览/账户余额-3.png)
2.  **提现中**
    * 显示已申请提现但尚未到账的资金总额
    * 提现处理通常需要1-3个工作日，期间资金状态为"提现中"
    * 点击可查看提现中的记录，了解每笔提现的申请时间和处理状态
    ![提现中详情](images/首页与数据概览/账户余额-4.png)
3.  **已提现**
    * 显示成功提现到商家银行卡或微信钱包的资金累计总额
    * 点击可查看历史提现记录，包括提现时间、金额和到账账户等信息
    ![已提现详情](images/首页与数据概览/账户余额-5.png)

#### 资金状态流转说明
商家店铺的资金通常会经历以下状态流转：
1.  买家下单并支付 → 平台收到款项
2.  买家确认收货 → 资金进入冻结状态
3.  冻结期满 → 资金转入可提现余额
4.  商家申请提现 → 资金进入提现中状态
5.  平台审核通过并处理 → 资金成功提现到商家账户

#### 操作指南

##### 查看账户余额
1.  打开小程序，进入首页
2.  账户余额显示在首页顶部蓝色卡片中
3.  如需刷新数据，可点击右上角刷新图标或下拉页面刷新

##### 查看资金明细
1.  在首页找到资金明细区域（账户余额卡片下方）
2.  查看冻结金额、提现中和已提现三项数据
3.  点击任一项可查看对应的详细记录

##### 跳转到资金页面
如需查看更详细的资金记录，可通过以下两种方式跳转到资金页面：
1.  点击账户余额卡片（蓝色区域）
2.  点击底部导航栏的"资金"选项

#### 注意事项
* 账户余额数据每次进入首页时会自动刷新
* 资金冻结期由平台统一设置，商家无法手动解冻
* 提现申请提交后，资金状态会立即从"账户余额"变为"提现中"
* 如发现资金数据异常，请及时联系平台客服处理

### 2.3 销售数据与业绩统计

#### 功能概述
销售数据与业绩统计功能为商家提供了店铺销售情况的数据概览，包括今日销售额、今日订单数和累计业绩等核心经营指标。通过这些数据，商家可以直观了解店铺的销售表现和经营趋势。

#### 操作路径
销售数据与业绩统计信息位于首页中部"销售概况"区域，无需额外操作即可查看。

#### 销售概况区域详解
销售概况区域展示了三个核心销售指标，帮助商家快速了解店铺经营状况。
![销售概况区域](images/首页与数据概览/销售数据-1.png)

该区域包含以下三个指标：

1.  **今日销售额**
    * 显示当天（0:00-24:00）的销售总金额
    * 计算方式：当天所有已支付订单的商品金额总和（不含退款订单）
    * 数据实时更新，反映最新的销售情况
    ![今日销售额](images/首页与数据概览/销售数据-2.png)
2.  **今日订单数**
    * 显示当天（0:00-24:00）的订单总数量
    * 计算方式：当天所有已支付订单的数量（不含已取消订单）
    * 帮助商家了解当天的订单处理量
    ![今日订单数](images/首页与数据概览/销售数据-3.png)
3.  **累计业绩**
    * 显示店铺开业以来的销售总金额
    * 计算方式：所有已完成订单的商品金额总和（不含退款订单）
    * 反映店铺的总体经营规模
    ![累计业绩](images/首页与数据概览/销售数据-4.png)

#### 订单状态区域详解
订单状态区域展示了各状态订单的数量统计，帮助商家及时处理不同状态的订单。
![订单状态区域](images/首页与数据概览/销售数据-5.png)

该区域包含以下四个状态：

1.  **全部**
    * 显示所有状态订单的总数量
    * 点击可查看全部订单列表
    ![全部订单](images/首页与数据概览/销售数据-6.png)
2.  **待发货**
    * 显示买家已支付但商家尚未发货的订单数量
    * 需要商家优先处理的订单
    * 点击可查看待发货订单列表
    ![待发货订单](images/首页与数据概览/销售数据-7.png)
3.  **待收货**
    * 显示商家已发货但买家尚未确认收货的订单数量
    * 点击可查看待收货订单列表
    ![待收货订单](images/首页与数据概览/销售数据-8.png)
4.  **已收货**
    * 显示买家已确认收货的订单数量
    * 点击可查看已收货订单列表
    ![已收货订单](images/首页与数据概览/销售数据-9.png)

#### 热销商品区域详解
热销商品区域展示了店铺销量排名靠前的商品，帮助商家了解哪些商品更受欢迎。
![热销商品区域](images/首页与数据概览/销售数据-10.png)

该区域包含以下内容：

1.  **区域标题**：显示"热销商品"文字。
2.  **查看更多**：点击可跳转至商品管理页面。
3.  **商品列表**：展示热销商品信息，每个商品包含：
    * 商品图片
    * 商品名称
    * 商品价格
    * 销量信息
    * 库存信息

#### 操作指南

##### 查看销售数据
1.  打开小程序，进入首页
2.  在"销售概况"区域查看今日销售额、今日订单数和累计业绩
3.  如需刷新数据，可下拉页面或点击账户余额卡片右上角的刷新图标

##### 查看订单状态统计
1.  在首页找到"订单状态"区域
2.  查看全部、待发货、待收货和已收货四种状态的订单数量
3.  点击任一状态可跳转至对应的订单列表页面

##### 查看热销商品
1.  在首页下滑至"热销商品"区域
2.  浏览展示的热销商品信息
3.  点击"查看更多"可跳转至商品管理页面，查看更多商品信息

#### 数据统计规则说明

1.  **销售额计算规则**
    * 今日销售额：当天0:00至当前时间的已支付订单金额总和
    * 累计业绩：店铺所有已完成订单的金额总和
2.  **订单数量统计规则**
    * 今日订单数：当天0:00至当前时间的新增订单数量
    * 各状态订单数：实时统计当前各状态的订单数量
3.  **热销商品排序规则**
    * 默认按照商品销量从高到低排序
    * 展示销量排名前3-5的商品

#### 注意事项
* 销售数据每次进入首页时会自动刷新，也可手动刷新
* 今日数据会在每天0:00自动重置
* 订单状态数量变化会实时反映在首页统计中
* 如发现数据异常，可尝试刷新页面或联系平台客服

### 2.4 扫码功能使用

#### 功能概述
扫码功能是八闽助业集市商家端小程序提供的便捷工具，主要用于核销自提订单。当买家选择到店自提方式下单并到店取货时，商家可通过扫描买家出示的订单二维码，快速完成订单核销操作。

#### 操作路径
扫码功能入口位于首页顶部导航栏左侧，点击扫码图标即可调用。

#### 操作步骤

1.  **进入首页**：打开小程序，进入首页。
    ![进入首页](images/首页与数据概览/扫码功能-1.png)
2.  **点击扫码图标**：点击首页顶部导航栏左侧的扫码图标。
    ![点击扫码图标](images/首页与数据概览/扫码功能-2.png)
3.  **授权相机权限**（首次使用时）：
    * 首次使用扫码功能时，系统会请求相机使用权限
    * 点击"允许"以授权小程序使用相机
    ![授权相机权限](images/首页与数据概览/扫码功能-3.png)
4.  **对准二维码扫描**：
    * 相机打开后，将取景框对准买家出示的订单二维码
    * 保持手机稳定，等待系统自动识别二维码
    ![对准二维码扫描](images/首页与数据概览/扫码功能-4.png)
5.  **核销结果处理**：
    * 扫码成功后，系统会自动处理核销请求
    * 页面会跳转至核销结果页面，显示核销成功或失败的信息
    ![核销结果处理](images/首页与数据概览/扫码功能-5.png)
6.  **查看核销结果**：
    * 核销成功：页面显示"核销成功"，订单状态自动更新为"已完成"
    * 核销失败：页面显示失败原因，如"订单不存在"、"订单已核销"等
    ![查看核销结果](images/首页与数据概览/扫码功能-6.png)
7.  **返回首页**：点击核销结果页面的"返回首页"按钮，返回小程序首页。
    ![返回首页](images/首页与数据概览/扫码功能-7.png)

#### 核销规则说明

1.  **可核销的订单类型**：
    * 仅支持核销"自提订单"，即买家选择到店自提的订单
    * 订单状态必须为"待自提"状态
2.  **核销权限**：
    * 只能核销本店铺的订单
    * 商家账号需有核销权限（默认已开通）
3.  **核销后的订单状态变化**：
    * 核销成功后，订单状态自动变更为"已完成"
    * 系统会自动记录核销时间和操作人员
4.  **重复核销处理**：
    * 已核销的订单无法重复核销
    * 重复扫码会提示"订单已核销"

#### 常见问题与解决方法

1.  **扫码失败**
    * 问题：扫码时提示"无法识别二维码"
    * 解决方法：
        * 确保环境光线充足
        * 保持手机稳定，与二维码保持适当距离
        * 确保二维码清晰可见，没有严重污损
2.  **核销失败**
    * 问题：扫码成功但核销失败
    * 可能原因及解决方法：
        * 订单不存在：确认买家出示的是正确的订单二维码
        * 订单已核销：确认该订单是否已被核销
        * 非自提订单：确认订单的配送方式为"自提"
        * 网络异常：检查网络连接，重新扫码尝试
3.  **无法调用相机**
    * 问题：点击扫码图标后无法打开相机
    * 解决方法：
        * 检查是否已授权相机权限
        * 在手机设置中找到微信应用，确认相机权限已开启
        * 重启微信或手机后重试

#### 注意事项
* 核销前请确认买家身份，防止错误核销
* 核销成功后，订单状态变更不可逆，请谨慎操作
* 如遇特殊情况需要取消已核销的订单，请联系平台客服处理
* 建议在网络稳定的环境下使用扫码功能，以确保核销结果能及时同步

---

## 3. 商品管理

### 3.1 商品列表查看与筛选

#### 功能概述
商品列表查看与筛选功能是八闽助业集市商家端小程序的核心功能之一，允许商家查看和管理自己店铺中的所有商品。通过不同的状态分类，商家可以快速找到需要处理的商品，提高店铺管理效率。

#### 操作路径
1.  点击底部导航栏的"商品"选项，进入商品管理页面

#### 操作步骤

##### 进入商品管理页面
1.  **打开小程序**：进入八闽助业集市商家端小程序。
    ![打开小程序](images/商品管理/商品列表-1.png)
2.  **进入商品页面**：点击底部导航栏的"商品"选项，进入商品管理页面。
    ![进入商品页面](images/商品管理/商品列表-2.png)

##### 查看不同状态的商品
商品管理页面顶部设有状态分类标签，包括以下几种状态：

1.  **在售中**：正在销售中的商品，买家可以在平台上看到并购买。
    ![在售中商品](images/商品管理/商品列表-3.png)
2.  **已下架**：已被商家手动下架的商品，买家无法在平台上看到。
    ![已下架商品](images/商品管理/商品列表-4.png)
3.  **已售罄**：库存为0的商品，买家可以看到但无法购买。
    ![已售罄商品](images/商品管理/商品列表-5.png)
4.  **发布中**：正在审核中的商品，等待平台审核通过后才能上架销售。
    ![发布中商品](images/商品管理/商品列表-6.png)
5.  **已驳回**：未通过平台审核的商品，需要修改后重新提交审核。
    ![已驳回商品](images/商品管理/商品列表-7.png)
6.  **草稿箱**：尚未提交审核的商品草稿。
    ![草稿箱商品](images/商品管理/商品列表-8.png)

切换状态分类的方法：
* 点击顶部对应的状态标签，即可切换到该状态的商品列表
* 左右滑动屏幕，也可以在不同状态的商品列表之间切换

##### 查看商品详情
在商品列表中，每个商品项都会显示以下信息：
* 商品图片
* 商品名称
* 累计销量
* 30日销量
* 库存数量
* 商品价格

![商品详情](images/商品管理/商品列表-9.png)

##### 刷新商品列表
商品列表支持两种刷新方式：

1.  **下拉刷新**：在商品列表页面，向下拉动屏幕，触发下拉刷新功能，刷新当前状态的商品列表。
    ![下拉刷新](images/商品管理/商品列表-10.png)
2.  **切换标签刷新**：切换不同的状态标签时，系统会自动刷新对应状态的商品列表。

##### 加载更多商品
当商品数量较多时，系统会采用分页加载的方式：

1.  **滚动加载**：向上滚动商品列表至底部，系统会自动加载更多商品。
    ![滚动加载](images/商品管理/商品列表-11.png)
2.  **加载状态提示**：
    * 加载中：显示"加载中..."提示
    * 加载完成：显示"没有更多数据了"提示

#### 注意事项
* 商品状态变更（如上架、下架、售罄）后，需要刷新列表才能看到最新状态
* 商品数量较多时，建议使用搜索功能快速找到特定商品
* 每个状态标签旁边的数字表示该状态下的商品数量
* 新上传的商品需要经过平台审核后才能在"在售中"状态显示

### 3.2 商品上架与下架

#### 功能概述
商品上架与下架功能允许商家控制商品在平台上的销售状态。通过上架操作，商家可以将商品展示给买家并开始销售；通过下架操作，商家可以临时停止商品的销售，或者永久删除不再销售的商品。

#### 操作路径
1.  点击底部导航栏的"商品"选项，进入商品管理页面
2.  在商品列表中找到需要操作的商品

#### 操作步骤

##### 商品上架
商品上架操作适用于"已下架"状态的商品：

1.  **进入已下架商品列表**：在商品管理页面，点击顶部的"已下架"标签。
    ![进入已下架商品列表](images/商品管理/商品上架-1.png)
2.  **找到需要上架的商品**：在已下架商品列表中，找到需要上架的商品。
    ![找到需要上架的商品](images/商品管理/商品上架-2.png)
3.  **点击上架按钮**：点击商品项右下角的"上架"按钮。
    ![点击上架按钮](images/商品管理/商品上架-3.png)
4.  **确认上架操作**：在弹出的确认对话框中，点击"确定"按钮。
    ![确认上架操作](images/商品管理/商品上架-4.png)
5.  **上架成功提示**：系统显示"上架成功"提示，商品状态变更为"在售中"。
    ![上架成功提示](images/商品管理/商品上架-5.png)

##### 商品下架
商品下架操作适用于"在售中"状态的商品：

1.  **进入在售中商品列表**：在商品管理页面，点击顶部的"在售中"标签。
    ![进入在售中商品列表](images/商品管理/商品下架-1.png)
2.  **找到需要下架的商品**：在在售中商品列表中，找到需要下架的商品。
    ![找到需要下架的商品](images/商品管理/商品下架-2.png)
3.  **点击下架按钮**：点击商品项右下角的"下架"按钮。
    ![点击下架按钮](images/商品管理/商品下架-3.png)
4.  **确认下架操作**：在弹出的确认对话框中，点击"确定"按钮。
    ![确认下架操作](images/商品管理/商品下架-4.png)
5.  **下架成功提示**：系统显示"下架成功"提示，商品状态变更为"已下架"。
    ![下架成功提示](images/商品管理/商品下架-5.png)

##### 删除商品
删除商品操作适用于非"在售中"状态的商品（如已下架、已售罄、已驳回等）：

1.  **进入相应状态的商品列表**：在商品管理页面，点击顶部对应的状态标签。
    ![进入商品列表](images/商品管理/商品删除-1.png)
2.  **找到需要删除的商品**：在商品列表中，找到需要删除的商品。
    ![找到需要删除的商品](images/商品管理/商品删除-2.png)
3.  **点击删除按钮**：点击商品项右下角的"删除"按钮。
    ![点击删除按钮](images/商品管理/商品删除-3.png)
4.  **确认删除操作**：在弹出的确认对话框中，点击"确定"按钮。
    ![确认删除操作](images/商品管理/商品删除-4.png)
5.  **删除成功提示**：系统显示"删除成功"提示，商品从列表中移除。
    ![删除成功提示](images/商品管理/商品删除-5.png)

#### 注意事项
* 商品上架后，买家可以在平台上看到并购买该商品
* 商品下架后，买家将无法在平台上看到该商品，但商家仍可在"已下架"列表中找到
* 删除操作是不可逆的，删除后商品将无法恢复，请谨慎操作
* 对于"在售中"的商品，只能执行下架操作，不能直接删除
* 商品状态变更可能需要一定时间生效，建议操作后刷新列表查看最新状态

### 3.3 商品库存与价格调整

#### 功能概述
商品库存与价格调整功能允许商家随时更新商品的库存数量和销售价格，以适应市场需求和库存变化。通过简单的操作，商家可以快速调整单个商品或多规格商品的库存和价格，无需重新发布商品。

#### 操作路径
1.  点击底部导航栏的"商品"选项，进入商品管理页面
2.  在商品列表中找到需要调整的商品

#### 操作步骤

##### 调整商品库存
1.  **找到需要调整库存的商品**：在商品列表中，找到需要调整库存的商品。
    ![找到商品](images/商品管理/商品库存-1.png)
2.  **点击改库存按钮**：点击商品项右下角的"改库存"按钮。
    ![点击改库存按钮](images/商品管理/商品库存-2.png)
3.  **进入库存修改页面**：系统跳转到库存修改页面，显示当前商品的库存信息。
    ![库存修改页面](images/商品管理/商品库存-3.png)
4.  **调整库存数量**：
    * **单规格商品**：直接使用数字输入框调整库存数量。
        ![单规格库存调整](images/商品管理/商品库存-4.png)
    * **多规格商品**：分别调整每个规格的库存数量。
        ![多规格库存调整](images/商品管理/商品库存-5.png)
5.  **保存库存修改**：调整完成后，点击页面底部的"保存"按钮。
    ![保存库存修改](images/商品管理/商品库存-6.png)
6.  **修改成功提示**：系统显示"修改库存成功"提示，并自动返回商品列表页面。
    ![修改成功提示](images/商品管理/商品库存-7.png)

##### 调整商品价格
1.  **找到需要调整价格的商品**：在商品列表中，找到需要调整价格的商品。
    ![找到商品](images/商品管理/商品价格-1.png)
2.  **点击改价按钮**：点击商品项右下角的"改价"按钮。
    ![点击改价按钮](images/商品管理/商品价格-2.png)
3.  **进入价格修改页面**：系统跳转到价格修改页面，显示当前商品的价格信息。
    ![价格修改页面](images/商品管理/商品价格-3.png)
4.  **调整商品价格**：
    * **单规格商品**：直接在价格输入框中输入新的价格。
        ![单规格价格调整](images/商品管理/商品价格-4.png)
    * **多规格商品**：分别调整每个规格的价格。
        ![多规格价格调整](images/商品管理/商品价格-5.png)
5.  **保存价格修改**：调整完成后，点击页面底部的"保存"按钮。
    ![保存价格修改](images/商品管理/商品价格-6.png)
6.  **修改成功提示**：系统显示"修改价格成功"提示，并自动返回商品列表页面。
    ![修改成功提示](images/商品管理/商品价格-7.png)

#### 库存与价格规则说明

##### 库存规则
* 库存数量必须为整数，不能为负数
* 库存上限为999999
* 当商品库存调整为0时，商品状态会自动变为"已售罄"
* 当"已售罄"商品的库存调整为大于0的数值时，商品状态会自动变为"在售中"

##### 价格规则
* 价格可以精确到小数点后两位（元）
* 价格必须大于0
* 多规格商品的不同规格可以设置不同价格
* 价格修改后会立即生效，无需平台审核

#### 注意事项
* 库存和价格调整操作可以在任何商品状态下进行
* 库存和价格调整不会影响商品的其他属性和状态
* 建议定期检查和更新商品库存，避免因库存不足导致无法正常销售
* 价格调整会影响所有新订单，但不会影响已下单未支付的订单
* 如需大幅调整价格，建议提前通知老客户，避免引起误解

### 3.4 商品搜索与批量操作

#### 功能概述
商品搜索功能允许商家快速找到特定商品，特别是在商品数量较多的情况下。通过搜索商品名称、ID或关键词，商家可以迅速定位到需要管理的商品，提高工作效率。

#### 操作路径
1.  点击底部导航栏的"商品"选项，进入商品管理页面
2.  使用搜索功能查找特定商品

#### 操作步骤

##### 搜索商品
1.  **进入商品管理页面**：点击底部导航栏的"商品"选项，进入商品管理页面。
    ![进入商品管理页面](images/商品管理/商品搜索-1.png)
2.  **点击搜索入口**：在商品列表页面，点击顶部的搜索图标或搜索框。
    ![点击搜索入口](images/商品管理/商品搜索-2.png)
3.  **进入搜索页面**：系统跳转到商品搜索页面。
    ![进入搜索页面](images/商品管理/商品搜索-3.png)
4.  **输入搜索关键词**：在搜索框中输入商品名称、ID或关键词。
    ![输入搜索关键词](images/商品管理/商品搜索-4.png)
5.  **执行搜索**：输入完成后，点击键盘上的"搜索"按钮或回车键执行搜索。
    ![执行搜索](images/商品管理/商品搜索-5.png)
6.  **查看搜索结果**：系统显示符合搜索条件的商品列表。
    ![查看搜索结果](images/商品管理/商品搜索-6.png)

##### 使用搜索历史
搜索页面会记录最近的搜索历史，方便商家快速重复之前的搜索：

1.  **查看搜索历史**：在搜索页面，如果没有输入关键词，系统会显示最近的搜索历史。
    ![查看搜索历史](images/商品管理/商品搜索-7.png)
2.  **使用历史记录**：点击任一历史记录项，系统会自动填充该关键词并执行搜索。
    ![使用历史记录](images/商品管理/商品搜索-8.png)
3.  **删除单条历史记录**：点击历史记录项右侧的"×"图标，可删除该条历史记录。
    ![删除单条历史记录](images/商品管理/商品搜索-9.png)
4.  **清空所有历史记录**：点击"最近搜索"右侧的垃圾桶图标，可清空所有搜索历史。
    ![清空所有历史记录](images/商品管理/商品搜索-10.png)

##### 批量操作商品
目前商家端小程序暂未开放批量操作功能，该功能将在后续版本中推出。批量操作将支持以下功能：

* 批量上架商品
* 批量下架商品
* 批量调整价格
* 批量调整库存
* 批量删除商品

#### 搜索技巧

1.  **精确搜索**：
    * 使用商品ID进行搜索，可以精确定位到特定商品
    * 使用完整商品名称搜索，可以减少无关结果
2.  **关键词搜索**：
    * 使用商品名称中的关键词进行搜索
    * 使用商品类别、品牌等关键词进行搜索
3.  **组合搜索**：
    * 使用多个关键词组合搜索，提高搜索精度
    * 例如："红色 T恤 L码"

#### 注意事项
* 搜索功能会在所有状态的商品中进行查找，不限于当前选中的状态标签
* 搜索结果会按照相关度排序，最相关的结果显示在前面
* 搜索历史最多保存10条记录，超出后会自动删除最早的记录
* 如果搜索结果为空，建议尝试使用不同的关键词或检查关键词拼写
* 批量操作功能将在后续版本中推出，敬请期待

---

## 4. 订单管理

### 4.1 订单列表与状态分类

#### 功能概述
订单列表与状态分类功能是八闽助业集市商家端小程序的核心功能之一，允许商家查看和管理店铺的所有订单。通过不同的状态分类，商家可以快速找到需要处理的订单，提高订单处理效率。

#### 操作路径
1.  点击底部导航栏的"订单"选项，进入订单管理页面

#### 操作步骤

##### 进入订单管理页面
1.  **打开小程序**：进入八闽助业集市商家端小程序。
    ![打开小程序](images/订单管理/订单列表-1.png)
2.  **进入订单页面**：点击底部导航栏的"订单"选项，进入订单管理页面。
    ![进入订单页面](images/订单管理/订单列表-2.png)

##### 查看不同状态的订单
订单管理页面顶部设有状态分类标签，包括以下几种状态：

1.  **全部**：显示所有状态的订单。
    ![全部订单](images/订单管理/订单列表-3.png)
2.  **待发货**：显示买家已付款但商家尚未发货的订单。
    ![待发货订单](images/订单管理/订单列表-4.png)
3.  **待收货**：显示商家已发货但买家尚未确认收货的订单。
    ![待收货订单](images/订单管理/订单列表-5.png)
4.  **已收货**：显示买家已确认收货的订单。
    ![已收货订单](images/订单管理/订单列表-6.png)
5.  **交易失败**：显示因各种原因导致交易未成功的订单。
    ![交易失败订单](images/订单管理/订单列表-7.png)

切换状态分类的方法：
* 点击顶部对应的状态标签，即可切换到该状态的订单列表
* 左右滑动屏幕，也可以在不同状态的订单列表之间切换

##### 订单列表信息展示
在订单列表中，每个订单项都会显示以下信息：
* 订单编号
* 下单时间
* 订单状态
* 商品图片和名称
* 商品规格
* 商品数量
* 订单金额
* 操作按钮（根据订单状态不同而变化）

![订单信息展示](images/订单管理/订单列表-8.png)

##### 刷新订单列表
订单列表支持两种刷新方式：

1.  **下拉刷新**：在订单列表页面，向下拉动屏幕，触发下拉刷新功能，刷新当前状态的订单列表。
    ![下拉刷新](images/订单管理/订单列表-9.png)
2.  **切换标签刷新**：切换不同的状态标签时，系统会自动刷新对应状态的订单列表。

##### 加载更多订单
当订单数量较多时，系统会采用分页加载的方式：

1.  **滚动加载**：向上滚动订单列表至底部，系统会自动加载更多订单。
    ![滚动加载](images/订单管理/订单列表-10.png)
2.  **加载状态提示**：
    * 加载中：显示"加载中..."提示
    * 加载完成：显示"没有更多数据了"提示

#### 注意事项
* 订单状态变更（如发货、收货）后，需要刷新列表才能看到最新状态
* 订单数量较多时，建议使用搜索功能快速找到特定订单
* 每个状态标签旁边的数字表示该状态下的订单数量
* 新订单会自动出现在对应的状态分类中，无需手动刷新

### 4.2 订单详情查看

#### 功能概述
订单详情查看功能允许商家查看单个订单的详细信息，包括订单状态、商品信息、买家信息、收货地址、物流信息等。通过订单详情页面，商家可以全面了解订单情况，并进行相应的操作。

#### 操作路径
1.  点击底部导航栏的"订单"选项，进入订单管理页面
2.  在订单列表中找到需要查看的订单，点击进入订单详情页面

#### 操作步骤

##### 进入订单详情页面
1.  **在订单列表中找到目标订单**：在订单管理页面，浏览订单列表找到需要查看的订单。
    ![找到目标订单](images/订单管理/订单详情-1.png)
2.  **点击订单项**：点击订单项的任意区域（除操作按钮外），进入订单详情页面。
    ![点击订单项](images/订单管理/订单详情-2.png)

##### 查看订单基本信息
订单详情页面顶部显示订单的基本状态信息：

1.  **订单状态**：显示当前订单的状态（待发货、待收货、已收货等）。
    ![订单状态](images/订单管理/订单详情-3.png)
2.  **订单编号**：显示订单的唯一编号。
    ![订单编号](images/订单管理/订单详情-4.png)
3.  **操作按钮**：根据订单状态显示不同的操作按钮。
    * 待发货状态：显示"取消并退款"和"立即发货"按钮
    * 待收货状态：显示"查看物流"按钮
    * 已收货状态：显示"查看物流"按钮
    ![操作按钮](images/订单管理/订单详情-5.png)

##### 查看商品信息
订单详情页面的商品信息区域显示订单中包含的商品详情：

1.  **商品列表**：显示订单中的所有商品，包括商品图片、名称、规格和数量。
    ![商品列表](images/订单管理/订单详情-6.png)
2.  **展开/收起**：点击"展开"或"收起"按钮，可以查看更多或隐藏部分商品详情。
    ![展开收起](images/订单管理/订单详情-7.png)
3.  **商品小计**：显示商品的数量和金额小计。
    ![商品小计](images/订单管理/订单详情-8.png)

##### 查看订单信息
订单信息区域显示与订单相关的详细信息：

1.  **店铺名称**：显示订单所属的店铺名称。
    ![店铺名称](images/订单管理/订单详情-9.png)
2.  **买家信息**：展开后可查看买家账号、昵称、会员类型等信息。
    ![买家信息](images/订单管理/订单详情-10.png)
3.  **收货信息**：展开后可查看收货人名称、联系电话和收货地址。
    ![收货信息](images/订单管理/订单详情-11.png)
4.  **订单时间**：展开后可查看订单的创建时间。
    ![订单时间](images/订单管理/订单详情-12.png)

##### 查看物流信息
对于已发货的订单（待收货或已收货状态），订单详情页面会显示物流信息区域：

1.  **快递信息**：显示供应商、运费模板、计费规则等信息。
    ![快递信息](images/订单管理/订单详情-13.png)
2.  **展开/收起**：点击"展开"或"收起"按钮，可以查看更多或隐藏部分物流详情。
    ![展开收起物流](images/订单管理/订单详情-14.png)

#### 注意事项
* 订单详情页面支持下拉刷新，可以获取最新的订单状态和信息
* 不同状态的订单会显示不同的操作按钮和信息区域
* 订单详情页面的信息是实时从服务器获取的，确保数据的准确性
* 如需对订单进行操作（如发货、取消），可以使用页面顶部的操作按钮

### 4.3 订单发货操作

#### 功能概述
订单发货操作是商家处理待发货订单的重要功能。通过该功能，商家可以为订单选择物流公司、填写快递单号，并完成发货处理，使订单状态从"待发货"变更为"待收货"。

#### 操作路径
1.  点击底部导航栏的"订单"选项，进入订单管理页面
2.  在"待发货"标签下找到需要发货的订单
3.  点击"发货"按钮或进入订单详情页点击"立即发货"按钮

#### 操作步骤

##### 方式一：从订单列表发货

1.  **进入待发货订单列表**：在订单管理页面，点击顶部的"待发货"标签。
    ![进入待发货订单列表](images/订单管理/订单发货-1.png)
2.  **找到需要发货的订单**：在待发货订单列表中，找到需要处理的订单。
    ![找到需要发货的订单](images/订单管理/订单发货-2.png)
3.  **点击发货按钮**：点击订单项右下角的"发货"按钮。
    ![点击发货按钮](images/订单管理/订单发货-3.png)
4.  **进入发货页面**：系统跳转到发货操作页面。
    ![进入发货页面](images/订单管理/订单发货-4.png)

##### 方式二：从订单详情发货

1.  **进入订单详情页面**：在订单列表中点击订单项，进入订单详情页面。
    ![进入订单详情页面](images/订单管理/订单发货-5.png)
2.  **点击立即发货按钮**：在订单详情页面顶部，点击"立即发货"按钮。
    ![点击立即发货按钮](images/订单管理/订单发货-6.png)
3.  **进入发货页面**：系统跳转到发货操作页面。
    ![进入发货页面](images/订单管理/订单发货-7.png)

##### 填写发货信息

1.  **选择包裹商品**：
    * 系统默认会将订单中的所有商品添加到第一个包裹中
    * 如需调整，可以点击"添加商品"按钮重新选择商品
    * 如需分多个包裹发货，可以点击"添加包裹"按钮
    ![选择包裹商品](images/订单管理/订单发货-8.png)
2.  **选择物流公司**：
    * 点击物流公司下拉框，从列表中选择一个物流公司
    * 系统会显示常用的物流公司供选择
    ![选择物流公司](images/订单管理/订单发货-9.png)
3.  **填写快递单号**：
    * 在快递单号输入框中，输入正确的快递单号
    * 确保单号准确无误，避免后续物流查询问题
    ![填写快递单号](images/订单管理/订单发货-10.png)
4.  **确认买家信息**：
    * 检查买家信息区域，确认收货人、联系电话和收货地址正确
    * 这些信息是系统自动填充的，无需手动输入
    ![确认买家信息](images/订单管理/订单发货-11.png)
5.  **确认订单信息**：
    * 检查订单信息区域，确认订单编号和创建时间正确
    * 这些信息是系统自动填充的，无需手动输入
    ![确认订单信息](images/订单管理/订单发货-12.png)
6.  **提交发货**：
    * 确认所有信息无误后，点击页面底部的"发货"按钮
    * 系统会提示"处理中..."，请耐心等待
    ![提交发货](images/订单管理/订单发货-13.png)
7.  **发货成功**：
    * 发货成功后，系统会显示"发货成功"提示
    * 订单状态会自动更新为"待收货"
    * 系统会自动返回订单列表页面
    ![发货成功](images/订单管理/订单发货-14.png)

##### 多包裹发货说明

如果订单中的商品需要分多个包裹发货，可以使用多包裹发货功能：

1.  **添加包裹**：点击发货页面顶部的"添加包裹"按钮，系统会新增一个包裹。
    ![添加包裹](images/订单管理/订单发货-15.png)
2.  **为每个包裹选择商品**：
    * 点击每个包裹的"添加商品"按钮，选择要放入该包裹的商品
    * 同一个商品不能添加到多个包裹中
    ![为每个包裹选择商品](images/订单管理/订单发货-16.png)
3.  **为每个包裹填写物流信息**：
    * 为每个包裹选择物流公司并填写快递单号
    * 不同包裹可以选择不同的物流公司
    ![为每个包裹填写物流信息](images/订单管理/订单发货-17.png)
4.  **删除包裹**：如需删除多余的包裹，点击包裹右上角的"删除包裹"按钮。
    ![删除包裹](images/订单管理/订单发货-18.png)

#### 注意事项
* 发货前请确认商品已经实际打包并交付物流公司
* 快递单号必须准确填写，否则买家无法正常查询物流信息
* 一旦发货成功，订单状态将无法回退到"待发货"状态
* 如发现填写错误，请联系平台客服处理
* 多包裹发货时，必须确保所有商品都被分配到包裹中
* 系统会自动记录发货时间，作为后续交易凭证

### 4.4 物流信息管理

#### 功能概述
物流信息管理功能允许商家查看已发货订单的物流跟踪信息，了解商品的配送状态。通过该功能，商家可以及时掌握订单的物流动态，解答买家关于物流的咨询，提高客户满意度。

#### 操作路径
1.  点击底部导航栏的"订单"选项，进入订单管理页面
2.  在"待收货"或"已收货"标签下找到需要查看物流的订单
3.  点击"查看物流"按钮或进入订单详情页点击"查看物流"按钮

#### 操作步骤

##### 方式一：从订单列表查看物流

1.  **进入待收货或已收货订单列表**：在订单管理页面，点击顶部的"待收货"或"已收货"标签。
    ![进入待收货订单列表](images/订单管理/物流信息-1.png)
2.  **找到需要查看物流的订单**：在订单列表中，找到需要查看物流的订单。
    ![找到需要查看物流的订单](images/订单管理/物流信息-2.png)
3.  **点击查看物流按钮**：点击订单项右下角的"查看物流"按钮。
    ![点击查看物流按钮](images/订单管理/物流信息-3.png)
4.  **进入物流信息页面**：系统跳转到物流信息页面。
    ![进入物流信息页面](images/订单管理/物流信息-4.png)

##### 方式二：从订单详情查看物流

1.  **进入订单详情页面**：在订单列表中点击订单项，进入订单详情页面。
    ![进入订单详情页面](images/订单管理/物流信息-5.png)
2.  **点击查看物流按钮**：在订单详情页面顶部，点击"查看物流"按钮。
    ![点击查看物流按钮](images/订单管理/物流信息-6.png)
3.  **进入物流信息页面**：系统跳转到物流信息页面。
    ![进入物流信息页面](images/订单管理/物流信息-7.png)

##### 查看物流基本信息

物流信息页面顶部显示包裹的基本信息：

1.  **包裹选择**：如果订单包含多个包裹，可以点击不同的包裹标签切换查看。
    ![包裹选择](images/订单管理/物流信息-8.png)
2.  **物流基本信息**：显示供应商、运单号码、物流公司等基本信息。
    ![物流基本信息](images/订单管理/物流信息-9.png)
3.  **收货和退货信息**：显示收货人和退货地址信息。
    ![收货和退货信息](images/订单管理/物流信息-10.png)

##### 查看物流跟踪详情

物流信息页面下方显示物流跟踪的详细信息：

1.  **物流时间线**：以时间线形式展示物流包裹的运输过程和状态变化。
    ![物流时间线](images/订单管理/物流信息-11.png)
2.  **最新物流状态**：时间线顶部显示最新的物流状态，用红色标记突出显示。
    ![最新物流状态](images/订单管理/物流信息-12.png)
3.  **历史物流记录**：时间线按时间倒序显示物流包裹的历史运输记录。
    ![历史物流记录](images/订单管理/物流信息-13.png)
4.  **暂无物流信息**：如果物流公司尚未更新物流信息，会显示"您的快件还未发货，请耐心等待..."提示。
    ![暂无物流信息](images/订单管理/物流信息-14.png)

##### 刷新物流信息

物流信息页面支持手动刷新，获取最新的物流动态：

1.  **下拉刷新**：在物流信息页面，向下拉动屏幕，触发下拉刷新功能，更新物流信息。
    ![下拉刷新](images/订单管理/物流信息-15.png)
2.  **自动刷新**：每次进入物流信息页面，系统会自动从物流服务商获取最新的物流信息。

#### 注意事项
* 物流信息由第三方物流公司提供，更新可能存在一定延迟
* 刚发货的订单可能暂无物流信息，需等待物流公司更新
* 如物流信息长时间未更新，可能是物流公司系统延迟或单号有误
* 物流信息页面支持下拉刷新，可随时获取最新物流动态
* 多包裹订单需分别查看各包裹的物流信息
* 如买家咨询物流问题，可截图物流信息页面提供给买家参考

### 4.5 订单搜索与筛选

#### 功能概述
订单搜索与筛选功能允许商家通过输入关键信息或设置筛选条件，快速找到特定的订单。这对于订单量大的商家特别有用，可以提高订单管理效率，节省查找时间。

#### 操作路径
1.  点击底部导航栏的"订单"选项，进入订单管理页面
2.  点击页面顶部的搜索图标，进入订单搜索页面

#### 操作步骤

##### 进入订单搜索页面

1.  **进入订单管理页面**：点击底部导航栏的"订单"选项，进入订单管理页面。
    ![进入订单管理页面](images/订单管理/订单搜索-1.png)
2.  **点击搜索图标**：点击页面顶部的搜索图标，进入订单搜索页面。
    ![点击搜索图标](images/订单管理/订单搜索-2.png)
3.  **进入搜索页面**：系统跳转到订单搜索页面。
    ![进入搜索页面](images/订单管理/订单搜索-3.png)

##### 输入搜索内容

订单搜索页面上半部分是搜索内容区域，可以输入以下信息进行搜索：

1.  **订单编号**：输入完整或部分订单编号。
    ![订单编号](images/订单管理/订单搜索-4.png)
2.  **收件人名字**：输入收货人的姓名。
    ![收件人名字](images/订单管理/订单搜索-5.png)
3.  **手机号码**：输入买家的手机号码。
    ![手机号码](images/订单管理/订单搜索-6.png)
4.  **商品名称**：输入订单中包含的商品名称。
    ![商品名称](images/订单管理/订单搜索-7.png)

##### 设置筛选条件

订单搜索页面下半部分是筛选条件区域，可以设置以下筛选条件：

1.  **下单时间**：选择订单创建的时间范围。
    * 点击时间选择器，弹出日期选择界面
    * 选择起始日期和结束日期
    * 系统会筛选该时间范围内创建的订单
    ![下单时间](images/订单管理/订单搜索-8.png)
2.  **订单状态**：选择特定的订单状态。
    * 点击状态下拉框，显示所有订单状态选项
    * 选择一个状态（待付款、待发货、待收货、交易成功、交易失败）
    * 系统会筛选该状态的订单
    ![订单状态](images/订单管理/订单搜索-9.png)

##### 执行搜索

完成搜索内容输入和筛选条件设置后，执行搜索操作：

1.  **重置**：如需清空所有输入和选择，点击页面底部的"重置"按钮。
    ![重置](images/订单管理/订单搜索-10.png)
2.  **确认**：点击页面底部的"确认"按钮，执行搜索操作。
    ![确认](images/订单管理/订单搜索-11.png)
3.  **查看搜索结果**：系统跳转到搜索结果页面，显示符合条件的订单列表。
    ![查看搜索结果](images/订单管理/订单搜索-12.png)

##### 搜索结果操作

在搜索结果页面，可以进行以下操作：

1.  **查看订单详情**：点击订单项，进入订单详情页面。
    ![查看订单详情](images/订单管理/订单搜索-13.png)
2.  **执行订单操作**：根据订单状态，可以执行发货、查看物流等操作。
    ![执行订单操作](images/订单管理/订单搜索-14.png)
3.  **返回搜索页面**：点击页面左上角的返回按钮，返回订单搜索页面修改搜索条件。
    ![返回搜索页面](images/订单管理/订单搜索-15.png)
4.  **返回订单列表**：点击页面左上角的返回按钮两次，或使用手机的返回功能，返回订单列表页面。
    ![返回订单列表](images/订单管理/订单搜索-16.png)

#### 搜索技巧

1.  **精确搜索**：
    * 使用订单编号进行搜索，可以精确定位到特定订单
    * 使用买家手机号码搜索，可以找到该买家的所有订单
2.  **组合搜索**：
    * 同时设置多个搜索条件，可以缩小搜索范围
    * 例如：结合商品名称和订单状态，可以找到特定商品的待发货订单
3.  **时间范围搜索**：
    * 设置合适的时间范围，避免结果过多
    * 对于近期订单，可以设置最近一周或一个月的时间范围
    * 对于历史订单，可以设置特定月份或季度的时间范围

#### 注意事项
* 搜索功能会在所有订单中进行查找，不限于当前选中的状态标签
* 搜索条件越具体，搜索结果越精确
* 如果搜索结果为空，建议减少搜索条件或使用更通用的关键词
* 订单编号、收件人名字、手机号码和商品名称支持模糊搜索，无需输入完整信息
* 搜索结果页面支持下拉刷新和上拉加载更多功能

---

## 5. 资金管理

### 5.1 账户余额查看

#### 功能概述
账户余额查看功能允许商家随时了解店铺的资金状况，包括可提现余额、冻结金额、提现中金额和已提现金额。通过这些数据，商家可以实时掌握店铺的资金流向和结算情况，便于财务管理和决策。

#### 操作路径
账户余额信息可以在首页和资金明细页面查看。

#### 操作步骤

##### 在首页查看账户余额

1.  **进入首页**：打开小程序，默认进入首页，或点击底部导航栏的"首页"选项。
    ![进入首页](images/资金管理/账户余额-1.png)
2.  **查看账户余额卡片**：首页顶部显示账户余额卡片，包含以下信息：
    * 账户余额：显示当前可提现的资金金额
    * 刷新按钮：点击可刷新余额数据
    * 提现按钮：点击可进入提现申请页面
    ![账户余额卡片](images/资金管理/账户余额-2.png)
3.  **查看资金明细区域**：账户余额卡片下方显示资金明细区域，包含以下信息：
    * 冻结金额：显示当前被冻结的资金总额
    * 提现中：显示已申请提现但尚未到账的资金总额
    * 已提现：显示成功提现到商家银行卡的资金累计总额
    ![资金明细区域](images/资金管理/账户余额-3.png)
4.  **刷新余额数据**：有两种方式可以刷新余额数据：
    * 点击账户余额卡片右上角的刷新图标
    * 下拉首页触发页面刷新
    ![刷新余额数据](images/资金管理/账户余额-4.png)

##### 在资金明细页面查看账户余额

1.  **进入资金明细页面**：点击底部导航栏的"资金"选项，进入资金明细页面。
    ![进入资金明细页面](images/资金管理/账户余额-5.png)
2.  **查看账户余额**：资金明细页面顶部会显示当前账户余额。
    ![查看账户余额](images/资金管理/账户余额-6.png)

#### 账户余额说明

1.  **可提现余额**：
    * 商家当前可以申请提现的资金金额
    * 来源于订单交易完成后的结算金额
    * 减去已申请提现的金额
2.  **冻结金额**：
    * 订单完成后，按平台规则需要冻结一定时间才能结算的资金
    * 冻结期通常为T+1或T+7天，具体以平台规则为准
    * 冻结期满后，资金会自动转入可提现余额
3.  **提现中金额**：
    * 商家已申请提现但尚未到账的资金
    * 提现申请审核通过后，资金会从可提现余额转入提现中状态
    * 提现处理完成后，资金会从提现中状态转出
4.  **已提现金额**：
    * 商家成功提现到银行卡的资金累计总额
    * 反映了商家历史提现的总体情况

#### 注意事项
* 账户余额数据每次进入首页或资金明细页面时会自动刷新
* 如发现余额数据异常，可尝试手动刷新或联系平台客服
* 冻结金额会按照平台规则自动解冻，无需商家手动操作
* 账户余额为0时无法进行提现操作

### 5.2 资金明细查询

#### 功能概述
资金明细查询功能允许商家查看店铺的所有资金流水记录，包括收入和支出明细。通过资金明细，商家可以了解每笔资金的来源、去向、时间和金额，便于核对账目和财务管理。

#### 操作路径
1.  点击底部导航栏的"资金"选项，进入资金明细页面

#### 操作步骤

##### 进入资金明细页面

1.  **打开小程序**：进入八闽助业集市商家端小程序。
    ![打开小程序](images/资金管理/资金明细-1.png)
2.  **进入资金明细页面**：点击底部导航栏的"资金"选项，进入资金明细页面。
    ![进入资金明细页面](images/资金管理/资金明细-2.png)

##### 查看不同类型的资金明细

资金明细页面顶部设有类型分类标签，包括以下几种类型：

1.  **全部**：显示所有类型的资金明细记录。
    ![全部明细](images/资金管理/资金明细-3.png)
2.  **收入**：仅显示收入类型的资金明细记录，如订单结算、退款返还等。
    ![收入明细](images/资金管理/资金明细-4.png)
3.  **支出**：仅显示支出类型的资金明细记录，如提现、手续费等。
    ![支出明细](images/资金管理/资金明细-5.png)

切换类型分类的方法：
* 点击顶部对应的类型标签，即可切换到该类型的资金明细列表
* 左右滑动屏幕，也可以在不同类型的资金明细列表之间切换

##### 资金明细列表信息展示

在资金明细列表中，每条记录都会显示以下信息：
* 资金类型图标和名称（如"订单结算"、"提现"等）
* 交易时间
* 交易金额（收入为绿色，支出为红色）
* 交易后账户余额

![明细信息展示](images/资金管理/资金明细-6.png)

##### 刷新资金明细列表

资金明细列表支持两种刷新方式：

1.  **下拉刷新**：在资金明细列表页面，向下拉动屏幕，触发下拉刷新功能，刷新当前类型的资金明细列表。
    ![下拉刷新](images/资金管理/资金明细-7.png)
2.  **切换标签刷新**：切换不同的类型标签时，系统会自动刷新对应类型的资金明细列表。

##### 加载更多资金明细

当资金明细记录较多时，系统会采用分页加载的方式：

1.  **滚动加载**：向上滚动资金明细列表至底部，系统会自动加载更多资金明细记录。
    ![滚动加载](images/资金管理/资金明细-8.png)
2.  **加载状态提示**：
    * 加载中：显示"加载中..."提示
    * 加载完成：显示"没有更多数据了"提示

#### 资金明细类型说明

1.  **收入类型**：
    * 订单结算：买家订单完成后的商品金额结算
    * 退款返还：因订单退款而返还的手续费
    * 其他收入：平台活动奖励、补贴等其他收入
2.  **支出类型**：
    * 提现：商家申请提现的金额
    * 提现手续费：提现时产生的服务费
    * 其他支出：平台扣除的其他费用

#### 注意事项
* 资金明细记录按时间倒序排列，最新的记录显示在最前面
* 资金明细数据每次进入页面时会自动刷新
* 如需查询特定时间段的资金明细，可使用平台提供的对账单功能
* 资金明细记录保存期限为一年，超过一年的记录可能无法查询

### 5.3 提现操作指南

#### 功能概述
提现功能允许商家将店铺账户中的可提现余额转入自己的银行卡。通过提现操作，商家可以实现线上销售收入向线下资金的转换，满足日常经营和资金周转需求。

#### 操作路径
提现功能有两种进入方式：
1.  在首页点击账户余额卡片上的"提现"按钮
2.  在首页点击"提现中"或"已提现"区域，进入提现明细页面，再点击"申请提现"按钮

#### 操作步骤

##### 方式一：从首页进入提现页面

1.  **进入首页**：打开小程序，默认进入首页，或点击底部导航栏的"首页"选项。
    ![进入首页](images/资金管理/提现操作-1.png)
2.  **点击提现按钮**：在首页账户余额卡片上，点击"提现"按钮。
    ![点击提现按钮](images/资金管理/提现操作-2.png)
3.  **进入提现页面**：系统跳转到提现申请页面。
    ![进入提现页面](images/资金管理/提现操作-3.png)

##### 方式二：从提现明细页面进入

1.  **进入提现明细页面**：在首页点击"提现中"或"已提现"区域，进入提现明细页面。
    ![进入提现明细页面](images/资金管理/提现操作-4.png)
2.  **点击申请提现**：在提现明细页面，点击"申请提现"按钮。
    ![点击申请提现](images/资金管理/提现操作-5.png)
3.  **进入提现页面**：系统跳转到提现申请页面。
    ![进入提现页面](images/资金管理/提现操作-6.png)

##### 填写提现信息

1.  **输入提现金额**：
    * 在提现金额输入框中，输入需要提现的金额
    * 系统会显示可提现金额和最低提现金额
    * 提现金额不能小于最低提现金额（通常为30元）
    * 提现金额不能大于可提现余额
    ![输入提现金额](images/资金管理/提现操作-7.png)
2.  **选择提现方式**：
    * 目前仅支持提现到银行卡
    * 如果已绑定银行卡，系统会显示银行卡信息
    * 如果未绑定银行卡，需要先绑定银行卡
    ![选择提现方式](images/资金管理/提现操作-8.png)
3.  **查看服务费和实际到账金额**：
    * 系统会根据提现金额自动计算服务费
    * 服务费通常为提现金额的0.4%
    * 实际到账金额 = 提现金额 - 服务费
    ![查看服务费和实际到账金额](images/资金管理/提现操作-9.png)
4.  **确认提现**：
    * 确认提现信息无误后，点击"确认"按钮
    * 系统会弹出确认提示框，再次确认提现信息
    * 点击提示框中的"确认"按钮，提交提现申请
    ![确认提现](images/资金管理/提现操作-10.png)
5.  **提现申请成功**：
    * 提现申请成功后，系统会显示"提现申请成功"提示
    * 提现金额会从可提现余额中扣除，转入提现中状态
    * 系统会自动跳转到提现明细页面
    ![提现申请成功](images/资金管理/提现操作-11.png)

##### 绑定银行卡

如果您尚未绑定银行卡，需要先完成银行卡绑定：

1.  **进入绑定银行卡页面**：
    * 在提现页面，如果未绑定银行卡，点击提现方式区域
    * 系统会提示"您还未设置提现银行卡，是否前往设置？"
    * 点击"确定"按钮，进入绑定银行卡页面
    ![进入绑定银行卡页面](images/资金管理/提现操作-12.png)
2.  **填写银行卡信息**：
    * 输入持卡人姓名（必须与商家实名认证信息一致）
    * 选择银行（从下拉列表中选择银行名称）
    * 输入银行卡号
    * 输入开户支行（可选）
    ![填写银行卡信息](images/资金管理/提现操作-13.png)
3.  **提交银行卡信息**：
    * 确认信息无误后，点击"确认"按钮
    * 绑定成功后，系统会返回提现页面
    * 此时可以看到已绑定的银行卡信息
    ![提交银行卡信息](images/资金管理/提现操作-14.png)

#### 提现规则说明

1.  **提现金额限制**：
    * 最低提现金额：30元（可能会根据平台规则调整）
    * 最高提现金额：无限制，但不能超过可提现余额
2.  **提现服务费**：
    * 每笔提现按照提现金额的0.4%收取服务费
    * 服务费从提现金额中扣除，实际到账金额 = 提现金额 - 服务费
3.  **提现处理时间**：
    * 提现申请提交后，平台会在1-3个工作日内处理
    * 处理完成后，资金会转入您绑定的银行卡
    * 节假日可能会延长处理时间
4.  **提现状态流转**：
    * 待审核：提现申请提交后的初始状态
    * 待打款：审核通过，等待转账
    * 已付款：转账完成，资金已到账
    * 失败：提现申请被拒绝或转账失败

#### 注意事项
* 提现前请确认银行卡信息准确无误，错误的银行卡信息可能导致提现失败
* 提现申请提交后无法撤销，请谨慎操作
* 如提现申请长时间未处理，请联系平台客服
* 为保障资金安全，建议定期修改账号密码
* 提现到账时间受银行系统影响，可能会有所延迟

### 5.4 账单与结算记录

#### 功能概述
账单与结算记录功能允许商家查看店铺的资金结算情况，包括待结算资金（冻结金额）、提现记录和结算规则。通过这些信息，商家可以了解资金结算的进度和历史记录，便于财务管理和对账。

#### 操作路径
账单与结算记录功能有多种进入方式：
1.  在首页点击"冻结金额"区域，进入待结算明细页面
2.  在首页点击"提现中"区域，进入提现明细页面（待审核/待打款标签）
3.  在首页点击"已提现"区域，进入提现明细页面（已付款标签）

#### 操作步骤

##### 查看待结算明细（冻结金额）

1.  **进入首页**：打开小程序，默认进入首页，或点击底部导航栏的"首页"选项。
    ![进入首页](images/资金管理/账单结算-1.png)
2.  **点击冻结金额**：在首页资金明细区域，点击"冻结金额"项。
    ![点击冻结金额](images/资金管理/账单结算-2.png)
3.  **进入待结算明细页面**：系统跳转到待结算明细页面，显示所有处于冻结状态的资金记录。
    ![待结算明细页面](images/资金管理/账单结算-3.png)
4.  **查看待结算明细**：待结算明细页面显示以下信息：
    * 订单编号
    * 冻结金额
    * 冻结时间
    * 预计解冻时间
    ![查看待结算明细](images/资金管理/账单结算-4.png)
5.  **刷新待结算明细**：下拉页面可刷新待结算明细列表。
    ![刷新待结算明细](images/资金管理/账单结算-5.png)

##### 查看提现记录

1.  **进入首页**：打开小程序，默认进入首页，或点击底部导航栏的"首页"选项。
    ![进入首页](images/资金管理/账单结算-6.png)
2.  **点击提现中或已提现**：在首页资金明细区域，点击"提现中"或"已提现"项。
    ![点击提现中或已提现](images/资金管理/账单结算-7.png)
3.  **进入提现明细页面**：系统跳转到提现明细页面，默认显示所有提现记录。
    ![提现明细页面](images/资金管理/账单结算-8.png)
4.  **查看不同状态的提现记录**：提现明细页面顶部设有状态分类标签，包括：
    * 所有：显示所有状态的提现记录
    * 待审核：显示等待平台审核的提现申请
    * 待打款：显示审核通过等待转账的提现申请
    * 已付款：显示已成功转账的提现记录
    * 失败：显示被拒绝或转账失败的提现记录
    ![不同状态的提现记录](images/资金管理/账单结算-9.png)
5.  **查看提现详情**：点击任一提现记录，可查看详细信息：
    * 提现金额
    * 服务费
    * 实际到账金额
    * 银行卡信息
    * 申请时间
    * 处理时间
    * 提现状态
    ![查看提现详情](images/资金管理/账单结算-10.png)
6.  **刷新提现记录**：下拉页面可刷新提现记录列表。
    ![刷新提现记录](images/资金管理/账单结算-11.png)

#### 结算规则说明

1.  **资金冻结规则**：
    * 买家确认收货后，订单金额进入冻结状态
    * 冻结期通常为T+1或T+7天，具体以平台规则为准
    * 冻结期满后，资金自动解冻并转入可提现余额
2.  **提现处理规则**：
    * 提现申请提交后，需经过平台审核
    * 审核通过后，进入待打款状态
    * 平台完成转账后，状态变更为已付款
    * 如审核不通过或转账失败，状态变更为失败
3.  **结算周期**：
    * 资金结算周期以平台公布的规则为准
    * 通常情况下，正常订单为T+1或T+7天结算
    * 特殊情况（如节假日）可能会延长结算周期

#### 注意事项
* 待结算资金会按照平台规则自动解冻，无需商家手动操作
* 提现记录保存期限为一年，超过一年的记录可能无法查询
* 如发现结算异常，请及时联系平台客服处理
* 建议定期核对待结算明细和提现记录，确保资金准确无误
* 平台可能会根据业务需要调整结算规则，请关注平台公告

---

## 6. 个人中心

### 6.1 个人信息查看与修改 (注：实施计划中为“商家信息管理”，此处采用模块文档标题)

#### 功能概述
个人信息查看与修改功能允许商家查看和管理自己的账号信息，包括店铺名称、账号信息等。通过这些功能，商家可以随时了解自己的账号状态，并根据需要进行信息更新。

#### 操作路径
1.  点击底部导航栏的"我的"选项，进入个人中心页面

#### 操作步骤

##### 进入个人中心页面

1.  **打开小程序**：进入八闽助业集市商家端小程序。
    ![打开小程序](images/个人中心/个人信息-1.png)
2.  **进入个人中心页面**：点击底部导航栏的"我的"选项，进入个人中心页面。
    ![进入个人中心页面](images/个人中心/个人信息-2.png)

##### 查看个人信息

个人中心页面顶部显示商家的基本信息：

1.  **店铺头像**：显示商家的店铺头像，如未设置则显示默认头像。
    ![店铺头像](images/个人中心/个人信息-3.png)
2.  **店铺名称**：显示商家的店铺名称，如未设置则显示"未设置店铺名称"。
    ![店铺名称](images/个人中心/个人信息-4.png)
3.  **账号信息**：显示商家的登录账号（通常是手机号码）。
    ![账号信息](images/个人中心/个人信息-5.png)

##### 修改个人信息

要修改个人信息，需要点击头像区域进入店铺信息页面：

1.  **点击头像区域**：在个人中心页面，点击头像区域。
    ![点击头像区域](images/个人中心/个人信息-6.png)
2.  **进入店铺信息页面**：系统跳转到店铺信息页面，显示更详细的店铺信息。
    ![进入店铺信息页面](images/个人中心/个人信息-7.png)
3.  **修改店铺信息**：在店铺信息页面，可以修改以下信息：
    * 店铺头像：点击头像区域，可以上传新的店铺头像
    * 店铺名称：点击店铺名称区域，可以修改店铺名称
    * 店铺简介：点击店铺简介区域，可以修改店铺简介
    ![修改店铺信息](images/个人中心/个人信息-8.png)
4.  **保存修改**：完成信息修改后，点击页面底部的"保存"按钮，保存修改的信息。
    ![保存修改](images/个人中心/个人信息-9.png)
5.  **修改成功**：修改成功后，系统会显示"保存成功"提示，并自动返回个人中心页面。
    ![修改成功](images/个人中心/个人信息-10.png)

#### 注意事项
* 店铺名称是商家在平台上的重要标识，建议设置为易于识别和记忆的名称
* 店铺头像建议使用清晰、美观的图片，以提升店铺形象
* 部分基本信息（如账号）无法直接修改，如需变更请联系平台客服
* 信息修改后会立即生效，在平台各处展示的都是最新信息

### 6.2 店铺信息管理 (注：实施计划中为“店铺信息设置”，此处采用模块文档标题)

#### 功能概述
店铺信息管理功能允许商家查看和管理店铺的详细信息，包括店铺头像、店铺名称、店铺简介等。通过完善店铺信息，商家可以提升店铺形象，增强买家信任感，促进销售转化。

#### 操作路径
1.  点击底部导航栏的"我的"选项，进入个人中心页面
2.  点击头像区域，进入店铺信息页面

#### 操作步骤

##### 进入店铺信息页面

1.  **进入个人中心页面**：点击底部导航栏的"我的"选项，进入个人中心页面。
    ![进入个人中心页面](images/个人中心/店铺信息-1.png)
2.  **点击头像区域**：在个人中心页面，点击头像区域。
    ![点击头像区域](images/个人中心/店铺信息-2.png)
3.  **进入店铺信息页面**：系统跳转到店铺信息页面。
    ![进入店铺信息页面](images/个人中心/店铺信息-3.png)

##### 管理店铺头像

1.  **点击店铺头像**：在店铺信息页面，点击头像区域。
    ![点击店铺头像](images/个人中心/店铺信息-4.png)
2.  **选择图片来源**：系统弹出选择框，可以选择"拍照"或"从相册选择"。
    ![选择图片来源](images/个人中心/店铺信息-5.png)
3.  **选择/拍摄图片**：根据选择的来源，拍摄新照片或从相册中选择图片。
    ![选择/拍摄图片](images/个人中心/店铺信息-6.png)
4.  **裁剪图片**：选择图片后，系统会提供裁剪功能，可以调整图片显示区域。
    ![裁剪图片](images/个人中心/店铺信息-7.png)
5.  **确认上传**：裁剪完成后，点击"确定"按钮，上传新的店铺头像。
    ![确认上传](images/个人中心/店铺信息-8.png)

##### 修改店铺名称

1.  **点击店铺名称区域**：在店铺信息页面，点击店铺名称区域。
    ![点击店铺名称区域](images/个人中心/店铺信息-9.png)
2.  **输入新店铺名称**：在弹出的输入框中，输入新的店铺名称。
    ![输入新店铺名称](images/个人中心/店铺信息-10.png)
3.  **确认修改**：输入完成后，点击"确定"按钮，保存新的店铺名称。
    ![确认修改](images/个人中心/店铺信息-11.png)

##### 修改店铺简介

1.  **点击店铺简介区域**：在店铺信息页面，点击店铺简介区域。
    ![点击店铺简介区域](images/个人中心/店铺信息-12.png)
2.  **输入新店铺简介**：在弹出的输入框中，输入新的店铺简介。
    ![输入新店铺简介](images/个人中心/店铺信息-13.png)
3.  **确认修改**：输入完成后，点击"确定"按钮，保存新的店铺简介。
    ![确认修改](images/个人中心/店铺信息-14.png)

##### 保存店铺信息

1.  **点击保存按钮**：完成所有修改后，点击页面底部的"保存"按钮。
    ![点击保存按钮](images/个人中心/店铺信息-15.png)
2.  **保存成功**：系统显示"保存成功"提示，并自动返回个人中心页面。
    ![保存成功](images/个人中心/店铺信息-16.png)

#### 注意事项
* 店铺头像建议使用清晰、美观的图片，尺寸建议为正方形，以获得最佳显示效果
* 店铺名称长度通常有限制，建议控制在20个字符以内
* 店铺简介可以简要介绍店铺特色、经营理念等，有助于提升店铺形象
* 所有信息修改后需点击"保存"按钮才能生效，否则修改将丢失
* 店铺信息是买家了解商家的重要途径，建议定期更新和完善

### 6.3 安全设置 (注：实施计划中为“账号安全设置”，此处采用模块文档标题)

#### 功能概述
安全设置功能允许商家管理账号的安全相关设置，包括修改登录密码和管理银行卡信息。通过这些功能，商家可以保障账号安全，并便捷地管理提现银行卡，确保资金安全。

#### 操作路径
1.  点击底部导航栏的"我的"选项，进入个人中心页面
2.  点击"账号安全"选项，进入账号安全页面

#### 操作步骤

##### 进入账号安全页面

1.  **进入个人中心页面**：点击底部导航栏的"我的"选项，进入个人中心页面。
    ![进入个人中心页面](images/个人中心/安全设置-1.png)
2.  **点击账号安全**：在个人中心页面，点击"账号安全"选项。
    ![点击账号安全](images/个人中心/安全设置-2.png)
3.  **进入账号安全页面**：系统跳转到账号安全页面，显示安全设置选项。
    ![进入账号安全页面](images/个人中心/安全设置-3.png)

##### 修改登录密码

1.  **点击修改密码**：在账号安全页面，点击"修改密码"选项。
    ![点击修改密码](images/个人中心/安全设置-4.png)
2.  **进入修改密码页面**：系统跳转到修改密码页面。
    ![进入修改密码页面](images/个人中心/安全设置-5.png)
3.  **输入新密码**：在修改密码页面，输入新密码。
    ![输入新密码](images/个人中心/安全设置-6.png)
4.  **确认新密码**：再次输入新密码进行确认。
    ![确认新密码](images/个人中心/安全设置-7.png)
5.  **点击确定按钮**：输入完成后，点击页面底部的"确定"按钮。
    ![点击确定按钮](images/个人中心/安全设置-8.png)
6.  **修改成功**：系统显示"密码修改成功"提示，并自动返回账号安全页面。
    ![修改成功](images/个人中心/安全设置-9.png)

##### 管理银行卡

1.  **点击我的银行卡**：在账号安全页面，点击"我的银行卡"选项。
    ![点击我的银行卡](images/个人中心/安全设置-10.png)
2.  **进入银行卡页面**：系统跳转到银行卡页面。
    ![进入银行卡页面](images/个人中心/安全设置-11.png)
3.  **查看银行卡信息**：如果已绑定银行卡，页面会显示银行卡信息；如果未绑定，则显示"您还未绑定银行卡"。
    ![查看银行卡信息](images/个人中心/安全设置-12.png)
4.  **绑定银行卡**：如果未绑定银行卡，点击"去绑定"按钮；如果已绑定，点击页面底部的"修改绑定"按钮。
    ![绑定银行卡](images/个人中心/安全设置-13.png)
5.  **进入绑定银行卡页面**：系统跳转到绑定银行卡页面。
    ![进入绑定银行卡页面](images/个人中心/安全设置-14.png)
6.  **填写银行卡信息**：在绑定银行卡页面，填写以下信息：
    * 手机号：输入银行预留手机号
    * 银行卡号：输入银行卡号
    * 户名：输入银行卡对应的户名
    * 证件号：输入证件号（身份证）
    * 开户银行：选择收款银行
    ![填写银行卡信息](images/个人中心/安全设置-15.png)
7.  **点击确定按钮**：填写完成后，点击页面底部的"确定"按钮。
    ![点击确定按钮](images/个人中心/安全设置-16.png)
8.  **确认提交**：系统弹出确认提示，点击"确认"按钮。
    ![确认提交](images/个人中心/安全设置-17.png)
9.  **绑定成功**：系统显示"绑定成功"提示，并自动返回银行卡页面。
    ![绑定成功](images/个人中心/安全设置-18.png)

#### 密码安全规则

1.  **密码长度要求**：密码长度为8-20位。
2.  **密码复杂度要求**：密码必须包含字母和数字，可以包含特殊字符。
3.  **密码修改频率**：建议定期修改密码，提高账号安全性。
4.  **密码保存**：请妥善保管密码，不要与他人共享。

#### 银行卡绑定规则

1.  **银行卡类型**：目前仅支持绑定个人银行卡（对私账户）。
2.  **绑定数量限制**：每个商家账号只能绑定一张银行卡。
3.  **信息要求**：
    * 户名必须与商家实名认证信息一致
    * 证件号必须与商家实名认证信息一致
    * 银行卡必须是持卡人本人的银行卡
4.  **信息修改限制**：户名和证件号设置后无法更改，请如实填写。

#### 注意事项
* 修改密码后，需要使用新密码重新登录
* 忘记密码时，可通过平台客服申请重置密码
* 银行卡信息关系到资金安全，请确保信息准确无误
* 绑定银行卡后，提现资金将转入该银行卡
* 如需更换银行卡，可通过"修改绑定"功能更新银行卡信息

### 6.4 退出登录操作
(注：此部分在 "八闽助业集市商家端小程序用户手册实施计划.md" 中提及，但在 "6.个人中心.md" 文件中未明确详细步骤，请根据实际功能补充)

---

## 7. 常见问题与故障排除
(注：此模块在 "八闽助业集市商家端小程序用户手册实施计划.md" 中规划，请根据实际情况补充详细内容，以下为计划中的子模块标题)

### 7.1 登录问题解决
(请补充内容)

### 7.2 商品管理常见问题
(请补充内容)

### 7.3 订单处理常见问题
(请补充内容)

### 7.4 资金相关常见问题
(请补充内容)

### 7.5 系统错误处理指南
(请补充内容)

---

## 附录：常见问题汇总
(注：此部分在 "八闽助业集市商家端小程序用户手册实施计划.md" 中规划，请根据实际情况补充详细内容)

---