# 《八闽助业集市》商城平台管理人员操作手册 - 店铺商品/商品列表

## 1. 功能概述

店铺商品列表功能是平台商品管理系统中的重要组成部分，用于管理和维护平台上所有店铺的商品信息。通过该功能，平台管理人员可以查看、新增、编辑店铺商品，以及进行上下架操作，确保平台商品信息的准确性和及时更新。

![店铺商品列表](../images/店铺商品/商品列表/店铺商品列表.png)

## 2. 页面布局

店铺商品列表页面主要分为三个区域：
- 左侧店铺列表区域：以树形结构展示平台上的所有店铺
- 中间分类列表区域：展示所选店铺的商品分类
- 右侧商品列表区域：展示符合条件的商品信息，包括查询区域、操作按钮区域和数据表格区域

## 3. 操作指南

### 3.1 商品查询

商品列表页面提供了多种查询条件，帮助管理员快速找到目标商品：

1. **选择店铺**：
   - 在左侧店铺列表中点击店铺名称，系统将自动加载该店铺的商品分类和商品列表

2. **选择商品分类**：
   - 在中间分类列表中点击分类名称，系统将自动筛选该分类下的商品

3. **基本查询**：
   - 商品编号：输入商品编号进行精确查询
   - 商品名称：输入商品名称关键字进行模糊查询
   - 状态：从下拉菜单中选择商品状态（全部、停用、启用）
   - 上下架状态：从下拉菜单中选择上下架状态（全部、下架、上架）

4. **执行查询**：
   - 设置好查询条件后，点击"查询"按钮执行查询
   - 系统将显示符合条件的商品列表

5. **重置查询条件**：
   - 点击"重置"按钮，清空所有已设置的查询条件

### 3.2 商品列表浏览

商品列表以表格形式展示商品信息，包含以下主要字段：

- 店铺名称：商品所属的店铺名称
- 商品分类：商品所属的分类名称
- 商品主图：商品的主要展示图片
- 商品编号：商品的唯一编号
- 商品名称：商品的名称
- 市场价：商品的市场参考价格
- 成本价：商品的成本价格（如有多规格，显示价格区间）
- 销售价：商品的实际销售价格（如有多规格，显示价格区间）
- 状态：商品的状态（启用/停用）
- 上下架状态：商品的上下架状态（上架/下架）
- 审核状态：商品的审核状态（草稿、待审核、审核通过、审核不通过）
- 状态说明：商品状态的补充说明
- 有无规格：商品是否有规格（有规格/无规格）
- 创建者：创建商品的操作人
- 创建时间：商品创建的时间
- 操作：提供对商品的操作按钮

### 3.3 批量操作

系统支持对商品记录进行批量选择操作：

1. **批量选择**：
   - 在商品列表左侧的复选框中，选择需要操作的记录
   - 可以单个选择，也可以点击表头的复选框全选当前页的所有记录
   - 页面上方会显示已选择的记录数量
   - 点击"清空"可以取消所有选择

2. **批量上架**：
   - 选择多个商品后，点击"批量上架"按钮
   - 系统弹出确认对话框
   - 点击"确定"按钮，所选商品将被批量上架

3. **批量下架**：
   - 选择多个商品后，点击"批量下架"按钮
   - 系统弹出下架原因输入对话框
   - 输入下架原因后点击"确定"按钮，所选商品将被批量下架

4. **批量删除**：
   - 选择多个商品后，点击"批量操作"下拉菜单
   - 选择"删除"选项
   - 系统弹出确认对话框
   - 点击"确定"按钮，所选商品将被批量删除

### 3.4 新增商品

平台管理员可以为店铺添加新商品，具体操作如下：

1. **点击新增按钮**：
   - 在商品列表页面点击"新增"按钮
   - 系统跳转到商品新增页面

2. **选择商品分类**：
   - 在新增商品页面，首先需要选择商品所属的分类
   
   ![新增商品-选择类目](../images/店铺商品/商品列表/新增商品-选择类目.png)
   
   - 点击分类名称，选择合适的商品分类
   - 点击"确定"按钮进入商品信息填写页面

3. **填写商品基本信息**：
   - 商品名称：输入商品的名称（必填）
   - 商品描述：输入商品的详细描述
   - 商品图片：上传商品的主图（最多5张）
   - 视频地址：上传商品的展示视频（可选）
   - 商品详情图：上传商品的详情图片（最多20张）

4. **设置商品规格和价格**：
   - 添加规格：点击"添加规格"按钮，可以添加最多两个规格维度（如颜色、尺寸等）
   - 填写规格值：为每个规格维度添加具体的规格值
   - 设置价格与库存：为每个规格组合设置销售价、成本价、会员价和库存
   - 填写SKU编码：为每个规格组合设置唯一的SKU编码
   - 上传规格图：为规格组合上传对应的规格图片
   - 设置商品市场价：输入商品的市场参考价格

5. **设置服务与承诺**：
   - 运费模板：选择商品的运费计算方式（包邮、按件、按重量）
   - 自提：选择是否支持自提
   - 配送：选择是否支持配送
   - 服务承诺：选择商品的服务承诺（如7天无理由退换货、假一赔十等）

6. **保存或提交商品**：
   - 保存草稿箱：点击"保存草稿箱"按钮，商品信息将被保存为草稿状态
   - 提交并上架：点击"提交并上架"按钮，商品将被提交审核并在审核通过后上架
   
   ![发布商品](../images/店铺商品/商品列表/发布商品.png)

### 3.5 编辑商品

平台管理员可以编辑已有商品的信息，具体操作如下：

1. **点击编辑按钮**：
   - 在商品列表中找到需要编辑的商品
   - 点击操作列中的"编辑"按钮
   - 系统跳转到商品编辑页面

2. **修改商品信息**：
   - 编辑页面与新增页面布局相同，但已填充现有商品信息
   - 可以修改商品的基本信息、规格价格、服务承诺等内容
   - 修改完成后，可以选择保存为草稿或提交上架

### 3.6 商品上下架操作

平台管理员可以对商品进行上下架操作，控制商品在前台的显示状态：

1. **上架商品**：
   - 在商品列表中找到状态为"下架"的商品
   - 点击操作列中的"上架"按钮
   - 系统弹出确认对话框
   
   ![上架商品](../images/店铺商品/商品列表/上架商品.png)
   
   - 点击"确定"按钮，商品状态将变更为"上架"

2. **下架商品**：
   - 在商品列表中找到状态为"上架"的商品
   - 点击操作列中的"下架"按钮
   - 系统弹出下架原因输入对话框
   
   ![下架商品](../images/店铺商品/商品列表/下架商品.png)
   
   - 输入下架原因后点击"确定"按钮，商品状态将变更为"下架"

### 3.7 商品排序

平台管理员可以调整商品的展示顺序：

1. **点击排序按钮**：
   - 在商品列表中找到需要调整排序的商品
   - 点击操作列中的"排序"按钮
   - 系统弹出排序设置对话框

2. **设置排序值**：
   - 输入排序数值（数值越小，排序越靠前）
   - 点击"确定"按钮保存排序设置

### 3.8 删除商品

平台管理员可以删除不需要的商品：

1. **点击删除按钮**：
   - 在商品列表中找到需要删除的商品
   - 点击操作列中的"更多"下拉菜单
   - 选择"删除"选项
   - 系统弹出确认对话框
   - 点击"确定"按钮，商品将被删除

## 4. 注意事项

1. **商品信息完整性**：
   - 添加或编辑商品时，确保必填信息完整准确
   - 商品图片应清晰展示商品特点，建议使用白底图片
   - 商品详情应详细描述商品的特点、规格、使用方法等信息

2. **规格设置**：
   - 规格名称应简洁明了，如"颜色"、"尺寸"等
   - 规格值应具体明确，如"红色"、"XL"等
   - 有规格商品必须为每个规格组合设置正确的价格和库存

3. **价格设置**：
   - 销售价不能低于成本价
   - 会员价应低于销售价
   - 市场价应高于销售价，作为参考价格展示

4. **上下架操作**：
   - 商品上架前应确保信息完整、图片清晰、价格合理
   - 下架商品时应填写明确的下架原因，便于后续处理
   - 批量上下架操作应谨慎使用，确保所选商品符合操作条件

5. **商品审核**：
   - 新增或修改的商品需要经过审核才能上架销售
   - 审核不通过的商品需要根据审核意见修改后重新提交

## 5. 常见问题

### 5.1 无法选择商品分类

**问题**：新增商品时无法选择商品分类。

**解决方案**：
- 确认已选择正确的店铺
- 检查该店铺是否已创建商品分类
- 如店铺没有商品分类，需先在商品分类管理中为该店铺添加分类

### 5.2 商品上架失败

**问题**：点击上架按钮后，商品状态未变更为上架。

**解决方案**：
- 检查商品是否已通过审核，只有审核通过的商品才能上架
- 确认商品信息是否完整，包括图片、价格、库存等必要信息
- 检查商品所属店铺是否处于正常营业状态

### 5.3 规格组合过多

**问题**：添加规格后，生成的规格组合过多，难以一一设置价格和库存。

**解决方案**：
- 尽量精简规格设置，避免不必要的规格维度
- 对于颜色、尺寸等常见规格，建议分别作为独立的规格维度
- 可以先设置一个基础价格和库存，然后针对特殊规格组合进行调整
