# 《八闽助业集市》商城操作手册编写指南

## 目录结构

```
docs/
├── images/                 # 存放操作手册中使用的截图
└── 操作手册/
    ├── 平台管理人员操作手册-大纲.md   # 平台管理人员操作手册大纲
    ├── 商家管理员操作手册-大纲.md     # 商家管理员操作手册大纲
    ├── 平台管理人员操作手册.md       # 平台管理人员完整操作手册（待完善）
    └── 商家管理员操作手册.md         # 商家管理员完整操作手册（待完善）
```

## 使用方法

1. **截图存放**：
   - 将业务功能截图存放在 `docs/images/` 目录下
   - 建议按模块分类存放，如 `docs/images/会员管理/`、`docs/images/订单管理/` 等

2. **操作手册编写**：
   - 基于大纲文件，逐步完善各模块的详细内容
   - 在操作手册中使用相对路径引用图片，如 `![会员列表](../images/会员管理/会员列表.png)`

3. **编写流程**：
   - 按照系统导航结构，从上到下、从左到右的顺序编写各模块内容
   - 先完成主要模块，再处理次要模块
   - 对于每个功能页面，建议包含：功能概述、操作步骤、注意事项三部分内容

## 提示词模板

使用以下简化版提示词模板向AI提供信息：

```
【模块类型】：[平台管理/商家管理]

【模块名称】：[模块名称]

【功能页面】：[页面名称]

【页面路径】：[前端路由或文件路径，如果知道]

【截图】：
[此处插入截图]

【需求】：请通过代码分析，生成此页面的操作手册内容
```

## 注意事项

1. 操作手册应以用户视角编写，避免使用技术术语
2. 重点说明操作流程和注意事项，帮助用户快速上手
3. 对于复杂功能，可以使用步骤编号和截图说明
4. 定期更新操作手册，确保与系统功能保持一致
