# 《八闽助业集市》商城平台管理人员操作手册 - 店铺订单/全部订单

## 1. 功能概述

店铺订单/全部订单功能是平台订单管理系统中的重要组成部分，用于管理和处理平台上所有店铺的订单信息。通过该功能，平台管理人员可以查看订单详情、处理订单发货、查看物流信息、处理退款等操作，确保订单流程的顺利进行和交易的安全完成。

![全部订单](../images/店铺订单/全部订单.png)

## 2. 页面布局

全部订单页面主要分为两个区域：
- 查询区域：位于页面上方，用于设置查询条件筛选订单
- 数据表格区域：位于页面主体部分，以表格形式展示订单信息

## 3. 操作指南

### 3.1 订单查询

全部订单页面提供了多种查询条件，帮助管理员快速找到目标订单：

1. **基本查询**：
   - 订单编号：输入订单编号进行精确查询
   - 订单类型：从下拉菜单中选择订单类型（如普通订单、团购订单等）

2. **展开更多查询条件**：
   - 点击查询区域右下方的"展开"按钮，可以显示更多查询条件
   - 展开后可使用的额外条件包括：
     - 买家账号：输入买家的手机号码进行查询
     - 店铺名称：输入店铺的名称关键字进行查询
     - 订单状态：从下拉菜单中选择订单状态（如待发货、待收货、交易成功、交易关闭、交易完成）
     - 关闭时间：选择时间范围，筛选特定时间段关闭的订单
     - 创建时间：选择时间范围，筛选特定时间段创建的订单
     - 付款时间：选择时间范围，筛选特定时间段付款的订单
     - 发货时间：选择时间范围，筛选特定时间段发货的订单
     - 收货时间：选择时间范围，筛选特定时间段收货的订单

3. **执行查询**：
   - 设置好查询条件后，点击"查询"按钮执行查询
   - 系统将显示符合条件的订单列表

4. **重置查询条件**：
   - 点击"重置"按钮，清空所有已设置的查询条件

### 3.2 订单列表浏览

订单列表以表格形式展示订单信息，包含以下主要字段：

- 订单编号：订单的唯一标识
- 店铺名称：订单所属的店铺名称
- 订单类型：订单的类型（如普通订单、团购订单等）
- 买家：买家的手机号码，点击可查看买家详细信息
- 留言：买家下单时的留言信息，点击可查看详细内容
- 商品：订单中的商品信息，点击可查看详细商品列表
- 订单状态：订单的当前状态（如待发货、待收货、交易成功、交易关闭、交易完成）
- 商品总价：订单中所有商品的总价
- 配送方式：订单的配送方式（如快递配送、自提等）
- 实付款：买家实际支付的金额
- 关闭类型：订单关闭的类型（如超时未付款、买家取消等）
- 关闭原因：订单关闭的具体原因
- 关闭时间：订单关闭的时间
- 创建时间：订单创建的时间
- 部分发货：是否为部分发货订单
- 操作：提供对订单的操作按钮，根据订单状态显示不同的操作选项

### 3.3 批量操作

系统支持对订单记录进行批量选择操作：

1. **批量选择**：
   - 在订单列表左侧的复选框中，选择需要操作的记录
   - 可以单个选择，也可以点击表头的复选框全选当前页的所有记录
   - 页面上方会显示已选择的记录数量
   - 点击"清空"可以取消所有选择

### 3.4 查看买家信息

平台管理员可以查看订单的买家信息，具体操作如下：

1. **点击买家账号**：
   - 在订单列表中找到需要查看的订单
   - 点击"买家"列中的手机号码
   - 系统弹出买家信息对话框
   
   ![订单买家信息查看](../images/店铺订单/订单买家信息查看.png)
   
   - 对话框中显示买家的基本信息、收货信息等内容

### 3.5 查看商品信息

平台管理员可以查看订单中的商品信息，具体操作如下：

1. **点击商品信息**：
   - 在订单列表中找到需要查看的订单
   - 点击"商品"列中的"商品信息"链接
   - 系统弹出商品信息对话框
   
   ![查看订单商品信息](../images/店铺订单/查看订单商品信息.png)
   
   - 对话框中显示订单中所有商品的详细信息，包括商品图片、名称、规格、单价、数量等

### 3.6 订单详情查看

平台管理员可以查看订单的详细信息，具体操作如下：

1. **点击详情按钮**：
   - 在订单列表中找到需要查看的订单
   - 点击操作列中的"详情"按钮
   - 系统跳转到订单详情页面
   
   ![查看订单详情](../images/店铺订单/查看订单详情.png)
   
   - 详情页面显示订单的完整信息，包括订单基本信息、买家信息、商品信息、支付信息等

### 3.7 订单发货操作

对于待发货状态的订单，平台管理员需要进行发货操作，具体步骤如下：

1. **点击发货按钮**：
   - 在订单列表中找到状态为"待发货"的订单
   - 点击操作列中的"发货"按钮
   - 系统跳转到发货操作页面

2. **确认收货信息**：
   - 发货页面上方显示订单编号、创建时间、付款时间等基本信息
   - 中部显示买家账号、昵称、收货人、联系电话、收货地址等信息
   - 确认信息无误后，进行下一步操作

3. **添加包裹**：
   - 点击"添加一个包裹"按钮
   - 系统自动创建一个包裹
   
   ![发货-添加包裹](../images/店铺订单/发货-添加包裹.png)

4. **选择发货商品**：
   - 在下方的商品列表中，为每个商品选择对应的包裹
   - 可以将不同商品分配到不同包裹中，实现分批发货

5. **填写物流信息**：
   - 为每个包裹选择物流公司
   - 输入快递单号

6. **确认发货/退货信息**：
   - 确认发货地址信息
   - 确认退货地址信息
   - 如需修改地址，点击"修改"按钮进行更新

7. **提交发货**：
   - 确认所有信息无误后，点击"发货"按钮
   - 系统提交发货信息，订单状态变更为"待收货"

### 3.8 订单取消并退款操作

对于已付款但尚未发货的订单，平台管理员可以进行取消并退款操作，具体步骤如下：

1. **点击取消并退款按钮**：
   - 在订单列表中找到状态为"待发货"的订单
   - 点击操作列中的"取消并退款"按钮
   - 系统弹出取消并退款对话框
   
   ![未发货-取消并退款](../images/店铺订单/未发货-取消并退款.png)

2. **填写退款信息**：
   - 选择关闭类型
   - 输入关闭原因
   - 确认退款金额

3. **提交退款**：
   - 确认信息无误后，点击"确定"按钮
   - 系统处理退款请求，订单状态变更为"交易关闭"

### 3.9 查看物流信息

对于已发货的订单，平台管理员可以查看物流信息，具体操作如下：

1. **点击查看物流按钮**：
   - 在订单列表中找到状态为"待收货"或"交易成功"的订单
   - 点击操作列中的"查看物流"按钮
   - 系统弹出物流信息对话框
   - 对话框中显示物流公司、快递单号、物流轨迹等信息

### 3.10 评价管理

对于已完成的订单，如果买家进行了评价，平台管理员可以查看和审批评价，具体操作如下：

1. **查看评价**：
   - 在订单列表中找到状态为"交易成功"或"交易完成"且有评价的订单
   - 点击操作列中的"查看评价"按钮
   - 系统弹出评价详情对话框
   - 对话框中显示买家的评价内容、评分等信息

2. **审批评价**：
   - 在订单列表中找到状态为"交易成功"或"交易完成"且有评价的订单
   - 点击操作列中的"审批"按钮
   - 系统弹出审批对话框
   - 选择审批结果（通过/不通过）
   - 如选择不通过，需填写不通过原因
   - 点击"确定"按钮提交审批结果

### 3.11 删除订单

对于已关闭的订单，平台管理员可以进行删除操作，具体步骤如下：

1. **点击删除按钮**：
   - 在订单列表中找到状态为"交易关闭"的订单
   - 点击操作列中的"删除"按钮
   - 系统弹出确认对话框
   - 点击"确定"按钮，订单将被删除

## 4. 注意事项

1. **订单状态流转**：
   - 订单状态的正常流转顺序为：待付款 → 待发货 → 待收货 → 交易成功 → 交易完成
   - 订单可能在任何阶段被取消，变为"交易关闭"状态
   - 不同状态的订单有不同的可执行操作

2. **发货操作**：
   - 发货前应确认收货地址信息的准确性
   - 必须填写正确的物流公司和快递单号，以便买家查询物流信息
   - 对于多件商品的订单，可以选择一次性发货或分批发货
   - 发货操作不可撤销，请谨慎操作

3. **退款操作**：
   - 退款操作会将订单金额退回给买家，请确认退款金额的准确性
   - 退款后订单状态将变为"交易关闭"，不可恢复
   - 退款原因应详细说明，便于后续查询和统计

4. **评价管理**：
   - 评价审核应遵循平台规定的标准，确保评价内容合规
   - 对于违规评价，应选择不通过并说明原因
   - 评价一旦审核通过，将在商品页面公开显示

5. **订单删除**：
   - 只有"交易关闭"状态的订单才能被删除
   - 删除操作不可恢复，请谨慎操作
   - 建议在删除前确认订单已无后续处理需求

## 5. 常见问题

### 5.1 无法发货

**问题**：点击发货按钮后，无法正常进行发货操作。

**解决方案**：
- 确认订单状态是否为"待发货"
- 检查是否已填写发货地址和退货地址，这两项是必填信息
- 确认是否已为所有商品选择了包裹
- 检查物流公司和快递单号是否已正确填写
- 如果问题持续，请联系技术支持

### 5.2 退款失败

**问题**：进行取消并退款操作时，系统提示退款失败。

**解决方案**：
- 确认订单状态是否为"待发货"
- 检查退款金额是否超过订单实付金额
- 确认是否已选择正确的关闭类型和填写关闭原因
- 如果问题持续，请联系财务部门或技术支持

### 5.3 物流信息不显示

**问题**：点击查看物流按钮后，无法看到物流轨迹信息。

**解决方案**：
- 确认物流公司和快递单号是否正确
- 物流信息可能存在延迟，建议稍后再查询
- 部分物流公司可能不支持轨迹查询，此时只能看到基本信息
- 如果问题持续，请联系物流公司或技术支持
