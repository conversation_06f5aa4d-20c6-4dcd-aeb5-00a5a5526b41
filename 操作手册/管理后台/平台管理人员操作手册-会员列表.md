# 《八闽助业集市》商城平台管理人员操作手册 - 会员列表

## 1. 功能概述

会员列表是平台管理系统中的核心功能之一，用于集中展示和管理所有注册会员的信息。通过会员列表，平台管理人员可以查看会员基本资料、管理会员状态、调整会员等级、查询会员推荐关系等，实现对会员的全面管理。

![会员列表页面](../images/会员管理/会员列表.png)

## 2. 页面布局

会员列表页面主要分为三个区域：
- 查询区域：位于页面上方，用于设置查询条件筛选会员
- 操作按钮区域：位于查询区域下方，提供新增、导出等功能按钮
- 数据表格区域：位于页面主体部分，以表格形式展示会员信息

## 3. 操作指南

### 3.1 会员查询

会员列表页面提供了丰富的查询条件，帮助管理员快速找到目标会员：

1. **基本查询**：
   - 用户编号：输入会员的唯一编号进行精确查询
   - 手机号：输入会员注册的手机号码
   - 昵称：输入会员的昵称关键字

2. **展开更多查询条件**：
   - 点击查询区域右下方的"展开"按钮，可以显示更多查询条件
   - 展开后可使用的额外条件包括：
     - 性别：从下拉菜单中选择会员性别
     - 会员类型：从下拉菜单中选择会员类型
     - 推广人：输入推广人姓名或关键字
     - 创建时间：选择时间范围，筛选特定时间段注册的会员

3. **执行查询**：
   - 设置好查询条件后，点击"查询"按钮执行查询
   - 系统将显示符合条件的会员列表

4. **重置查询条件**：
   - 点击"重置"按钮，清空所有已设置的查询条件

### 3.2 会员信息浏览

会员列表以表格形式展示会员信息，包含以下主要字段：

- 会员编号：会员的唯一标识
- 头像：会员的个人头像图片
- 手机号：会员注册的手机号码
- 昵称：会员的昵称
- 会员类型：显示会员的类型分类
- 是否员工：标识会员是否为平台员工
- 推广人：显示推荐该会员的上级推广人
- 余额：会员账户当前可用余额
- 累计充值金额：会员历史充值总额
- 是否助梦家：标识会员是否为助梦家身份
- 累计佣金：会员获得的总佣金金额
- 累计业绩：会员产生的总业绩
- 品牌馆业绩：会员在品牌馆产生的业绩
- 生活馆业绩：会员在生活馆产生的业绩
- 创业馆业绩：会员在创业馆产生的业绩
- 创建时间：会员注册的时间
- 状态：会员当前状态（启用/停用）
- 停用说明：会员被停用的原因说明

### 3.3 会员管理操作

#### 3.3.1 新增企业家

1. 点击操作按钮区域的"新增企业家"按钮
2. 在弹出的表单中填写企业家信息，包括基本资料、联系方式等
3. 填写完成后点击"确定"按钮提交

![新增企业家](../images/会员管理/新增企业家.png)

#### 3.3.2 会员等级调整

1. 在会员列表中找到需要调整等级的会员
2. 点击会员等级信息（如有显示）或相关操作按钮
3. 在弹出的等级设置对话框中，选择新的会员等级
4. 点击"确定"按钮完成等级调整

#### 3.3.3 会员状态管理

**停用会员**：
1. 在会员列表中找到需要停用的会员
2. 点击操作列中的"停用"按钮
3. 在弹出的确认对话框中，输入停用原因
4. 点击"确认"按钮完成停用操作
5. 停用后，该会员将无法访问商城

**启用会员**：
1. 在会员列表中找到需要启用的已停用会员
2. 点击操作列中的"启用"按钮
3. 在弹出的确认对话框中，点击"确定"按钮
4. 启用后，该会员将恢复商城访问权限

#### 3.3.4 编辑会员信息

1. 在会员列表中找到需要编辑的会员
2. 点击操作列中的"编辑"按钮
3. 在弹出的编辑表单中修改会员信息
4. 点击"保存"按钮提交修改

### 3.4 批量操作

#### 3.4.1 批量选择

1. 在会员列表左侧的复选框中，选择需要批量操作的会员
2. 可以单个选择，也可以点击表头的复选框全选当前页的所有会员

#### 3.4.2 批量删除

1. 选择需要删除的会员后，点击"批量操作"按钮
2. 在下拉菜单中选择"删除"选项
3. 在弹出的确认对话框中，点击"确定"按钮
4. 系统将删除所选的会员记录

### 3.5 数据导出

1. 设置查询条件筛选需要导出的会员数据
2. 点击操作按钮区域的"导出"按钮（如果启用）
3. 系统将生成Excel文件并自动下载到本地
4. 导出的文件包含会员列表中显示的所有字段信息

## 4. 注意事项

1. **数据安全**：
   - 会员信息涉及用户隐私，请遵守相关法律法规，保护用户信息安全
   - 不要将会员敏感信息泄露给无关人员

2. **操作谨慎**：
   - 停用会员会直接影响用户使用体验，请谨慎操作
   - 批量删除操作不可恢复，执行前请再次确认

3. **查询优化**：
   - 使用精确的查询条件可以提高查询效率
   - 对于大数据量查询，建议使用会员编号或手机号等唯一标识进行查询

4. **浏览器兼容性**：
   - 推荐使用Chrome、Firefox等现代浏览器访问管理系统
   - 确保浏览器已启用JavaScript和Cookie

## 5. 常见问题

### 5.1 查询无结果

**问题**：设置查询条件后，没有显示任何会员记录。

**解决方案**：
- 检查查询条件是否正确，尤其是精确匹配的字段如会员编号、手机号等
- 尝试减少查询条件，使用更宽泛的条件重新查询
- 点击"重置"按钮清空所有条件，重新设置查询条件

### 5.2 会员状态修改失败

**问题**：尝试启用或停用会员时，操作失败。

**解决方案**：
- 检查网络连接是否正常
- 确认您是否有足够的操作权限
- 刷新页面后重试操作
- 如果问题持续，请联系系统管理员

### 5.3 导出数据量大导致超时

**问题**：导出大量会员数据时，操作超时或失败。

**解决方案**：
- 使用更精确的查询条件，减少导出的数据量
- 分批次导出数据，例如按注册时间段分多次导出
- 在网络状况良好的环境下进行导出操作
