# 《八闽助业集市》商城平台管理人员操作手册 - 店铺余额

## 1. 功能概述

店铺余额功能是平台管理系统中的重要组成部分，用于查询和监控平台上所有店铺的账户余额情况。通过该功能，平台管理人员可以全面了解各个店铺的资金状况，便于财务核对和资金监控。

![店铺余额](../images/店铺管理/店铺余额.png)

## 2. 页面布局

店铺余额页面主要分为两个区域：
- 查询区域：位于页面上方，用于设置查询条件筛选店铺
- 数据表格区域：位于页面主体部分，以表格形式展示店铺的余额信息

## 3. 操作指南

### 3.1 店铺余额查询

店铺余额页面提供了多种查询条件，帮助管理员快速找到目标店铺：

1. **基本查询**：
   - 账号：输入店铺的登录账号进行查询
   - 联系人：输入店铺联系人姓名进行查询
   - 联系人手机：输入店铺联系人的手机号码进行查询

2. **展开更多查询条件**：
   - 点击查询区域右下方的"展开"按钮，可以显示更多查询条件
   - 展开后可使用的额外条件包括：
     - 店铺名称：输入店铺的名称关键字
     - 状态：从下拉菜单中选择店铺状态（启用/停用）
     - 所在城市：通过级联选择器选择店铺所在的城市

3. **执行查询**：
   - 设置好查询条件后，点击"查询"按钮执行查询
   - 系统将显示符合条件的店铺列表及其余额信息

4. **重置查询条件**：
   - 点击"重置"按钮，清空所有已设置的查询条件

### 3.2 店铺余额信息浏览

店铺余额表格展示店铺的基本信息和余额，包含以下主要字段：

- 序号：表格中的序号，从1开始递增
- 账号：店铺的登录账号
- 店铺名称：店铺的名称
- 城市：店铺所在的城市
- 联系人：店铺的联系人姓名
- 联系人手机：店铺联系人的手机号码
- 状态：店铺的当前状态（启用/停用）
- 余额：店铺账户当前的可用余额

### 3.3 批量操作

系统支持对店铺记录进行批量选择操作：

1. **批量选择**：
   - 在店铺列表左侧的复选框中，选择需要操作的店铺记录
   - 可以单个选择，也可以点击表头的复选框全选当前页的所有记录
   - 页面上方会显示已选择的记录数量
   - 点击"清空"可以取消所有选择

## 4. 注意事项

1. **数据安全**：
   - 店铺余额信息涉及商家资金，请遵守相关法律法规，保护商家信息安全
   - 不要将店铺余额信息泄露给无关人员

2. **查询优化**：
   - 使用精确的查询条件可以提高查询效率
   - 对于大数据量查询，建议使用店铺账号或联系人手机号等唯一标识进行查询

3. **功能限制**：
   - 当前页面主要用于查询和浏览店铺余额信息，不支持直接修改店铺余额
   - 店铺余额的变动通常由系统根据交易自动计算，或通过其他专门的财务模块进行调整

## 5. 常见问题

### 5.1 查询无结果

**问题**：设置查询条件后，没有显示任何店铺记录。

**解决方案**：
- 检查查询条件是否正确，尤其是精确匹配的字段如账号、联系人手机等
- 尝试减少查询条件，使用更宽泛的条件重新查询
- 点击"重置"按钮清空所有条件，重新设置查询条件
- 确认店铺状态（启用/停用）是否影响了查询结果

### 5.2 余额显示异常

**问题**：某店铺的余额显示与实际情况不符。

**解决方案**：
- 检查该店铺最近的交易记录，确认是否有未计入的交易
- 联系财务人员核对该店铺的账务情况
- 如确认存在数据不一致，请联系技术人员进行核查

### 5.3 城市筛选不生效

**问题**：使用所在城市条件筛选时，结果不符合预期。

**解决方案**：
- 确保正确选择了完整的城市层级（省/市/区县）
- 系统可能需要完全匹配城市名称，检查店铺记录中的城市名称格式
- 尝试使用其他条件结合城市进行筛选
- 如果问题持续，请联系系统管理员
