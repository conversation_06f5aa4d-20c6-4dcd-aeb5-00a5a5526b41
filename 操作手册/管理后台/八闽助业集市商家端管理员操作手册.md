# 《八闽助业集市》商城商家端管理员操作手册

## 目录

1. [前言](#1-前言)
2. [系统登录](#2-系统登录)
3. [店铺商品](#3-店铺商品)
   1. [商品分类](#31-商品分类)
   2. [商品列表](#32-商品列表)
   3. [商品草稿箱](#33-商品草稿箱)
   4. [商品回收站](#34-商品回收站)
   5. [商品审核记录](#35-商品审核记录)
4. [店铺订单](#4-店铺订单)
   1. [全部订单](#41-全部订单)
   2. [待发货订单](#42-待发货订单)
   3. [待收货订单](#43-待收货订单)
   4. [交易成功订单](#44-交易成功订单)
5. [店铺资金](#5-店铺资金)
   1. [余额明细](#51-余额明细)
   2. [资金流水](#52-资金流水)
6. [常见问题](#6-常见问题)

## 1. 前言

### 1.1 手册说明

本操作手册是为《八闽助业集市》商城的商家端管理员设计的，旨在帮助商家管理员快速了解和掌握系统的各项功能，高效地管理自己的店铺商品、订单和资金。

### 1.2 系统概述

《八闽助业集市》商城商家端管理系统是一个专为商家设计的管理平台，提供了商品管理、订单处理、资金查询等功能，帮助商家高效地运营自己的店铺。与平台管理员系统不同，商家端只能查看和管理自己店铺的数据，无法访问其他店铺的信息。

### 1.3 使用对象

本手册的使用对象为《八闽助业集市》商城的商家端管理员，包括但不限于：
- 店铺管理员
- 店铺运营人员
- 店铺客服人员

## 2. 系统登录

### 2.1 登录入口

商家端管理系统的登录入口为：[https://merchant.example.com](https://merchant.example.com)（示例链接，请以实际提供的链接为准）

![系统登录页面](../images/系统登录.png)

### 2.2 登录步骤

1. 在浏览器中输入商家端管理系统的网址
2. 在登录页面输入您的账号和密码
3. 点击"登录"按钮进入系统

### 2.3 账号安全

为了保障您的账号安全，请注意以下几点：
- 定期修改密码，并设置复杂密码
- 不要将账号密码告知他人
- 不要在公共电脑上保存登录信息
- 使用完毕后，请点击右上角的"退出"按钮安全退出系统

## 3. 店铺商品

店铺商品是商家端管理系统中的核心功能模块，用于管理和维护店铺的商品信息。通过店铺商品管理模块，商家可以查看、新增、编辑店铺商品，以及进行上下架操作，确保店铺商品信息的准确性和及时更新。

### 3.1 商品分类

商品分类功能用于管理店铺商品的分类体系，帮助商家更好地组织和展示商品。

#### 3.1.1 页面布局

商品分类页面主要分为两个区域：
- 左侧分类树形结构：以树形结构展示商品分类的层级关系
- 右侧分类详情区域：显示所选分类的详细信息和子分类列表

#### 3.1.2 查看分类

1. 在左侧导航菜单中点击"店铺商品"展开子菜单
2. 点击"商品分类"进入商品分类页面
3. 在左侧分类树中可以查看所有分类的层级结构
4. 点击某个分类，右侧会显示该分类的详细信息和子分类列表

#### 3.1.3 使用分类

商品分类主要用于新增和编辑商品时选择所属分类，商家无法自行创建或修改分类，这些分类由平台统一管理和维护。

### 3.2 商品列表

商品列表功能是店铺商品管理系统中的重要组成部分，用于管理和维护店铺的所有商品信息。通过该功能，商家可以查看、新增、编辑店铺商品，以及进行上下架操作，确保店铺商品信息的准确性和及时更新。

![店铺商品列表](../images/店铺商品/商品列表/店铺商品列表.png)

#### 3.2.1 页面布局

商品列表页面主要分为两个区域：
- 查询区域：位于页面上方，用于设置查询条件筛选商品
- 数据表格区域：位于页面主体部分，以表格形式展示商品信息

#### 3.2.2 商品查询

商品列表页面提供了多种查询条件，帮助商家快速找到目标商品：

1. **基本查询**：
   - 商品编号：输入商品编号进行精确查询
   - 商品名称：输入商品名称关键字进行模糊查询
   - 状态：从下拉菜单中选择商品状态（全部、停用、启用）
   - 上下架状态：从下拉菜单中选择上下架状态（全部、下架、上架）

2. **执行查询**：
   - 设置好查询条件后，点击"查询"按钮执行查询
   - 系统将显示符合条件的商品列表

3. **重置查询条件**：
   - 点击"重置"按钮，清空所有已设置的查询条件

#### 3.2.3 商品列表浏览

商品列表以表格形式展示商品信息，包含以下主要字段：

- 商品分类：商品所属的分类名称
- 商品主图：商品的主要展示图片
- 商品编号：商品的唯一编号
- 商品名称：商品的名称
- 市场价：商品的市场参考价格
- 成本价：商品的成本价格（如有多规格，显示价格区间）
- 销售价：商品的实际销售价格（如有多规格，显示价格区间）
- 状态：商品的状态（启用/停用）
- 上下架状态：商品的上下架状态（上架/下架）
- 审核状态：商品的审核状态（草稿、待审核、审核通过、审核不通过）
- 状态说明：商品状态的补充说明
- 有无规格：商品是否有规格（有规格/无规格）
- 创建时间：商品创建的时间
- 操作：提供对商品的操作按钮

#### 3.2.4 批量操作

系统支持对商品记录进行批量选择操作：

1. **批量选择**：
   - 在商品列表左侧的复选框中，选择需要操作的记录
   - 可以单个选择，也可以点击表头的复选框全选当前页的所有记录
   - 页面上方会显示已选择的记录数量
   - 点击"清空"可以取消所有选择

2. **批量上架**：
   - 选择多个商品后，点击"批量上架"按钮
   - 系统弹出确认对话框
   - 点击"确定"按钮，所选商品将被批量上架

3. **批量下架**：
   - 选择多个商品后，点击"批量下架"按钮
   - 系统弹出下架原因输入对话框
   - 输入下架原因后点击"确定"按钮，所选商品将被批量下架

4. **批量删除**：
   - 选择多个商品后，点击"批量操作"下拉菜单
   - 选择"删除"选项
   - 系统弹出确认对话框
   - 点击"确定"按钮，所选商品将被批量删除

#### 3.2.5 新增商品

商家可以添加新的商品，具体操作如下：

1. **点击新增按钮**：
   - 在商品列表页面点击"新增"按钮
   - 系统跳转到商品新增页面

2. **选择商品分类**：
   - 在新增商品页面，首先需要选择商品所属的分类
   - 点击分类名称，选择合适的商品分类
   - 点击"确定"按钮进入商品信息填写页面

![新增商品-选择类目](../images/店铺商品/商品列表/新增商品-选择类目.png)

3. **填写商品基本信息**：
   - 商品名称：输入商品的名称（必填）
   - 商品描述：输入商品的详细描述
   - 商品图片：上传商品的主图（最多5张）
   - 视频地址：上传商品的展示视频（可选）
   - 商品详情图：上传商品的详情图片（最多20张）

4. **设置商品规格和价格**：
   - 添加规格：点击"添加规格"按钮，可以添加最多两个规格维度（如颜色、尺寸等）
   - 填写规格值：为每个规格维度添加具体的规格值
   - 设置价格与库存：为每个规格组合设置销售价、成本价、会员价和库存
   - 填写SKU编码：为每个规格组合设置唯一的SKU编码
   - 上传规格图：为规格组合上传对应的规格图片
   - 设置商品市场价：输入商品的市场参考价格

5. **设置服务与承诺**：
   - 运费模板：选择商品的运费计算方式（包邮、按件、按重量）
   - 自提：选择是否支持自提
   - 配送：选择是否支持配送
   - 服务承诺：选择商品的服务承诺（如7天无理由退换货、假一赔十等）

6. **保存或提交商品**：
   - 保存草稿箱：点击"保存草稿箱"按钮，商品信息将被保存为草稿状态
   - 提交并上架：点击"提交并上架"按钮，商品将被提交审核并在审核通过后上架

![发布商品](../images/店铺商品/商品列表/发布商品.png)

#### 3.2.6 编辑商品

商家可以编辑已有商品的信息，具体操作如下：

1. **点击编辑按钮**：
   - 在商品列表中找到需要编辑的商品
   - 点击操作列中的"编辑"按钮
   - 系统跳转到商品编辑页面

2. **修改商品信息**：
   - 编辑页面与新增页面布局相同，但已填充现有商品信息
   - 可以修改商品的基本信息、规格价格、服务承诺等内容
   - 修改完成后，可以选择保存为草稿或提交上架

#### 3.2.7 商品上下架操作

商家可以对商品进行上下架操作，控制商品在前台的显示状态：

1. **上架商品**：
   - 在商品列表中找到状态为"下架"的商品
   - 点击操作列中的"上架"按钮
   - 系统弹出确认对话框
   - 点击"确定"按钮，商品状态将变更为"上架"

![上架商品](../images/店铺商品/商品列表/上架商品.png)

2. **下架商品**：
   - 在商品列表中找到状态为"上架"的商品
   - 点击操作列中的"下架"按钮
   - 系统弹出下架原因输入对话框
   - 输入下架原因后点击"确定"按钮，商品状态将变更为"下架"

![下架商品](../images/店铺商品/商品列表/下架商品.png)

#### 3.2.8 其他操作

1. **商品排序**：
   - 在商品列表中找到需要调整排序的商品
   - 点击操作列中的"排序"按钮
   - 系统弹出排序设置对话框
   - 输入排序数值（数值越小，排序越靠前）
   - 点击"确定"按钮保存排序设置

2. **删除商品**：
   - 在商品列表中找到需要删除的商品
   - 点击操作列中的"更多"下拉菜单
   - 选择"删除"选项
   - 系统弹出确认对话框
   - 点击"确定"按钮，商品将被删除

#### 3.2.9 注意事项

1. **商品信息完整性**：
   - 添加或编辑商品时，确保必填信息完整准确
   - 商品图片应清晰展示商品特点，建议使用白底图片
   - 商品详情应详细描述商品的特点、规格、使用方法等信息

2. **规格设置**：
   - 规格名称应简洁明了，如"颜色"、"尺寸"等
   - 规格值应具体明确，如"红色"、"XL"等
   - 有规格商品必须为每个规格组合设置正确的价格和库存

3. **价格设置**：
   - 销售价不能低于成本价
   - 会员价应低于销售价
   - 市场价应高于销售价，作为参考价格展示

4. **上下架操作**：
   - 商品上架前应确保信息完整、图片清晰、价格合理
   - 下架商品时应填写明确的下架原因，便于后续处理
   - 批量上下架操作应谨慎使用，确保所选商品符合操作条件

5. **审核流程**：
   - 新增或修改的商品需要经过平台审核才能上架
   - 审核通过后，商品才能在前台显示
   - 审核不通过的商品需要根据审核意见修改后重新提交

### 3.3 商品草稿箱

商品草稿箱功能用于存储和管理尚未提交审核的商品信息，帮助商家保存未完成的商品编辑工作，以便后续继续完善。

#### 3.3.1 页面布局

商品草稿箱页面主要分为两个区域：
- 查询区域：位于页面上方，用于设置查询条件筛选草稿商品
- 数据表格区域：位于页面主体部分，以表格形式展示草稿商品信息

#### 3.3.2 草稿商品查询

商品草稿箱页面提供了多种查询条件，帮助商家快速找到目标草稿商品：

1. **基本查询**：
   - 商品编号：输入商品编号进行精确查询
   - 商品名称：输入商品名称关键字进行模糊查询

2. **执行查询**：
   - 设置好查询条件后，点击"查询"按钮执行查询
   - 系统将显示符合条件的草稿商品列表

3. **重置查询条件**：
   - 点击"重置"按钮，清空所有已设置的查询条件

#### 3.3.3 草稿商品列表浏览

草稿商品列表以表格形式展示商品信息，包含以下主要字段：

- 商品分类：商品所属的分类名称
- 商品主图：商品的主要展示图片
- 商品编号：商品的唯一编号
- 商品名称：商品的名称
- 市场价：商品的市场参考价格
- 成本价：商品的成本价格（如有多规格，显示价格区间）
- 销售价：商品的实际销售价格（如有多规格，显示价格区间）
- 有无规格：商品是否有规格（有规格/无规格）
- 创建时间：商品创建的时间
- 操作：提供对草稿商品的操作按钮

#### 3.3.4 草稿商品操作

1. **继续编辑**：
   - 在草稿商品列表中找到需要继续编辑的商品
   - 点击操作列中的"编辑"按钮
   - 系统跳转到商品编辑页面，可以继续完善商品信息
   - 编辑完成后，可以选择保存为草稿或提交审核

2. **删除草稿**：
   - 在草稿商品列表中找到需要删除的商品
   - 点击操作列中的"删除"按钮
   - 系统弹出确认对话框
   - 点击"确定"按钮，草稿商品将被删除

3. **提交审核**：
   - 在草稿商品列表中找到需要提交的商品
   - 点击操作列中的"提交审核"按钮
   - 系统弹出确认对话框
   - 点击"确定"按钮，草稿商品将被提交审核

#### 3.3.5 注意事项

1. **草稿保存**：
   - 草稿商品会自动保存，无需担心信息丢失
   - 草稿商品不会在前台显示，也不会被计入商品库存

2. **草稿时效**：
   - 草稿商品没有时效限制，可以长期保存
   - 建议及时处理草稿商品，避免积累过多无用草稿

3. **提交审核前检查**：
   - 提交审核前，确保商品信息完整准确
   - 检查商品图片、价格、规格等关键信息
   - 提交审核后，商品将从草稿箱移至商品列表，状态为"待审核"

### 3.4 商品回收站

商品回收站功能用于存储和管理已删除的商品信息，提供恢复和彻底删除的操作选项。

#### 3.4.1 页面布局

商品回收站页面主要分为两个区域：
- 查询区域：位于页面上方，用于设置查询条件筛选已删除商品
- 数据表格区域：位于页面主体部分，以表格形式展示已删除商品信息

#### 3.4.2 已删除商品查询

商品回收站页面提供了多种查询条件，帮助商家快速找到目标已删除商品：

1. **基本查询**：
   - 商品编号：输入商品编号进行精确查询
   - 商品名称：输入商品名称关键字进行模糊查询
   - 删除时间：选择时间范围，筛选特定时间段删除的商品

2. **执行查询**：
   - 设置好查询条件后，点击"查询"按钮执行查询
   - 系统将显示符合条件的已删除商品列表

3. **重置查询条件**：
   - 点击"重置"按钮，清空所有已设置的查询条件

#### 3.4.3 已删除商品列表浏览

已删除商品列表以表格形式展示商品信息，包含以下主要字段：

- 商品分类：商品所属的分类名称
- 商品主图：商品的主要展示图片
- 商品编号：商品的唯一编号
- 商品名称：商品的名称
- 市场价：商品的市场参考价格
- 成本价：商品的成本价格（如有多规格，显示价格区间）
- 销售价：商品的实际销售价格（如有多规格，显示价格区间）
- 有无规格：商品是否有规格（有规格/无规格）
- 删除时间：商品被删除的时间
- 操作：提供对已删除商品的操作按钮

#### 3.4.4 已删除商品操作

1. **恢复商品**：
   - 在已删除商品列表中找到需要恢复的商品
   - 点击操作列中的"恢复"按钮
   - 系统弹出确认对话框
   - 点击"确定"按钮，商品将被恢复到商品列表中

2. **彻底删除**：
   - 在已删除商品列表中找到需要彻底删除的商品
   - 点击操作列中的"彻底删除"按钮
   - 系统弹出确认对话框
   - 点击"确定"按钮，商品将被彻底删除，无法恢复

#### 3.4.5 批量操作

系统支持对已删除商品记录进行批量选择操作：

1. **批量选择**：
   - 在已删除商品列表左侧的复选框中，选择需要操作的记录
   - 可以单个选择，也可以点击表头的复选框全选当前页的所有记录
   - 页面上方会显示已选择的记录数量
   - 点击"清空"可以取消所有选择

2. **批量恢复**：
   - 选择多个已删除商品后，点击"批量恢复"按钮
   - 系统弹出确认对话框
   - 点击"确定"按钮，所选商品将被批量恢复到商品列表中

3. **批量彻底删除**：
   - 选择多个已删除商品后，点击"批量彻底删除"按钮
   - 系统弹出确认对话框
   - 点击"确定"按钮，所选商品将被批量彻底删除，无法恢复

#### 3.4.6 注意事项

1. **回收站保留时间**：
   - 回收站中的商品会保留30天，超过30天将自动彻底删除
   - 建议及时处理回收站中的商品，恢复有用的商品，彻底删除无用的商品

2. **恢复操作**：
   - 恢复的商品将回到删除前的状态，包括上下架状态、审核状态等
   - 如果商品在删除前是上架状态，恢复后也会是上架状态，但需要重新经过平台审核

3. **彻底删除操作**：
   - 彻底删除是不可逆操作，请谨慎执行
   - 建议在彻底删除前，确认商品确实不再需要

### 3.5 商品审核记录

商品审核记录功能用于查看和管理商品的审核历史，帮助商家了解商品审核的状态和结果。

#### 3.5.1 页面布局

商品审核记录页面主要分为两个区域：
- 查询区域：位于页面上方，用于设置查询条件筛选审核记录
- 数据表格区域：位于页面主体部分，以表格形式展示审核记录信息

#### 3.5.2 审核记录查询

商品审核记录页面提供了多种查询条件，帮助商家快速找到目标审核记录：

1. **基本查询**：
   - 商品编号：输入商品编号进行精确查询
   - 商品名称：输入商品名称关键字进行模糊查询
   - 审核状态：从下拉菜单中选择审核状态（全部、待审核、审核通过、审核不通过）
   - 审核时间：选择时间范围，筛选特定时间段的审核记录

2. **执行查询**：
   - 设置好查询条件后，点击"查询"按钮执行查询
   - 系统将显示符合条件的审核记录列表

3. **重置查询条件**：
   - 点击"重置"按钮，清空所有已设置的查询条件

#### 3.5.3 审核记录列表浏览

审核记录列表以表格形式展示审核信息，包含以下主要字段：

- 商品分类：商品所属的分类名称
- 商品主图：商品的主要展示图片
- 商品编号：商品的唯一编号
- 商品名称：商品的名称
- 审核状态：商品的审核状态（待审核、审核通过、审核不通过）
- 审核意见：平台审核人员的审核意见，特别是审核不通过时的原因说明
- 提交时间：商品提交审核的时间
- 审核时间：平台完成审核的时间
- 操作：提供对审核记录的操作按钮

#### 3.5.4 审核记录操作

1. **查看详情**：
   - 在审核记录列表中找到需要查看的记录
   - 点击操作列中的"查看"按钮
   - 系统弹出审核详情对话框，显示完整的审核信息和意见

2. **重新提交**：
   - 在审核记录列表中找到审核状态为"审核不通过"的记录
   - 点击操作列中的"编辑"按钮
   - 系统跳转到商品编辑页面，可以根据审核意见修改商品信息
   - 修改完成后，点击"提交并上架"按钮重新提交审核

#### 3.5.5 注意事项

1. **审核时间**：
   - 商品审核通常在工作日内完成，节假日可能会有延迟
   - 审核时间一般为1-2个工作日，具体以平台公告为准

2. **审核不通过处理**：
   - 审核不通过时，请仔细阅读审核意见，了解不通过的原因
   - 根据审核意见修改商品信息，重点关注违规内容、价格异常、图片质量等问题
   - 修改后重新提交审核，不要反复提交相同的违规内容

3. **审核规则**：
   - 商品信息必须真实准确，不得有虚假宣传
   - 商品图片必须清晰，与实际商品一致
   - 商品价格必须合理，不得有明显的价格欺诈
   - 商品描述不得含有违法违规内容
   - 详细的审核规则请参考平台商家规则

## 4. 店铺订单

店铺订单是商家端管理系统中的核心功能模块，用于管理和处理店铺的所有订单信息。通过店铺订单管理模块，商家可以查看订单详情、处理订单发货、查看物流信息、处理退款等操作，确保订单流程的顺利进行和交易的安全完成。

### 4.1 全部订单

全部订单功能是店铺订单管理系统中的重要组成部分，用于管理和处理店铺的所有订单信息。通过该功能，商家可以全面了解店铺的订单情况，进行各种订单操作。

![全部订单](../images/店铺订单/全部订单.png)

#### 4.1.1 页面布局

全部订单页面主要分为两个区域：
- 查询区域：位于页面上方，用于设置查询条件筛选订单
- 数据表格区域：位于页面主体部分，以表格形式展示订单信息

#### 4.1.2 订单查询

全部订单页面提供了多种查询条件，帮助商家快速找到目标订单：

1. **基本查询**：
   - 订单编号：输入订单编号进行精确查询
   - 订单类型：从下拉菜单中选择订单类型（如普通订单、团购订单等）

2. **展开更多查询条件**：
   - 点击查询区域右下方的"展开"按钮，可以显示更多查询条件
   - 展开后可使用的额外条件包括：
     - 买家账号：输入买家的手机号码进行查询
     - 订单状态：从下拉菜单中选择订单状态（如待发货、待收货、交易成功、交易关闭、交易完成）
     - 关闭时间：选择时间范围，筛选特定时间段关闭的订单
     - 创建时间：选择时间范围，筛选特定时间段创建的订单
     - 付款时间：选择时间范围，筛选特定时间段付款的订单
     - 发货时间：选择时间范围，筛选特定时间段发货的订单
     - 收货时间：选择时间范围，筛选特定时间段收货的订单

3. **执行查询**：
   - 设置好查询条件后，点击"查询"按钮执行查询
   - 系统将显示符合条件的订单列表

4. **重置查询条件**：
   - 点击"重置"按钮，清空所有已设置的查询条件

#### 4.1.3 订单列表浏览

订单列表以表格形式展示订单信息，包含以下主要字段：

- 订单编号：订单的唯一标识
- 订单类型：订单的类型（如普通订单、团购订单等）
- 买家：买家的手机号码，点击可查看买家详细信息
- 留言：买家下单时的留言信息，点击可查看详细内容
- 商品：订单中的商品信息，点击可查看详细商品列表
- 订单状态：订单的当前状态（如待发货、待收货、交易成功、交易关闭、交易完成）
- 商品总价：订单中所有商品的总价
- 配送方式：订单的配送方式（如快递配送、自提等）
- 实付款：买家实际支付的金额
- 关闭类型：订单关闭的类型（如超时未付款、买家取消等）
- 关闭原因：订单关闭的具体原因
- 关闭时间：订单关闭的时间
- 创建时间：订单创建的时间
- 部分发货：是否为部分发货订单
- 操作：提供对订单的操作按钮，根据订单状态显示不同的操作选项

#### 4.1.4 查看买家信息

商家可以查看订单的买家信息，具体操作如下：

1. **点击买家账号**：
   - 在订单列表中找到需要查看的订单
   - 点击"买家"列中的手机号码
   - 系统弹出买家信息对话框
   - 对话框中显示买家的基本信息、收货信息等内容

![订单买家信息查看](../images/店铺订单/订单买家信息查看.png)

#### 4.1.5 查看商品信息

商家可以查看订单中的商品信息，具体操作如下：

1. **点击商品信息**：
   - 在订单列表中找到需要查看的订单
   - 点击"商品"列中的"商品信息"链接
   - 系统弹出商品信息对话框
   - 对话框中显示订单中所有商品的详细信息，包括商品图片、名称、规格、单价、数量等

![查看订单商品信息](../images/店铺订单/查看订单商品信息.png)

#### 4.1.6 订单详情查看

商家可以查看订单的详细信息，具体操作如下：

1. **点击详情按钮**：
   - 在订单列表中找到需要查看的订单
   - 点击操作列中的"详情"按钮
   - 系统跳转到订单详情页面
   - 详情页面显示订单的完整信息，包括订单基本信息、买家信息、商品信息、支付信息等

![查看订单详情](../images/店铺订单/查看订单详情.png)

#### 4.1.7 订单发货操作

对于待发货状态的订单，商家需要进行发货操作，具体步骤如下：

1. **点击发货按钮**：
   - 在订单列表中找到状态为"待发货"的订单
   - 点击操作列中的"发货"按钮
   - 系统跳转到发货操作页面

2. **确认收货信息**：
   - 发货页面上方显示订单编号、创建时间、付款时间等基本信息
   - 中部显示买家账号、昵称、收货人、联系电话、收货地址等信息
   - 确认信息无误后，进行下一步操作

3. **添加包裹**：
   - 点击"添加一个包裹"按钮
   - 系统自动创建一个包裹

![发货-添加包裹](../images/店铺订单/发货-添加包裹.png)

4. **选择发货商品**：
   - 在下方的商品列表中，为每个商品选择对应的包裹
   - 可以将不同商品分配到不同包裹中，实现分批发货

5. **填写物流信息**：
   - 为每个包裹选择物流公司
   - 输入快递单号

6. **确认发货/退货信息**：
   - 确认发货地址信息
   - 确认退货地址信息
   - 如需修改地址，点击"修改"按钮进行更新

7. **提交发货**：
   - 确认所有信息无误后，点击"发货"按钮
   - 系统提交发货信息，订单状态变更为"待收货"

#### 4.1.8 查看物流信息

对于已发货的订单，商家可以查看物流信息，具体操作如下：

1. **点击查看物流按钮**：
   - 在订单列表中找到状态为"待收货"或"交易成功"的订单
   - 点击操作列中的"查看物流"按钮
   - 系统弹出物流信息对话框
   - 对话框中显示物流公司、快递单号、物流轨迹等信息

#### 4.1.9 评价管理

对于已完成的订单，如果买家进行了评价，商家可以查看评价：

1. **查看评价**：
   - 在订单列表中找到状态为"交易成功"或"交易完成"且有评价的订单
   - 点击操作列中的"查看评价"按钮
   - 系统弹出评价详情对话框
   - 对话框中显示买家的评价内容、评分等信息

2. **回复评价**：
   - 在评价详情对话框中，可以对买家的评价进行回复
   - 输入回复内容
   - 点击"回复"按钮提交回复

#### 4.1.10 注意事项

1. **订单状态流转**：
   - 订单状态的正常流转顺序为：待付款 → 待发货 → 待收货 → 交易成功 → 交易完成
   - 订单可能在任何阶段被取消，变为"交易关闭"状态
   - 不同状态的订单有不同的可执行操作

2. **发货操作**：
   - 发货前应确认收货地址信息的准确性
   - 必须填写正确的物流公司和快递单号，以便买家查询物流信息
   - 对于多件商品的订单，可以选择一次性发货或分批发货
   - 发货操作不可撤销，请谨慎操作

3. **核销订单处理**：
   - 对于自提（核销）订单，系统会生成核销码
   - 可以查看核销状态（未核销、已核销、已失效）
   - 点击"查看核销码"可以显示核销二维码

4. **取消订单操作**：
   - 对于未发货的订单，商家可以进行取消并退款操作
   - 点击操作列中的"取消并退款"按钮
   - 在弹出的对话框中填写取消原因
   - 点击"确定"按钮完成取消操作

![未发货-取消并退款](../images/店铺订单/未发货-取消并退款.png)

### 4.2 待发货订单

待发货订单功能是店铺订单管理系统中的重要组成部分，专门用于管理和处理需要商家发货的订单。通过该功能，商家可以集中处理所有待发货的订单，提高发货效率。

#### 4.2.1 页面布局

待发货订单页面主要分为两个区域：
- 查询区域：位于页面上方，用于设置查询条件筛选待发货订单
- 数据表格区域：位于页面主体部分，以表格形式展示待发货订单信息

#### 4.2.2 订单查询

待发货订单页面提供了多种查询条件，帮助商家快速找到目标订单：

1. **基本查询**：
   - 订单编号：输入订单编号进行精确查询
   - 订单类型：从下拉菜单中选择订单类型（如普通订单、团购订单等）
   - 买家账号：输入买家的手机号码进行查询
   - 付款时间：选择时间范围，筛选特定时间段付款的订单

2. **执行查询**：
   - 设置好查询条件后，点击"查询"按钮执行查询
   - 系统将显示符合条件的待发货订单列表

3. **重置查询条件**：
   - 点击"重置"按钮，清空所有已设置的查询条件

#### 4.2.3 订单列表浏览

待发货订单列表以表格形式展示订单信息，包含以下主要字段：

- 订单编号：订单的唯一标识
- 订单类型：订单的类型（如普通订单、团购订单等）
- 买家：买家的手机号码，点击可查看买家详细信息
- 留言：买家下单时的留言信息，点击可查看详细内容
- 商品：订单中的商品信息，点击可查看详细商品列表
- 商品总价：订单中所有商品的总价
- 配送方式：订单的配送方式（如快递配送、自提等）
- 实付款：买家实际支付的金额
- 付款时间：订单付款的时间
- 操作：提供对订单的操作按钮，主要包括"发货"和"详情"

#### 4.2.4 批量发货

系统支持对待发货订单进行批量发货操作：

1. **批量选择**：
   - 在待发货订单列表左侧的复选框中，选择需要批量发货的订单
   - 可以单个选择，也可以点击表头的复选框全选当前页的所有订单
   - 页面上方会显示已选择的订单数量
   - 点击"清空"可以取消所有选择

2. **批量发货**：
   - 选择多个待发货订单后，点击"批量发货"按钮
   - 系统跳转到批量发货页面
   - 在批量发货页面，可以为多个订单同时设置物流信息
   - 设置完成后点击"确认发货"按钮，所选订单将被批量发货

#### 4.2.5 注意事项

1. **发货时效**：
   - 商家应在买家付款后的约定时间内完成发货（通常为48小时内）
   - 超时未发货可能会影响店铺的服务评分和排名

2. **批量发货限制**：
   - 批量发货仅适用于配送方式相同、收货地址相近的订单
   - 不同配送方式的订单不能批量发货
   - 自提（核销）订单不能与快递配送订单一起批量发货

3. **特殊商品处理**：
   - 对于易碎、易腐、贵重等特殊商品，建议单独发货并选择合适的物流方式
   - 大件商品可能需要特殊的物流服务，请提前与物流公司确认

### 4.3 待收货订单

待收货订单功能是店铺订单管理系统中的重要组成部分，用于管理和查看已发货但买家尚未确认收货的订单。通过该功能，商家可以跟踪订单的物流状态，及时处理可能出现的物流问题。

#### 4.3.1 页面布局

待收货订单页面主要分为两个区域：
- 查询区域：位于页面上方，用于设置查询条件筛选待收货订单
- 数据表格区域：位于页面主体部分，以表格形式展示待收货订单信息

#### 4.3.2 订单查询

待收货订单页面提供了多种查询条件，帮助商家快速找到目标订单：

1. **基本查询**：
   - 订单编号：输入订单编号进行精确查询
   - 订单类型：从下拉菜单中选择订单类型（如普通订单、团购订单等）
   - 买家账号：输入买家的手机号码进行查询
   - 发货时间：选择时间范围，筛选特定时间段发货的订单

2. **执行查询**：
   - 设置好查询条件后，点击"查询"按钮执行查询
   - 系统将显示符合条件的待收货订单列表

3. **重置查询条件**：
   - 点击"重置"按钮，清空所有已设置的查询条件

#### 4.3.3 订单列表浏览

待收货订单列表以表格形式展示订单信息，包含以下主要字段：

- 订单编号：订单的唯一标识
- 订单类型：订单的类型（如普通订单、团购订单等）
- 买家：买家的手机号码，点击可查看买家详细信息
- 商品：订单中的商品信息，点击可查看详细商品列表
- 商品总价：订单中所有商品的总价
- 配送方式：订单的配送方式（如快递配送、自提等）
- 实付款：买家实际支付的金额
- 发货时间：订单发货的时间
- 物流公司：订单使用的物流公司名称
- 物流单号：订单的物流单号
- 操作：提供对订单的操作按钮，主要包括"查看物流"和"详情"

#### 4.3.4 查看物流信息

商家可以查看待收货订单的物流信息，具体操作如下：

1. **点击查看物流按钮**：
   - 在待收货订单列表中找到需要查看的订单
   - 点击操作列中的"查看物流"按钮
   - 系统弹出物流信息对话框
   - 对话框中显示物流公司、快递单号、物流轨迹等信息

#### 4.3.5 提醒收货

对于发货时间较长但买家尚未确认收货的订单，商家可以进行提醒收货操作：

1. **点击提醒收货按钮**：
   - 在待收货订单列表中找到发货超过一定时间（通常为7天）的订单
   - 点击操作列中的"提醒收货"按钮
   - 系统会向买家发送提醒消息，提醒其确认收货

#### 4.3.6 注意事项

1. **物流跟踪**：
   - 商家应定期查看待收货订单的物流状态，确保物流正常
   - 如发现物流异常，应及时联系物流公司和买家，协商解决方案

2. **自动确认收货**：
   - 系统设置了自动确认收货时间（通常为发货后15天）
   - 超过自动确认收货时间后，系统会自动将订单状态变更为"交易成功"

3. **物流纠纷处理**：
   - 如买家反馈未收到货但物流显示已签收，应及时与物流公司核实
   - 对于物流丢失或损坏的情况，应按照平台规则进行赔付处理

### 4.4 交易成功订单

交易成功订单功能是店铺订单管理系统中的重要组成部分，用于管理和查看买家已确认收货的订单。通过该功能，商家可以了解交易完成情况，查看买家评价，处理售后服务等。

#### 4.4.1 页面布局

交易成功订单页面主要分为两个区域：
- 查询区域：位于页面上方，用于设置查询条件筛选交易成功订单
- 数据表格区域：位于页面主体部分，以表格形式展示交易成功订单信息

#### 4.4.2 订单查询

交易成功订单页面提供了多种查询条件，帮助商家快速找到目标订单：

1. **基本查询**：
   - 订单编号：输入订单编号进行精确查询
   - 订单类型：从下拉菜单中选择订单类型（如普通订单、团购订单等）
   - 买家账号：输入买家的手机号码进行查询
   - 收货时间：选择时间范围，筛选特定时间段收货的订单
   - 评价状态：从下拉菜单中选择评价状态（全部、已评价、未评价）

2. **执行查询**：
   - 设置好查询条件后，点击"查询"按钮执行查询
   - 系统将显示符合条件的交易成功订单列表

3. **重置查询条件**：
   - 点击"重置"按钮，清空所有已设置的查询条件

#### 4.4.3 订单列表浏览

交易成功订单列表以表格形式展示订单信息，包含以下主要字段：

- 订单编号：订单的唯一标识
- 订单类型：订单的类型（如普通订单、团购订单等）
- 买家：买家的手机号码，点击可查看买家详细信息
- 商品：订单中的商品信息，点击可查看详细商品列表
- 商品总价：订单中所有商品的总价
- 配送方式：订单的配送方式（如快递配送、自提等）
- 实付款：买家实际支付的金额
- 收货时间：买家确认收货的时间
- 评价状态：买家是否已评价（已评价/未评价）
- 操作：提供对订单的操作按钮，主要包括"查看评价"和"详情"

#### 4.4.4 查看评价

对于已评价的订单，商家可以查看买家的评价内容：

1. **点击查看评价按钮**：
   - 在交易成功订单列表中找到评价状态为"已评价"的订单
   - 点击操作列中的"查看评价"按钮
   - 系统弹出评价详情对话框
   - 对话框中显示买家的评价内容、评分、评价时间等信息

2. **回复评价**：
   - 在评价详情对话框中，可以对买家的评价进行回复
   - 输入回复内容
   - 点击"回复"按钮提交回复

#### 4.4.5 售后服务

对于交易成功的订单，如果买家申请售后服务，商家需要及时处理：

1. **查看售后申请**：
   - 在交易成功订单列表中，带有售后标识的订单表示买家已申请售后
   - 点击操作列中的"售后处理"按钮
   - 系统跳转到售后处理页面
   - 页面显示买家的售后申请信息，包括申请类型、原因、凭证等

2. **处理售后申请**：
   - 根据买家的售后申请类型（退款、换货、维修等）进行相应处理
   - 可以选择同意或拒绝售后申请
   - 如选择拒绝，需要填写拒绝原因
   - 如选择同意，需要按照申请类型进行后续处理

#### 4.4.6 注意事项

1. **评价管理**：
   - 商家应重视买家评价，特别是负面评价
   - 对于负面评价，应及时回复并解决买家的问题
   - 良好的评价回复可以提升店铺形象和服务水平

2. **售后时效**：
   - 买家在确认收货后的一定时间内（通常为7天）可以申请售后服务
   - 商家应在收到售后申请后的规定时间内（通常为48小时）进行处理
   - 超时未处理可能会影响店铺的服务评分

3. **交易数据分析**：
   - 交易成功订单是店铺销售数据的重要来源
   - 商家可以通过分析交易成功订单，了解热销商品、客户偏好等信息
   - 这些数据可以帮助商家优化商品结构和营销策略

## 5. 店铺资金

店铺资金是商家端管理系统中的重要功能模块，用于管理和查询店铺的资金情况。通过店铺资金管理模块，商家可以查看店铺的余额明细、资金流水，了解店铺的收支情况，确保资金的准确性和透明度。

### 5.1 余额明细

余额明细功能是店铺资金管理系统中的重要组成部分，用于查询和管理店铺账户余额及交易明细。通过该功能，商家可以查看店铺的余额信息和交易明细，了解店铺的资金状况。

![余额明细](../images/财务/会员资金/余额明细.png)

#### 5.1.1 页面布局

余额明细页面主要分为两个区域：
- 余额信息区域：位于页面上方，显示店铺当前的可用余额
- 交易明细区域：位于页面下方，以表格形式展示店铺的交易明细记录

#### 5.1.2 余额信息查看

余额信息区域显示店铺的资金概况，包含以下主要信息：
- 可用余额：店铺当前可以使用的余额
- 冻结余额：因提现申请等原因被临时冻结的余额
- 总余额：可用余额和冻结余额的总和

#### 5.1.3 交易明细查询

交易明细区域提供了多种查询条件，帮助商家快速找到目标交易记录：

1. **基本查询**：
   - 流水号：输入交易流水号进行精确查询
   - 交易单号：输入交易单号进行精确查询
   - 交易类型：从下拉菜单中选择交易类型（如订单收入、提现、退款等）
   - 收入支出：从下拉菜单中选择收入或支出
   - 交易时间：选择时间范围，筛选特定时间段的交易记录

2. **执行查询**：
   - 设置好查询条件后，点击"查询"按钮执行查询
   - 系统将显示符合条件的交易记录

3. **重置查询条件**：
   - 点击"重置"按钮，清空所有已设置的查询条件

#### 5.1.4 交易明细浏览

交易明细表格展示店铺的交易记录，包含以下主要字段：

- 流水号：交易的唯一标识
- 交易类型：显示交易的类型（如订单收入、提现、退款等）
- 收入/支出：标识该笔交易是收入还是支出
- 交易金额：本次交易的金额
- 可用余额：交易后的账户余额
- 交易时间：交易发生的时间
- 交易单号：关联的订单或业务单号
- 备注：交易的说明信息

![余额明细详情](../images/财务/会员资金/余额明细详情.png)

#### 5.1.5 提现申请

商家可以通过余额明细页面申请提现，将店铺余额提取到绑定的银行卡中：

1. **点击提现按钮**：
   - 在余额信息区域点击"提现"按钮
   - 系统弹出提现申请对话框

2. **填写提现信息**：
   - 提现金额：输入需要提现的金额（不能超过可用余额）
   - 提现方式：选择提现到银行卡
   - 银行卡信息：系统自动显示已绑定的银行卡信息
   - 备注：可以输入提现的备注信息

3. **确认提现**：
   - 确认提现信息无误后，点击"确定"按钮
   - 系统提交提现申请，相应金额将被冻结
   - 提现申请将由平台审核，审核通过后资金将转入指定银行卡

#### 5.1.6 注意事项

1. **提现规则**：
   - 提现金额必须大于等于最低提现金额（通常为100元）
   - 提现金额不能超过当前可用余额
   - 每日提现次数和金额可能有限制，具体以平台规则为准

2. **提现手续费**：
   - 提现可能会产生手续费，具体费率以平台规则为准
   - 手续费通常从提现金额中扣除，实际到账金额为提现金额减去手续费

3. **提现时效**：
   - 提现申请通常在1-3个工作日内审核
   - 审核通过后，资金将在1-3个工作日内到账
   - 具体到账时间可能受银行处理时间影响

### 5.2 资金流水

资金流水功能是店铺资金管理系统中的重要组成部分，用于查询和管理店铺的所有资金交易记录。通过该功能，商家可以全面了解店铺的资金流水情况，包括订单收入、提现、退款等各类交易记录，便于财务核对和问题排查。

![会员资金明细](../images/财务/会员资金/资金明细.png)

#### 5.2.1 页面布局

资金流水页面主要分为两个区域：
- 查询区域：位于页面上方，用于设置查询条件筛选资金交易记录
- 数据表格区域：位于页面主体部分，以表格形式展示资金交易记录

#### 5.2.2 资金流水查询

资金流水页面提供了多种查询条件，帮助商家快速找到目标交易记录：

1. **基本查询**：
   - 流水号：输入交易流水号进行精确查询
   - 交易单号：输入交易单号进行精确查询
   - 交易类型：从下拉菜单中选择交易类型（如订单收入、提现、退款等）
   - 交易状态：从下拉菜单中选择交易状态（如未支付、进行中、交易完成、已退款等）
   - 收入支出：从下拉菜单中选择收入或支出
   - 交易时间：选择时间范围，筛选特定时间段的交易记录

2. **执行查询**：
   - 设置好查询条件后，点击"查询"按钮执行查询
   - 系统将显示符合条件的交易记录

3. **重置查询条件**：
   - 点击"重置"按钮，清空所有已设置的查询条件

#### 5.2.3 资金流水浏览

资金流水表格展示店铺的交易记录，包含以下主要字段：

- 流水号：交易的唯一标识
- 交易单号：关联的订单或业务单号
- 交易类型：显示交易的类型（如订单收入、提现、退款等）
- 收入/支出：标识该笔交易是收入还是支出
- 交易金额：本次交易的金额
- 交易状态：显示交易的当前状态（如未支付、进行中、交易完成、已退款等）
- 交易时间：交易发生的时间
- 备注：交易的说明信息

#### 5.2.4 交易详情查看

商家可以查看交易的详细信息，具体操作如下：

1. **点击详情按钮**：
   - 在资金流水列表中找到需要查看的交易记录
   - 点击操作列中的"详情"按钮
   - 系统弹出交易详情对话框
   - 对话框中显示交易的完整信息，包括交易基本信息、关联订单信息等

#### 5.2.5 导出资金流水

商家可以导出资金流水记录，便于财务核对和存档：

1. **点击导出按钮**：
   - 在资金流水页面点击"导出"按钮
   - 系统弹出导出设置对话框

2. **设置导出条件**：
   - 导出时间范围：选择需要导出的交易记录时间范围
   - 导出格式：选择导出文件的格式（如Excel、CSV等）

3. **确认导出**：
   - 设置完成后点击"确定"按钮
   - 系统生成导出文件并自动下载到本地

#### 5.2.6 注意事项

1. **数据安全**：
   - 资金流水涉及店铺资金信息，请妥善保管导出的资金流水文件
   - 不要将资金流水信息泄露给无关人员

2. **查询优化**：
   - 使用精确的查询条件可以提高查询效率
   - 对于大数据量查询，建议使用流水号或交易单号等唯一标识进行查询
   - 使用交易时间范围可以有效缩小查询范围

3. **数据解读**：
   - 交易类型和交易状态使用了系统预设的数据字典，请正确理解各状态的含义
   - 收入/支出是从店铺角度定义的，"收入"表示店铺账户资金增加，"支出"表示店铺账户资金减少

4. **财务核对**：
   - 定期核对资金流水与实际业务情况，确保资金准确无误
   - 如发现异常交易，及时联系平台客服处理
   - 建议每月进行一次资金对账，确保账目清晰

## 6. 常见问题

本章节汇总了商家在使用八闽助业集市商家端管理系统过程中可能遇到的常见问题及解决方案，帮助商家快速解决使用中的疑难问题。

### 6.1 账号登录问题

**Q1: 忘记登录密码怎么办？**

A1: 如果忘记登录密码，可以通过以下步骤重置密码：
1. 在登录页面点击"忘记密码"
2. 输入注册时使用的手机号码
3. 获取并输入手机验证码
4. 设置新密码并确认
5. 使用新密码登录系统

**Q2: 账号被锁定怎么办？**

A2: 账号连续多次输入错误密码后会被临时锁定，解决方法如下：
1. 等待30分钟后再尝试登录
2. 如果仍然无法登录，请联系平台客服解锁账号
3. 解锁后请立即修改密码，并妥善保管

**Q3: 如何修改登录密码？**

A3: 登录系统后，可以通过以下步骤修改密码：
1. 点击右上角的用户头像
2. 在下拉菜单中选择"账号设置"
3. 在账号设置页面，点击"修改密码"
4. 输入原密码和新密码，点击"确定"完成修改

### 6.2 商品管理问题

**Q1: 为什么我的商品一直处于"待审核"状态？**

A1: 商品审核通常在1-2个工作日内完成，如果超过这个时间仍未审核，可能有以下原因：
1. 平台审核人员较忙，审核队列较长
2. 节假日期间审核时间可能延长
3. 商品信息存在问题，需要额外审核
如果长时间未审核，建议联系平台客服咨询具体原因。

**Q2: 商品审核不通过的常见原因有哪些？**

A2: 商品审核不通过的常见原因包括：
1. 商品图片不清晰或与描述不符
2. 商品名称或描述中含有违规词汇
3. 商品价格设置不合理（如明显高于或低于市场价）
4. 商品分类选择错误
5. 商品信息不完整（如缺少规格、详情等）
建议根据审核意见修改后重新提交。

**Q3: 如何批量上传商品？**

A3: 目前系统不支持批量上传商品，需要逐个添加。如果有大量商品需要上传，建议：
1. 提前准备好商品图片和信息，按照统一格式整理
2. 按照商品分类逐类添加，提高效率
3. 联系平台客服，了解是否有其他解决方案

**Q4: 商品上架后如何修改价格？**

A4: 修改已上架商品的价格步骤如下：
1. 在商品列表中找到需要修改的商品
2. 点击"编辑"按钮进入编辑页面
3. 修改价格信息
4. 点击"提交并上架"按钮
5. 价格修改需要重新审核，审核通过后新价格才会生效

### 6.3 订单处理问题

**Q1: 如何处理买家申请退款的订单？**

A1: 处理退款申请的步骤如下：
1. 在订单列表中找到申请退款的订单
2. 点击"售后处理"按钮
3. 查看买家的退款原因和凭证
4. 根据实际情况选择"同意"或"拒绝"
5. 如选择拒绝，需要填写拒绝原因
6. 如选择同意，系统会自动处理退款流程

**Q2: 发货后买家表示未收到货怎么办？**

A2: 当买家反馈未收到货时，可以采取以下措施：
1. 查询物流信息，确认包裹的实际配送状态
2. 如物流显示已签收，联系物流公司核实签收情况
3. 如物流显示配送中，建议买家耐心等待或联系快递员
4. 如物流显示异常，及时与物流公司和买家沟通解决方案
5. 必要时可以联系平台客服协助处理

**Q3: 如何修改已发货订单的物流信息？**

A3: 目前系统不支持直接修改已发货订单的物流信息。如果发现物流信息填写错误，建议：
1. 联系买家说明情况，提供正确的物流信息
2. 联系物流公司，说明情况并请求协助
3. 如需修改系统中的物流信息，请联系平台客服处理

**Q4: 订单超时未发货会有什么影响？**

A4: 订单超时未发货可能带来以下影响：
1. 影响店铺的服务评分和排名
2. 买家可能申请取消订单并退款
3. 严重情况下可能影响店铺的经营资格
建议商家在承诺的发货时间内及时处理订单，如有特殊情况无法按时发货，应提前与买家沟通。

### 6.4 资金问题

**Q1: 提现申请多久能到账？**

A1: 提现到账时间通常如下：
1. 提现申请审核：1-3个工作日
2. 审核通过后资金到账：1-3个工作日
实际到账时间可能受银行处理时间影响，建议耐心等待。如超过5个工作日仍未到账，请联系平台客服查询。

**Q2: 为什么我的提现申请被拒绝？**

A2: 提现申请被拒绝的常见原因包括：
1. 银行卡信息有误（如卡号、开户行、持卡人信息不匹配）
2. 账户存在异常交易，需要核实
3. 提现金额超过可用余额
4. 提现金额低于最低提现限额
建议根据拒绝原因修正后重新申请。

**Q3: 订单完成后，货款多久会结算到店铺余额？**

A3: 订单资金结算规则如下：
1. 买家确认收货后，订单进入结算周期
2. 标准结算周期为T+7（即买家确认收货后7天）
3. 结算周期结束后，货款自动结算到店铺余额
4. 结算后的资金可用于提现或其他用途
具体结算规则可能会根据平台政策调整，请以最新公告为准。

**Q4: 如何查询历史结算记录？**

A4: 查询历史结算记录的步骤如下：
1. 进入"店铺资金"模块
2. 选择"资金流水"功能
3. 在查询条件中选择交易类型为"订单结算"
4. 设置需要查询的时间范围
5. 点击"查询"按钮获取结算记录
6. 如需导出记录，点击"导出"按钮保存到本地