# 八闽助业集市平台操作手册

## 目录

1. [前言](#1-前言)
2. [系统登录](#2-系统登录)
3. [会员管理](#3-会员管理)
   1. [会员列表](#31-会员列表)
   2. [会员银行卡](#32-会员银行卡)
   3. [收货地址](#33-收货地址)
   4. [余额明细](#34-余额明细)
   5. [资金明细](#35-资金明细)
   6. [提现记录](#36-提现记录)
   7. [平台分销记录](#37-平台分销记录)
4. [店铺管理](#4-店铺管理)
   1. [店铺列表](#41-店铺列表)
   2. [店铺余额](#42-店铺余额)
   3. [店铺银行卡](#43-店铺银行卡)
5. [店铺商品](#5-店铺商品)
   1. [商品列表](#51-商品列表)
6. [店铺订单](#6-店铺订单)
   1. [全部订单](#61-全部订单)
7. [财务管理](#7-财务管理)
   1. [店铺资金](#71-店铺资金)
   2. [提现审批](#72-提现审批)
8. [营销管理](#8-营销管理)
   1. [广告轮播图设置](#81-广告轮播图设置)
9. [常见问题](#9-常见问题)
10. [附录](#10-附录)

## 1. 前言

《八闽助业集市》是一个综合性电商平台，旨在为用户提供便捷的购物体验和为商家提供高效的销售渠道。本操作手册详细介绍了平台管理系统的各项功能和操作方法，帮助管理人员快速上手使用系统。

### 1.1 系统概述

《八闽助业集市》平台管理系统是一个基于Web的后台管理系统，主要用于管理平台上的会员、商家、商品、订单等信息。系统采用现代化的设计理念，界面简洁直观，功能丰富完善，为平台的日常运营提供强大的支持。

### 1.2 使用对象

本操作手册主要面向以下用户：
- 平台管理员：负责整个平台的管理和维护
- 客服人员：负责处理用户咨询和投诉
- 财务人员：负责处理平台的财务相关事务
- 运营人员：负责平台的日常运营和推广

### 1.3 使用说明

本手册按照系统的功能模块进行编排，每个模块包含功能概述、操作指南、注意事项等内容。用户可以根据需要查阅相应章节，也可以通过目录快速定位到所需内容。

## 2. 系统登录

待完善...

## 3. 会员管理

会员管理是平台管理系统的核心功能之一，用于管理平台上的所有用户信息。通过会员管理模块，管理员可以查看和管理会员的基本信息、账户状态、银行卡信息、收货地址、资金明细等。

### 3.1 会员列表

会员列表是平台管理系统中的核心功能之一，用于集中展示和管理所有注册会员的信息。通过会员列表，平台管理人员可以查看会员基本资料、管理会员状态、调整会员等级、查询会员推荐关系等，实现对会员的全面管理。

![会员列表页面](../images/会员管理/会员列表.png)

#### 3.1.1 页面布局

会员列表页面主要分为三个区域：
- 查询区域：位于页面上方，用于设置查询条件筛选会员
- 操作按钮区域：位于查询区域下方，提供新增、导出等功能按钮
- 数据表格区域：位于页面主体部分，以表格形式展示会员信息

#### 3.1.2 会员查询

会员列表页面提供了丰富的查询条件，帮助管理员快速找到目标会员：

1. **基本查询**：
   - 用户编号：输入会员的唯一编号进行精确查询
   - 手机号：输入会员注册的手机号码
   - 昵称：输入会员的昵称关键字

2. **展开更多查询条件**：
   - 点击查询区域右下方的"展开"按钮，可以显示更多查询条件
   - 展开后可使用的额外条件包括：
     - 性别：从下拉菜单中选择会员性别
     - 会员类型：从下拉菜单中选择会员类型
     - 推广人：输入推广人姓名或关键字
     - 创建时间：选择时间范围，筛选特定时间段注册的会员

3. **执行查询**：
   - 设置好查询条件后，点击"查询"按钮执行查询
   - 系统将显示符合条件的会员列表

4. **重置查询条件**：
   - 点击"重置"按钮，清空所有已设置的查询条件

#### 3.1.3 会员信息浏览

会员列表以表格形式展示会员信息，包含以下主要字段：

- 会员编号：会员的唯一标识
- 头像：会员的个人头像图片
- 手机号：会员注册的手机号码
- 昵称：会员的昵称
- 会员类型：显示会员的类型分类
- 是否员工：标识会员是否为平台员工
- 推广人：显示推荐该会员的上级推广人
- 余额：会员账户当前可用余额
- 累计充值金额：会员历史充值总额
- 是否助梦家：标识会员是否为助梦家身份
- 累计佣金：会员获得的总佣金金额
- 累计业绩：会员产生的总业绩
- 品牌馆业绩：会员在品牌馆产生的业绩
- 生活馆业绩：会员在生活馆产生的业绩
- 创业馆业绩：会员在创业馆产生的业绩
- 创建时间：会员注册的时间
- 状态：会员当前状态（启用/停用）
- 停用说明：会员被停用的原因说明

#### 3.1.4 会员管理操作

**新增企业家**：
1. 点击操作按钮区域的"新增企业家"按钮
2. 在弹出的表单中填写企业家信息，包括基本资料、联系方式等
3. 填写完成后点击"确定"按钮提交

![新增企业家](../images/会员管理/新增企业家.png)

**会员等级调整**：
1. 在会员列表中找到需要调整等级的会员
2. 点击会员等级信息（如有显示）或相关操作按钮
3. 在弹出的等级设置对话框中，选择新的会员等级
4. 点击"确定"按钮完成等级调整

**停用会员**：
1. 在会员列表中找到需要停用的会员
2. 点击操作列中的"停用"按钮
3. 在弹出的确认对话框中，输入停用原因
4. 点击"确认"按钮完成停用操作
5. 停用后，该会员将无法访问商城

**启用会员**：
1. 在会员列表中找到需要启用的已停用会员
2. 点击操作列中的"启用"按钮
3. 在弹出的确认对话框中，点击"确定"按钮
4. 启用后，该会员将恢复商城访问权限

**编辑会员信息**：
1. 在会员列表中找到需要编辑的会员
2. 点击操作列中的"编辑"按钮
3. 在弹出的编辑表单中修改会员信息
4. 点击"保存"按钮提交修改

#### 3.1.5 批量操作

**批量选择**：
1. 在会员列表左侧的复选框中，选择需要批量操作的会员
2. 可以单个选择，也可以点击表头的复选框全选当前页的所有会员

**批量删除**：
1. 选择需要删除的会员后，点击"批量操作"按钮
2. 在下拉菜单中选择"删除"选项
3. 在弹出的确认对话框中，点击"确定"按钮
4. 系统将删除所选的会员记录

#### 3.1.6 注意事项

1. **数据安全**：
   - 会员信息涉及用户隐私，请遵守相关法律法规，保护用户信息安全
   - 不要将会员敏感信息泄露给无关人员

2. **操作谨慎**：
   - 停用会员会直接影响用户的正常使用，请谨慎操作
   - 批量删除操作不可恢复，执行前请再次确认

### 3.2 会员银行卡

会员银行卡功能是平台管理系统中的重要组成部分，用于管理会员绑定的银行卡信息。通过该功能，平台管理人员可以查看、编辑会员的银行卡信息，确保会员提现等资金操作的安全性和准确性。

![会员银行卡页面](../images/会员管理/会员银行卡.png)

#### 3.2.1 页面布局

会员银行卡页面主要分为三个区域：
- 查询区域：位于页面上方，用于设置查询条件筛选银行卡信息
- 操作按钮区域：位于查询区域下方，提供相关功能按钮
- 数据表格区域：位于页面主体部分，以表格形式展示会员银行卡信息

#### 3.2.2 银行卡信息查询

会员银行卡页面提供了多种查询条件，帮助管理员快速找到目标银行卡信息：

1. **基本查询**：
   - 手机号：输入会员的手机号码进行查询
   - 昵称：输入会员的昵称关键字
   - 联系人手机号：输入银行卡关联的联系人手机号

2. **展开更多查询条件**：
   - 点击查询区域右下方的"展开"按钮，可以显示更多查询条件
   - 展开后可使用的额外条件包括：
     - 开户行：输入银行名称关键字
     - 银行卡号：输入银行卡号进行精确查询
     - 创建时间：选择时间范围，筛选特定时间段添加的银行卡
     - 修改时间：选择时间范围，筛选特定时间段修改过的银行卡

3. **执行查询**：
   - 设置好查询条件后，点击"查询"按钮执行查询
   - 系统将显示符合条件的银行卡列表

4. **重置查询条件**：
   - 点击"重置"按钮，清空所有已设置的查询条件

#### 3.2.3 银行卡信息浏览

银行卡列表以表格形式展示会员银行卡信息，包含以下主要字段：

- 头像：会员的个人头像图片
- 手机号：会员注册的手机号码
- 昵称：会员的昵称
- 联系人手机号：银行卡关联的联系人手机号
- 收款人：银行卡持卡人姓名
- 身份证号：持卡人的身份证号码
- 开户行：银行名称
- 开户行分支行：具体的开户银行分支机构
- 银行卡号：银行卡账号
- 创建时间：银行卡信息添加的时间
- 变更说明：最近一次变更的说明内容
- 变更凭证：银行卡信息变更的证明材料
- 修改人：最后修改银行卡信息的操作人
- 修改时间：最后一次修改的时间

#### 3.2.4 银行卡管理操作

**编辑银行卡信息**：
1. 在银行卡列表中找到需要编辑的银行卡记录
2. 点击操作列中的"编辑"按钮
3. 在弹出的编辑表单中，可以修改以下信息：
   - 联系人手机号：银行卡关联的联系人手机号
   - 收款人：银行卡持卡人姓名
   - 身份证号：持卡人的身份证号码
   - 银行：从下拉列表中选择银行
   - 银行卡号：银行卡账号
   - 开户行分支行：具体的开户银行分支机构
   - 变更说明：说明本次修改的原因和内容
   - 变更凭证：上传银行卡信息变更的证明材料（支持jpg、jpeg、png格式，大小不超过500k）
4. 填写完成后点击"确定"按钮提交修改

#### 3.2.5 批量操作

**批量选择**：
1. 在银行卡列表左侧的复选框中，选择需要批量操作的银行卡记录
2. 可以单个选择，也可以点击表头的复选框全选当前页的所有记录
3. 页面上方会显示已选择的记录数量
4. 点击"清空"可以取消所有选择

#### 3.2.6 注意事项

1. **数据安全**：
   - 银行卡信息涉及用户资金安全，请遵守相关法律法规，保护用户信息安全
   - 不要将银行卡敏感信息泄露给无关人员

2. **操作谨慎**：
   - 修改银行卡信息可能会影响会员的提现操作，请谨慎操作
   - 每次修改银行卡信息时，必须填写变更说明并上传变更凭证

3. **信息验证**：
   - 编辑银行卡信息时，系统会自动验证手机号、身份证号和银行卡号的格式
   - 确保输入的信息准确无误，特别是银行卡号和身份证号

### 3.3 收货地址

会员收货地址功能是平台管理系统中的重要组成部分，用于管理会员的收货地址信息。通过该功能，平台管理人员可以查看会员的收货地址详情，包括联系人、收货人手机号、收货城市、详细地址等信息，便于在订单处理、物流配送等环节提供支持和服务。

![会员收货地址页面](../images/会员管理/收货地址.png)

#### 3.3.1 页面布局

会员收货地址页面主要分为两个区域：
- 查询区域：位于页面上方，用于设置查询条件筛选收货地址信息
- 数据表格区域：位于页面主体部分，以表格形式展示会员收货地址信息

#### 3.3.2 收货地址信息查询

会员收货地址页面提供了多种查询条件，帮助管理员快速找到目标收货地址信息：

1. **基本查询**：
   - 手机号：输入会员的手机号码进行查询
   - 微信昵称：输入会员的微信昵称关键字
   - 联系人：输入收货地址的联系人姓名

2. **展开更多查询条件**：
   - 点击查询区域右下方的"展开"按钮，可以显示更多查询条件
   - 展开后可使用的额外条件包括：
     - 收货人手机号：输入收货地址上的联系电话
     - 是否默认：从下拉菜单中选择是否为默认地址（是/否）
     - 创建时间：选择时间范围，筛选特定时间段添加的收货地址

3. **执行查询**：
   - 设置好查询条件后，点击"查询"按钮执行查询
   - 系统将显示符合条件的收货地址列表

4. **重置查询条件**：
   - 点击"重置"按钮，清空所有已设置的查询条件

#### 3.3.3 收货地址信息浏览

收货地址列表以表格形式展示会员收货地址信息，包含以下主要字段：

- 头像：会员的个人头像图片
- 手机号：会员注册的手机号码
- 微信昵称：会员的微信昵称
- 联系人：收货地址的联系人姓名
- 收货人手机号：收货地址上的联系电话
- 收货城市：收货地址所在的城市信息
- 详细地址：具体的街道门牌号等详细地址
- 是否默认：标识该地址是否为会员的默认收货地址（是/否）
- 创建时间：收货地址添加的时间

#### 3.3.4 批量操作

系统支持对收货地址记录进行批量选择操作：

1. **批量选择**：
   - 在收货地址列表左侧的复选框中，选择需要操作的收货地址记录
   - 可以单个选择，也可以点击表头的复选框全选当前页的所有记录

#### 3.3.5 注意事项

1. **数据安全**：
   - 收货地址信息涉及用户隐私，请遵守相关法律法规，保护用户信息安全
   - 不要将收货地址信息泄露给无关人员

2. **查询优化**：
   - 使用精确的查询条件可以提高查询效率
   - 对于大数据量查询，建议使用会员手机号或收货人手机号等唯一标识进行查询

3. **功能限制**：
   - 当前页面主要用于查询和浏览收货地址信息，不支持直接添加或编辑收货地址
   - 收货地址的添加和修改通常由会员在前端商城中自行操作

### 3.4 余额明细

余额明细功能是平台管理系统中的重要组成部分，用于管理和查询会员账户余额的变动记录。通过该功能，平台管理人员可以查看会员的余额信息、交易明细，以及进行余额调整操作，确保会员账户资金的准确性和透明度。

![余额明细列表](../images/会员管理/余额明细列表.png)

#### 3.4.1 页面布局

余额明细页面主要分为两个主要页面：

1. **余额明细列表页面**：
   - 查询区域：位于页面上方，用于设置查询条件筛选会员
   - 数据表格区域：位于页面主体部分，以表格形式展示会员基本信息和余额

2. **余额明细详情页面**：
   - 查询区域：位于弹窗上方，用于设置查询条件筛选交易记录
   - 数据表格区域：位于弹窗主体部分，以表格形式展示会员的交易明细记录

#### 3.4.2 会员余额查询

余额明细列表页面提供了多种查询条件，帮助管理员快速找到目标会员：

1. **基本查询**：
   - 手机号：输入会员的手机号码进行查询
   - 会员昵称：输入会员的昵称关键字
   - 会员编号：输入会员的唯一编号进行精确查询

2. **执行查询**：
   - 设置好查询条件后，点击"查询"按钮执行查询
   - 系统将显示符合条件的会员列表

3. **重置查询条件**：
   - 点击"重置"按钮，清空所有已设置的查询条件

#### 3.4.3 会员余额信息浏览

余额明细列表以表格形式展示会员基本信息和余额，包含以下主要字段：

- 会员编号：会员的唯一标识
- 头像：会员的个人头像图片
- 手机号：会员注册的手机号码
- 会员昵称：会员的昵称
- 余额：会员账户当前可用余额
- 操作：提供"余额明细"按钮，用于查看详细交易记录

#### 3.4.4 查看余额明细详情

1. 在余额明细列表中找到需要查看的会员
2. 点击操作列中的"余额明细"按钮
3. 系统弹出余额明细详情弹窗，显示该会员的交易记录

![余额明细详情列表](../images/会员管理/余额明细详情列表.png)

**余额明细详情查询**：

余额明细详情弹窗提供了多种查询条件，帮助管理员筛选交易记录：

1. **查询条件**：
   - 流水号：输入交易流水号进行精确查询
   - 交易单号：输入交易单号进行精确查询
   - 交易类型：从下拉菜单中选择交易类型（如充值、消费、提现等）
   - 收入支出：从下拉菜单中选择收入或支出
   - 交易时间：选择时间范围，筛选特定时间段的交易记录

2. **执行查询**：
   - 设置好查询条件后，点击"查询"按钮执行查询
   - 系统将显示符合条件的交易记录

**余额明细详情浏览**：

余额明细详情表格展示会员的交易记录，包含以下主要字段：

- 流水号：交易的唯一标识
- 交易类型：显示交易的类型（如充值、消费、提现等）
- 收入/支出：标识该笔交易是收入还是支出
- 交易金额：本次交易的金额
- 可用余额：交易后的账户余额
- 交易时间：交易发生的时间
- 交易单号：关联的订单或业务单号
- 备注：交易的说明信息

#### 3.4.5 余额调整操作

平台管理人员可以对会员的余额进行调整，包括增加余额（系统补发）和减少余额（系统扣减）。

![余额调整](../images/会员管理/余额调整.png)

**调整会员余额**：

1. 在余额明细列表中找到需要调整余额的会员
2. 点击操作列中的"调整"按钮（如果启用）
3. 系统弹出余额调整弹窗，显示会员基本信息
4. 在弹窗中填写以下信息：
   - 交易类型：选择"系统补发余额"或"系统扣减余额"
   - 交易额：输入需要调整的金额（精确到小数点后两位）
5. 点击"确定"按钮提交调整

**余额调整规则**：

1. **系统补发余额**：
   - 选择交易类型为"系统补发余额"
   - 系统自动将交易方向设置为"收入"
   - 输入的金额将增加到会员账户余额中

2. **系统扣减余额**：
   - 选择交易类型为"系统扣减余额"
   - 系统自动将交易方向设置为"支出"
   - 输入的金额将从会员账户余额中扣除
   - 扣减金额不能超过会员当前可用余额

#### 3.4.6 注意事项

1. **数据安全**：
   - 余额信息涉及用户资金，请遵守相关法律法规，保护用户信息安全
   - 不要将余额信息泄露给无关人员

2. **操作谨慎**：
   - 余额调整直接影响会员的账户资金，请谨慎操作
   - 每次调整前，确认会员身份和调整金额的准确性
   - 系统会记录所有余额调整操作，包括操作人和操作时间

3. **交易记录**：
   - 交易记录是重要的财务数据，不可删除或修改
   - 如发现交易记录有误，应通过新的调整交易进行纠正

### 3.5 资金明细

资金明细功能是平台管理系统中的重要组成部分，用于查询和管理会员的所有资金交易记录。通过该功能，平台管理人员可以全面了解会员的资金流水情况，包括订单交易、余额提现、订单退款、分销奖励等各类交易记录，便于财务核对和问题排查。

![资金明细](../images/会员管理/资金明细.png)

#### 3.5.1 页面布局

资金明细页面主要分为两个区域：
- 查询区域：位于页面上方，用于设置查询条件筛选资金交易记录
- 数据表格区域：位于页面主体部分，以表格形式展示会员的资金交易记录

#### 3.5.2 资金明细查询

资金明细页面提供了多种查询条件，帮助管理员快速找到目标交易记录：

1. **基本查询**：
   - 流水号：输入交易流水号进行精确查询
   - 会员账号：输入会员的手机号码进行查询
   - 会员昵称：输入会员的昵称关键字进行查询

2. **展开更多查询条件**：
   - 点击查询区域右下方的"展开"按钮，可以显示更多查询条件
   - 展开后可使用的额外条件包括：
     - 交易类型：从下拉菜单中选择交易类型（如订单交易、余额提现、订单退款、分销奖励等）
     - 交易状态：从下拉菜单中选择交易状态（如未支付、进行中、交易完成、已退款等）
     - 收入支出：从下拉菜单中选择收入或支出
     - 交易时间：选择时间范围，筛选特定时间段的交易记录

3. **执行查询**：
   - 设置好查询条件后，点击"查询"按钮执行查询
   - 系统将显示符合条件的交易记录

4. **重置查询条件**：
   - 点击"重置"按钮，清空所有已设置的查询条件

#### 3.5.3 资金明细浏览

资金明细表格展示会员的交易记录，包含以下主要字段：

- 流水号：交易的唯一标识
- 会员账号：会员的手机号码
- 会员昵称：会员的昵称
- 交易类型：显示交易的类型（如订单交易、余额提现、订单退款、分销奖励等）
- 收入/支出：标识该笔交易是收入还是支出
- 交易金额：本次交易的金额
- 交易状态：显示交易的当前状态（如未支付、进行中、交易完成、已退款等）
- 交易时间：交易发生的时间
- 备注：交易的说明信息

#### 3.5.4 批量操作

系统支持对交易记录进行批量选择操作：

1. **批量选择**：
   - 在交易记录列表左侧的复选框中，选择需要操作的记录
   - 可以单个选择，也可以点击表头的复选框全选当前页的所有记录
   - 页面上方会显示已选择的记录数量
   - 点击"清空"可以取消所有选择

#### 3.5.5 注意事项

1. **数据安全**：
   - 资金明细涉及用户资金信息，请遵守相关法律法规，保护用户信息安全
   - 不要将资金交易信息泄露给无关人员

2. **查询优化**：
   - 使用精确的查询条件可以提高查询效率
   - 对于大数据量查询，建议使用流水号或会员账号等唯一标识进行查询
   - 使用交易时间范围可以有效缩小查询范围

3. **数据解读**：
   - 交易类型和交易状态使用了系统预设的数据字典，请正确理解各状态的含义
   - 收入/支出是从会员角度定义的，"收入"表示会员账户资金增加，"支出"表示会员账户资金减少

### 3.6 提现记录

提现记录功能是平台管理系统中的重要组成部分，用于管理和处理会员的提现申请。通过该功能，平台管理人员可以查看会员的提现申请记录，进行审核和打款操作，确保会员提现流程的顺利进行和资金安全。

![提现记录](../images/会员管理/提现记录.png)

#### 3.6.1 页面布局

提现记录页面主要分为两个区域：
- 查询区域：位于页面上方，用于设置查询条件筛选提现记录
- 数据表格区域：位于页面主体部分，以表格形式展示会员的提现申请记录

#### 3.6.2 提现记录查询

提现记录页面提供了多种查询条件，帮助管理员快速找到目标提现记录：

1. **基本查询**：
   - 流水号：输入提现流水号进行精确查询
   - 会员账号：输入会员的手机号码进行查询
   - 会员昵称：输入会员的昵称关键字进行查询

2. **展开更多查询条件**：
   - 点击查询区域右下方的"展开"按钮，可以显示更多查询条件
   - 展开后可使用的额外条件包括：
     - 手机号：输入会员的手机号码进行查询
     - 提现方式：从下拉菜单中选择提现方式（微信、支付宝、银行卡）
     - 提现账号：输入提现账号进行查询
     - 收款人：输入收款人姓名进行查询
     - 申请时间：选择时间范围，筛选特定时间段的提现申请
     - 状态：从下拉菜单中选择提现状态（待审核、待打款、已付款、无效）
     - 审核时间：选择时间范围，筛选特定时间段审核的提现记录
     - 打款时间：选择时间范围，筛选特定时间段打款的提现记录

3. **执行查询**：
   - 设置好查询条件后，点击"查询"按钮执行查询
   - 系统将显示符合条件的提现记录

4. **重置查询条件**：
   - 点击"重置"按钮，清空所有已设置的查询条件

#### 3.6.3 提现记录浏览

提现记录表格展示会员的提现申请记录，包含以下主要字段：

- 流水号：提现申请的唯一标识
- 会员账号：会员的手机号码
- 会员昵称：会员的昵称
- 手机号：会员的联系电话
- 提现金额：会员申请提现的金额
- 提现方式：显示提现的方式（微信钱包、支付宝、银行卡）
- 提现账号：提现到的账号信息
- 开户行分支行：银行卡提现时的开户行分支行信息
- 收款人：提现账户的持有人姓名
- 手续费：提现产生的手续费
- 实际金额：扣除手续费后实际到账的金额
- 申请时间：会员提交提现申请的时间
- 状态：提现申请的当前状态（待审核、待打款、已打款、无效）
- 审核时间：平台管理员审核提现申请的时间
- 打款时间：平台管理员执行打款操作的时间
- 不通过原因：审核不通过时的原因说明
- 备注：提现申请的备注信息

#### 3.6.4 批量操作

系统支持对提现记录进行批量选择操作：

1. **批量选择**：
   - 在提现记录列表左侧的复选框中，选择需要操作的记录
   - 可以单个选择，也可以点击表头的复选框全选当前页的所有记录
   - 页面上方会显示已选择的记录数量
   - 点击"清空"可以取消所有选择

#### 3.6.5 提现审核操作

平台管理员需要对会员的提现申请进行审核，确认提现信息的准确性和合法性：

1. **审核提现申请**：
   - 在提现记录列表中找到状态为"待审核"的记录
   - 点击操作列中的"审核"按钮
   - 系统弹出审核对话框
   - 选择审核结果：
     - 通过：选择"通过"选项，提现申请状态将变更为"待打款"
     - 不通过：选择"不通过"选项，并在文本框中输入不通过原因，提现申请状态将变更为"无效"
   - 点击"确认"按钮提交审核结果

#### 3.6.6 提现打款操作

对于审核通过的提现申请，平台管理员需要执行打款操作：

1. **执行打款操作**：
   - 在提现记录列表中找到状态为"待打款"的记录
   - 点击操作列中的"打款"按钮
   - 系统弹出打款确认对话框
   - 选择打款状态：
     - 待打款：保持当前状态不变
     - 已打款：选择"已打款"选项，并可在文本框中输入打款备注，提现申请状态将变更为"已打款"
   - 点击"确认"按钮提交打款结果

#### 3.6.7 注意事项

1. **资金安全**：
   - 提现涉及实际资金操作，请谨慎处理每一笔提现申请
   - 审核时需要核对提现账户信息的准确性，确保资金安全

2. **审核标准**：
   - 审核提现申请时，需要确认会员账户余额是否足够
   - 检查提现账户信息是否完整、准确
   - 对于异常提现申请（如短时间内多次大额提现），应进行额外核实

3. **打款操作**：
   - 打款操作应在确认资金已实际转出后才标记为"已打款"
   - 打款时应核对收款账户信息，确保打款准确无误
   - 建议保留打款凭证，以便日后核对

4. **状态流转**：
   - 提现申请状态的正常流转顺序为：待审核 → 待打款 → 已打款
   - 审核不通过的提现申请状态为"无效"，系统会自动将冻结的资金返还到会员账户

## 4. 店铺管理

店铺管理是平台管理系统中的核心功能模块，用于管理平台上的所有店铺信息。通过店铺管理模块，平台管理人员可以对店铺进行全生命周期的管理，包括创建店铺、编辑店铺信息、管理店铺状态、设置分佣规则等，确保平台店铺的规范运营和有序管理。

### 4.1 店铺列表

店铺列表功能是平台管理系统中的核心组成部分，用于管理平台上的所有店铺信息。通过该功能，平台管理人员可以查看、创建、编辑店铺信息，管理店铺状态，以及设置店铺的分佣规则等，实现对平台店铺的全面管理和监控。

![店铺列表](../images/店铺管理/店铺列表/店铺列表.png)

#### 4.1.1 页面布局

店铺列表页面主要分为三个区域：
- 查询区域：位于页面上方，用于设置查询条件筛选店铺
- 操作按钮区域：位于查询区域下方，提供创建店铺等功能按钮
- 数据表格区域：位于页面主体部分，以表格形式展示店铺信息

#### 4.1.2 店铺查询

店铺列表页面提供了多种查询条件，帮助管理员快速找到目标店铺：

1. **基本查询**：
   - 店铺账号：输入店铺的登录账号进行查询
   - 联系人：输入店铺联系人姓名进行查询
   - 联系人手机：输入店铺联系人的手机号码进行查询

2. **展开更多查询条件**：
   - 点击查询区域右下方的"展开"按钮，可以显示更多查询条件
   - 展开后可使用的额外条件包括：
     - 门店名称：输入店铺的门店名称关键字
     - 店铺类型：从下拉菜单中选择店铺类型
     - 所在城市：通过级联选择器选择店铺所在的城市
     - 主体类型：从下拉菜单中选择主体类型（个体/企业或个体户）
     - 主营分类：从下拉菜单中选择店铺的主营分类
     - 店铺编号：输入店铺的唯一编号进行精确查询

3. **执行查询**：
   - 设置好查询条件后，点击"查询"按钮执行查询
   - 系统将显示符合条件的店铺列表

4. **重置查询条件**：
   - 点击"重置"按钮，清空所有已设置的查询条件

#### 4.1.3 创建店铺

平台管理员可以创建新的店铺，具体操作如下：

1. 点击操作按钮区域的"创建店铺"按钮
2. 系统弹出创建店铺表单，需要填写以下信息：

   ![创建店铺](../images/店铺管理/店铺列表/创建店铺.jpg)

   **基础信息**：
   - 老板姓名：店铺负责人姓名
   - 老板手机：店铺负责人手机号码
   - 门店名称：店铺的名称
   - Logo：上传店铺的Logo图片
   - 店铺宣传图：上传店铺的宣传图片（最多5张）
   - 门脸照：上传店铺门面的照片
   - 店内照：上传店铺内部的照片
   - 所在城市：选择店铺所在的城市
   - 门店详细地址：输入店铺的详细地址
   - 主体类型：选择店铺的主体类型（个体/企业或个体户）
   - 店铺类型：选择店铺的类型
   - 店铺等级：选择店铺的等级
   - 主营分类：选择店铺的主营分类
   - 客服电话：输入店铺的客服联系电话
   - 商家介绍：输入店铺的详细介绍（不少于50字）
   - 备注：输入其他需要说明的信息

3. 填写完成后点击"确定"按钮提交创建申请

#### 4.1.4 编辑店铺信息

平台管理员可以编辑已有店铺的信息，分为基础信息和证件信息两部分：

1. **编辑基础信息**：
   - 在店铺列表中找到需要编辑的店铺
   - 点击操作列中的"基础信息"按钮
   - 系统弹出编辑基础信息表单，可以修改店铺的基本信息
   - 修改完成后点击"确定"按钮保存更改

   ![编辑店铺基础信息](../images/店铺管理/店铺列表/编辑店铺基础信息.jpg)

2. **编辑证件信息**：
   - 在店铺列表中找到需要编辑的店铺
   - 点击操作列中的"证件信息"按钮
   - 系统弹出编辑证件信息表单，可以修改店铺的证件相关信息
   - 修改完成后点击"确定"按钮保存更改

   ![编辑店铺证件信息](../images/店铺管理/店铺列表/编辑店铺证件信息.png)

#### 4.1.5 店铺状态管理

平台管理员可以管理店铺的启用和停用状态：

1. **停用店铺**：
   - 在店铺列表中找到需要停用的店铺
   - 点击操作列中的"停用"按钮
   - 系统弹出停用确认对话框，需要输入停用原因
   - 点击"确定"按钮完成停用操作
   - 停用后，店铺状态将变为"停用"，店铺将无法正常运营

   ![停用店铺](../images/店铺管理/店铺列表/停用店铺.png)

2. **启用店铺**：
   - 在店铺列表中找到需要启用的已停用店铺
   - 点击操作列中的"启用"按钮
   - 系统弹出启用确认对话框
   - 点击"确定"按钮完成启用操作
   - 启用后，店铺状态将变为"启用"，店铺可以恢复正常运营

   ![启用店铺](../images/店铺管理/店铺列表/启用店铺.png)

#### 4.1.6 企业家分佣设置

平台管理员可以为店铺设置企业家分佣规则：

1. 在店铺列表中找到需要设置的店铺
2. 点击操作列中的"企业家分佣设置"按钮
3. 系统弹出分佣设置表单，可以设置不同类型订单的分佣比例
4. 设置完成后点击"确定"按钮保存设置

#### 4.1.7 批量操作

系统支持对店铺进行批量操作：

1. **批量选择**：
   - 在店铺列表左侧的复选框中，选择需要批量操作的店铺
   - 可以单个选择，也可以点击表头的复选框全选当前页的所有店铺

2. **批量删除**：
   - 选择需要删除的店铺后，点击"批量操作"按钮
   - 在下拉菜单中选择"删除"选项
   - 在弹出的确认对话框中，点击"确定"按钮
   - 系统将删除所选的店铺记录

#### 4.1.8 店铺信息浏览

店铺列表以表格形式展示店铺信息，包含以下主要字段：

- 店铺编号：店铺的唯一标识
- 店铺账号：店铺的登录账号
- 店铺等级：店铺的等级分类
- 联系人：店铺的联系人姓名
- 联系人手机号：店铺联系人的手机号码
- 门店名称：店铺的名称
- 城市：店铺所在的城市
- 门店地址：店铺的详细地址
- 店铺类型：店铺的类型分类
- 主体类型：店铺的主体类型（个体/企业或个体户）
- 主营分类：店铺的主营业务分类
- 客服电话：店铺的客服联系电话
- 统一社会信用代码：企业或个体户的统一社会信用代码
- 经办人类型：店铺经办人的类型
- 姓名：经办人的姓名
- 身份证号码：经办人的身份证号码
- 状态：店铺的当前状态（启用/停用）
- 操作：提供对店铺的各种操作按钮

#### 4.1.9 注意事项

1. **数据安全**：
   - 店铺信息涉及商家隐私，请遵守相关法律法规，保护商家信息安全
   - 不要将店铺敏感信息泄露给无关人员

2. **操作谨慎**：
   - 停用店铺会直接影响商家的正常经营，请谨慎操作
   - 批量删除操作不可恢复，执行前请再次确认
   - 创建店铺时，确保填写的信息准确无误，特别是联系方式和证件信息

3. **图片上传**：
   - 上传的图片应清晰可辨，能够真实反映店铺情况
   - Logo和店铺宣传图是必传项，直接影响店铺在前端的展示效果
   - 证件照片应确保真实有效，不得使用PS等工具修改

4. **分佣设置**：
   - 设置企业家分佣比例时，需要考虑平台整体的分佣策略
   - 分佣比例设置不合理可能影响商家积极性或平台收益

### 4.2 店铺余额

店铺余额功能是平台管理系统中的重要组成部分，用于查询和监控平台上所有店铺的账户余额情况。通过该功能，平台管理人员可以全面了解各个店铺的资金状况，便于财务核对和资金监控。

![店铺余额](../images/店铺管理/店铺余额.png)

#### 4.2.1 页面布局

店铺余额页面主要分为两个区域：
- 查询区域：位于页面上方，用于设置查询条件筛选店铺
- 数据表格区域：位于页面主体部分，以表格形式展示店铺的余额信息

#### 4.2.2 店铺余额查询

店铺余额页面提供了多种查询条件，帮助管理员快速找到目标店铺：

1. **基本查询**：
   - 账号：输入店铺的登录账号进行查询
   - 联系人：输入店铺联系人姓名进行查询
   - 联系人手机：输入店铺联系人的手机号码进行查询

2. **展开更多查询条件**：
   - 点击查询区域右下方的"展开"按钮，可以显示更多查询条件
   - 展开后可使用的额外条件包括：
     - 店铺名称：输入店铺的名称关键字
     - 状态：从下拉菜单中选择店铺状态（启用/停用）
     - 所在城市：通过级联选择器选择店铺所在的城市

3. **执行查询**：
   - 设置好查询条件后，点击"查询"按钮执行查询
   - 系统将显示符合条件的店铺列表及其余额信息

4. **重置查询条件**：
   - 点击"重置"按钮，清空所有已设置的查询条件

#### 4.2.3 店铺余额信息浏览

店铺余额表格展示店铺的基本信息和余额，包含以下主要字段：

- 序号：表格中的序号，从1开始递增
- 账号：店铺的登录账号
- 店铺名称：店铺的名称
- 城市：店铺所在的城市
- 联系人：店铺的联系人姓名
- 联系人手机：店铺联系人的手机号码
- 状态：店铺的当前状态（启用/停用）
- 余额：店铺账户当前的可用余额

#### 4.2.4 批量操作

系统支持对店铺记录进行批量选择操作：

1. **批量选择**：
   - 在店铺列表左侧的复选框中，选择需要操作的店铺记录
   - 可以单个选择，也可以点击表头的复选框全选当前页的所有记录
   - 页面上方会显示已选择的记录数量
   - 点击"清空"可以取消所有选择

#### 4.2.5 注意事项

1. **数据安全**：
   - 店铺余额信息涉及商家资金，请遵守相关法律法规，保护商家信息安全
   - 不要将店铺余额信息泄露给无关人员

2. **查询优化**：
   - 使用精确的查询条件可以提高查询效率
   - 对于大数据量查询，建议使用店铺账号或联系人手机号等唯一标识进行查询

3. **功能限制**：
   - 当前页面主要用于查询和浏览店铺余额信息，不支持直接修改店铺余额
   - 店铺余额的变动通常由系统根据交易自动计算，或通过其他专门的财务模块进行调整

### 4.3 店铺银行卡

店铺银行卡功能是平台管理系统中的重要组成部分，用于管理和维护平台上所有店铺的银行卡信息。通过该功能，平台管理人员可以查看、编辑店铺的银行卡信息，确保店铺提现等资金操作的安全性和准确性。

![店铺银行卡](../images/店铺管理/店铺银行卡.png)

#### 4.3.1 页面布局

店铺银行卡页面主要分为两个区域：
- 查询区域：位于页面上方，用于设置查询条件筛选店铺银行卡信息
- 数据表格区域：位于页面主体部分，以表格形式展示店铺的银行卡信息

#### 4.3.2 店铺银行卡查询

店铺银行卡页面提供了多种查询条件，帮助管理员快速找到目标店铺的银行卡信息：

1. **基本查询**：
   - 店铺账号：输入店铺的登录账号进行查询
   - 店铺名称：输入店铺的名称关键字进行查询
   - 联系人手机号：输入店铺联系人的手机号码进行查询

2. **展开更多查询条件**：
   - 点击查询区域右下方的"展开"按钮，可以显示更多查询条件
   - 展开后可使用的额外条件包括：
     - 开户行：输入银行名称关键字进行查询
     - 银行卡号：输入银行卡号进行精确查询
     - 创建时间：选择时间范围，筛选特定时间段添加的银行卡
     - 修改时间：选择时间范围，筛选特定时间段修改过的银行卡

3. **执行查询**：
   - 设置好查询条件后，点击"查询"按钮执行查询
   - 系统将显示符合条件的银行卡列表

4. **重置查询条件**：
   - 点击"重置"按钮，清空所有已设置的查询条件

#### 4.3.3 店铺银行卡信息浏览

店铺银行卡表格展示店铺的银行卡信息，包含以下主要字段：

- 序号：表格中的序号，从1开始递增
- 头像：店铺的头像图片
- 店铺账号：店铺的登录账号
- 店铺名称：店铺的名称
- 联系人手机号：店铺联系人的手机号码
- 收款人：银行卡持卡人姓名
- 身份证号：持卡人的身份证号码
- 开户行所在城市：银行卡开户行所在的城市
- 开户行：银行名称
- 银行卡号：银行卡账号
- 创建时间：银行卡信息添加的时间
- 变更说明：最近一次变更的说明内容
- 变更凭证：银行卡信息变更的证明材料
- 修改人：最后修改银行卡信息的操作人
- 修改时间：最后一次修改的时间
- 操作：提供对银行卡信息的操作按钮

#### 4.3.4 编辑店铺银行卡信息

平台管理员可以编辑店铺的银行卡信息，具体操作如下：

1. 在店铺银行卡列表中找到需要编辑的记录
2. 点击操作列中的"编辑"按钮
3. 系统弹出编辑银行卡信息表单，可以修改以下信息：

   ![编辑店铺银行卡](../images/店铺管理/编辑店铺银行卡.png)

   - 联系人手机号：银行卡关联的联系人手机号
   - 收款人：银行卡持卡人姓名
   - 身份证号：持卡人的身份证号码
   - 银行：从下拉列表中选择银行
   - 银行卡号：银行卡账号
   - 开户行分支行：具体的开户银行分支机构
   - 变更说明：说明本次修改的原因和内容
   - 变更凭证：上传银行卡信息变更的证明材料（支持jpg、jpeg、png格式，大小不超过500k）

4. 填写完成后点击"确定"按钮提交修改

#### 4.3.5 批量操作

系统支持对银行卡记录进行批量选择操作：

1. **批量选择**：
   - 在银行卡列表左侧的复选框中，选择需要操作的记录
   - 可以单个选择，也可以点击表头的复选框全选当前页的所有记录
   - 页面上方会显示已选择的记录数量
   - 点击"清空"可以取消所有选择

#### 4.3.6 注意事项

1. **数据安全**：
   - 银行卡信息涉及店铺资金安全，请遵守相关法律法规，保护商家信息安全
   - 不要将银行卡敏感信息泄露给无关人员

2. **操作谨慎**：
   - 修改银行卡信息可能会影响店铺的提现操作，请谨慎操作
   - 每次修改银行卡信息时，必须填写变更说明并上传变更凭证

3. **信息验证**：
   - 编辑银行卡信息时，系统会自动验证手机号、身份证号和银行卡号的格式
   - 确保输入的信息准确无误，特别是银行卡号和身份证号

4. **变更凭证要求**：
   - 变更凭证必须清晰可辨
   - 支持jpg、jpeg、png格式
   - 图片大小不超过500k

## 5. 店铺商品

店铺商品管理是平台管理系统中的核心功能模块，用于管理和维护平台上所有店铺的商品信息。通过店铺商品管理模块，平台管理人员可以查看、新增、编辑店铺商品，以及进行上下架操作，确保平台商品信息的准确性和及时更新。

### 5.1 商品列表

商品列表功能是店铺商品管理系统中的重要组成部分，用于管理和维护平台上所有店铺的商品信息。通过该功能，平台管理人员可以查看、新增、编辑店铺商品，以及进行上下架操作，确保平台商品信息的准确性和及时更新。

![店铺商品列表](../images/店铺商品/商品列表/店铺商品列表.png)

#### 5.1.1 页面布局

店铺商品列表页面主要分为三个区域：
- 左侧店铺列表区域：以树形结构展示平台上的所有店铺
- 中间分类列表区域：展示所选店铺的商品分类
- 右侧商品列表区域：展示符合条件的商品信息，包括查询区域、操作按钮区域和数据表格区域

#### 5.1.2 商品查询

商品列表页面提供了多种查询条件，帮助管理员快速找到目标商品：

1. **选择店铺**：
   - 在左侧店铺列表中点击店铺名称，系统将自动加载该店铺的商品分类和商品列表

2. **选择商品分类**：
   - 在中间分类列表中点击分类名称，系统将自动筛选该分类下的商品

3. **基本查询**：
   - 商品编号：输入商品编号进行精确查询
   - 商品名称：输入商品名称关键字进行模糊查询
   - 状态：从下拉菜单中选择商品状态（全部、停用、启用）
   - 上下架状态：从下拉菜单中选择上下架状态（全部、下架、上架）

4. **执行查询**：
   - 设置好查询条件后，点击"查询"按钮执行查询
   - 系统将显示符合条件的商品列表

5. **重置查询条件**：
   - 点击"重置"按钮，清空所有已设置的查询条件

#### 5.1.3 商品列表浏览

商品列表以表格形式展示商品信息，包含以下主要字段：

- 店铺名称：商品所属的店铺名称
- 商品分类：商品所属的分类名称
- 商品主图：商品的主要展示图片
- 商品编号：商品的唯一编号
- 商品名称：商品的名称
- 市场价：商品的市场参考价格
- 成本价：商品的成本价格（如有多规格，显示价格区间）
- 销售价：商品的实际销售价格（如有多规格，显示价格区间）
- 状态：商品的状态（启用/停用）
- 上下架状态：商品的上下架状态（上架/下架）
- 审核状态：商品的审核状态（草稿、待审核、审核通过、审核不通过）
- 状态说明：商品状态的补充说明
- 有无规格：商品是否有规格（有规格/无规格）
- 创建者：创建商品的操作人
- 创建时间：商品创建的时间
- 操作：提供对商品的操作按钮

#### 5.1.4 批量操作

系统支持对商品记录进行批量选择操作：

1. **批量选择**：
   - 在商品列表左侧的复选框中，选择需要操作的记录
   - 可以单个选择，也可以点击表头的复选框全选当前页的所有记录
   - 页面上方会显示已选择的记录数量
   - 点击"清空"可以取消所有选择

2. **批量上架**：
   - 选择多个商品后，点击"批量上架"按钮
   - 系统弹出确认对话框
   - 点击"确定"按钮，所选商品将被批量上架

3. **批量下架**：
   - 选择多个商品后，点击"批量下架"按钮
   - 系统弹出下架原因输入对话框
   - 输入下架原因后点击"确定"按钮，所选商品将被批量下架

4. **批量删除**：
   - 选择多个商品后，点击"批量操作"下拉菜单
   - 选择"删除"选项
   - 系统弹出确认对话框
   - 点击"确定"按钮，所选商品将被批量删除

#### 5.1.5 新增商品

平台管理员可以为店铺添加新商品，具体操作如下：

1. **点击新增按钮**：
   - 在商品列表页面点击"新增"按钮
   - 系统跳转到商品新增页面

2. **选择商品分类**：
   - 在新增商品页面，首先需要选择商品所属的分类

   ![新增商品-选择类目](../images/店铺商品/商品列表/新增商品-选择类目.png)

   - 点击分类名称，选择合适的商品分类
   - 点击"确定"按钮进入商品信息填写页面

3. **填写商品基本信息**：
   - 商品名称：输入商品的名称（必填）
   - 商品描述：输入商品的详细描述
   - 商品图片：上传商品的主图（最多5张）
   - 视频地址：上传商品的展示视频（可选）
   - 商品详情图：上传商品的详情图片（最多20张）

4. **设置商品规格和价格**：
   - 添加规格：点击"添加规格"按钮，可以添加最多两个规格维度（如颜色、尺寸等）
   - 填写规格值：为每个规格维度添加具体的规格值
   - 设置价格与库存：为每个规格组合设置销售价、成本价、会员价和库存
   - 填写SKU编码：为每个规格组合设置唯一的SKU编码
   - 上传规格图：为规格组合上传对应的规格图片
   - 设置商品市场价：输入商品的市场参考价格

5. **设置服务与承诺**：
   - 运费模板：选择商品的运费计算方式（包邮、按件、按重量）
   - 自提：选择是否支持自提
   - 配送：选择是否支持配送
   - 服务承诺：选择商品的服务承诺（如7天无理由退换货、假一赔十等）

6. **保存或提交商品**：
   - 保存草稿箱：点击"保存草稿箱"按钮，商品信息将被保存为草稿状态
   - 提交并上架：点击"提交并上架"按钮，商品将被提交审核并在审核通过后上架

   ![发布商品](../images/店铺商品/商品列表/发布商品.png)

#### 5.1.6 编辑商品

平台管理员可以编辑已有商品的信息，具体操作如下：

1. **点击编辑按钮**：
   - 在商品列表中找到需要编辑的商品
   - 点击操作列中的"编辑"按钮
   - 系统跳转到商品编辑页面

2. **修改商品信息**：
   - 编辑页面与新增页面布局相同，但已填充现有商品信息
   - 可以修改商品的基本信息、规格价格、服务承诺等内容
   - 修改完成后，可以选择保存为草稿或提交上架

#### 5.1.7 商品上下架操作

平台管理员可以对商品进行上下架操作，控制商品在前台的显示状态：

1. **上架商品**：
   - 在商品列表中找到状态为"下架"的商品
   - 点击操作列中的"上架"按钮
   - 系统弹出确认对话框

   ![上架商品](../images/店铺商品/商品列表/上架商品.png)

   - 点击"确定"按钮，商品状态将变更为"上架"

2. **下架商品**：
   - 在商品列表中找到状态为"上架"的商品
   - 点击操作列中的"下架"按钮
   - 系统弹出下架原因输入对话框

   ![下架商品](../images/店铺商品/商品列表/下架商品.png)

   - 输入下架原因后点击"确定"按钮，商品状态将变更为"下架"

#### 5.1.8 其他操作

1. **商品排序**：
   - 在商品列表中找到需要调整排序的商品
   - 点击操作列中的"排序"按钮
   - 系统弹出排序设置对话框
   - 输入排序数值（数值越小，排序越靠前）
   - 点击"确定"按钮保存排序设置

2. **删除商品**：
   - 在商品列表中找到需要删除的商品
   - 点击操作列中的"更多"下拉菜单
   - 选择"删除"选项
   - 系统弹出确认对话框
   - 点击"确定"按钮，商品将被删除

#### 5.1.9 注意事项

1. **商品信息完整性**：
   - 添加或编辑商品时，确保必填信息完整准确
   - 商品图片应清晰展示商品特点，建议使用白底图片
   - 商品详情应详细描述商品的特点、规格、使用方法等信息

2. **规格设置**：
   - 规格名称应简洁明了，如"颜色"、"尺寸"等
   - 规格值应具体明确，如"红色"、"XL"等
   - 有规格商品必须为每个规格组合设置正确的价格和库存

3. **价格设置**：
   - 销售价不能低于成本价
   - 会员价应低于销售价
   - 市场价应高于销售价，作为参考价格展示

4. **上下架操作**：
   - 商品上架前应确保信息完整、图片清晰、价格合理
   - 下架商品时应填写明确的下架原因，便于后续处理
   - 批量上下架操作应谨慎使用，确保所选商品符合操作条件

## 6. 店铺订单

店铺订单管理是平台管理系统中的核心功能模块，用于管理和处理平台上所有店铺的订单信息。通过店铺订单管理模块，平台管理人员可以查看订单详情、处理订单发货、查看物流信息、处理退款等操作，确保订单流程的顺利进行和交易的安全完成。

### 6.1 全部订单

全部订单功能是店铺订单管理系统中的重要组成部分，用于管理和处理平台上所有店铺的订单信息。通过该功能，平台管理人员可以查看订单详情、处理订单发货、查看物流信息、处理退款等操作，确保订单流程的顺利进行和交易的安全完成。

![全部订单](../images/店铺订单/全部订单.png)

#### 6.1.1 页面布局

全部订单页面主要分为两个区域：
- 查询区域：位于页面上方，用于设置查询条件筛选订单
- 数据表格区域：位于页面主体部分，以表格形式展示订单信息

#### 6.1.2 订单查询

全部订单页面提供了多种查询条件，帮助管理员快速找到目标订单：

1. **基本查询**：
   - 订单编号：输入订单编号进行精确查询
   - 订单类型：从下拉菜单中选择订单类型（如普通订单、团购订单等）

2. **展开更多查询条件**：
   - 点击查询区域右下方的"展开"按钮，可以显示更多查询条件
   - 展开后可使用的额外条件包括：
     - 买家账号：输入买家的手机号码进行查询
     - 店铺名称：输入店铺的名称关键字进行查询
     - 订单状态：从下拉菜单中选择订单状态（如待发货、待收货、交易成功、交易关闭、交易完成）
     - 关闭时间：选择时间范围，筛选特定时间段关闭的订单
     - 创建时间：选择时间范围，筛选特定时间段创建的订单
     - 付款时间：选择时间范围，筛选特定时间段付款的订单
     - 发货时间：选择时间范围，筛选特定时间段发货的订单
     - 收货时间：选择时间范围，筛选特定时间段收货的订单

3. **执行查询**：
   - 设置好查询条件后，点击"查询"按钮执行查询
   - 系统将显示符合条件的订单列表

4. **重置查询条件**：
   - 点击"重置"按钮，清空所有已设置的查询条件

#### 6.1.3 订单列表浏览

订单列表以表格形式展示订单信息，包含以下主要字段：

- 订单编号：订单的唯一标识
- 店铺名称：订单所属的店铺名称
- 订单类型：订单的类型（如普通订单、团购订单等）
- 买家：买家的手机号码，点击可查看买家详细信息
- 留言：买家下单时的留言信息，点击可查看详细内容
- 商品：订单中的商品信息，点击可查看详细商品列表
- 订单状态：订单的当前状态（如待发货、待收货、交易成功、交易关闭、交易完成）
- 商品总价：订单中所有商品的总价
- 配送方式：订单的配送方式（如快递配送、自提等）
- 实付款：买家实际支付的金额
- 关闭类型：订单关闭的类型（如超时未付款、买家取消等）
- 关闭原因：订单关闭的具体原因
- 关闭时间：订单关闭的时间
- 创建时间：订单创建的时间
- 部分发货：是否为部分发货订单
- 操作：提供对订单的操作按钮，根据订单状态显示不同的操作选项

#### 6.1.4 批量操作

系统支持对订单记录进行批量选择操作：

1. **批量选择**：
   - 在订单列表左侧的复选框中，选择需要操作的记录
   - 可以单个选择，也可以点击表头的复选框全选当前页的所有记录
   - 页面上方会显示已选择的记录数量
   - 点击"清空"可以取消所有选择

#### 6.1.5 查看买家信息

平台管理员可以查看订单的买家信息，具体操作如下：

1. **点击买家账号**：
   - 在订单列表中找到需要查看的订单
   - 点击"买家"列中的手机号码
   - 系统弹出买家信息对话框

   ![订单买家信息查看](../images/店铺订单/订单买家信息查看.png)

   - 对话框中显示买家的基本信息、收货信息等内容

#### 6.1.6 查看商品信息

平台管理员可以查看订单中的商品信息，具体操作如下：

1. **点击商品信息**：
   - 在订单列表中找到需要查看的订单
   - 点击"商品"列中的"商品信息"链接
   - 系统弹出商品信息对话框

   ![查看订单商品信息](../images/店铺订单/查看订单商品信息.png)

   - 对话框中显示订单中所有商品的详细信息，包括商品图片、名称、规格、单价、数量等

#### 6.1.7 订单详情查看

平台管理员可以查看订单的详细信息，具体操作如下：

1. **点击详情按钮**：
   - 在订单列表中找到需要查看的订单
   - 点击操作列中的"详情"按钮
   - 系统跳转到订单详情页面

   ![查看订单详情](../images/店铺订单/查看订单详情.png)

   - 详情页面显示订单的完整信息，包括订单基本信息、买家信息、商品信息、支付信息等

#### 6.1.8 订单发货操作

对于待发货状态的订单，平台管理员需要进行发货操作，具体步骤如下：

1. **点击发货按钮**：
   - 在订单列表中找到状态为"待发货"的订单
   - 点击操作列中的"发货"按钮
   - 系统跳转到发货操作页面

2. **确认收货信息**：
   - 发货页面上方显示订单编号、创建时间、付款时间等基本信息
   - 中部显示买家账号、昵称、收货人、联系电话、收货地址等信息
   - 确认信息无误后，进行下一步操作

3. **添加包裹**：
   - 点击"添加一个包裹"按钮
   - 系统自动创建一个包裹

   ![发货-添加包裹](../images/店铺订单/发货-添加包裹.png)

4. **选择发货商品**：
   - 在下方的商品列表中，为每个商品选择对应的包裹
   - 可以将不同商品分配到不同包裹中，实现分批发货

5. **填写物流信息**：
   - 为每个包裹选择物流公司
   - 输入快递单号

6. **确认发货/退货信息**：
   - 确认发货地址信息
   - 确认退货地址信息
   - 如需修改地址，点击"修改"按钮进行更新

7. **提交发货**：
   - 确认所有信息无误后，点击"发货"按钮
   - 系统提交发货信息，订单状态变更为"待收货"

#### 6.1.9 订单取消并退款操作

对于已付款但尚未发货的订单，平台管理员可以进行取消并退款操作，具体步骤如下：

1. **点击取消并退款按钮**：
   - 在订单列表中找到状态为"待发货"的订单
   - 点击操作列中的"取消并退款"按钮
   - 系统弹出取消并退款对话框

   ![未发货-取消并退款](../images/店铺订单/未发货-取消并退款.png)

2. **填写退款信息**：
   - 选择关闭类型
   - 输入关闭原因
   - 确认退款金额

3. **提交退款**：
   - 确认信息无误后，点击"确定"按钮
   - 系统处理退款请求，订单状态变更为"交易关闭"

#### 6.1.10 其他操作

1. **查看物流信息**：
   - 在订单列表中找到状态为"待收货"或"交易成功"的订单
   - 点击操作列中的"查看物流"按钮
   - 系统弹出物流信息对话框
   - 对话框中显示物流公司、快递单号、物流轨迹等信息

2. **评价管理**：
   - 在订单列表中找到状态为"交易成功"或"交易完成"且有评价的订单
   - 点击操作列中的"查看评价"按钮查看评价内容
   - 点击操作列中的"审批"按钮进行评价审核

3. **删除订单**：
   - 在订单列表中找到状态为"交易关闭"的订单
   - 点击操作列中的"删除"按钮
   - 系统弹出确认对话框
   - 点击"确定"按钮，订单将被删除

#### 6.1.11 注意事项

1. **订单状态流转**：
   - 订单状态的正常流转顺序为：待付款 → 待发货 → 待收货 → 交易成功 → 交易完成
   - 订单可能在任何阶段被取消，变为"交易关闭"状态
   - 不同状态的订单有不同的可执行操作

2. **发货操作**：
   - 发货前应确认收货地址信息的准确性
   - 必须填写正确的物流公司和快递单号，以便买家查询物流信息
   - 对于多件商品的订单，可以选择一次性发货或分批发货
   - 发货操作不可撤销，请谨慎操作

3. **退款操作**：
   - 退款操作会将订单金额退回给买家，请确认退款金额的准确性
   - 退款后订单状态将变为"交易关闭"，不可恢复
   - 退款原因应详细说明，便于后续查询和统计

4. **核销订单处理**：
   - 对于自提（核销）订单，系统会生成核销码
   - 可以查看核销状态（未核销、已核销、已失效）
   - 点击"查看核销码"可以显示核销二维码

## 7. 财务管理

财务管理是平台管理系统中的核心功能模块，用于管理和监控平台上的所有资金流动。通过财务管理模块，平台管理人员可以查看会员资金明细、店铺结算记录、提现申请等，确保平台资金流转的准确性和透明度。

### 7.1 会员资金

会员资金是财务管理中的重要组成部分，用于管理和监控平台会员的资金情况。

#### 7.1.1 余额明细

余额明细功能是平台财务管理系统中的重要组成部分，用于查询和管理会员的账户余额及交易明细。通过该功能，平台管理人员可以查看会员的余额信息、交易明细，以及进行余额调整操作，确保会员账户资金的准确性和透明度。

![余额明细](../images/财务/会员资金/余额明细.png)

##### 页面布局

余额明细功能主要包含两个页面：

1. **余额明细列表页面**：
   - 查询区域：位于页面上方，用于设置查询条件筛选会员
   - 数据表格区域：位于页面主体部分，以表格形式展示会员基本信息和余额

2. **余额明细详情页面**：
   - 查询区域：位于弹窗上方，用于设置查询条件筛选交易记录
   - 数据表格区域：位于弹窗主体部分，以表格形式展示会员的交易明细记录

![余额明细详情](../images/财务/会员资金/余额明细详情.png)

##### 会员余额查询

余额明细列表页面提供了多种查询条件，帮助管理员快速找到目标会员：

1. **基本查询**：
   - 手机号：输入会员的手机号码进行查询
   - 会员昵称：输入会员的昵称关键字
   - 会员编号：输入会员的唯一编号进行精确查询

2. **执行查询**：
   - 设置好查询条件后，点击"查询"按钮执行查询
   - 系统将显示符合条件的会员列表

3. **重置查询条件**：
   - 点击"重置"按钮，清空所有已设置的查询条件

##### 会员余额信息浏览

余额明细列表以表格形式展示会员基本信息和余额，包含以下主要字段：

- 会员编号：会员的唯一标识
- 头像：会员的个人头像图片
- 手机号：会员注册的手机号码
- 会员昵称：会员的昵称
- 余额：会员账户当前可用余额
- 操作：提供"余额明细"按钮，用于查看详细交易记录

##### 查看余额明细详情

1. 在余额明细列表中找到需要查看的会员
2. 点击操作列中的"余额明细"按钮
3. 系统弹出余额明细详情弹窗，显示该会员的交易记录

**余额明细详情查询**：

余额明细详情弹窗提供了多种查询条件，帮助管理员筛选交易记录：

1. **查询条件**：
   - 流水号：输入交易流水号进行精确查询
   - 交易单号：输入交易单号进行精确查询
   - 交易类型：从下拉菜单中选择交易类型（如充值、消费、提现等）
   - 收入支出：从下拉菜单中选择收入或支出
   - 交易时间：选择时间范围，筛选特定时间段的交易记录

2. **执行查询**：
   - 设置好查询条件后，点击"查询"按钮执行查询
   - 系统将显示符合条件的交易记录

**余额明细详情浏览**：

余额明细详情表格展示会员的交易记录，包含以下主要字段：

- 流水号：交易的唯一标识
- 交易类型：显示交易的类型（如充值、消费、提现等）
- 收入/支出：标识该笔交易是收入还是支出
- 交易金额：本次交易的金额
- 可用余额：交易后的账户余额
- 交易时间：交易发生的时间
- 交易单号：关联的订单或业务单号
- 备注：交易的说明信息

##### 余额调整操作

平台管理人员可以对会员的余额进行调整，包括增加余额（系统补发）和减少余额（系统扣减）。

> 注意：根据当前代码分析，虽然系统中存在余额调整功能，但在界面上"调整"按钮被注释掉了，可能暂时不对外开放此功能。以下是该功能的操作流程，仅供参考。

**调整会员余额**：

1. 在余额明细列表中找到需要调整余额的会员
2. 点击操作列中的"调整"按钮（如果启用）
3. 系统弹出余额调整弹窗，显示会员基本信息
4. 在弹窗中填写以下信息：
   - 交易类型：选择"系统补发余额"或"系统扣减余额"
   - 交易额：输入需要调整的金额（精确到小数点后两位）
5. 点击"确定"按钮提交调整

**余额调整规则**：

1. **系统补发余额**：
   - 选择交易类型为"系统补发余额"
   - 系统自动将交易方向设置为"收入"
   - 输入的金额将增加到会员账户余额中

2. **系统扣减余额**：
   - 选择交易类型为"系统扣减余额"
   - 系统自动将交易方向设置为"支出"
   - 输入的金额将从会员账户余额中扣除
   - 扣减金额不能超过会员当前可用余额

##### 注意事项

1. **数据安全**：
   - 余额信息涉及用户资金，请遵守相关法律法规，保护用户信息安全
   - 不要将余额信息泄露给无关人员

2. **操作谨慎**：
   - 余额调整直接影响会员的账户资金，请谨慎操作
   - 每次调整前，确认会员身份和调整金额的准确性
   - 系统会记录所有余额调整操作，包括操作人和操作时间

3. **交易记录**：
   - 交易记录是重要的财务数据，不可删除或修改
   - 如发现交易记录有误，应通过新的调整交易进行纠正

#### 7.1.2 资金明细

会员资金明细功能是平台财务管理系统中的重要组成部分，用于查询和管理平台会员的所有资金交易记录。通过该功能，平台管理人员可以全面了解会员的资金流水情况，包括充值、消费、提现、退款等各类交易记录，便于财务核对和问题排查。

![![会员资金明细](images/](../images/财务/会员资金/资金明细.png)

##### 页面布局

会员资金明细页面主要分为两个区域：
- 查询区域：位于页面上方，用于设置查询条件筛选资金交易记录
- 数据表格区域：位于页面主体部分，以表格形式展示会员的资金交易记录

##### 资金明细查询

会员资金明细页面提供了多种查询条件，帮助管理员快速找到目标交易记录：

1. **基本查询**：
   - 流水号：输入交易流水号进行精确查询
   - 交易单号：输入交易单号进行精确查询
   - 会员账号：输入会员的手机号码进行查询
   - 会员昵称：输入会员的昵称关键字进行查询

2. **展开更多查询条件**：
   - 点击查询区域右下方的"展开"按钮，可以显示更多查询条件
   - 展开后可使用的额外条件包括：
     - 交易类型：从下拉菜单中选择交易类型（如充值、消费、提现、退款等）
     - 交易状态：从下拉菜单中选择交易状态（如未支付、进行中、交易完成、已退款等）
     - 收入支出：从下拉菜单中选择收入或支出
     - 交易时间：选择时间范围，筛选特定时间段的交易记录

3. **执行查询**：
   - 设置好查询条件后，点击"查询"按钮执行查询
   - 系统将显示符合条件的交易记录

4. **重置查询条件**：
   - 点击"重置"按钮，清空所有已设置的查询条件

##### 资金明细浏览

资金明细表格展示会员的交易记录，包含以下主要字段：

- 序号：表格中的序号，从1开始递增
- 流水号：交易的唯一标识
- 交易单号：关联的订单或业务单号
- 会员账号：会员的手机号码
- 会员昵称：会员的昵称
- 交易类型：显示交易的类型（如充值、消费、提现、退款等）
- 收入/支出：标识该笔交易是收入还是支出
- 交易金额：本次交易的金额
- 交易状态：显示交易的当前状态（如未支付、进行中、交易完成、已退款等）
- 交易时间：交易发生的时间
- 备注：交易的说明信息

##### 批量操作

系统支持对交易记录进行批量选择操作：

1. **批量选择**：
   - 在交易记录列表左侧的复选框中，选择需要操作的记录
   - 可以单个选择，也可以点击表头的复选框全选当前页的所有记录
   - 页面上方会显示已选择的记录数量
   - 点击"清空"可以取消所有选择

##### 注意事项

1. **数据安全**：
   - 资金明细涉及用户资金信息，请遵守相关法律法规，保护用户信息安全
   - 不要将资金交易信息泄露给无关人员

2. **查询优化**：
   - 使用精确的查询条件可以提高查询效率
   - 对于大数据量查询，建议使用流水号或交易单号等唯一标识进行查询
   - 使用交易时间范围可以有效缩小查询范围

3. **数据解读**：
   - 交易类型和交易状态使用了系统预设的数据字典，请正确理解各状态的含义
   - 收入/支出是从会员角度定义的，"收入"表示会员账户资金增加，"支出"表示会员账户资金减少

4. **功能限制**：
   - 当前页面主要用于查询和浏览资金交易记录，不支持直接修改交易记录
   - 交易记录是系统根据会员操作自动生成的，通常不需要手动干预

#### 7.1.3 提现审批

提现审批功能是平台财务管理系统中的重要组成部分，用于管理和处理会员的提现申请。通过该功能，平台管理人员可以查看会员的提现申请记录，进行审核和打款操作，确保会员提现流程的顺利进行和资金安全。

![![提现审批](images/](../images/财务/会员资金/提现审批.png)

##### 页面布局

提现审批页面主要分为两个区域：
- 查询区域：位于页面上方，用于设置查询条件筛选提现记录
- 数据表格区域：位于页面主体部分，以表格形式展示会员的提现申请记录

##### 提现记录查询

提现审批页面提供了多种查询条件，帮助管理员快速找到目标提现记录：

1. **基本查询**：
   - 流水号：输入提现流水号进行精确查询
   - 会员账号：输入会员的手机号码进行查询
   - 会员昵称：输入会员的昵称关键字进行查询

2. **展开更多查询条件**：
   - 点击查询区域右下方的"展开"按钮，可以显示更多查询条件
   - 展开后可使用的额外条件包括：
     - 手机号：输入会员的手机号码进行查询
     - 提现方式：从下拉菜单中选择提现方式（微信、支付宝、银行卡）
     - 提现账号：输入提现账号进行查询
     - 收款人：输入收款人姓名进行查询
     - 身份证号：输入身份证号码进行查询
     - 申请时间：选择时间范围，筛选特定时间段的提现申请
     - 状态：从下拉菜单中选择提现状态（待审核、待打款、已付款、无效）
     - 审核时间：选择时间范围，筛选特定时间段审核的提现记录
     - 打款时间：选择时间范围，筛选特定时间段打款的提现记录

3. **执行查询**：
   - 设置好查询条件后，点击"查询"按钮执行查询
   - 系统将显示符合条件的提现记录

4. **重置查询条件**：
   - 点击"重置"按钮，清空所有已设置的查询条件

##### 提现记录浏览

提现记录表格展示会员的提现申请记录，包含以下主要字段：

- 序号：表格中的序号，从1开始递增
- 流水号：提现申请的唯一标识
- 会员账号：会员的手机号码
- 会员昵称：会员的昵称
- 手机号：会员的联系电话
- 提现金额：会员申请提现的金额
- 提现方式：显示提现的方式（微信钱包、支付宝、银行卡）
- 提现账号：提现到的账号信息
- 开户行分支行：银行卡提现时的开户行分支机构
- 收款人：提现账户的持有人姓名
- 身份证号：持卡人的身份证号码
- 手续费：提现产生的手续费
- 实际金额：扣除手续费后实际到账的金额
- 申请时间：会员提交提现申请的时间
- 状态：提现申请的当前状态（待审核、待打款、已打款、无效）
- 审核时间：平台管理员审核提现申请的时间
- 打款时间：平台管理员执行打款操作的时间
- 不通过原因：审核不通过时的原因说明
- 备注：提现申请的备注信息
- 操作：提供对提现申请的操作按钮

##### 批量操作

系统支持对提现记录进行批量选择操作：

1. **批量选择**：
   - 在提现记录列表左侧的复选框中，选择需要操作的记录
   - 可以单个选择，也可以点击表头的复选框全选当前页的所有记录
   - 页面上方会显示已选择的记录数量
   - 点击"清空"可以取消所有选择

##### 提现审核操作

平台管理员需要对会员的提现申请进行审核，确认提现信息的准确性和合法性：

1. **审核提现申请**：
   - 在提现记录列表中找到状态为"待审核"的记录
   - 点击操作列中的"审核"按钮
   - 系统弹出审核对话框
   - 选择审核结果：
     - 通过：选择"通过"选项，提现申请状态将变更为"待打款"
     - 不通过：选择"不通过"选项，并在文本框中输入不通过原因，提现申请状态将变更为"无效"
   - 点击"确认"按钮提交审核结果

##### 提现打款操作

对于审核通过的提现申请，平台管理员需要执行打款操作：

1. **执行打款操作**：
   - 在提现记录列表中找到状态为"待打款"的记录
   - 点击操作列中的"打款"按钮
   - 系统弹出打款确认对话框
   - 选择打款状态：
     - 待打款：保持当前状态不变
     - 已打款：选择"已打款"选项，并可在文本框中输入打款备注，提现申请状态将变更为"已打款"
   - 点击"确认"按钮提交打款结果

##### 注意事项

1. **资金安全**：
   - 提现涉及实际资金操作，请谨慎处理每一笔提现申请
   - 审核时需要核对提现账户信息的准确性，确保资金安全

2. **审核标准**：
   - 审核提现申请时，需要确认会员账户余额是否足够
   - 检查提现账户信息是否完整、准确
   - 对于异常提现申请（如短时间内多次大额提现），应进行额外核实

3. **打款操作**：
   - 打款操作应在确认资金已实际转出后才标记为"已打款"
   - 打款时应核对收款账户信息，确保打款准确无误
   - 建议保留打款凭证，以便日后核对

4. **状态流转**：
   - 提现申请状态的正常流转顺序为：待审核 → 待打款 → 已打款
   - 审核不通过的提现申请状态为"无效"，系统会自动将冻结的资金返还到会员账户

### 7.2 店铺资金

店铺资金是财务管理中的重要组成部分，用于管理和监控平台店铺的资金情况。

#### 7.2.1 资金明细

店铺资金明细功能是平台财务管理系统中的重要组成部分，用于查询和管理平台店铺的所有资金交易记录。通过该功能，平台管理人员可以全面了解店铺的资金流水情况，包括订单交易、提现、退款等各类交易记录，便于财务核对和问题排查。

![![店铺资金明细](images/](../images/财务/店铺资金/资金明细.png)

##### 页面布局

店铺资金明细页面主要分为两个区域：
- 查询区域：位于页面上方，用于设置查询条件筛选资金交易记录
- 数据表格区域：位于页面主体部分，以表格形式展示店铺的资金交易记录

##### 资金明细查询

店铺资金明细页面提供了多种查询条件，帮助管理员快速找到目标交易记录：

1. **基本查询**：
   - 流水号：输入交易流水号进行精确查询
   - 店铺账号：输入店铺的登录账号进行查询
   - 店铺名称：输入店铺的名称关键字进行查询

2. **展开更多查询条件**：
   - 点击查询区域右下方的"展开"按钮，可以显示更多查询条件
   - 展开后可使用的额外条件包括：
     - 交易类型：从下拉菜单中选择交易类型（如订单交易、提现、退款等）
     - 交易状态：从下拉菜单中选择交易状态（如未支付、进行中、交易完成、已退款等）
     - 收入支出：从下拉菜单中选择收入或支出
     - 交易时间：选择时间范围，筛选特定时间段的交易记录

3. **执行查询**：
   - 设置好查询条件后，点击"查询"按钮执行查询
   - 系统将显示符合条件的交易记录

4. **重置查询条件**：
   - 点击"重置"按钮，清空所有已设置的查询条件

##### 资金明细浏览

资金明细表格展示店铺的交易记录，包含以下主要字段：

- 序号：表格中的序号，从1开始递增
- 流水号：交易的唯一标识
- 店铺账号：店铺的登录账号
- 店铺名称：店铺的名称
- 交易类型：显示交易的类型（如订单交易、提现、退款等）
- 收入/支出：标识该笔交易是收入还是支出
- 交易金额：本次交易的金额
- 交易状态：显示交易的当前状态（如未支付、进行中、交易完成、已退款等）
- 交易时间：交易发生的时间
- 备注：交易的说明信息

##### 批量操作

系统支持对交易记录进行批量选择操作：

1. **批量选择**：
   - 在交易记录列表左侧的复选框中，选择需要操作的记录
   - 可以单个选择，也可以点击表头的复选框全选当前页的所有记录
   - 页面上方会显示已选择的记录数量
   - 点击"清空"可以取消所有选择

##### 注意事项

1. **数据安全**：
   - 资金明细涉及店铺资金信息，请遵守相关法律法规，保护商家信息安全
   - 不要将资金交易信息泄露给无关人员

2. **查询优化**：
   - 使用精确的查询条件可以提高查询效率
   - 对于大数据量查询，建议使用流水号或店铺账号等唯一标识进行查询
   - 使用交易时间范围可以有效缩小查询范围

3. **数据解读**：
   - 交易类型和交易状态使用了系统预设的数据字典，请正确理解各状态的含义
   - 收入/支出是从店铺角度定义的，"收入"表示店铺账户资金增加，"支出"表示店铺账户资金减少

4. **功能限制**：
   - 当前页面主要用于查询和浏览资金交易记录，不支持直接修改交易记录
   - 交易记录是系统根据店铺操作自动生成的，通常不需要手动干预

#### 7.2.2 提现审批

店铺提现审批功能是平台财务管理系统中的重要组成部分，用于管理和处理店铺的提现申请。通过该功能，平台管理人员可以查看店铺的提现申请记录，进行审核和打款操作，确保店铺提现流程的顺利进行和资金安全。

![![店铺提现审批](images/](../images/财务/店铺资金/提现审批.png)

##### 页面布局

店铺提现审批页面主要分为两个区域：
- 查询区域：位于页面上方，用于设置查询条件筛选提现记录
- 数据表格区域：位于页面主体部分，以表格形式展示店铺的提现申请记录

##### 提现记录查询

店铺提现审批页面提供了多种查询条件，帮助管理员快速找到目标提现记录：

1. **基本查询**：
   - 流水号：输入提现流水号进行精确查询
   - 店铺账号：输入店铺的登录账号进行查询
   - 店铺名称：输入店铺的名称关键字进行查询

2. **展开更多查询条件**：
   - 点击查询区域右下方的"展开"按钮，可以显示更多查询条件
   - 展开后可使用的额外条件包括：
     - 手机号：输入店铺联系人的手机号码进行查询
     - 提现方式：从下拉菜单中选择提现方式（微信、支付宝、银行卡）
     - 提现账号：输入提现账号进行查询
     - 收款人：输入收款人姓名进行查询
     - 申请时间：选择时间范围，筛选特定时间段的提现申请
     - 状态：从下拉菜单中选择提现状态（待审核、待打款、已付款、无效）
     - 审核时间：选择时间范围，筛选特定时间段审核的提现记录
     - 打款时间：选择时间范围，筛选特定时间段打款的提现记录

3. **执行查询**：
   - 设置好查询条件后，点击"查询"按钮执行查询
   - 系统将显示符合条件的提现记录

4. **重置查询条件**：
   - 点击"重置"按钮，清空所有已设置的查询条件

##### 提现记录浏览

提现记录表格展示店铺的提现申请记录，包含以下主要字段：

- 序号：表格中的序号，从1开始递增
- 流水号：提现申请的唯一标识
- 店铺账号：店铺的登录账号
- 店铺名称：店铺的名称
- 手机号：店铺联系人的手机号码
- 提现金额：店铺申请提现的金额
- 提现方式：显示提现的方式（微信、支付宝、银行卡）
- 提现账号：提现到的账号信息
- 开户行分支行：银行卡提现时的开户行分支机构
- 收款人：提现账户的持有人姓名
- 手续费：提现产生的手续费
- 实际金额：扣除手续费后实际到账的金额
- 申请时间：店铺提交提现申请的时间
- 状态：提现申请的当前状态（待审核、待打款、已打款、无效）
- 审核时间：平台管理员审核提现申请的时间
- 打款时间：平台管理员执行打款操作的时间
- 不通过原因：审核不通过时的原因说明
- 备注：提现申请的备注信息
- 操作：提供对提现申请的操作按钮

##### 批量操作

系统支持对提现记录进行批量选择操作：

1. **批量选择**：
   - 在提现记录列表左侧的复选框中，选择需要操作的记录
   - 可以单个选择，也可以点击表头的复选框全选当前页的所有记录
   - 页面上方会显示已选择的记录数量
   - 点击"清空"可以取消所有选择

##### 提现审核操作

平台管理员需要对店铺的提现申请进行审核，确认提现信息的准确性和合法性：

1. **审核提现申请**：
   - 在提现记录列表中找到状态为"待审核"的记录
   - 点击操作列中的"审核"按钮
   - 系统弹出审核对话框

   ![![提现审批不通过](images/](../images/财务/店铺资金/提现审批不通过.png)

   - 选择审核结果：
     - 通过：选择"通过"选项，提现申请状态将变更为"待打款"
     - 不通过：选择"不通过"选项，并在文本框中输入不通过原因，提现申请状态将变更为"无效"
   - 点击"确认"按钮提交审核结果

##### 提现打款操作

对于审核通过的提现申请，平台管理员需要执行打款操作：

1. **执行打款操作**：
   - 在提现记录列表中找到状态为"待打款"的记录
   - 点击操作列中的"打款"按钮
   - 系统弹出打款确认对话框

   ![![打款确认](images/](../images/财务/店铺资金/打款确认.png)

   - 确认实际金额和线下转账金额
   - 在文本框中输入打款备注（如转账凭证号码、转账时间等信息）
   - 点击"确认"按钮提交打款结果，提现申请状态将变更为"已打款"

##### 注意事项

1. **资金安全**：
   - 提现涉及实际资金操作，请谨慎处理每一笔提现申请
   - 审核时需要核对提现账户信息的准确性，确保资金安全

2. **审核标准**：
   - 审核提现申请时，需要确认店铺账户余额是否足够
   - 检查提现账户信息是否完整、准确
   - 对于异常提现申请（如短时间内多次大额提现），应进行额外核实
   - 确认提现账户的持有人信息与店铺注册信息是否一致

3. **打款操作**：
   - 打款操作应在确认资金已实际转出后才进行确认
   - 打款时应核对收款账户信息，确保打款准确无误
   - 建议保留打款凭证，并在备注中记录相关信息，以便日后核对
   - 系统会自动记录打款操作的时间

4. **状态流转**：
   - 提现申请状态的正常流转顺序为：待审核 → 待打款 → 已打款
   - 审核不通过的提现申请状态为"无效"，系统会自动将冻结的资金返还到店铺账户



## 8. 营销管理

营销管理是平台管理系统中的重要功能模块，用于管理和维护平台的各种营销活动和推广工具。通过营销管理模块，平台管理人员可以设置广告轮播图、管理优惠券、配置促销活动等，提升平台的用户体验和商品曝光度，促进销售转化。

### 8.1 广告轮播图设置

广告轮播图设置是平台营销管理系统中的重要组成部分，用于管理和维护平台各个页面的广告轮播图。通过该功能，平台管理人员可以新增、编辑、启用/停用广告轮播图，设置广告的展示位置、展示时间、跳转链接等，实现对平台广告资源的精细化管理。

![广告轮播配置](../images/广告轮播配置.png)

#### 8.1.1 页面布局

广告轮播图设置页面主要分为三个区域：
- 查询区域：位于页面上方，用于设置查询条件筛选广告轮播图
- 操作按钮区域：位于查询区域下方，提供新增、批量操作等功能按钮
- 数据表格区域：位于页面主体部分，以表格形式展示广告轮播图信息

#### 8.1.2 广告轮播图查询

广告轮播图设置页面提供了多种查询条件，帮助管理员快速找到目标广告：

1. **基本查询**：
   - 广告标题：输入广告标题关键字进行查询
   - 广告类型：从下拉菜单中选择广告类型（平台广告、店铺广告）
   - 广告位置：从下拉菜单中选择广告位置（首页、分类页等）
   - 跳转类型：从下拉菜单中选择跳转类型（无、商品详情等）
   - 状态：从下拉菜单中选择广告状态（启用、停用）

2. **时间查询**：
   - 开始时间：选择广告展示开始时间范围
   - 结束时间：选择广告展示结束时间范围
   - 创建时间：选择广告创建时间范围

3. **执行查询**：
   - 设置好查询条件后，点击"查询"按钮执行查询
   - 系统将显示符合条件的广告轮播图列表

4. **重置查询条件**：
   - 点击"重置"按钮，清空所有已设置的查询条件

#### 8.1.3 广告轮播图浏览

广告轮播图列表以表格形式展示广告信息，包含以下主要字段：

- 广告标题：广告的标题名称
- 广告类型：广告的类型（平台广告、店铺广告）
- 广告位置：广告展示的位置（首页、分类页等）
- 图片：广告的图片预览
- 背景色：广告的背景颜色（主要用于首页轮播图）
- 跳转类型：点击广告后的跳转类型（无、商品详情等）
- 地址：跳转的目标地址（商品名称、详情图等）
- 排序：广告的展示顺序（数值越小排序越靠前）
- 开始时间：广告展示的开始时间
- 结束时间：广告展示的结束时间
- 状态：广告的当前状态（启用、停用）
- 创建者：创建该广告的操作人
- 创建时间：广告创建的时间
- 更新者：最后更新该广告的操作人
- 更新时间：广告最后更新的时间
- 操作：提供对广告的操作按钮（编辑、启用/停用、删除）

#### 8.1.4 新增广告轮播图

平台管理员可以添加新的广告轮播图，具体操作如下：

1. **点击新增按钮**：
   - 在操作按钮区域点击"新增"按钮
   - 系统弹出新增广告轮播图对话框

   ![新增编辑广告轮播](../images/新增编辑广告轮播.png)

2. **填写广告基本信息**：
   - 广告标题：输入广告的标题名称（必填）
   - 广告类型：选择广告类型（平台广告或店铺广告）（必填）
   - 店铺：如选择店铺广告，需选择关联的店铺（必填）
   - 广告位置：选择广告展示的位置（必填）
   - 图片：上传广告图片，支持jpg、jpeg、png格式，大小不超过5M（必填）
   - 轮播图背景色：如选择首页位置，可设置轮播图背景色（可选）

3. **设置跳转信息**：
   - 跳转类型：选择点击广告后的跳转类型（无、商品详情等）（必填）
   - 平台/店铺商品：如选择商品详情跳转类型，需选择关联的商品（必填）

4. **设置展示信息**：
   - 排序：设置广告的展示顺序，数值越小排序越靠前（必填）
   - 开始时间：设置广告展示的开始时间（必填）
   - 结束时间：设置广告展示的结束时间（必填）
   - 状态：设置广告的初始状态（启用或停用）（必填）

5. **保存广告信息**：
   - 填写完成后点击"确定"按钮
   - 系统验证信息无误后，保存广告轮播图信息
   - 新增的广告将显示在广告轮播图列表中

#### 8.1.5 编辑广告轮播图

平台管理员可以编辑已有的广告轮播图，具体操作如下：

1. **点击编辑按钮**：
   - 在广告列表中找到需要编辑的广告
   - 点击操作列中的"编辑"按钮
   - 系统弹出编辑广告轮播图对话框，显示当前广告信息

2. **修改广告信息**：
   - 可以修改广告的标题、类型、位置、图片、跳转类型等信息
   - 对于已上传的图片，可以查看预览，也可以重新上传替换

3. **保存修改**：
   - 修改完成后点击"确定"按钮
   - 系统验证信息无误后，更新广告轮播图信息
   - 更新后的广告信息将显示在广告轮播图列表中

#### 8.1.6 启用/停用广告轮播图

平台管理员可以控制广告轮播图的启用状态，具体操作如下：

1. **点击启用/停用按钮**：
   - 在广告列表中找到需要操作的广告
   - 点击操作列中的"启用"或"停用"按钮（根据当前状态显示相反操作）
   - 系统立即更新广告的状态

2. **状态说明**：
   - 启用状态：广告在指定时间范围内会在前端展示
   - 停用状态：广告不会在前端展示，即使在有效时间范围内

#### 8.1.7 删除广告轮播图

平台管理员可以删除不需要的广告轮播图，具体操作如下：

1. **单个删除**：
   - 在广告列表中找到需要删除的广告
   - 点击操作列中的"删除"按钮
   - 系统弹出确认对话框
   - 点击"确定"按钮，系统删除该广告

2. **批量删除**：
   - 在广告列表中选择多个需要删除的广告（勾选复选框）
   - 点击"批量操作"下拉菜单
   - 选择"删除"选项
   - 系统弹出确认对话框
   - 点击"确定"按钮，系统批量删除所选广告

#### 8.1.8 批量操作

系统支持对广告轮播图进行批量选择操作：

1. **批量选择**：
   - 在广告列表左侧的复选框中，选择需要操作的广告
   - 可以单个选择，也可以点击表头的复选框全选当前页的所有广告
   - 页面上方会显示已选择的广告数量
   - 点击"清空"可以取消所有选择

2. **批量删除**：
   - 选择多个广告后，点击"批量操作"按钮
   - 在下拉菜单中选择"删除"选项
   - 系统弹出确认对话框
   - 点击"确定"按钮，系统批量删除所选广告

#### 8.1.9 注意事项

1. **图片规格**：
   - 广告图片建议使用338*100像素的规格，以确保最佳显示效果
   - 支持jpg、jpeg、png格式，大小不超过5M
   - 图片应清晰、美观，符合平台整体风格

2. **时间设置**：
   - 广告的开始时间和结束时间决定了广告的展示周期
   - 超出展示时间范围的广告不会在前端显示，即使状态为"启用"
   - 建议合理规划广告的展示时间，避免过多广告同时展示造成用户体验下降

3. **排序规则**：
   - 排序值越小，广告在同一位置的展示顺序越靠前
   - 建议为不同广告设置不同的排序值，便于管理和调整
   - 对于重要广告，可以设置较小的排序值确保优先展示

4. **广告类型选择**：
   - 平台广告：由平台统一管理，展示在平台公共区域
   - 店铺广告：关联特定店铺，通常展示在店铺相关页面
   - 选择广告类型后，相关的配置选项会相应变化，请根据实际需求选择

5. **跳转类型设置**：
   - 无：点击广告不跳转到任何页面
   - 商品详情：点击广告跳转到指定商品的详情页面
   - 选择跳转类型后，需要配置相应的跳转目标（如选择商品）