# 《八闽助业集市》商城平台管理人员操作手册 - 财务/会员资金/提现审批

## 1. 功能概述

提现审批功能是平台财务管理系统中的重要组成部分，用于管理和处理会员的提现申请。通过该功能，平台管理人员可以查看会员的提现申请记录，进行审核和打款操作，确保会员提现流程的顺利进行和资金安全。

![提现审批](../images/财务/会员资金/提现审批.png)

## 2. 页面布局

提现审批页面主要分为两个区域：
- 查询区域：位于页面上方，用于设置查询条件筛选提现记录
- 数据表格区域：位于页面主体部分，以表格形式展示会员的提现申请记录

## 3. 操作指南

### 3.1 提现记录查询

提现审批页面提供了多种查询条件，帮助管理员快速找到目标提现记录：

1. **基本查询**：
   - 流水号：输入提现流水号进行精确查询
   - 会员账号：输入会员的手机号码进行查询
   - 会员昵称：输入会员的昵称关键字进行查询

2. **展开更多查询条件**：
   - 点击查询区域右下方的"展开"按钮，可以显示更多查询条件
   - 展开后可使用的额外条件包括：
     - 手机号：输入会员的手机号码进行查询
     - 提现方式：从下拉菜单中选择提现方式（微信、支付宝、银行卡）
     - 提现账号：输入提现账号进行查询
     - 收款人：输入收款人姓名进行查询
     - 身份证号：输入身份证号码进行查询
     - 申请时间：选择时间范围，筛选特定时间段的提现申请
     - 状态：从下拉菜单中选择提现状态（待审核、待打款、已付款、无效）
     - 审核时间：选择时间范围，筛选特定时间段审核的提现记录
     - 打款时间：选择时间范围，筛选特定时间段打款的提现记录

3. **执行查询**：
   - 设置好查询条件后，点击"查询"按钮执行查询
   - 系统将显示符合条件的提现记录

4. **重置查询条件**：
   - 点击"重置"按钮，清空所有已设置的查询条件

### 3.2 提现记录浏览

提现记录表格展示会员的提现申请记录，包含以下主要字段：

- 序号：表格中的序号，从1开始递增
- 流水号：提现申请的唯一标识
- 会员账号：会员的手机号码
- 会员昵称：会员的昵称
- 手机号：会员的联系电话
- 提现金额：会员申请提现的金额
- 提现方式：显示提现的方式（微信钱包、支付宝、银行卡）
- 提现账号：提现到的账号信息
- 开户行分支行：银行卡提现时的开户行分支机构
- 收款人：提现账户的持有人姓名
- 身份证号：持卡人的身份证号码
- 手续费：提现产生的手续费
- 实际金额：扣除手续费后实际到账的金额
- 申请时间：会员提交提现申请的时间
- 状态：提现申请的当前状态（待审核、待打款、已打款、无效）
- 审核时间：平台管理员审核提现申请的时间
- 打款时间：平台管理员执行打款操作的时间
- 不通过原因：审核不通过时的原因说明
- 备注：提现申请的备注信息
- 操作：提供对提现申请的操作按钮

### 3.3 批量操作

系统支持对提现记录进行批量选择操作：

1. **批量选择**：
   - 在提现记录列表左侧的复选框中，选择需要操作的记录
   - 可以单个选择，也可以点击表头的复选框全选当前页的所有记录
   - 页面上方会显示已选择的记录数量
   - 点击"清空"可以取消所有选择

### 3.4 提现审核操作

平台管理员需要对会员的提现申请进行审核，确认提现信息的准确性和合法性：

1. **审核提现申请**：
   - 在提现记录列表中找到状态为"待审核"的记录
   - 点击操作列中的"审核"按钮
   - 系统弹出审核对话框
   - 选择审核结果：
     - 通过：选择"通过"选项，提现申请状态将变更为"待打款"
     - 不通过：选择"不通过"选项，并在文本框中输入不通过原因，提现申请状态将变更为"无效"
   - 点击"确认"按钮提交审核结果

### 3.5 提现打款操作

对于审核通过的提现申请，平台管理员需要执行打款操作：

1. **执行打款操作**：
   - 在提现记录列表中找到状态为"待打款"的记录
   - 点击操作列中的"打款"按钮
   - 系统弹出打款确认对话框
   - 选择打款状态：
     - 待打款：保持当前状态不变
     - 已打款：选择"已打款"选项，并可在文本框中输入打款备注，提现申请状态将变更为"已打款"
   - 点击"确认"按钮提交打款结果

## 4. 注意事项

1. **资金安全**：
   - 提现涉及实际资金操作，请谨慎处理每一笔提现申请
   - 审核时需要核对提现账户信息的准确性，确保资金安全

2. **审核标准**：
   - 审核提现申请时，需要确认会员账户余额是否足够
   - 检查提现账户信息是否完整、准确
   - 对于异常提现申请（如短时间内多次大额提现），应进行额外核实

3. **打款操作**：
   - 打款操作应在确认资金已实际转出后才标记为"已打款"
   - 打款时应核对收款账户信息，确保打款准确无误
   - 建议保留打款凭证，以便日后核对

4. **状态流转**：
   - 提现申请状态的正常流转顺序为：待审核 → 待打款 → 已打款
   - 审核不通过的提现申请状态为"无效"，系统会自动将冻结的资金返还到会员账户

## 5. 常见问题

### 5.1 提现申请积压

**问题**：系统中存在大量待审核或待打款的提现申请。

**解决方案**：
- 设置合理的查询条件（如按申请时间排序），优先处理较早的提现申请
- 适当增加处理提现申请的人员
- 考虑设置自动审核规则，对满足特定条件的小额提现申请自动审核通过

### 5.2 提现信息有误

**问题**：审核过程中发现提现账户信息有误。

**解决方案**：
- 选择"不通过"，并在不通过原因中说明具体问题
- 通过客服联系会员，指导其修正提现账户信息后重新申请

### 5.3 打款失败

**问题**：执行打款操作后，银行或第三方支付平台返回打款失败。

**解决方案**：
- 核实失败原因（如账户信息错误、账户冻结等）
- 如果是临时性问题，可以稍后重试
- 如果是账户信息问题，将提现申请标记为"无效"，并联系会员重新提交正确的账户信息
