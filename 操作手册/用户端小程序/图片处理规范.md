# 八闽助业集市用户操作手册图片处理规范

## 一、概述

本规范旨在解决八闽助业集市用户操作手册中手机截图分辨率高、体积大导致的文档排版问题，确保最终文档中的图片既清晰又不影响整体排版。通过统一的图片处理标准，提高操作手册的专业性和可读性。

## 二、图片压缩衡量标准

### 1. 文件大小标准

不同类型截图的理想文件大小范围严格控制如下：

| 图片类型 | 理想大小范围 | 最大限制 | 适用场景 |
|---------|------------|---------|---------|
| 全幅界面截图 | 100-200KB | 250KB | 完整页面展示，如首页、列表页等 |
| 功能区域截图 | 50-100KB | 150KB | 特定功能区域，如表单、操作按钮组等 |
| 细节截图 | 20-50KB | 80KB | UI元素细节，如单个按钮、图标等 |
| 流程示意图 | 150-300KB | 350KB | 多步骤操作流程的组合图 |

**注意**：任何超过最大限制的图片必须进一步压缩或裁剪，不得例外。

### 2. 分辨率和尺寸标准

图片分辨率和尺寸应严格遵循以下标准：

| 图片类型 | 理想宽度(像素) | 最大高度(像素) | DPI设置 |
|---------|--------------|--------------|---------|
| 全幅界面截图 | 600-750 | 1200 | 72-96 |
| 功能区域截图 | 400-600 | 800 | 72-96 |
| 细节截图 | 200-350 | 400 | 72-96 |
| 流程示意图 | 800-1000 | 1500 | 72-96 |

**关键要求**：
- 严格保持原始截图的宽高比，避免图片变形
- 手机竖屏截图宽度统一设置为600像素
- 平板横屏截图宽度统一设置为750像素
- 对于超长截图，可适当裁剪或分割为多张图片

### 3. 清晰度判断标准

图片压缩后必须满足以下清晰度标准：

| 评估项目 | 合格标准 | 不合格标准 |
|---------|---------|-----------|
| 文字可读性 | 所有UI文字清晰可辨，无明显模糊 | 文字出现锯齿、模糊或像素化 |
| UI元素边缘 | 按钮、图标边缘清晰，线条完整 | 边缘模糊、线条断裂或变形 |
| 颜色还原度 | 颜色过渡自然，无明显色块 | 出现明显色带、马赛克或失真 |
| 细节保留 | 小图标、符号细节保留完整 | 小元素模糊不清或完全丢失 |

**测试方法**：
- 在100%缩放比例下检查图片
- 确保12号以上字体完全清晰可读
- 确保所有交互元素（按钮、开关等）的状态可明确辨认

### 4. 压缩前后质量对比方法

每批图片处理时，应执行以下质量对比流程：

1. **抽样检查**：
   - 每10张图片中随机抽取1张进行详细对比
   - 必须保留原始截图作为对比基准

2. **对比项目**：
   - 文件大小减少百分比（目标：减少60-80%）
   - 关键区域1:1像素对比（放大200%检查）
   - 文字清晰度对比（特别是小字体）

3. **验收标准**：
   - 文件大小必须符合第1节规定的范围
   - 图片尺寸必须符合第2节规定的标准
   - 清晰度必须通过第3节的所有评估项目
   - 压缩后的图片在Markdown预览中显示正常，不破坏排版

4. **记录表格**：
   为每批处理的图片创建质量对比记录，格式如下：

   | 图片名称 | 原始大小 | 处理后大小 | 尺寸变化 | 清晰度评分(1-5) | 是否通过 |
   |---------|---------|-----------|---------|---------------|---------|
   | 图片1.png | 1.2MB | 180KB | 750×1600→600×1280 | 4.5 | 通过 |

## 三、推荐图片处理工具

### 1. 桌面端工具

1. **Adobe Photoshop**
   - 适用场景：需要精细调整和专业处理的图片
   - 推荐设置："存储为Web所用格式"，JPEG质量70-80%

2. **GIMP (免费开源)**
   - 适用场景：需要专业处理但预算有限
   - 推荐设置：导出为PNG-8或JPEG，质量75%

3. **XnConvert (免费)**
   - 适用场景：批量调整大量截图
   - 推荐设置：批处理模式，调整大小+优化压缩

4. **ImageOptim (Mac) / FileOptimizer (Windows)**
   - 适用场景：已调整好尺寸，只需压缩
   - 推荐设置：默认设置，无损压缩模式

### 2. 在线工具

1. **Squoosh (https://squoosh.app/)**
   - 适用场景：少量图片快速处理
   - 推荐设置：MozJPEG编码器，质量75%

2. **TinyPNG (https://tinypng.com/)**
   - 适用场景：批量压缩PNG和JPEG
   - 特点：智能压缩，自动平衡质量和大小

## 四、图片处理工作流程

### 1. 准备阶段

1. 创建工作目录结构：
   ```
   /原始截图/[模块名称]/
   /处理后图片/[模块名称]/
   ```

2. 按照命名规范对截图进行命名：
   - 格式：`[功能名称]-[步骤编号].png`
   - 示例：`登录-1.png`、`购物车-3.png`

### 2. 处理流程

1. **尺寸调整**：
   - 将原始截图调整至规定尺寸
   - 保持原始宽高比
   - 使用高质量重采样算法（如Lanczos）

2. **图片优化**：
   - 对PNG图片进行颜色优化（减少颜色数量）
   - 对JPEG图片调整压缩质量（70-80%）
   - 移除元数据（EXIF信息等）

3. **批量处理**：
   - 使用批处理工具统一处理同类型图片
   - 确保处理参数一致，保持风格统一

4. **质量检查**：
   - 按照第二节的标准检查处理后的图片
   - 对不符合标准的图片进行重新处理

### 3. 文档整合

1. **图片引用**：
   在Markdown文档中使用以下格式引用图片：
   ```markdown
   <div align="center">
     <img src="images/模块名称/图片文件名.png" width="600" alt="图片描述">
     <p>图 X-X: 图片标题</p>
   </div>
   ```

2. **图片排版**：
   - 确保图片居中显示
   - 添加适当的图片标题
   - 控制图片之间的间距（建议30-50像素）

## 五、Markdown中控制图片显示的方法

### 1. 控制图片大小

```markdown
<!-- 固定宽度 -->
<img src="images/模块名称/图片文件名.png" width="600" alt="图片描述">

<!-- 相对宽度 -->
<img src="images/模块名称/图片文件名.png" width="80%" alt="图片描述">
```

### 2. 图片居中与添加标题

```markdown
<div align="center">
  <img src="images/模块名称/图片文件名.png" width="600" alt="图片描述">
  <p>图 3-1: 购物车界面</p>
</div>
```

### 3. 并排显示多张图片

```markdown
<div style="display: flex; justify-content: space-between;">
  <img src="images/模块名称/图片1.png" width="48%" alt="描述1">
  <img src="images/模块名称/图片2.png" width="48%" alt="描述2">
</div>
```

## 六、实施建议

1. **建立图片处理模板**：
   - 为常见的截图类型创建处理模板
   - 保存预设参数，确保处理一致性

2. **定期审查**：
   - 定期抽查文档中的图片是否符合标准
   - 及时调整不符合要求的图片

3. **团队培训**：
   - 确保所有团队成员了解并掌握图片处理规范
   - 提供必要的工具和培训支持

4. **版本控制**：
   - 保留原始截图，便于后续重新处理
   - 记录处理参数，确保可重复性

## 七、附录：图片处理脚本

### Python批量处理脚本

```python
from PIL import Image
import os
import glob

# 设置输入和输出文件夹
input_folder = "原始截图/"
output_folder = "处理后图片/"

# 创建输出文件夹（如果不存在）
if not os.path.exists(output_folder):
    os.makedirs(output_folder)

# 设置目标宽度
target_width = 600

# 处理所有PNG和JPG图片
for img_path in glob.glob(input_folder + "*.png") + glob.glob(input_folder + "*.jpg"):
    # 打开图片
    img = Image.open(img_path)
    
    # 计算新高度，保持宽高比
    width_percent = (target_width / float(img.size[0]))
    target_height = int((float(img.size[1]) * float(width_percent)))
    
    # 调整图片大小
    resized_img = img.resize((target_width, target_height), Image.LANCZOS)
    
    # 获取文件名
    filename = os.path.basename(img_path)
    
    # 保存处理后的图片
    output_path = os.path.join(output_folder, filename)
    resized_img.save(output_path, optimize=True, quality=80)
    
    print(f"处理完成: {filename}")

print("所有图片处理完毕!")
```

使用方法：
1. 安装Python和Pillow库：`pip install Pillow`
2. 将脚本保存为`resize_images.py`
3. 修改输入和输出文件夹路径
4. 运行脚本：`python resize_images.py`
