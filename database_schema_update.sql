-- 修改店铺补助金状态字段默认值为空
-- 只有当交易类型为店铺补助金相关时才有意义
ALTER TABLE `member_account_capital` 
MODIFY COLUMN `store_subsidy_status` varchar(10) DEFAULT NULL COMMENT '店铺补助金状态：0-可用，1-已过期，2-已用尽（仅对pay_type=50,51,52有效）';

-- 清理历史数据中非店铺补助金记录的状态字段
UPDATE `member_account_capital` 
SET `store_subsidy_status` = NULL 
WHERE `pay_type` NOT IN ('50', '51', '52'); 

-- 为PayOrderCarLog表添加店铺补助金支付金额字段（如果不存在）
ALTER TABLE `pay_order_car_log` 
ADD COLUMN IF NOT EXISTS `store_subsidy_amount` decimal(10,2) DEFAULT 0.00 COMMENT '店铺补助金支付金额';

-- 为OrderStoreList表添加店铺补助金支付金额字段（如果不存在）
ALTER TABLE `order_store_list` 
ADD COLUMN IF NOT EXISTS `store_subsidy_amount` decimal(10,2) DEFAULT 0.00 COMMENT '店铺补助金支付金额';

-- 更新PayOrderCarLog表的balance字段注释，明确其含义
ALTER TABLE `pay_order_car_log` 
MODIFY COLUMN `balance` decimal(10,2) DEFAULT 0.00 COMMENT '通用余额支付金额（不包含店铺补助金）';

-- 更新OrderStoreList表的balance字段注释，明确其含义
ALTER TABLE `order_store_list` 
MODIFY COLUMN `balance` decimal(10,2) DEFAULT 0.00 COMMENT '通用余额支付金额（不包含店铺补助金）'; 