package org.jeecg.modules.storeLevelChangeLogs.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.storeLevelChangeLogs.entity.StoreLevelChangeLogs;
import com.github.yulichang.base.MPJBaseMapper;

/**
 * @Description: 店铺等级变更及奖励日志表
 * @Author: jeecg-boot
 * @Date:   2025-05-26
 * @Version: V1.0
 */
public interface StoreLevelChangeLogsMapper extends MPJBaseMapper<StoreLevelChangeLogs> {

}
