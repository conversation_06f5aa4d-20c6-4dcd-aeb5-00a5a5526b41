package org.jeecg.modules.store.model;

import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 店铺等级评定结果
 * 
 * <AUTHOR>
 * @date 2024-03-21
 */
@Data
@Builder
public class StoreLevelAssessmentResult {
    
    /**
     * 店铺ID
     */
    private String storeId;
    
    /**
     * 评定周期 (YYYY-MM)
     */
    private String assessmentPeriod;
    
    /**
     * 变更前等级
     */
    private String previousLevel;
    
    /**
     * 变更后等级
     */
    private String currentLevel;
    
    /**
     * 评定使用的上月业绩金额
     */
    private BigDecimal assessedSales;
    
    /**
     * 评定使用的合作企业家数量
     */
    private Long assessedEntrepreneurCount;
    
    /**
     * 变更类型: UPGRADE, DOWNGRADE, MAINTAIN
     */
    private String changeType;
    
    /**
     * 发放的奖励金额
     */
    private BigDecimal rewardBalanceAmount;
    
    /**
     * 备注信息
     */
    private String remarks;
} 