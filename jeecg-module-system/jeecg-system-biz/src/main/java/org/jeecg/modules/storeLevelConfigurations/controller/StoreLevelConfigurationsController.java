package org.jeecg.modules.storeLevelConfigurations.controller;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.storeLevelConfigurations.entity.StoreLevelConfigurations;
import org.jeecg.modules.storeLevelConfigurations.service.IStoreLevelConfigurationsService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: 店铺等级评定配置表
 * @Author: jeecg-boot
 * @Date:   2025-05-26
 * @Version: V1.0
 */
@Api(tags="店铺等级评定配置表")
@RestController
@RequestMapping("/storeLevelConfigurations/storeLevelConfigurations")
@Slf4j
public class StoreLevelConfigurationsController extends JeecgController<StoreLevelConfigurations, IStoreLevelConfigurationsService> {
	@Autowired
	private IStoreLevelConfigurationsService storeLevelConfigurationsService;
	
	/**
	 * 分页列表查询
	 *
	 * @param storeLevelConfigurations
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "店铺等级评定配置表-分页列表查询")
	@ApiOperation(value="店铺等级评定配置表-分页列表查询", notes="店铺等级评定配置表-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<StoreLevelConfigurations>> queryPageList(StoreLevelConfigurations storeLevelConfigurations,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<StoreLevelConfigurations> queryWrapper = QueryGenerator.initQueryWrapper(storeLevelConfigurations, req.getParameterMap());
		Page<StoreLevelConfigurations> page = new Page<StoreLevelConfigurations>(pageNo, pageSize);
		IPage<StoreLevelConfigurations> pageList = storeLevelConfigurationsService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param storeLevelConfigurations
	 * @return
	 */
	@AutoLog(value = "店铺等级评定配置表-添加")
	@ApiOperation(value="店铺等级评定配置表-添加", notes="店铺等级评定配置表-添加")
	@RequiresPermissions("storeLevelConfigurations:store_level_configurations:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody StoreLevelConfigurations storeLevelConfigurations) {
		storeLevelConfigurationsService.save(storeLevelConfigurations);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param storeLevelConfigurations
	 * @return
	 */
	@AutoLog(value = "店铺等级评定配置表-编辑")
	@ApiOperation(value="店铺等级评定配置表-编辑", notes="店铺等级评定配置表-编辑")
	@RequiresPermissions("storeLevelConfigurations:store_level_configurations:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody StoreLevelConfigurations storeLevelConfigurations) {
		storeLevelConfigurationsService.updateById(storeLevelConfigurations);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "店铺等级评定配置表-通过id删除")
	@ApiOperation(value="店铺等级评定配置表-通过id删除", notes="店铺等级评定配置表-通过id删除")
	@RequiresPermissions("storeLevelConfigurations:store_level_configurations:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		storeLevelConfigurationsService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "店铺等级评定配置表-批量删除")
	@ApiOperation(value="店铺等级评定配置表-批量删除", notes="店铺等级评定配置表-批量删除")
	@RequiresPermissions("storeLevelConfigurations:store_level_configurations:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.storeLevelConfigurationsService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "店铺等级评定配置表-通过id查询")
	@ApiOperation(value="店铺等级评定配置表-通过id查询", notes="店铺等级评定配置表-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<StoreLevelConfigurations> queryById(@RequestParam(name="id",required=true) String id) {
		StoreLevelConfigurations storeLevelConfigurations = storeLevelConfigurationsService.getById(id);
		if(storeLevelConfigurations==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(storeLevelConfigurations);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param storeLevelConfigurations
    */
    @RequiresPermissions("storeLevelConfigurations:store_level_configurations:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, StoreLevelConfigurations storeLevelConfigurations) {
        return super.exportXls(request, storeLevelConfigurations, StoreLevelConfigurations.class, "店铺等级评定配置表");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("storeLevelConfigurations:store_level_configurations:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, StoreLevelConfigurations.class);
    }

}
