package org.jeecg.modules.storeLevelConfigurations.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 店铺等级评定配置表
 * @Author: jeecg-boot
 * @Date:   2025-05-26
 * @Version: V1.0
 */
@Data
@TableName("store_level_configurations")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="store_level_configurations对象", description="店铺等级评定配置表")
public class StoreLevelConfigurations implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键ID*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键ID")
    private java.lang.String id;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private java.lang.String createBy;
	/**创建时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private java.util.Date createTime;
	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private java.lang.String updateBy;
	/**更新时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新时间")
    private java.util.Date updateTime;
	/**删除状态 0=未删除 1=已删除*/
	@Excel(name = "删除状态 0=未删除 1=已删除", width = 15)
    @ApiModelProperty(value = "删除状态 0=未删除 1=已删除")
    @TableLogic
    private java.lang.String delFlag;
	/**店铺等级值*/
	@Excel(name = "店铺等级值", width = 15, dicCode = "store_level")
	@Dict(dicCode = "store_level")
    @ApiModelProperty(value = "店铺等级值")
    private java.lang.String levelValue;
	/**店铺等级名称*/
	@Excel(name = "店铺等级名称", width = 15)
    @ApiModelProperty(value = "店铺等级名称")
    private java.lang.String levelName;
	/**最低月业绩要求*/
	@Excel(name = "最低月业绩要求", width = 15)
    @ApiModelProperty(value = "最低月业绩要求")
    private java.math.BigDecimal minMonthlySales;
	/**最低关联企业家数量要求*/
	@Excel(name = "最低关联企业家数量要求", width = 15)
    @ApiModelProperty(value = "最低关联企业家数量要求")
    private java.lang.Integer minEntrepreneurCount;
	/**月度奖励金额*/
	@Excel(name = "月度奖励金额", width = 15)
    @ApiModelProperty(value = "月度奖励金额")
    private java.math.BigDecimal monthlyRewardBalanceAmount;
	/**等级说明*/
	@Excel(name = "等级说明", width = 15)
    @ApiModelProperty(value = "等级说明")
    private java.lang.String description;
	/**排序字段*/
	@Excel(name = "排序字段", width = 15)
    @ApiModelProperty(value = "排序字段")
    private java.lang.Integer sortOrder;
	/**是否启用*/
	@Excel(name = "是否启用", width = 15)
    @ApiModelProperty(value = "是否启用")
    private java.lang.Integer isActive;
}
