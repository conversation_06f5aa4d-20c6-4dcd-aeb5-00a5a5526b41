package org.jeecg.modules.storeLevelChangeLogs.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 店铺等级变更及奖励日志表
 * @Author: jeecg-boot
 * @Date:   2025-05-26
 * @Version: V1.0
 */
@Data
@TableName("store_level_change_logs")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="store_level_change_logs对象", description="店铺等级变更及奖励日志表")
public class StoreLevelChangeLogs implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键ID*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键ID")
    private java.lang.String id;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private java.lang.String createBy;
	/**创建时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private java.util.Date createTime;
	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private java.lang.String updateBy;
	/**更新时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新时间")
    private java.util.Date updateTime;
	/**删除状态*/
	@Excel(name = "删除状态", width = 15)
    @ApiModelProperty(value = "删除状态")
    @TableLogic
    private java.lang.String delFlag;
	/**店铺ID*/
	@Excel(name = "店铺ID", width = 15, dictTable = "store_manage where del_flag=0", dicText = "store_name", dicCode = "id")
	@Dict(dictTable = "store_manage where del_flag=0", dicText = "store_name", dicCode = "id")
    @ApiModelProperty(value = "店铺ID")
    private java.lang.String storeManageId;
	/**评定业绩周期 YYYY-MM*/
	@Excel(name = "评定业绩周期 YYYY-MM", width = 15)
    @ApiModelProperty(value = "评定业绩周期 YYYY-MM")
    private java.lang.String assessmentPeriod;
	/**变更前等级*/
	@Excel(name = "变更前等级", width = 15, dicCode = "store_level")
	@Dict(dicCode = "store_level")
    @ApiModelProperty(value = "变更前等级")
    private java.lang.String previousLevel;
	/**变更后等级*/
	@Excel(name = "变更后等级", width = 15, dicCode = "store_level")
	@Dict(dicCode = "store_level")
    @ApiModelProperty(value = "变更后等级")
    private java.lang.String currentLevel;
	/**评定使用的上月业绩金额*/
	@Excel(name = "评定使用的上月业绩金额", width = 15)
    @ApiModelProperty(value = "评定使用的上月业绩金额")
    private java.math.BigDecimal assessedSales;
	/**评定使用的合作企业家数量*/
	@Excel(name = "评定使用的合作企业家数量", width = 15)
    @ApiModelProperty(value = "评定使用的合作企业家数量")
    private java.lang.Integer assessedEntrepreneurCount;
	/**变更类型: UPGRADE, DOWNGRADE, MAINTAIN*/
	@Excel(name = "变更类型: UPGRADE, DOWNGRADE, MAINTAIN", width = 15, dicCode = "store_level_change_type")
	@Dict(dicCode = "store_level_change_type")
    @ApiModelProperty(value = "变更类型: UPGRADE, DOWNGRADE, MAINTAIN")
    private java.lang.String changeType;
	/**发放的奖励金额*/
	@Excel(name = "发放的奖励金额", width = 15)
    @ApiModelProperty(value = "发放的奖励金额")
    private java.math.BigDecimal rewardBalanceAmountGiven;
	/**备注信息*/
	@Excel(name = "备注信息", width = 15)
    @ApiModelProperty(value = "备注信息")
    private java.lang.String remarks;
}
