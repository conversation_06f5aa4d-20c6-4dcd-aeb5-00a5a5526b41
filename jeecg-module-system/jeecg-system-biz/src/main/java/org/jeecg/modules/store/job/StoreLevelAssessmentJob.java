package org.jeecg.modules.store.job;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.system.api.ISysBaseAPI;
import org.jeecg.modules.store.entity.StoreManage;
import org.jeecg.modules.store.model.StoreLevelAssessmentResult;
import org.jeecg.modules.store.service.IStoreLevelAssessmentService;
import org.jeecg.modules.store.service.IStoreManageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 店铺等级评定定时任务
 * 每月1日凌晨2点执行，对所有店铺进行等级评定
 */
@Slf4j
@Component
public class StoreLevelAssessmentJob implements Job {
    
    @Autowired
    private IStoreManageService storeManageService;
    
    @Autowired
    private IStoreLevelAssessmentService assessmentService;
    
    @Autowired
    private ISysBaseAPI sysBaseAPI;
    
    @Override
    public void execute(JobExecutionContext context) throws JobExecutionException {
        log.info("开始执行店铺等级评定任务");
        
        try {
            // 获取上个月的年月格式（YYYY-MM）
            String assessmentPeriod = LocalDate.now().minusMonths(1)
                    .format(DateTimeFormatter.ofPattern("yyyy-MM"));
            
            // 获取所有有效的店铺
            List<StoreManage> eligibleStores = getEligibleStores();
            log.info("本次需要评定的店铺数量: {}", eligibleStores.size());
            
            // 分批处理，每批50个店铺
            int batchSize = 50;
            AtomicInteger successCount = new AtomicInteger(0);
            AtomicInteger failCount = new AtomicInteger(0);
            
            for (int i = 0; i < eligibleStores.size(); i += batchSize) {
                int end = Math.min(i + batchSize, eligibleStores.size());
                List<StoreManage> batch = eligibleStores.subList(i, end);
                
                try {
                    processBatch(batch, assessmentPeriod, successCount, failCount);
                } catch (Exception e) {
                    log.error("批次处理异常，批次范围[{}-{}]", i, end, e);
                    failCount.addAndGet(batch.size());
                }
            }
            
            log.info("店铺等级评定任务执行完成，成功：{}，失败：{}", 
                    successCount.get(), failCount.get());
            
        } catch (Exception e) {
            log.error("店铺等级评定任务执行失败", e);
            throw new JobExecutionException(e);
        }
    }
    
    /**
     * 获取需要评定的有效店铺列表
     */
    private List<StoreManage> getEligibleStores() {
        LambdaQueryWrapper<StoreManage> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StoreManage::getDelFlag, "0")  // 未删除
                   .eq(StoreManage::getStatus, "1");   // 正常状态
        
        return storeManageService.list(queryWrapper);
    }
    
    /**
     * 处理一批店铺的等级评定
     */
    private void processBatch(List<StoreManage> stores, String assessmentPeriod,
                            AtomicInteger successCount, AtomicInteger failCount) {
        for (StoreManage store : stores) {
            try {
                StoreLevelAssessmentResult result = assessmentService.assessStoreLevel(
                        store.getId(), assessmentPeriod);
                
                log.info("店铺[{}]等级评定完成: {} -> {}, 类型: {}", 
                        store.getId(), result.getPreviousLevel(), 
                        result.getCurrentLevel(), result.getChangeType());
                
                successCount.incrementAndGet();
            } catch (Exception e) {
                log.error("店铺[{}]等级评定失败", store.getId(), e);
                failCount.incrementAndGet();
            }
        }
    }
} 