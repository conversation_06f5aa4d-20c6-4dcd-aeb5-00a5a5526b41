<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.member.mapper.MemberAccountCapitalMapper">
    <select id="findMemberAccountCapitalByMemberId" parameterType="map" resultType="map">
        SELECT amount                                     AS amount,
               go_and_come                                AS goAndCome,
               pay_type                                   AS payType,
               DATE_FORMAT(create_time, '%Y-%m-%d %H:%i') AS createTime,
               remarks                                    AS remarks
        FROM `member_account_capital`
        WHERE member_list_id = #{paramMap.memberId}
          AND del_flag = '0'
        <if test="paramMap.pattern == 0">
            AND go_and_come = '0'
        </if>
        <if test="paramMap.pattern == 1">
            AND go_and_come = '1'
        </if>
        ORDER BY create_time DESC, balance DESC
    </select>
    <select id="getMemberAccountCapitalList" resultType="org.jeecg.modules.member.dto.MemberAccountCapitalDTO">
        SELECT mac.id,
               mac.member_list_id,
               mac.pay_type,
               mac.go_and_come,
               mac.amount,
               mac.order_no,
               mac.balance,
               ml.phone     AS phone,
               ml.nick_name AS nickName,
               mac.create_time,
               mac.trade_no AS tradeNo,
               mac.sys_user_id AS sysUserId,
               mac.remarks AS remarks,
               mac.expire_time AS expireTime,
               mac.subsidy_store_id AS subsidyStoreId,
               mac.remaining_amount AS remainingAmount,
               sm.store_name AS subsidyStoreName,
               mac.store_subsidy_status AS storeSubsidyStatus
        FROM
        (SELECT *
         FROM member_account_capital
        WHERE member_list_id = #{memberAccountCapitalVO.memberListId}
        <if test="memberAccountCapitalVO.id != null and memberAccountCapitalVO.id != ''">
            AND id LIKE CONCAT('%', #{memberAccountCapitalVO.id}, '%')
        </if>
        )mac
            LEFT JOIN member_list ml
                      ON mac.member_list_id = ml.id
            LEFT JOIN store_manage sm
                      ON mac.subsidy_store_id = sm.id
        WHERE mac.del_flag = 0
          AND ml.del_flag = 0
        <if test="memberAccountCapitalVO != null and memberAccountCapitalVO.createTime_begin != null and memberAccountCapitalVO.createTime_begin != ''">
            and mac.create_time <![CDATA[>=]]> concat(#{memberAccountCapitalVO.createTime_begin}, ' 00:00:00')
        </if>
        <if test="memberAccountCapitalVO != null and memberAccountCapitalVO.createTime_end != null and memberAccountCapitalVO.createTime_end != ''">
            AND mac.create_time <![CDATA[<=]]> concat(#{memberAccountCapitalVO.createTime_end}, ' 23:59:59')
        </if>
        <if test="memberAccountCapitalVO != null and memberAccountCapitalVO.payType != null and memberAccountCapitalVO.payType != ''">
    AND mac.pay_type IN
    <foreach collection="memberAccountCapitalVO.payType.split(',')" item="type" open="(" separator="," close=")">
        #{type}
    </foreach>
</if>
        <if test="memberAccountCapitalVO != null and memberAccountCapitalVO.payTypes != null and memberAccountCapitalVO.payTypes != ''">
    AND mac.pay_type IN
    <foreach collection="memberAccountCapitalVO.payTypes.split(',')" item="type" open="(" separator="," close=")">
        #{type}
    </foreach>
</if>
        <if test="memberAccountCapitalVO != null and memberAccountCapitalVO.orderNo != null and memberAccountCapitalVO.orderNo != ''">
            AND mac.order_no LIKE CONCAT(CONCAT('%', #{memberAccountCapitalVO.orderNo}), '%')
        </if>
        <if test="memberAccountCapitalVO != null and memberAccountCapitalVO.tradeNo != null and memberAccountCapitalVO.tradeNo != ''">
            AND mac.trade_no LIKE CONCAT(CONCAT('%', #{memberAccountCapitalVO.tradeNo}), '%')
        </if>
        <if test="memberAccountCapitalVO != null and memberAccountCapitalVO.phone != null and memberAccountCapitalVO.phone != ''">
            AND ml.phone LIKE CONCAT(CONCAT('%', #{memberAccountCapitalVO.phone}), '%')
        </if>
        <if test="memberAccountCapitalVO != null and memberAccountCapitalVO.nickName != null and memberAccountCapitalVO.nickName != ''">
            AND ml.nick_name LIKE CONCAT(CONCAT('%', #{memberAccountCapitalVO.nickName}), '%')
        </if>
        <if test="memberAccountCapitalVO != null and memberAccountCapitalVO.goAndCome != null and memberAccountCapitalVO.goAndCome != ''">
            AND mac.go_and_come = #{memberAccountCapitalVO.goAndCome}
        </if>
        <if test="memberAccountCapitalVO != null and memberAccountCapitalVO.sysUserId != null and memberAccountCapitalVO.sysUserId != ''">
            AND ml.sys_user_id = #{memberAccountCapitalVO.sysUserId}
        </if>
        <if test="memberAccountCapitalVO != null and memberAccountCapitalVO.subsidyStoreName != null and memberAccountCapitalVO.subsidyStoreName != ''">
            AND sm.store_name LIKE CONCAT('%', #{memberAccountCapitalVO.subsidyStoreName}, '%')
        </if>
        <if test="memberAccountCapitalVO != null and memberAccountCapitalVO.storeSubsidyStatus != null and memberAccountCapitalVO.storeSubsidyStatus != ''">
            AND mac.store_subsidy_status = #{memberAccountCapitalVO.storeSubsidyStatus}
        </if>
        ORDER BY mac.create_time DESC,
                 mac.balance DESC
    </select>
    <select id="findAccountCapitalByMemberId" resultType="map">
        SELECT (SELECT s.item_text
                FROM sys_dict_item s
                WHERE s.dict_id =
                      (SELECT id
                       FROM sys_dict
                       WHERE dict_code = 'member_deal_type')
                  AND s.`item_value` = mac.`pay_type`) AS payType,
               DATE_FORMAT(
                       mac.`create_time`,
                       '%Y-%m-%d %H:%i:%s'
               )                                       AS createTime,
               (
                   CASE
                       mac.`go_and_come`
                       WHEN 0
                           THEN CONCAT('+', mac.`amount`)
                       WHEN 1
                           THEN CONCAT('-', mac.`amount`)
                       ELSE '异常'
                       END
                   )                                   AS amount,
               concat('余额 ', mac.`balance`)          AS balance,
               mac.balance                             AS currentBalance,
               mac.`go_and_come`                       AS goAndCome,
               mac.order_no                            as orderNo,
               mac.remarks                             as remarks,
               DATE_FORMAT(
                       mac.expire_time,
                       '%Y-%m-%d %H:%i:%s'
               )                                       AS expireTime,
               mac.subsidy_store_id                    AS subsidyStoreId,
               sm.store_name                          AS subsidyStoreName,
               mac.remaining_amount                    AS remainingAmount,
               mac.store_subsidy_status               AS storeSubsidyStatus,
               mac.pay_type                           AS payTypeCode
        FROM member_account_capital mac
        LEFT JOIN store_manage sm ON mac.subsidy_store_id = sm.id
        WHERE mac.`del_flag` = '0'
          AND mac.`member_list_id` = #{map.memberId}
        <if test="map.pattern != 2">
            AND mac.`go_and_come` = #{map.pattern}
        </if>
        <if test="map.memberSource != null">
            and mac.member_source = #{map.memberSource}
        </if>
        <if test="map.payType != null and map.payType != ''">
            AND mac.pay_type = #{map.payType}
        </if>
        <if test="map.payTypes != null and map.payTypes != ''">
            AND mac.pay_type IN
            <foreach collection="map.payTypes.split(',')" item="type" open="(" separator="," close=")">
                #{type}
            </foreach>
        </if>
        <if test="map.storeSubsidyStatus != null and map.storeSubsidyStatus != ''">
            AND mac.store_subsidy_status = #{map.storeSubsidyStatus}
        </if>
        ORDER BY mac.`create_time` DESC, mac.`amount` DESC
    </select>
    <select id="getEnterMoney" resultType="org.jeecg.modules.member.vo.MemberAccountCapitalVO">
        SELECT mac.id,
               mac.`order_no`,
               ml.`phone`,
               ml.`nick_name`,
               mac.`amount`
        FROM member_account_capital mac
                 LEFT JOIN member_list ml
                           ON mac.`member_list_id` = ml.`id`
        WHERE mac.`del_flag` = '0'
          AND mac.`pay_type` = '8'
          AND mac.`order_no` = #{marketingEnterMoney.orderNo}
    </select>
</mapper>