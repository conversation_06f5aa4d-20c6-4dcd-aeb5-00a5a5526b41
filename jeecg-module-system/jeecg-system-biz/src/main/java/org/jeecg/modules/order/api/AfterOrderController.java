package org.jeecg.modules.order.api;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.comparator.CompareUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.ocean.rawsdk.client.Request;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.api.dto.message.BusTemplateMessageDTO;
import org.jeecg.common.api.dto.message.WxSubscribeMsgDTO;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.constant.enums.SysAnnmentTypeEnum;
import org.jeecg.common.exception.JeecgBootException;
import org.jeecg.common.system.api.ISysBaseAPI;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.common.util.DateUtils;
import org.jeecg.modules.good.entity.GoodSpecification;
import org.jeecg.modules.good.entity.GoodStoreList;
import org.jeecg.modules.good.service.IGoodListService;
import org.jeecg.modules.good.service.IGoodSpecificationService;
import org.jeecg.modules.good.service.IGoodStoreListService;
import org.jeecg.modules.map.utils.TengxunMapUtils;
import org.jeecg.modules.marketing.dto.MarketingDisountGoodDTO;
import org.jeecg.modules.marketing.entity.*;
import org.jeecg.modules.marketing.service.*;
import org.jeecg.modules.marketing.store.prefecture.service.IMarketingStorePrefectureGiveService;
import org.jeecg.modules.member.entity.MemberList;
import org.jeecg.modules.member.entity.MemberShippingAddress;
import org.jeecg.modules.member.entity.MemberShoppingCart;
import org.jeecg.modules.member.service.IMemberListService;
import org.jeecg.modules.member.service.IMemberShippingAddressService;
import org.jeecg.modules.member.service.IMemberShoppingCartService;
import org.jeecg.modules.message.handle.impl.SystemSendMsgHandle;
import org.jeecg.modules.order.dto.OrderStoreSubListDTO;
import org.jeecg.modules.order.entity.OrderList;
import org.jeecg.modules.order.entity.OrderStoreGoodRecord;
import org.jeecg.modules.order.entity.OrderStoreList;
import org.jeecg.modules.order.mapper.OrderStoreGoodRecordMapper;
import org.jeecg.modules.order.service.*;
import org.jeecg.modules.order.utils.TotalPayUtils;
import org.jeecg.modules.pay.entity.PayOrderCarLog;
import org.jeecg.modules.pay.service.IPayOrderCarLogService;
import org.jeecg.modules.provider.service.IProviderTemplateService;
import org.jeecg.modules.store.entity.StoreManage;
import org.jeecg.modules.store.service.IStoreManageService;
import org.jeecg.modules.store.service.IStoreTemplateService;
import org.jeecg.modules.store.util.StoreUtils;
import org.jeecg.modules.system.entity.SysUser;
import org.jeecg.modules.system.service.ISysDictService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 订单api控制器
 */
@RequestMapping("after/order")
@Controller
@Slf4j
public class AfterOrderController {

    @Autowired
    private IMemberShoppingCartService iMemberShoppingCartService;

    @Autowired
    private IGoodListService iGoodListService;

    @Autowired
    private IMemberListService iMemberListService;

    @Autowired
    private IMemberShippingAddressService iMemberShippingAddressService;

    @Autowired
    private IOrderListService iOrderListService;

    @Autowired
    private IOrderStoreListService iOrderStoreListService;

    @Autowired
    private IOrderProviderGoodRecordService iOrderProviderGoodRecordService;

    @Autowired
    private IOrderStoreGoodRecordService iOrderStoreGoodRecordService;

    @Autowired
    private IStoreTemplateService iStoreTemplateService;

    @Autowired
    private IProviderTemplateService iProviderTemplateService;

    @Autowired
    private IMarketingDiscountCouponService iMarketingDiscountCouponService;

    @Autowired
    private IMarketingPrefectureGoodService iMarketingPrefectureGoodService;

    @Autowired
    private IMarketingPrefectureGoodSpecificationService iMarketingPrefectureGoodSpecificationService;

    @Autowired
    private IMarketingPrefectureService iMarketingPrefectureService;

    @Autowired
    private IGoodSpecificationService iGoodSpecificationService;

    @Autowired
    private IMarketingCertificateRecordService iMarketingCertificateRecordServicel;

    @Autowired
    private IMarketingCertificateGoodService iMarketingCertificateGoodService;

    @Autowired
    private TotalPayUtils totalPayUtils;

    @Autowired
    private IMarketingWelfarePaymentsSettingService iMarketingWelfarePaymentsSettingService;

    @Autowired
    private IPayOrderCarLogService iPayOrderCarLogService;

    @Autowired
    private IMarketingGiftBagRecordService iMarketingGiftBagRecordService;


    @Autowired
    private IMarketingStorePrefectureGiveService iMarketingStorePrefectureGiveService;

    @Autowired
    private ISysBaseAPI sysBaseAPI;

    @Autowired
    private IStoreManageService storeManageService;

    @Autowired
    private IOrderStoreSubListService orderStoreSubListService;

    @Autowired
    @Lazy
    private IMarketingStoreDistributionSettingService iMarketingStoreDistributionSettingService;

    @Autowired
    @Lazy
    private IGoodStoreListService iGoodStoreListService;

    @Autowired
    private ISysDictService iSysDictService;

    /**
     * 兑换券线上兑换
     *
     * @param marketingCertificateRecordId
     * @return
     */
    @RequestMapping("submitCertificate")
    @ResponseBody
    public Result<?> submitCertificate(String marketingCertificateRecordId,
                                       String marketingCertificateGoodIds,
                                       String memberShippingAddressId,
                                       @RequestAttribute(value = "memberId", required = false) String memberId) {
        Result<Map<String, Object>> result = new Result<>();

//        return Result.error("功能升级中,请选择线下核销!");

        //检测参数不能为空
        if (StringUtils.isBlank(marketingCertificateRecordId)) {
            result.error500("兑换券id不能为空");
            return result;
        }

        //兑换券记录状态必须正确
        MarketingCertificateRecord marketingCertificateRecord = iMarketingCertificateRecordServicel.getById(marketingCertificateRecordId);
        if (marketingCertificateRecord == null) {
            result.error500("兑换券在系统中不存在！！！");
            return result;
        }

        if (!marketingCertificateRecord.getStatus().equals("1")) {
            result.error500("兑换券的状态不是未使用状态！！！");
            return result;
        }

        //查询兑换券商品

        if (StringUtils.isBlank(marketingCertificateGoodIds)) {
            result.error500("兑换券商品必须提交！！！");
            return result;
        }
        iMarketingCertificateRecordServicel.saveOrUpdate(marketingCertificateRecord.setUserTime(new Date()));
        List<String> marketingCertificateGoodList = Arrays.asList(StringUtils.split(marketingCertificateGoodIds, ","));

        //购物车的列表信息
        List<String> shopCarIds = Lists.newArrayList();

        for (String id : marketingCertificateGoodList) {

            MarketingCertificateGood mcg = iMarketingCertificateGoodService.getById(id);

            //兑换券商品加入购物车
            String backResult = iMemberShoppingCartService.addGoodToShoppingCartCertificate(Integer.parseInt(mcg.getIsPlatform()), mcg.getGoodSpecificationId(), memberId, mcg.getQuantity().intValue(), "0", marketingCertificateRecordId);

            if (backResult.indexOf("SUCCESS") == -1) {
                result.error500(backResult);
                return result;
            } else {
                shopCarIds.add(StringUtils.substringAfter(backResult, "="));
            }
        }

        // 兑换券不需要分享折扣，传入BigDecimal.ZERO
        return affirmOrder(StringUtils.join(shopCarIds, ","), memberShippingAddressId, null, memberId, memberId, BigDecimal.ZERO);
    }


    /**
     * 确认收货
     *
     * @param isPlatform
     * @param id
     * @param memberId
     * @return
     */
    @RequestMapping("affirmOrderDelivery")
    @ResponseBody
    public Result<String> affirmOrderDelivery(Integer isPlatform, String id, @RequestAttribute(value = "memberId", required = false) String memberId) {
        Result<String> result = new Result<>();

        //参数校验
        if (isPlatform == null) {
            result.error500("isPlatform是否平台类型参数不能为空！！！");
            return result;
        }

        if (StringUtils.isBlank(id)) {
            result.error500("id不能为空！！！");
            return result;
        }


        if (isPlatform == 0) {
            //店铺订单
            OrderStoreList orderStoreList = iOrderStoreListService.getById(id);
            if (!orderStoreList.getMemberListId().equals(memberId)) {
                result.error500("订单不是用户的订单！！！");
                return result;
            }
            iOrderStoreListService.affirmOrder(id);
        } else if (isPlatform == 1) {
            //平台订单
            OrderList orderList = iOrderListService.getById(id);
            if (!orderList.getMemberListId().equals(memberId)) {
                result.error500("订单不是用户的订单！！！");
                return result;
            }
            iOrderListService.affirmOrder(id);
        } else {
            result.error500("isPlatform参数不正确请联系平台管理员！！！");
            return result;
        }
        result.success("订单确认收货成功");
        return result;
    }

    /**
     * 取消订单
     *
     * @param isPlatform
     * @param id
     * @return
     */
    @RequestMapping("abrogateOrder")
    @ResponseBody
    public Result<String> abrogateOrder(Integer isPlatform, String id, String value, @RequestAttribute(value = "memberId", required = false) String memberId) {
        Result<String> result = new Result<>();

        //参数校验
        if (isPlatform == null) {

            result.error500("isPlatform是否平台类型参数不能为空！！！");
            return result;
        }

        if (StringUtils.isBlank(id)) {
            result.error500("id不能为空！！！");
            return result;
        }

        if (StringUtils.isBlank(value)) {
            result.error500("value值不能为空！！！");
            return result;
        }

        if (isPlatform == 0) {
            //店铺订单
            OrderStoreList orderStoreList = iOrderStoreListService.getById(id);
            if (!orderStoreList.getMemberListId().equals(memberId)) {
                result.error500("订单不是用户的订单！！！");
                return result;
            }
            iOrderStoreListService.abrogateOrder(id, value, "1");
        } else if (isPlatform == 1) {
            //平台订单
            OrderList orderStoreList = iOrderListService.getById(id);
            if (!orderStoreList.getMemberListId().equals(memberId)) {
                result.error500("订单不是用户的订单！！！");
                return result;
            }
            iOrderListService.abrogateOrder(id, value, "1");
        } else {
            result.error500("isPlatform参数不正确请联系平台管理员！！！");
            return result;
        }
        result.success("订单取消成功");
        return result;
    }


    /**
     * 查询订单详情
     *
     * @param id
     * @param isPlatform
     * @return
     */
    @RequestMapping("viewOrderInfo")
    @ResponseBody
    public Result<Map<String, Object>> viewOrderInfo(@RequestParam("id") String id, @RequestParam("isPlatform") Integer isPlatform) {
        Result<Map<String, Object>> result = new Result<>();
        Map<String, Object> objectMap = Maps.newHashMap();
        //参数判断
        if (StringUtils.isBlank(id)) {
            result.error500("订单id不能为空！！！");
            return result;
        }
        if (isPlatform == null) {
            result.error500("isPlatform是否平台类型参数不能为空！！！");
            return result;
        }

        //店铺订单
        if (isPlatform == 0) {
            OrderStoreList orderStoreList = iOrderStoreListService.getById(id);
            //收货地址信息
            objectMap.put("consignee", orderStoreList.getConsignee());
            objectMap.put("contactNumber", orderStoreList.getContactNumber());
            objectMap.put("shippingAddress", orderStoreList.getShippingAddress());
            objectMap.put("houseNumber", orderStoreList.getHouseNumber());
            //留言
            objectMap.put("message", orderStoreList.getMessage());
            //订单价格
            objectMap.put("goodsTotal", orderStoreList.getGoodsTotal());
            objectMap.put("shipFee", orderStoreList.getShipFee());
            objectMap.put("coupon", orderStoreList.getCoupon());
            objectMap.put("actualPayment", orderStoreList.getActualPayment());

            //门店自提
            objectMap.put("pickUpQrCode", orderStoreList.getPickUpQrCode());
            objectMap.put("pickUpQrAddr", orderStoreList.getPickUpQrAddr());
            objectMap.put("pickUpVerificationStatus", orderStoreList.getPickUpVerificationStatus());


            objectMap.put("balance", orderStoreList.getBalance());
            objectMap.put("payWelfarePayments", orderStoreList.getPayWelfarePayments());//支付的积分
            objectMap.put("payWelfarePaymentsPrice", orderStoreList.getPayWelfarePaymentsPrice());//约等于金额
            objectMap.put("modePayment", orderStoreList.getModePayment());//0:微信；1：支付宝；2：余额
            objectMap.put("discount", orderStoreList.getDiscount());//折扣
            objectMap.put("payPrice", orderStoreList.getPayPrice());//支付金额
            objectMap.put("status", orderStoreList.getStatus());
            //订单商品
            List<Map<String, Object>> orderStoreGoodRecordList = iOrderStoreGoodRecordService.getOrderStoreGoodRecordByOrderId(orderStoreList.getId());
            objectMap.put("goods", orderStoreGoodRecordList);

            //订单基础信息
            objectMap.put("id", orderStoreList.getId());
            objectMap.put("isPlatform", isPlatform);
            objectMap.put("orderNo", orderStoreList.getOrderNo());

            objectMap.put("createTime", DateUtil.formatDateTime(orderStoreList.getCreateTime()));

            //订单已发货包裹信息
            List<OrderStoreSubListDTO> orderStoreSubLists = orderStoreSubListService.selectorderStoreListId(orderStoreList.getId(), orderStoreList.getSysUserId(), null, "0");
            if (!orderStoreSubLists.isEmpty()) {
                orderStoreSubLists.forEach(opl -> {
                    //添加商品信息
                    List<OrderStoreGoodRecord> orderStoreGoodRecords = iOrderStoreGoodRecordService.selectOrderStoreSubListId(opl.getId());
                    //添加供应商订单商品记录
                    if (!orderStoreGoodRecords.isEmpty()) {
                        opl.setOrderStoreGoodRecords(orderStoreGoodRecords);
                    }
                });
            }
            objectMap.put("orderStoreSubLists", orderStoreSubLists);
            //订单未发货包裹信息
            List<OrderStoreSubListDTO> listOrderStoreSubList = orderStoreSubListService.selectorderStoreListId(orderStoreList.getId(), orderStoreList.getSysUserId(), "0", null);
            if (!listOrderStoreSubList.isEmpty()) {
                for (OrderStoreSubListDTO lopl : listOrderStoreSubList) {
                    //添加商品信息
                    List<OrderStoreGoodRecord> orderStoreGoodRecords = iOrderStoreGoodRecordService.selectOrderStoreSubListId(lopl.getId());
                    //添加供应商订单商品记录
                    if (!orderStoreGoodRecords.isEmpty()) {
                        lopl.setOrderStoreGoodRecords(orderStoreGoodRecords);
                    }
                }
            }
            objectMap.put("listOrderStoreSubList", listOrderStoreSubList);

            if (orderStoreList.getPayTime() != null) {
                objectMap.put("payTime", DateUtil.formatDateTime(orderStoreList.getPayTime()));
            } else {
                objectMap.put("payTime", "");
            }

            if (orderStoreList.getShipmentsTime() != null) {
                objectMap.put("shipmentsTime", DateUtil.formatDateTime(orderStoreList.getShipmentsTime()));
            } else {
                objectMap.put("shipmentsTime", "");
            }


            if (orderStoreList.getDeliveryTime() != null) {
                objectMap.put("deliveryTime", DateUtil.formatDateTime(orderStoreList.getDeliveryTime()));
            } else {
                objectMap.put("deliveryTime", "");
            }

            if (orderStoreList.getCloseTime() != null) {
                objectMap.put("closeTime", DateUtil.formatDateTime(orderStoreList.getCloseTime()));
            } else {
                objectMap.put("closeTime", "");
            }

            // 添加关闭类型和关闭原因
            String closeType = orderStoreList.getCloseType();
            String closeExplain = orderStoreList.getCloseExplain();
            objectMap.put("closeType", closeType);
            objectMap.put("closeExplain", closeExplain);

            // 手动进行字典翻译
            if (closeType != null) {
                String closeTypeText = iSysDictService.queryDictTextByKey("oder_close_type", closeType);
                objectMap.put("closeType_dictText", closeTypeText);
            }

            if (closeExplain != null) {
                String closeExplainText = iSysDictService.queryDictTextByKey("oder_close_explain", closeExplain);
                objectMap.put("closeExplain_dictText", closeExplainText);
            }

            if (orderStoreList.getCompletionTime() != null) {
                objectMap.put("completionTime", DateUtil.formatDateTime(orderStoreList.getCompletionTime()));
            } else {
                objectMap.put("completionTime", "");
            }


            if (orderStoreList.getEvaluateTime() != null) {
                objectMap.put("evaluateTime", DateUtil.formatDateTime(orderStoreList.getEvaluateTime()));
            } else {
                objectMap.put("evaluateTime", "");
            }
        } else
            //平台订单
            if (isPlatform.intValue() == 1) {
                OrderList orderList = iOrderListService.getById(id);
                //收货地址信息
                objectMap.put("consignee", orderList.getConsignee());
                objectMap.put("contactNumber", orderList.getContactNumber());
                objectMap.put("shippingAddress", orderList.getShippingAddress());
                objectMap.put("houseNumber", orderList.getHouseNumber());
                //留言
                objectMap.put("message", orderList.getMessage());
                //订单价格
                objectMap.put("goodsTotal", orderList.getGoodsTotal());//商品总价
                objectMap.put("shipFee", orderList.getShipFee());//运费
                objectMap.put("coupon", orderList.getDiscountOuponPrice());//优惠金额

                objectMap.put("memberDiscountPriceTotal", orderList.getMemberDiscountPriceTotal());
                objectMap.put("welfarePayments", orderList.getWelfarePayments());
                objectMap.put("welfarePaymentsPrice", orderList.getWelfarePaymentsPrice());
                objectMap.put("balance", orderList.getBalance());
                objectMap.put("payWelfarePayments", orderList.getPayWelfarePayments());//支付的积分
                objectMap.put("payWelfarePaymentsPrice", orderList.getPayWelfarePaymentsPrice());//约等于金额
                objectMap.put("giveWelfarePayments", orderList.getGiveWelfarePayments());
                objectMap.put("vipLowerTotal", orderList.getVipLowerTotal());
                objectMap.put("actualPayment", orderList.getActualPayment());
                objectMap.put("modePayment", orderList.getModePayment());//0:微信；1：支付宝；2：余额

                objectMap.put("status", orderList.getStatus());
                //获取支付记录
                if (StringUtils.isNotBlank(orderList.getSerialNumber())) {
                    PayOrderCarLog payOrderCarLog = iPayOrderCarLogService.getById(orderList.getSerialNumber());
                    objectMap.put("integral", payOrderCarLog.getIntegral());//积分数量
                    objectMap.put("integralPrice", payOrderCarLog.getIntegralPrice());//积分价值
                    objectMap.put("payPrice", payOrderCarLog.getPayPrice());//支付金额
                }

                //订单商品
                objectMap.put("goods", iOrderProviderGoodRecordService.getOrderProviderGoodRecordByOrderId(orderList.getId()));

                //订单基础信息
                objectMap.put("id", orderList.getId());
                objectMap.put("isPlatform", isPlatform);
                objectMap.put("orderNo", orderList.getOrderNo());
                objectMap.put("createTime", DateUtil.formatDateTime(orderList.getCreateTime()));
                //付款时间
                if (orderList.getPayTime() != null) {
                    objectMap.put("payTime", DateUtil.formatDateTime(orderList.getPayTime()));
                } else {
                    objectMap.put("payTime", "");
                }
                //发货时间
                if (orderList.getShipmentsTime() != null) {
                    objectMap.put("shipmentsTime", DateUtil.formatDateTime(orderList.getShipmentsTime()));
                } else {
                    objectMap.put("shipmentsTime", "");
                }

                if (orderList.getDeliveryTime() != null) {
                    objectMap.put("deliveryTime", DateUtil.formatDateTime(orderList.getDeliveryTime()));
                } else {
                    objectMap.put("deliveryTime", "");
                }

                if (orderList.getCloseTime() != null) {
                    objectMap.put("closeTime", DateUtil.formatDateTime(orderList.getCloseTime()));
                } else {
                    objectMap.put("closeTime", "");
                }

                // 添加关闭类型和关闭原因
                String closeType = orderList.getCloseType();
                String closeExplain = orderList.getCloseExplain();
                objectMap.put("closeType", closeType);
                objectMap.put("closeExplain", closeExplain);

                // 手动进行字典翻译
                if (closeType != null) {
                    String closeTypeText = iSysDictService.queryDictTextByKey("oder_close_type", closeType);
                    objectMap.put("closeType_dictText", closeTypeText);
                }

                if (closeExplain != null) {
                    String closeExplainText = iSysDictService.queryDictTextByKey("oder_close_explain", closeExplain);
                    objectMap.put("closeExplain_dictText", closeExplainText);
                }

                if (orderList.getCompletionTime() != null) {
                    objectMap.put("completionTime", DateUtil.formatDateTime(orderList.getCompletionTime()));
                } else {
                    objectMap.put("completionTime", "");
                }


                if (orderList.getEvaluateTime() != null) {
                    objectMap.put("evaluateTime", DateUtil.formatDateTime(orderList.getEvaluateTime()));
                } else {
                    objectMap.put("evaluateTime", "");
                }

                //免单专区商品是否免单


            } else {
                result.error500("isPlatform参数不正确请联系平台管理员！！！");
                return result;
            }

        result.setResult(objectMap);
        result.success("查询订单详情成功");
        return result;
    }


    /**
     * 待支付订单支付
     *
     * @param id
     * @param isPlatform
     * @param request
     * @return
     */
    @RequestMapping("unpaidOrderSubmit")
    @ResponseBody
    public Result<Map<String, Object>> unpaidOrderSubmit(String id, Integer isPlatform,
                                                         @RequestAttribute(value = "memberId", required = false) String memberId,
                                                         @RequestHeader(name = "softModel", required = false, defaultValue = "") String softModel,
                                                         HttpServletRequest request) {
        Result<Map<String, Object>> result = new Result<>();
        //参数判断
        if (StringUtils.isBlank(id)) {
            result.error500("订单id不能为空！！！");
            return result;
        }
        if (isPlatform == null) {
            result.error500("isPlatform是否平台类型参数不能为空！！！");
            return result;
        }


        //价格
        BigDecimal allTotalPrice = new BigDecimal(0);

        //将所有订单信息记录到支付日志
        Map<String, Object> payOrderLog = Maps.newHashMap();

        //店铺订单
        if (isPlatform.intValue() == 0) {
            List<String> storeGoodsList = Lists.newArrayList();
            OrderStoreList orderStoreList = iOrderStoreListService.getById(id);
            allTotalPrice = orderStoreList.getActualPayment();
            storeGoodsList.add(orderStoreList.getId());
            payOrderLog.put("storeGoods", storeGoodsList);
        } else
            //平台订单
            if (isPlatform.intValue() == 1) {

                OrderList orderList = iOrderListService.getById(id);
                allTotalPrice = orderList.getActualPayment();
                String orderType = orderList.getOrderType();
                if (!orderType.equals("3")) {
                    payOrderLog.put("goods", orderList.getId());
                }
                //免单专区
                if (orderType.equals("3")) {
                    payOrderLog.put("marketingFreeGoods", orderList.getId());
                }
            } else {
                result.error500("isPlatform参数不正确请联系平台管理员！！！");
                return result;
            }
        //支付
        Map<String, Object> objectMap = totalPayUtils.payOrder(allTotalPrice, payOrderLog, memberId, request, softModel);

        log.info("订单支付信息汇总：" + JSON.toJSONString(objectMap));
        result.setResult(objectMap);
        result.success("生成待支付订单成功");
        return result;
    }

    /**
     * 订单查询接口
     *
     * @param status
     * @param pageNo
     * @param pageSize
     * @param memberId
     * @return
     */
    @RequestMapping("orderList")
    @ResponseBody
    public Result<IPage<Map<String, Object>>> orderList(@RequestParam(value = "status",required = false) Integer status,
                                                        @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                        @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                                        @RequestAttribute(value = "memberId", required = false) String memberId) {
        Result<IPage<Map<String, Object>>> result = new Result<>();
        //组织查询参数
        Page<Map<String, Object>> page = new Page<Map<String, Object>>(pageNo, pageSize);
        Map<String, Object> paramObjectMap = Maps.newHashMap();
        paramObjectMap.put("memberId", memberId);
        paramObjectMap.put("status", status);
        //查询订单数据
        IPage<Map<String, Object>> orderListMapIPage = iOrderListService.getOrderListByMemberIdAndStatus(page, paramObjectMap);
        orderListMapIPage.getRecords().forEach(o -> {
            //给订单查询商品数据
            //平台商品
            if (o.get("isPlatform").toString().equals("1")) {
                o.put("goods", iOrderProviderGoodRecordService.getOrderProviderGoodRecordByOrderId(o.get("id").toString()));
            }
            //店铺商品
            if (o.get("isPlatform").toString().equals("0")) {
                o.put("goods", iOrderStoreGoodRecordService.getOrderStoreGoodRecordByOrderId(o.get("id").toString()));
            }
        });
        result.setResult(orderListMapIPage);
        result.success("订单列表查询成功");
        return result;
    }


    /**
     * 立即购买到确认订单
     *
     * @param goodId
     * @param specification
     * @param isPlatform
     * @param quantity
     * @return
     */
    @RequestMapping("promptlyAffirmOrder")
    @ResponseBody
    public Result<?> promptlyAffirmOrder(String goodId,
                                         @RequestParam(required = false) String orderJson,
                                         String specification,
                                         Integer isPlatform,
                                         Integer quantity,
                                         @RequestParam(value = "marketingGroupRecordId", defaultValue = "", required = false) String marketingGroupRecordId,
                                         @RequestParam(value = "marketingPrefectureId", defaultValue = "", required = false) String marketingPrefectureId,
                                         @RequestParam(value = "marketingFreeGoodListId", defaultValue = "", required = false) String marketingFreeGoodListId,
                                         @RequestParam(value = "marketingStoreGiftCardMemberListId", defaultValue = "", required = false) String marketingStoreGiftCardMemberListId,
                                         @RequestParam(value = "marketingRushGroupId", defaultValue = "", required = false) String marketingRushGroupId,
                                         @RequestParam(name = "marketingLeagueGoodListId", defaultValue = "", required = false) String marketingLeagueGoodListId,
                                         @RequestParam(name = "marketingStorePrefectureGoodId", defaultValue = "", required = false) String marketingStorePrefectureGoodId,
                                         @RequestParam(required = false) String memberShippingAddressId,
                                         @RequestAttribute(value = "memberId", required = false) String memberId,
                                         @RequestHeader(value = "tMemberId") String tMemberId,
                                         @RequestHeader(value = "shareDiscountRatio", required = false, defaultValue = "0") BigDecimal shareDiscountRatio,
                                         HttpServletRequest request) {
        Result<Map<String, Object>> result = new Result<>();
        //参数判断
        if (isPlatform == null) {
            result.error500("isPlatform是否平台类型参数不能为空！！！");
            return result;
        }

        if (StringUtils.isBlank(goodId)) {
            result.error500("goodId商品id不能为空！！！");
            return result;
        }

        if (quantity == null) {
            result.error500("quantity不能为空！！！");
            return result;
        }
        if (quantity.intValue() < 1) {
            result.error500("购买商品数必须大于等于1！！！");
            return result;
        }
        if (specification == null) {
            result.error500("specification不能为空！！！");
            return result;
        }
        log.info("推荐会员id！！！！"+tMemberId);
        //2024-08-29 02:02:10判断如果入参没有推荐会员那就那头部会员
        if (StringUtils.isBlank(tMemberId)){
            if (StringUtils.isNotBlank(request.getHeader("tMemberId"))){
                tMemberId = request.getHeader("tMemberId");
            }
        }

        // 校验分享折扣比例：只有在有分享人ID的情况下才允许使用分享折扣
        if (shareDiscountRatio != null && shareDiscountRatio.compareTo(BigDecimal.ZERO) > 0) {
            if (StringUtils.isBlank(tMemberId)) {
                result.error500("使用分享折扣必须有分享人ID");
                return result;
            }
        }
        if (StringUtils.isNotBlank(marketingPrefectureId)) {

            MemberList memberList = iMemberListService.getById(memberId);

            MarketingPrefecture marketingPrefecture = iMarketingPrefectureService.getById(marketingPrefectureId);
            if (StringUtils.indexOf(marketingPrefecture.getBuyerLimit(), memberList.getMemberType()) == -1) {
                if (memberList.getMemberType().equals("0")) {
                    result.error500("本商品普通会员不可购买");
                    return result;
                }
                if (memberList.getMemberType().equals("1")) {
                    result.error500("本商品VIP会员不可购买");
                    return result;
                }
            }
        }


        /*店铺专区*/
        if (StringUtils.isNotBlank(marketingStorePrefectureGoodId)) {
            if (!iMarketingStorePrefectureGiveService.ifBuy(memberId, marketingStorePrefectureGoodId)) {
                result.error500("本限购专区购买条件不达标或者周期内已经购买");
                return result;
            }
        }

        String backResult = iMemberShoppingCartService.addGoodToShoppingCart(isPlatform, goodId, specification, memberId, quantity, "0", marketingPrefectureId, marketingFreeGoodListId, marketingGroupRecordId, marketingStoreGiftCardMemberListId, marketingRushGroupId, marketingLeagueGoodListId, marketingStorePrefectureGoodId, tMemberId);

        if (backResult.indexOf("SUCCESS") == -1) {
            result.error500(backResult);
            return result;
        }
        return affirmOrder(StringUtils.substringAfter(backResult, "="), memberShippingAddressId, orderJson, memberId, tMemberId, shareDiscountRatio);
    }


    /**
     * 确认订单接口
     *
     * @return
     */
    @RequestMapping("affirmOrder")
    @ResponseBody
    public Result<?> affirmOrder(String ids,
                                 String memberShippingAddressId,
                                 @RequestParam(required = false) String orderJson,
                                 @RequestAttribute(value = "memberId", required = false) String memberId,
                                 @RequestHeader(value = "tMemberId", required = false) String tMemberId,
                                 @RequestHeader(value = "shareDiscountRatio", required = false, defaultValue = "0") BigDecimal shareDiscountRatio) {
        Result<Map<String, Object>> result = new Result<>();
        Map<String, Object> objectMap = Maps.newHashMap();

        //总运费
        BigDecimal allFreight = new BigDecimal(0);
        //件数
        BigDecimal allNumberUnits = new BigDecimal(0);
        //价格
        BigDecimal allTotalPrice = new BigDecimal(0);
        //推荐者分享折扣总金额
        BigDecimal totalShareDiscountAmount = new BigDecimal(0);
        //推荐人分销佣金总金额
        BigDecimal totalCommissionAmount = new BigDecimal(0);
        //总的店铺补助金抵扣金额
        BigDecimal totalStoreSubsidyDeduction = new BigDecimal(0);

        if (StringUtils.isBlank(ids)) {
            result.error500("ids不能为空！！！");
            return result;
        }

        // 推荐人信息对象
        Map<String, Object> promoterInfo = Maps.newHashMap();
        // 判断分享人是否为助梦家且分享折扣比例是否大于0
        boolean enableShareDiscount = false;
        String referrerId = null;
        MemberList referrer = null;

        // 判断当前会员是否有推荐人
        MemberList memberList = iMemberListService.getById(memberId);

        // 优先使用会员的绑定推荐人
        if (StringUtils.isNotBlank(memberList.getPromoter())) {
            referrerId = memberList.getPromoter();
        } else if (StringUtils.isNotBlank(tMemberId)) {
            // 如果没有绑定推荐人，使用请求头中的分享人id
            referrerId = tMemberId;
        }

        // 如果找到推荐人ID，判断其是否为助梦家
        if (StringUtils.isNotBlank(referrerId)) {
            referrer = iMemberListService.getById(referrerId);
            if (referrer != null && "1".equals(referrer.getIsLoveAmbassador())) {
                enableShareDiscount = true;
                log.info("启用分享折扣逻辑，推荐人ID: {}, 分享折扣比例: {}", referrerId, shareDiscountRatio);

                // 设置推荐人信息
                promoterInfo.put("id", referrer.getId());
                promoterInfo.put("nickname", referrer.getNickName());
                promoterInfo.put("avatar", referrer.getHeadPortrait());
                promoterInfo.put("phone", referrer.getPhone());
                promoterInfo.put("isLoveAmbassador", referrer.getIsLoveAmbassador());
            } else {
                log.warn("推荐人不是爱心大使，不能使用分享折扣");
                // 强制设置分享折扣比例为0
                shareDiscountRatio = BigDecimal.ZERO;
            }
        } else {
            log.warn("没有找到推荐人ID，不能使用分享折扣");
            // 强制设置分享折扣比例为0
            shareDiscountRatio = BigDecimal.ZERO;
        }

        //查询购物车商品
        Collection<MemberShoppingCart> memberShoppingCarts = iMemberShoppingCartService.listByIds(StrUtil.split(ids, StrUtil.C_COMMA));

        //商品归类处理
        Map<String, Object> stringObjectMap = iGoodListService.getCarGoodByMemberId(CollUtil.newArrayList(memberShoppingCarts), memberId);

        //店铺商品
        List<Map<String, Object>> storeGoods = (List<Map<String, Object>>) stringObjectMap.get("storeGoods");
        //平台商品
        List<Map<String, Object>> goods = (List<Map<String, Object>>) stringObjectMap.get("goods");
        //无效商品
        List<Map<String, Object>> disableGoods = (List<Map<String, Object>>) stringObjectMap.get("disableGoods");

        if (!disableGoods.isEmpty()) {
            objectMap.put("disableGoods", disableGoods);
            result.setResult(objectMap);
            result.error500("您提交的订单商品已售馨!!!");
            return result;
        }

        // 校验分享折扣使用条件
        if (shareDiscountRatio != null && shareDiscountRatio.compareTo(BigDecimal.ZERO) > 0) {
            // 校验是否为单店铺单商品场景
            boolean isSingleStoreAndSingleGood = false;

            // 检查是否只有一个店铺
            if (storeGoods.size() == 1) {
                // 检查店铺是否只有一个商品
                Map<String, Object> store = storeGoods.get(0);
                List<Map<String, Object>> myStoreGoods = (List<Map<String, Object>>) store.get("myStoreGoods");
                if (myStoreGoods != null && myStoreGoods.size() == 1) {
                    isSingleStoreAndSingleGood = true;
                }
            }

            if (!isSingleStoreAndSingleGood) {
                log.warn("分享折扣只能用于单店铺单商品场景，当前商品数量不符合要求");
                // 强制设置分享折扣比例为0
                shareDiscountRatio = BigDecimal.ZERO;
                throw new JeecgBootException("分享折扣只能用于单店铺单商品场景");
            }
        }

        MemberShippingAddress memberShippingAddress = null;

        //收货地址写入
        if (StringUtils.isBlank(memberShippingAddressId)) {
            memberShippingAddress = iMemberShippingAddressService.getOne(new LambdaQueryWrapper<MemberShippingAddress>()
                    .eq(MemberShippingAddress::getMemberListId, memberId)
                    .eq(MemberShippingAddress::getIsDefault, "1"));
        } else {
            memberShippingAddress = iMemberShippingAddressService.getById(memberShippingAddressId);
        }

        //收货地址数据
        Map<String, Object> memberShippingAddressMaps = Maps.newHashMap();
        if (memberShippingAddress != null) {
            memberShippingAddressMaps.put("isDefault", memberShippingAddress.getIsDefault());
            memberShippingAddressMaps.put("phone", memberShippingAddress.getPhone());
            memberShippingAddressMaps.put("houseNumber", memberShippingAddress.getHouseNumber());
            memberShippingAddressMaps.put("areaAddress", memberShippingAddress.getAreaAddress());
            memberShippingAddressMaps.put("id", memberShippingAddress.getId());
            memberShippingAddressMaps.put("linkman", memberShippingAddress.getLinkman());
            memberShippingAddressMaps.put("sysAreaId", memberShippingAddress.getSysAreaId());
            memberShippingAddressMaps.put("areaExplan", memberShippingAddress.getAreaExplan());
            memberShippingAddressMaps.put("areaExplanIds", memberShippingAddress.getAreaExplanIds());
        }

        objectMap.put("memberShippingAddress", memberShippingAddressMaps);

        //店铺订单，生成确认订单数据
        if (!storeGoods.isEmpty()) {
            for (Map<String, Object> s : storeGoods) {
                String storeId = Convert.toStr(s.get("id")); //店铺ID

                JSONObject jsonGoods = null;
                if (StrUtil.isNotBlank(orderJson)) {
                    //解析订单json
                    JSONObject orderJsonObject = JSON.parseObject(orderJson);
                    JSONArray storeGoods1 = orderJsonObject.getJSONArray("storeGoods");

                    for (Object s1 : storeGoods1) {
                        JSONObject ss = (JSONObject) s1;
                        if (StrUtil.equals(ss.getString("id"), storeId)) {
                            jsonGoods = ss;
                            break;
                        }
                    }
                }

                StoreManage storeManage = storeManageService.getStoreManageBySysUserId(storeId);
                if (storeManage == null) {
                    throw new JeecgBootException("店铺信息对象为空，请检查 orderJson 参数！");
                }
//                if (jsonGoods == null) {
//                    throw new JeecgBootException("店铺信息对象为空，请检查 orderJson 参数！");
//                }
                String distribution = "0";
                String storeSysUserId = null;
                if (jsonGoods != null) {
                    distribution = jsonGoods.getString("distribution");
                    storeSysUserId = jsonGoods.getString("id");
                }
                List<Map<String, Object>> myStoreGoods = (List<Map<String, Object>>) s.get("myStoreGoods");

                //总运费
                BigDecimal freight = new BigDecimal(0);
                //件数
                BigDecimal numberUnits = new BigDecimal(0);
                //价格
                BigDecimal totalPrice = new BigDecimal(0);
                //店铺商品分享折扣金额
                BigDecimal storeShareDiscountAmount = new BigDecimal(0);
                //店铺商品推荐人佣金金额
                BigDecimal storeCommissionAmount = new BigDecimal(0);

                // 如果有推荐人且是爱心大使，提前获取店铺分销设置
                MarketingStoreDistributionSetting distributionSetting = null;
                if (referrer != null && "1".equals(referrer.getIsLoveAmbassador())) {
                    // 获取店铺分销设置
                    distributionSetting = iMarketingStoreDistributionSettingService.getOne(
                        new LambdaQueryWrapper<MarketingStoreDistributionSetting>()
                            .eq(MarketingStoreDistributionSetting::getSysUserId, storeId)
                            .eq(MarketingStoreDistributionSetting::getDelFlag, "0")
                    );
                }

                for (Map<String, Object> my : myStoreGoods) {
                    String goodId = Convert.toStr(my.get("goodId"));
                    //计算总件数
                    numberUnits = numberUnits.add((BigDecimal) my.get("quantity"));

                    //计算商品价格
                    BigDecimal price = (BigDecimal) my.get("price");
                    BigDecimal quantity = (BigDecimal) my.get("quantity");
                    BigDecimal itemTotalPrice = price.multiply(quantity);

                    // 计算分享折扣金额 - 基于原始商品总价
                    BigDecimal itemShareDiscountAmount = new BigDecimal(0);
                    if (enableShareDiscount && shareDiscountRatio != null && shareDiscountRatio.compareTo(BigDecimal.ZERO) > 0) {
                        itemShareDiscountAmount = itemTotalPrice.multiply(shareDiscountRatio.divide(new BigDecimal(100), 2, RoundingMode.HALF_UP));
                        storeShareDiscountAmount = storeShareDiscountAmount.add(itemShareDiscountAmount);

                        // 添加分享折扣金额到商品信息中
                        my.put("shareDiscountAmount", itemShareDiscountAmount);
                    } else {
                        my.put("shareDiscountAmount", BigDecimal.ZERO);
                    }

                    //计算总价格(原价)
                    totalPrice = totalPrice.add(itemTotalPrice);
                    my.put("totalPrice", itemTotalPrice);

                    // 计算推荐人佣金（基于原始商品总价计算）
                    if (referrer != null && "1".equals(referrer.getIsLoveAmbassador())) {
                        // 获取商品信息和分佣比例
                        GoodStoreList goodStoreList = iGoodStoreListService.getById(goodId);
                        BigDecimal shareCommissionRate = null;
                        if (goodStoreList != null) {
                            shareCommissionRate = goodStoreList.getShareCommissionRate();
                        }

                        if (shareCommissionRate == null || shareCommissionRate.compareTo(BigDecimal.ZERO) <= 0) {
                            if (distributionSetting != null) {
                                shareCommissionRate = distributionSetting.getCommonFirst();
                            }
                        }

                        // 计算实际分佣比例 - 分享折扣是从分享佣金中得来的
                        BigDecimal actualCommissionRate = shareCommissionRate;
                        if (shareCommissionRate != null && enableShareDiscount && shareDiscountRatio.compareTo(BigDecimal.ZERO) > 0) {
                            // 实际分佣比例 = 原分佣比例 - 分享折扣比例
                            actualCommissionRate = shareCommissionRate.subtract(shareDiscountRatio);
                            // 确保分佣比例不小于0
                            if (actualCommissionRate.compareTo(BigDecimal.ZERO) < 0) {
                                actualCommissionRate = BigDecimal.ZERO;
                            }
                        }

                        // 计算商品分销佣金 - 基于原始商品总价
                        if (actualCommissionRate != null && actualCommissionRate.compareTo(BigDecimal.ZERO) > 0) {
                            BigDecimal itemCommission = itemTotalPrice.multiply(actualCommissionRate).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP);
                            storeCommissionAmount = storeCommissionAmount.add(itemCommission);

                            // 添加分销佣金到商品信息中
                            my.put("commissionAmount", itemCommission);
                            // 添加实际分佣比例到商品信息中
                            my.put("actualCommissionRate", actualCommissionRate);
                        } else {
                            my.put("commissionAmount", BigDecimal.ZERO);
                            my.put("actualCommissionRate", BigDecimal.ZERO);
                        }
                    } else {
                        // 如果没有推荐人或不是爱心大使，佣金为0
                        my.put("commissionAmount", BigDecimal.ZERO);
                        my.put("actualCommissionRate", BigDecimal.ZERO);
                    }

                    if (memberShippingAddress != null) {
                        //  2023/9/11 不同配送方式，判断是否配送
                        boolean opinion = true;
                        if (StrUtil.equals(distribution, "0")) {
                            opinion = iStoreTemplateService.opinionCalculate(my, memberShippingAddress.getSysAreaId());
                        } else if (StrUtil.equals(distribution, "1")) {
//                            opinion = true;
                        } else if (StrUtil.equals(distribution, "2")) {
//                            opinion = true;
                        }

                        if (opinion) {
                            my.put("opinion", "1");
                        } else {
                            my.put("opinion", "0");
                        }
                    } else {
                        my.put("opinion", "1");
                    }
                }

                //有地址计算运费
                if (memberShippingAddress != null) {
                    if (StrUtil.equals(distribution, "0")) {
                        freight = iStoreTemplateService.calculateFreight(myStoreGoods, memberShippingAddress.getSysAreaId());
                    }
                }

                // 计算店铺补助金抵扣
                BigDecimal storeSubsidyDeduction = new BigDecimal(0);
                BigDecimal availableStoreSubsidyAmount = new BigDecimal(0);
                String nearestExpireTime = "";
                
                try {
                    // 获取用户在该店铺的补助金信息
                    Map<String, Object> subsidyInfo = iMemberListService.getMemberStoreSubsidyInfo(memberId, storeManage.getId());
                    if (subsidyInfo != null) {
                        availableStoreSubsidyAmount = (BigDecimal) subsidyInfo.get("totalAmount");
                        nearestExpireTime = (String) subsidyInfo.get("nearestExpireTime");
                        
                        if (availableStoreSubsidyAmount != null && availableStoreSubsidyAmount.compareTo(BigDecimal.ZERO) > 0) {
                            // 正确的抵扣顺序：商品原价 → 分享折扣 → 补助金抵扣
                            // 先计算分享折扣后的商品金额（分享折扣是商品价格优惠，优先扣除）
                            BigDecimal afterShareDiscountAmount = totalPrice.subtract(storeShareDiscountAmount);
                            
                            // 店铺补助金对折扣后的金额进行抵扣：取折扣后金额和可用补助金的较小值
                            storeSubsidyDeduction = afterShareDiscountAmount.min(availableStoreSubsidyAmount);
                            
                            // 确保抵扣金额不为负数
                            if (storeSubsidyDeduction.compareTo(BigDecimal.ZERO) < 0) {
                                storeSubsidyDeduction = BigDecimal.ZERO;
                            }
                            
                            log.info("店铺[{}]补助金抵扣计算：商品原价={}, 分享折扣={}, 折扣后金额={}, 可用补助金={}, 补助金抵扣={}", 
                                storeId, totalPrice, storeShareDiscountAmount, afterShareDiscountAmount, availableStoreSubsidyAmount, storeSubsidyDeduction);
                        }
                    }
                } catch (Exception e) {
                    log.error("计算店铺[{}]补助金抵扣失败", storeId, e);
                    // 补助金计算失败不影响订单确认，设置为0
                    storeSubsidyDeduction = BigDecimal.ZERO;
                    availableStoreSubsidyAmount = BigDecimal.ZERO;
                }

                // 计算最终价格：商品原价 - 分享折扣 - 补助金抵扣
                BigDecimal discountedPrice = totalPrice.subtract(storeShareDiscountAmount).subtract(storeSubsidyDeduction);

                BigDecimal finalPrice = discountedPrice.add(freight);

                allNumberUnits = allNumberUnits.add(numberUnits);
                allFreight = allFreight.add(freight);
                allTotalPrice = allTotalPrice.add(totalPrice);
                totalShareDiscountAmount = totalShareDiscountAmount.add(storeShareDiscountAmount);
                totalCommissionAmount = totalCommissionAmount.add(storeCommissionAmount);
                totalStoreSubsidyDeduction = totalStoreSubsidyDeduction.add(storeSubsidyDeduction);

                s.put("totalPrice", finalPrice);
                s.put("originalPrice", totalPrice);
                s.put("numberUnits", numberUnits);
                s.put("freight", freight);
                s.put("storeType", storeManage.getStoreType());
                // 将分享折扣和佣金信息添加到店铺对象中
                s.put("shareDiscountRatio", shareDiscountRatio);
                s.put("shareDiscountAmount", storeShareDiscountAmount);
                s.put("commissionAmount", storeCommissionAmount);
                s.put("promoterId", referrerId);
                s.put("promoterInfo", promoterInfo);
                
                // 添加店铺补助金相关信息
                s.put("availableStoreSubsidyAmount", availableStoreSubsidyAmount);
                s.put("storeSubsidyDeduction", storeSubsidyDeduction);
                s.put("nearestExpireTime", nearestExpireTime);
                s.put("storeSubsidyAfterPrice", discountedPrice.add(freight)); // 补助金抵扣后的支付金额
            }
        }

        Map<String, Object> goodsMap = Maps.newHashMap();

        //平台订单
        if (!goods.isEmpty()) {
            //总运费
            BigDecimal freight = new BigDecimal(0);
            //件数
            BigDecimal numberUnits = new BigDecimal(0);
            //价格
            BigDecimal totalPrice = new BigDecimal(0);


            BigDecimal welfarePayments = new BigDecimal(0);

            BigDecimal giveWelfarePayments = new BigDecimal(0);

            //会员直降总额
            BigDecimal vipLowerTotal = new BigDecimal(0);
            //会员等级折扣金额
            BigDecimal memberDiscountPrice = new BigDecimal(0);
            BigDecimal integralValue = iMarketingWelfarePaymentsSettingService.getIntegralValue();
            List<String> goodsList = Lists.newArrayList();
            Map<String, Object> goodPrices = Maps.newHashMap();
            String memberGradeContent = "";
            //拆分供应商
            for (Map<String, Object> g : goods) {
                //计算总件数
                numberUnits = numberUnits.add((BigDecimal) g.get("quantity"));
                if (g.containsKey("memberDiscountPrice")) {
                    //会员等级折扣金额 (折扣金额*件数  累加)
                    memberDiscountPrice = memberDiscountPrice.add(((BigDecimal) g.get("memberDiscountPrice")).multiply((BigDecimal) g.get("quantity")));
                    if (StringUtils.isBlank("memberGradeContent")) {
                        if (g.containsKey("memberGradeContent")) {
                            memberGradeContent = g.get("memberGradeContent").toString();
                        }
                    }
                }

                //计算总价格
                totalPrice = totalPrice.add(((BigDecimal) g.get("price")).multiply((BigDecimal) g.get("quantity")));
                //商品id对于的金额
                if (goodPrices.get(g.get("goodId").toString()) != null) {
                    goodPrices.put(g.get("goodId").toString(), ((BigDecimal) goodPrices.get(g.get("goodId").toString())).add(((BigDecimal) g.get("price")).multiply((BigDecimal) g.get("quantity"))));
                } else {
                    goodPrices.put(g.get("goodId").toString(), ((BigDecimal) g.get("price")).multiply((BigDecimal) g.get("quantity")));
                }
                goodsList.add(g.get("goodId").toString());
                //判断专区商品
                if (g.get("marketingPrefectureId") != null) {
                    //获取专区商品
                    MarketingPrefectureGood marketingPrefectureGood = iMarketingPrefectureGoodService.getOne(new LambdaQueryWrapper<MarketingPrefectureGood>()
                            .eq(MarketingPrefectureGood::getMarketingPrefectureId, g.get("marketingPrefectureId").toString())
                            .eq(MarketingPrefectureGood::getGoodListId, g.get("goodId").toString()));

                    //获取专区规格
                    MarketingPrefectureGoodSpecification marketingPrefectureGoodSpecification = iMarketingPrefectureGoodSpecificationService.getOne(new LambdaQueryWrapper<MarketingPrefectureGoodSpecification>()
                            .eq(MarketingPrefectureGoodSpecification::getMarketingPrefectureGoodId, marketingPrefectureGood.getId())
                            .eq(MarketingPrefectureGoodSpecification::getGoodSpecificationId, g.get("goodSpecificationId").toString()));
                    //可抵用福利金的计划
                    BigDecimal welfareProportion = marketingPrefectureGoodSpecification.getWelfareProportion();
                    if (marketingPrefectureGoodSpecification.getIsWelfare().equals("1")) {
                        welfareProportion = new BigDecimal(100);
                    }
                    BigDecimal welfareProportionPrice = null;

                    GoodSpecification goodSpecification = iGoodSpecificationService.getById(g.get("goodSpecificationId").toString());

                    if (marketingPrefectureGoodSpecification.getIsWelfare().equals("3")) {
                        welfareProportionPrice = marketingPrefectureGoodSpecification.getPrefecturePrice().subtract(goodSpecification.getSupplyPrice()).multiply(welfareProportion.divide(new BigDecimal(100))).multiply((BigDecimal) g.get("quantity")).divide(integralValue, 2, RoundingMode.DOWN);
                    } else {
                        welfareProportionPrice = marketingPrefectureGoodSpecification.getPrefecturePrice().multiply(welfareProportion.divide(new BigDecimal(100))).multiply((BigDecimal) g.get("quantity")).divide(integralValue, 2, RoundingMode.DOWN);
                    }

                    //判断专区会员直降
                    MarketingPrefecture marketingPrefecture = iMarketingPrefectureService.getById(g.get("marketingPrefectureId").toString());


                    if (!marketingPrefecture.getStatus().equals("1")) {
                        return Result.error("专区已停用");
                    }

                    //判断专区和礼包的关系判断是否团队成员
                    if (marketingPrefecture.getIsDesignation().equals("1")) {
                        long giftCount = iMarketingGiftBagRecordService.count(new LambdaQueryWrapper<MarketingGiftBagRecord>().eq(MarketingGiftBagRecord::getMemberListId, memberId));
                        if (giftCount == 0) {
                            return Result.error("你没有权限购买，请先购买礼包");
                        }
                    }

                    //支持会员免福利金
                    if (marketingPrefecture.getIsVipLower().equals("1") && memberList.getMemberType().equals("1")) {
                        vipLowerTotal = vipLowerTotal.add(welfareProportionPrice).multiply(integralValue);
                        welfareProportionPrice = new BigDecimal(0);
                    }

                    welfarePayments = welfarePayments.add(welfareProportionPrice);
                    //赠送福利金的计算
                    BigDecimal giveWelfareProportion = marketingPrefectureGoodSpecification.getGiveWelfareProportion();
                    giveWelfarePayments = giveWelfarePayments.add(marketingPrefectureGoodSpecification.getPrefecturePrice().multiply(giveWelfareProportion.divide(new BigDecimal(100))).multiply((BigDecimal) g.get("quantity"))).divide(integralValue, 2, RoundingMode.DOWN);

                }

                if (memberShippingAddress != null) {


                    //判断是否配送
                    boolean opinion = iProviderTemplateService.opinionCalculate(g, memberShippingAddress.getSysAreaId());


                    if (opinion) {
                        g.put("opinion", "1");
                    } else {
                        g.put("opinion", "0");
                    }
                } else {
                    g.put("opinion", "1");
                }
            }

            //抵扣福利金的总数与会员福利金的情况进行限制
            if (iMemberListService.getById(memberId).getWelfarePayments().subtract(welfarePayments).doubleValue() < 0) {
                welfarePayments = iMemberListService.getById(memberId).getWelfarePayments();
            }

            //获取优惠券
            Map<String, Object> paramMap = Maps.newHashMap();

            paramMap.put("goodIds", StringUtils.join(goodsList, ","));
            paramMap.put("memberId", memberId);
            List<Map<String, Object>> marketingDiscountCouponMaps = iMarketingDiscountCouponService.findMarketingDiscountCouponByGoodIds(paramMap);

            //获取过滤的优惠券列表
            List<Map<String, Object>> discounts = Lists.newArrayList();
            for (Map<String, Object> mdc : marketingDiscountCouponMaps) {
//                if (StrUtil.equals(Convert.toStr(mdc.get("isNomal")),"2")) {
//                    discounts.add(mdc);
//                    continue;
//                }
                if (mdc.get("isThreshold").toString().equals("0")) {
                    mdc.put("logoAddr", String.valueOf(mdc.get("logoAddr")));
                    discounts.add(mdc);
                } else {
                    List<String> goodIds = Arrays.asList(StringUtils.split(mdc.get("goodIds").toString(), ","));
                    BigDecimal totalGoodsPrice = new BigDecimal(0);
                    for (String g : goodIds) {
                        totalGoodsPrice = totalGoodsPrice.add((BigDecimal) goodPrices.get(g));
                    }

                    BigDecimal completely = new BigDecimal(mdc.get("completely").toString());
                    log.info("completely:" + completely.doubleValue() + "====totalGoodsPrice:" + totalGoodsPrice.doubleValue());
                    if (totalGoodsPrice.doubleValue() >= completely.doubleValue()) {
                        discounts.add(mdc);
                    }
                }
            }

            //有地址计算运费
            if (memberShippingAddress != null) {
                freight = iProviderTemplateService.calculateFreight(goods, memberShippingAddress.getSysAreaId());
            }
            allNumberUnits = allNumberUnits.add(numberUnits);
            allFreight = allFreight.add(freight);
            totalPrice = totalPrice.subtract(welfarePayments.multiply(integralValue)).subtract(vipLowerTotal).subtract(memberDiscountPrice).setScale(2, RoundingMode.HALF_UP);
            allTotalPrice = allTotalPrice.add(totalPrice);
            totalPrice = totalPrice.add(freight);
            goodsMap.put("discounts", discounts);
            goodsMap.put("welfarePayments", welfarePayments);
            goodsMap.put("welfarePaymentsPrice", welfarePayments.multiply(integralValue).setScale(2, RoundingMode.HALF_UP));
            goodsMap.put("giveWelfarePayments", giveWelfarePayments);
            goodsMap.put("vipLowerTotal", vipLowerTotal);
            goodsMap.put("totalPrice", totalPrice);
            goodsMap.put("numberUnits", numberUnits);
            goodsMap.put("freight", freight);
            goodsMap.put("goods", goods);
            //会员等级折扣金额
            goodsMap.put("memberDiscountPrice", memberDiscountPrice);
            //会员等级信息
            goodsMap.put("memberGradeContent", memberGradeContent);

        }

        Map<String, Object> marketingFreeGoodsMap = Maps.newHashMap();

        objectMap.put("allFreight", allFreight);
        objectMap.put("allNumberUnits", allNumberUnits);
        objectMap.put("allTotalPrice", allTotalPrice.add(allFreight));
        objectMap.put("storeGoods", storeGoods);
        objectMap.put("goods", goodsMap);
        objectMap.put("marketingFreeGoods", marketingFreeGoodsMap);

        // 添加分享折扣总金额
        objectMap.put("totalShareDiscountAmount", totalShareDiscountAmount);
        objectMap.put("shareDiscountRatio", shareDiscountRatio);
        objectMap.put("enableShareDiscount", enableShareDiscount);

        // 添加推荐人信息和佣金
        objectMap.put("promoterInfo", promoterInfo);
        objectMap.put("totalCommissionAmount", totalCommissionAmount);
        
        // 添加店铺补助金抵扣总金额
        objectMap.put("totalStoreSubsidyDeduction", totalStoreSubsidyDeduction);

        result.setResult(objectMap);
        result.success("确认订单成功");
        return result;
    }


    /**
     * 提交订单并支付
     *
     * @param request
     * @return
     */
    @RequestMapping("submitOrder")
    @ResponseBody
    public Result<Map<String, Object>> submitOrder(String ids,
                                                   String memberShippingAddressId,
                                                   String orderJson,
                                                   @RequestHeader(defaultValue = "") String sysUserId,
                                                   @RequestHeader(defaultValue = "") String longitude,
                                                   @RequestHeader(defaultValue = "") String latitude,
                                                   @RequestHeader(name = "softModel", required = false, defaultValue = "") String softModel,
                                                   @RequestAttribute(value = "memberId", required = false) String memberId,
                                                   @RequestHeader(value = "tMemberId", required = false) String tMemberId,
                                                   @RequestHeader(value = "shareDiscountRatio", required = false, defaultValue = "0") BigDecimal shareDiscountRatio,
                                                   HttpServletRequest request) {
        Result<Map<String, Object>> result = new Result<>();
        Map<String, Object> objectMap = Maps.newHashMap();
        //价格
        BigDecimal allTotalPrice = new BigDecimal(0);


        if (StringUtils.isBlank(ids)) {
            result.error500("ids不能为空！！！");
            return result;
        }

        if (StringUtils.isBlank(orderJson)) {
            result.error500("订单json不允许为空");
            return result;
        }

        // 判断分享人是否为助梦家且分享折扣比例是否大于0
        boolean enableShareDiscount = false;
        String referrerId = null;
        MemberList referrer = null;

        log.info("确认订单，购物车id:{},orderJson: {}", ids, orderJson);
        //查询购物车商品
        Collection<MemberShoppingCart> memberShoppingCarts = iMemberShoppingCartService.listByIds(StrUtil.split(ids, StrUtil.C_COMMA));

        //商品归类处理
        Map<String, Object> stringObjectMap = iGoodListService.getCarGoodByMemberId(CollUtil.newArrayList(memberShoppingCarts), memberId);

        //店铺商品
        List<Map<String, Object>> storeGoods = (List<Map<String, Object>>) stringObjectMap.get("storeGoods");
        //平台商品
        List<Map<String, Object>> goods = (List<Map<String, Object>>) stringObjectMap.get("goods");
        //无效商品
        List<Map<String, Object>> disableGoods = (List<Map<String, Object>>) stringObjectMap.get("disableGoods");

        if (!disableGoods.isEmpty()) {
            objectMap.put("disableGoods", disableGoods);
            result.error500("您提交的订单商品已售馨!!!");
            return result;
        }

        // 校验分享折扣使用条件
        if (shareDiscountRatio != null && shareDiscountRatio.compareTo(BigDecimal.ZERO) > 0) {
            // 校验是否为单店铺单商品场景
            boolean isSingleStoreAndSingleGood = false;

            // 检查是否只有一个店铺
            if (storeGoods.size() == 1) {
                // 检查店铺是否只有一个商品
                Map<String, Object> store = storeGoods.get(0);
                List<Map<String, Object>> myStoreGoods = (List<Map<String, Object>>) store.get("myStoreGoods");
                if (myStoreGoods != null && myStoreGoods.size() == 1) {
                    isSingleStoreAndSingleGood = true;
                }
            }

            if (!isSingleStoreAndSingleGood) {
                log.warn("提交订单：分享折扣只能用于单店铺单商品场景，当前商品数量不符合要求");
                // 强制设置分享折扣比例为0
                shareDiscountRatio = BigDecimal.ZERO;
                throw new JeecgBootException("分享折扣只能用于单店铺单商品场景");
            } else {
                // 判断当前会员是否有推荐人
                MemberList memberList = iMemberListService.getById(memberId);

                // 优先使用会员的绑定推荐人
                if (StringUtils.isNotBlank(memberList.getPromoter())) {
                    referrerId = memberList.getPromoter();
                } else if (StringUtils.isNotBlank(tMemberId)) {
                    // 如果没有绑定推荐人，使用请求头中的分享人id
                    referrerId = tMemberId;
                }

                // 如果找到推荐人ID，判断其是否为助梦家
                if (StringUtils.isNotBlank(referrerId)) {
                    referrer = iMemberListService.getById(referrerId);
                    if (referrer != null && "1".equals(referrer.getIsLoveAmbassador())) {
                        enableShareDiscount = true;
                        log.info("提交订单：启用分享折扣逻辑，推荐人ID: {}, 分享折扣比例: {}", referrerId, shareDiscountRatio);
                    } else {
                        log.warn("提交订单：推荐人不是爱心大使，不能使用分享折扣");
                        // 强制设置分享折扣比例为0
                        shareDiscountRatio = BigDecimal.ZERO;
                    }
                } else {
                    log.warn("提交订单：没有找到推荐人ID，不能使用分享折扣");
                    // 强制设置分享折扣比例为0
                    shareDiscountRatio = BigDecimal.ZERO;
                }
            }
        }

        //查询收货地址
        MemberShippingAddress memberShippingAddress = iMemberShippingAddressService.getById(memberShippingAddressId);


        //将所有订单信息记录到支付日志
        Map<String, Object> payOrderLog = Maps.newHashMap();

        //店铺订单，进行订单处理生成待支付订单
        if (!storeGoods.isEmpty()) {
            List<String> storeGoodsList = Lists.newArrayList();
            for (Map<String, Object> s : storeGoods) {
                // 计算分享折扣和分销佣金
                if (enableShareDiscount) {
                    s.put("shareDiscountRatio", shareDiscountRatio);
                    s.put("promoterId", referrerId);
                }

                OrderStoreList orderStoreList = iOrderStoreListService.submitOrderStoreGoods(s, memberId, orderJson, memberShippingAddress, longitude, latitude, tMemberId);
                allTotalPrice = allTotalPrice.add(orderStoreList.getActualPayment());
                storeGoodsList.add(orderStoreList.getId());
            }
            payOrderLog.put("storeGoods", storeGoodsList);
        }


        //平台订单
        if (!goods.isEmpty()) {
            if (memberShippingAddress == null) {
                result.error500("收货地址不存在！！！");
                return result;
            }
            OrderList orderList = iOrderListService.submitOrderGoods(goods, memberId, orderJson, memberShippingAddress, sysUserId, 0, longitude, latitude);
            allTotalPrice = allTotalPrice.add(orderList.getActualPayment());
            payOrderLog.put("goods", orderList.getId());
        }
        //支付
        objectMap.putAll(totalPayUtils.payOrder(allTotalPrice, payOrderLog, memberId, request, softModel));
        //删除购物车商品
        List<String> mIds = memberShoppingCarts.stream().map(MemberShoppingCart::getId).collect(Collectors.toList());
        //删除购物车
        if (!mIds.isEmpty()) {
            iMemberShoppingCartService.removeByIds(mIds);
        }

        log.info("订单支付信息汇总：" + JSON.toJSONString(objectMap));

        result.setResult(objectMap);
        result.success("生成待支付订单成功");
        return result;
    }

    /**
     * 待支付订单计时器
     *
     * @param orderId
     * @param isPlatform
     * @return
     */
    @RequestMapping("prepaidOrderTimer")
    @ResponseBody
    public Result<Map<String, Object>> prepaidOrderTimer(String orderId, Integer isPlatform) {
        Result<Map<String, Object>> result = new Result<>();
        Map<String, Object> objectMap = Maps.newHashMap();
        //参数判断
        if (isPlatform == null) {
            result.error500("isPlatform是否平台类型参数不能为空！！！");
            return result;
        }

        if (StringUtils.isBlank(orderId)) {
            result.error500("orderId订单id不能为空！！！");
            return result;
        }
        //获取倒计时时间
        String timer = iOrderListService.prepaidOrderTimer(orderId, isPlatform);
        if (StringUtils.isBlank(timer)) {
            result.error500("未找到订单倒计时数据!");
            return result;
        }
        objectMap.put("timer", timer);
        result.setResult(objectMap);
        result.success("请求成功");
        return result;

    }


    /**
     * 确认收货订单计时器
     *
     * @param orderId
     * @param isPlatform
     * @return
     */
    @RequestMapping("confirmReceiptTimer")
    @ResponseBody
    public Result<Map<String, Object>> confirmReceiptTimer(String orderId, Integer isPlatform) {
        Result<Map<String, Object>> result = new Result<>();
        Map<String, Object> objectMap = Maps.newHashMap();
        //参数判断
        if (isPlatform == null) {
            result.error500("isPlatform是否平台类型参数不能为空！！！");
            return result;
        }

        if (StringUtils.isBlank(orderId)) {
            result.error500("orderId订单id不能为空！！！");
            return result;
        }
        //获取倒计时时间
        String timer = iOrderListService.confirmReceiptTimer(orderId, isPlatform);
        if (StringUtils.isBlank(timer)) {
            result.error500("未找到订单倒计时数据!");
            return result;
        }
        objectMap.put("timer", timer);
        result.setResult(objectMap);
        result.success("请求成功");
        return result;
    }

    /**
     * 催发货
     *
     * @param orderId
     * @param isPlatform
     * @return
     */
    @PostMapping("/expeditedDelivery")
    @ResponseBody
    public Result<Map<String, Object>> expeditedDelivery(String orderId, Integer isPlatform) {
        Result<Map<String, Object>> result = new Result<>();
        //参数判断
        if (isPlatform == null) {
            result.error500("isPlatform是否平台类型参数不能为空！！！");
            return result;
        }

        if (StringUtils.isBlank(orderId)) {
            result.error500("orderId订单id不能为空！！！");
            return result;
        }
        if (isPlatform == 0) {
            OrderStoreList orderStoreList = iOrderStoreListService.getById(orderId);
            if (orderStoreList == null) {
                throw new JeecgBootException("订单不存在");
            }
            // 发送系统通知
            try {
                LoginUser loginUser = sysBaseAPI.getUserById(orderStoreList.getSysUserId());
                if (loginUser != null) {
                    Map<String, String> templateParam = MapUtil.newHashMap();
                    templateParam.put("orderNo", orderStoreList.getOrderNo());
                    BusTemplateMessageDTO busTemplateMessageDTO = new BusTemplateMessageDTO(SystemSendMsgHandle.FROM_USER, loginUser.getUsername(), null, templateParam, "expeditedDeliveryMsgStore", SysAnnmentTypeEnum.ORDER_STORE.getType(), orderStoreList.getOrderNo());

                    // 同步发现微信订阅消息: 催发货
                    Map<String, Object> data = new HashMap<>();

                    Map<String, Object> character_string1 = new HashMap<>(); //订单编号
                    character_string1.put("value",orderStoreList.getOrderNo());
                    data.put("character_string1",character_string1);

                    Map<String, Object> thing4 = new HashMap<>(); //备注信息
                    thing4.put("value","待发货订单，客户催发货，请尽快发货！");
                    data.put("thing4",thing4);

                    Map<String, Object> date3 = new HashMap<>(); //支付时间
                    date3.put("value",DateUtil.formatDateTime(orderStoreList.getPayTime()));
                    data.put("date3",date3);

                    WxSubscribeMsgDTO wxSubscribeMsgDTO = new WxSubscribeMsgDTO(loginUser.getOpenid(),"AiD7gccYBOKdwRTWVNpTx2wtRmuHCKEcY3gxjZp5Vp8","","qhf_store",data);
                    busTemplateMessageDTO.setWxSubscribeMsgDTO(wxSubscribeMsgDTO);

                    sysBaseAPI.sendBusTemplateAnnouncement(busTemplateMessageDTO);
                }
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
        } else {
            // TODO: 2023/9/1 平台订单催发货?
        }
        result.success("已提醒商家尽快发货，请耐心等待~");
        return result;

    }

}
