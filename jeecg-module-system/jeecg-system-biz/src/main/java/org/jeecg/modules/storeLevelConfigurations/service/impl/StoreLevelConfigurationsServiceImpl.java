package org.jeecg.modules.storeLevelConfigurations.service.impl;

import org.jeecg.modules.storeLevelConfigurations.entity.StoreLevelConfigurations;
import org.jeecg.modules.storeLevelConfigurations.mapper.StoreLevelConfigurationsMapper;
import org.jeecg.modules.storeLevelConfigurations.service.IStoreLevelConfigurationsService;
import org.springframework.stereotype.Service;

import com.github.yulichang.base.MPJBaseServiceImpl;

/**
 * @Description: 店铺等级评定配置表
 * @Author: jeecg-boot
 * @Date:   2025-05-26
 * @Version: V1.0
 */
@Service
public class StoreLevelConfigurationsServiceImpl extends MPJBaseServiceImpl<StoreLevelConfigurationsMapper, StoreLevelConfigurations> implements IStoreLevelConfigurationsService {

}
