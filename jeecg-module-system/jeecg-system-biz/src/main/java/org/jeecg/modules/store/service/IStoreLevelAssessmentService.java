package org.jeecg.modules.store.service;

import org.jeecg.modules.store.model.StoreLevelAssessmentResult;
import java.math.BigDecimal;

/**
 * 店铺等级评定服务
 * 
 * <AUTHOR>
 * @date 2024-03-21
 */
public interface IStoreLevelAssessmentService {
    
    /**
     * 执行店铺等级评定
     * 
     * @param storeId 店铺ID
     * @param assessmentPeriod 评定周期 (YYYY-MM)
     * @return 评定结果
     */
    StoreLevelAssessmentResult assessStoreLevel(String storeId, String assessmentPeriod);
    
    /**
     * 获取店铺当前等级
     * 
     * @param storeId 店铺ID
     * @return 当前等级值
     */
    String getCurrentStoreLevel(String storeId);
    
    /**
     * 获取下一个等级
     * 
     * @param currentLevel 当前等级
     * @return 下一个等级值，如果已是最高等级则返回null
     */
    String getNextLevel(String currentLevel);
    
    /**
     * 获取上一个等级
     * 
     * @param currentLevel 当前等级
     * @return 上一个等级值，如果已是最低等级则返回当前等级
     */
    String getPreviousLevel(String currentLevel);
    
    /**
     * 判断是否可以维持当前等级
     * 
     * @param currentLevel 当前等级
     * @param actualSales 实际业绩
     * @param actualEntrepreneurs 实际企业家数量
     * @return 是否可以维持当前等级
     */
    boolean canMaintainCurrentLevel(String currentLevel, BigDecimal actualSales, Long actualEntrepreneurs);
    
    /**
     * 判断是否可以升级到下一等级
     * 
     * @param nextLevel 下一等级
     * @param actualSales 实际业绩
     * @param actualEntrepreneurs 实际企业家数量
     * @return 是否可以升级到下一等级
     */
    boolean canUpgradeToNextLevel(String nextLevel, BigDecimal actualSales, Long actualEntrepreneurs);
} 