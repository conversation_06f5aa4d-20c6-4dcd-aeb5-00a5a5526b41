package org.jeecg.modules.member.job;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.dto.message.MessageDTO;
import org.jeecg.common.constant.CommonConstant;
import org.jeecg.common.system.api.ISysBaseAPI;
import org.jeecg.modules.member.entity.MemberAccountCapital;
import org.jeecg.modules.member.service.IMemberAccountCapitalService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;

import java.util.Date;

/**
 * 店铺补助金状态更新定时任务
 * 检查过期的店铺补助金并更新其状态
 */
@Slf4j
@Component
public class MemberStoreSubsidyStatusJob implements Job {
    
    @Autowired
    private IMemberAccountCapitalService memberAccountCapitalService;
    
    @Autowired
    private ISysBaseAPI sysBaseAPI;
    
    @Override
    public void execute(JobExecutionContext jobExecutionContext) throws JobExecutionException {
        log.info("开始执行店铺补助金状态更新任务");
        try {
            Date now = new Date();
            
            // 查询条件：店铺补助金(50) + 收入类型(0) + 可用状态(0) + 过期时间小于当前时间
            LambdaUpdateWrapper<MemberAccountCapital> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(MemberAccountCapital::getPayType, "50")
                    .eq(MemberAccountCapital::getGoAndCome, "0")
                    .eq(MemberAccountCapital::getStoreSubsidyStatus, "0")
                    .lt(MemberAccountCapital::getExpireTime, now)
                    .eq(MemberAccountCapital::getDelFlag, "0");
            
            // 更新为已过期状态(1)
            updateWrapper.set(MemberAccountCapital::getStoreSubsidyStatus, "1");
            
            // 执行更新
            boolean result = memberAccountCapitalService.update(updateWrapper);
            
            // 获取当前更新的记录数 (这里无法直接获取更新的记录数，只能得到是否成功)
            log.info("店铺补助金状态更新完成，更新结果：{}", result ? "成功" : "失败");
            
            // 查询已过期的补助金数量
            long expiredCount = memberAccountCapitalService.count(
                new LambdaQueryWrapper<MemberAccountCapital>()
                    .eq(MemberAccountCapital::getPayType, "50")
                    .eq(MemberAccountCapital::getGoAndCome, "0")
                    .eq(MemberAccountCapital::getStoreSubsidyStatus, "1")
                    .lt(MemberAccountCapital::getExpireTime, now)
                    .eq(MemberAccountCapital::getDelFlag, "0")
            );
            
            log.info("当前系统中已过期店铺补助金数量：{}", expiredCount);
        } catch (Exception e) {
            log.error("店铺补助金状态更新任务执行失败", e);
            // 记录系统异常日志
            // sysBaseAPI.addLog("店铺补助金状态更新任务执行失败：" + e.getMessage(), 2, 3);
        }
    }
} 