package org.jeecg.modules.store.service;

import java.math.BigDecimal;

/**
 * 店铺业绩统计服务
 * 
 * <AUTHOR>
 * @date 2024-03-21
 */
public interface IStorePerformanceStatisticsService {
    
    /**
     * 统计店铺上月业绩
     * 
     * @param storeId 店铺ID
     * @param assessmentMonth 评定月份 (YYYY-MM)
     * @return 上月业绩总额
     */
    BigDecimal calculateMonthlyPerformance(String storeId, String assessmentMonth);
    
    /**
     * 统计店铺合作企业家数量
     * 
     * @param storeId 店铺ID
     * @return 合作企业家数量
     */
    Long calculateEntrepreneurCount(String storeId);
} 