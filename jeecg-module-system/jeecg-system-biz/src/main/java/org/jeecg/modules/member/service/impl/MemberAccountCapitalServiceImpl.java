package org.jeecg.modules.member.service.impl;

import cn.hutool.core.convert.Convert;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import org.jeecg.common.system.api.ISysBaseAPI;
import org.jeecg.modules.agency.entity.AgencyAccountCapital;
import org.jeecg.modules.marketing.entity.MarketingEnterMoney;
import org.jeecg.modules.member.dto.MemberAccountCapitalDTO;
import org.jeecg.modules.member.entity.MemberAccountCapital;
import org.jeecg.modules.member.mapper.MemberAccountCapitalMapper;
import org.jeecg.modules.member.service.IMemberAccountCapitalService;
import org.jeecg.modules.member.service.IMemberListService;
import org.jeecg.modules.member.vo.MemberAccountCapitalVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Description: 会员资金明细
 * @Author: jeecg-boot
 * @Date:   2019-12-17
 * @Version: V1.0
 */
@Service
public class MemberAccountCapitalServiceImpl extends ServiceImpl<MemberAccountCapitalMapper, MemberAccountCapital> implements IMemberAccountCapitalService {
    @Autowired(required = false)
    private  MemberAccountCapitalMapper memberAccountCapitalMapper;
    @Autowired
    private ISysBaseAPI sysBaseApi;
    
    @Autowired
    private IMemberListService memberListService;
    
    @Override
    public IPage<Map<String, Object>> findMemberAccountCapitalByMemberId(Page<Map<String, Object>> page, Map<String, Object> paramMap) {
        return baseMapper.findMemberAccountCapitalByMemberId(page,paramMap);
    }


    @Override
    public IPage<MemberAccountCapitalDTO> getMemberAccountCapitalList(Page<MemberAccountCapital> page, MemberAccountCapitalVO memberAccountCapitalVO){
        // 获取原始查询结果
        IPage<MemberAccountCapitalDTO> pageResult = memberAccountCapitalMapper.getMemberAccountCapitalList(page, memberAccountCapitalVO);
        
        // 不再将店铺补助金加入balance字段
        // 直接返回原始查询结果
        return pageResult;
    }

    @Override
    public IPage<Map<String, Object>> findAccountCapitalByMemberId(Page<Map<String, Object>> page, HashMap<String, Object> map) {
        Object memberSource = map.get("memberSource");
        map.put("memberSource", Convert.toInt(memberSource,0));
        IPage<Map<String, Object>> result = baseMapper.findAccountCapitalByMemberId(page, map);
        
        // 不再将店铺补助金加入balance和currentBalance字段
        
        // 遍历结果集，添加字典文本
        for (Map<String, Object> record : result.getRecords()) {
            String goAndCome = Convert.toStr(record.get("goAndCome"));
            record.put("goAndCome_dictText", sysBaseApi.translateDict("go_and_come", goAndCome));
        }
        
        return result;
    }

    @Override
    public IPage<MemberAccountCapitalVO> getEnterMoney(Page<AgencyAccountCapital> page, MarketingEnterMoney marketingEnterMoney) {
        return baseMapper.getEnterMoney(page,marketingEnterMoney);
    }
}
