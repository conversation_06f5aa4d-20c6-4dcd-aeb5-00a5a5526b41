package org.jeecg.modules.member.api;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import org.jeecg.modules.member.dto.BusinessPerformanceDTO;
import org.jeecg.modules.member.dto.DistributionCommissionDTO;
import org.jeecg.modules.member.dto.DistributionOrderDTO;
import org.jeecg.common.system.vo.PageDTO;
import org.jeecg.modules.member.dto.TeamDetailDTO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.util.DateUtils;
import org.jeecg.common.util.PasswordUtil;
import org.jeecg.common.util.RedisUtil;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.marketing.entity.*;
import org.jeecg.modules.marketing.service.*;
import org.jeecg.modules.member.entity.*;
import org.jeecg.modules.member.service.*;
import org.jeecg.modules.member.utils.MemberUtils;
import org.jeecg.modules.member.utils.PromotionCodeUtils;
import org.jeecg.modules.member.utils.QrCodeUtils;
import org.jeecg.modules.member.vo.MemberDesignationCountVO;
import org.jeecg.modules.member.vo.MemberListVO;
import org.jeecg.modules.order.entity.OrderList;
import org.jeecg.modules.order.entity.OrderRefundList;
import org.jeecg.modules.order.entity.OrderStoreList;
import org.jeecg.modules.order.service.IOrderListService;
import org.jeecg.modules.order.service.IOrderRefundListService;
import org.jeecg.modules.order.service.IOrderStoreListService;
import org.jeecg.modules.pay.utils.NotifyUrlUtils;
import org.jeecg.modules.store.entity.StoreManage;
import org.jeecg.modules.store.service.IStoreFranchiserService;
import org.jeecg.modules.store.service.IStoreManageService;
import org.jeecg.modules.system.service.ISysDictService;
import org.jeecg.modules.system.service.ISysSmallcodeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.*;

/**
 * 会员api接口
 */
@RequestMapping("after/member")
@RestController
@Slf4j
public class AfterMemberController {

    @Autowired
    private IMemberListService iMemberListService;

    @Autowired
    private IMarketingDiscountCouponService iMarketingDiscountCouponService;

    @Autowired
    private ISysSmallcodeService iSysSmallcodeService;

    @Autowired
    private IMarketingCertificateRecordService iMarketingCertificateRecordService;

    @Autowired
    private IStoreManageService iStoreManageService;

    @Autowired
    private IMemberRechargeRecordService iMemberRechargeRecordService;

    @Autowired
    private IMarketingDistributionSettingService iMarketingDistributionSettingService;

    @Autowired
    private IMemberBankCardService iMemberBankCardService;

    @Autowired
    private QrCodeUtils qrCodeUtils;

    @Autowired
    private IOrderListService iOrderListService;
    @Autowired
    private IOrderStoreListService iOrderStoreListService;

    @Autowired
    private ISysDictService iSysDictService;

    @Autowired
    private IMemberGradeService iMemberGradeService;

    @Autowired
    private IMemberDesignationService iMemberDesignationService;

    @Autowired
    private IMemberDesignationCountService iMemberDesignationCountService;
    @Autowired
    private IMemberDesignationMemberListService iMemberDesignationMemberListService;

    @Autowired
    private IMarketingIntegralSettingService iMarketingIntegralSettingService;

    @Autowired
    private IMarketingIntegralTaskService iMarketingIntegralTaskService;

    @Autowired
    private IMarketingFourthIntegralSettingService iMarketingFourthIntegralSettingService;

    @Autowired
    private PromotionCodeUtils promotionCodeUtils;

    @Autowired
    private IMarketingWelfarePaymentsSettingService iMarketingWelfarePaymentsSettingService;

    @Autowired
    private IMarketingDistributionLevelService iMarketingDistributionLevelService;

    @Autowired
    private IMemberDistributionLevelService iMemberDistributionLevelService;

    @Autowired
    private MemberUtils memberUtils;

    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    private IMemberBusinessDesignationService iMemberBusinessDesignationService;

    @Autowired
    private IMarketingBusinessDesignationService iMarketingBusinessDesignationService;

    @Autowired
    private IMemberGiveWelfarePaymentsService iMemberGiveWelfarePaymentsService;

    @Autowired
    private NotifyUrlUtils notifyUrlUtils;

    @Autowired
    private IOrderRefundListService orderRefundListService;
    @Autowired
    private IStoreFranchiserService iStoreFranchiserService;
    @Autowired
    private IMarketingActivityListRecordService iMarketingActivityListRecordService;
    @Autowired
    private IMarketingGiftBagService iMarketingGiftBagService;

    @Autowired
    private IMarketingGiftBagRecordService iMarketingGiftBagRecordService;

    @Autowired
    private IMemberDesignationGroupService iMemberDesignationGroupService;

    /**
     * 验证昵称和头像是否存在
     *
     * @param memberId
     * @return
     */
    @PostMapping("hasMemberNickName")
    public Result<?> hasMemberNickName(@RequestAttribute("memberId") String memberId) {
        MemberList memberList = iMemberListService.getById(memberId);
        if (StringUtils.isBlank(memberList.getHeadPortrait())) {
            return Result.error("头像不存在");
        }
        if (StringUtils.isBlank(memberList.getNickName())) {
            return Result.error("昵称不存在");
        }
        return Result.ok("会员基本信息存在");
    }

    /**
     * 是否能分享推广码
     * @param memberId
     * @return
     */
    @PostMapping("isMemberShare")
    public Result<?> isMemberShare(@RequestAttribute("memberId") String memberId) {
        HashMap<String, Object> stringObjectHashMap = new HashMap<>();
        int i = 1;
        /*MemberList memberList = iMemberListService.getById(memberId);
        if (memberList.getIsRecommendStatus()==1){
            i = 1;
        }else {
            List<Map<String, Object>> memberDesignationList = iMemberDesignationMemberListService.findMemberDesignationList(0,memberId);
            for (Map<String, Object> stringObjectMap : memberDesignationList) {
                if (stringObjectMap.get("name").toString().contains("创始人")||stringObjectMap.get("name").toString().contains("合伙人")||stringObjectMap.get("name").toString().contains("分享大使")||stringObjectMap.get("name").toString().contains("创业伙伴")){
                    i = 1;
                }
            }
        }*/
        stringObjectHashMap.put("isMemberShare",i);
        return Result.ok(stringObjectHashMap);
    }
    /**
     * 用户完善资料接口
     *
     * @param memberId
     * @param headPortrait
     * @param sex
     * @param nickName
     * @return
     */
    @PostMapping("completeInformation")
    public Result<?> completeInformation(@RequestAttribute("memberId") String memberId, String headPortrait, String nickName,
                                         @RequestParam(value = "sex",required = false,defaultValue = "0") String sex,
                                         @RequestParam(value = "areaAddr",required = false) String areaAddr) {
        //参数校验
        if (StringUtils.isBlank(headPortrait)) {
            return Result.error("请上传头像");
        }
        if (StringUtils.isBlank(nickName)) {
            return Result.error("请填写昵称");
        }
        iMemberListService.updateById(new MemberList().setId(memberId).setHeadPortrait(headPortrait).setNickName(nickName).setSex(sex).setAreaAddr(areaAddr));
        return Result.ok("完善资料成功！！！");
    }


    /**
     * 支付密码验证接口
     * <p>
     * 张靠勤  2021-4-2
     *
     * @param srcTransactionPassword
     * @param memberId
     * @return
     */
    @RequestMapping("srcTransactionPasswordValid")
    @ResponseBody
    public Result<?> srcTransactionPasswordValid(String srcTransactionPassword
            , @RequestAttribute("memberId") String memberId) {
        MemberList memberList = iMemberListService.getById(memberId);
        if (StringUtils.isNotBlank(memberList.getTransactionPassword()) && StringUtils.isBlank(srcTransactionPassword)) {
            return Result.error("原交易密码不能为空");
        }
        log.info("支付密码校验：加密密码=" + memberList.getTransactionPassword() + "；用户输入密码=" + srcTransactionPassword);

        String passwd = null;
        if (StringUtils.isNotBlank(memberList.getTransactionPassword())) {
            passwd = PasswordUtil.decrypt(memberList.getTransactionPassword(), srcTransactionPassword, PasswordUtil.Salt);
        }
        if (StringUtils.isBlank(passwd) ||
                (!PasswordUtil.decrypt(memberList.getTransactionPassword(), srcTransactionPassword, PasswordUtil.Salt).equals(srcTransactionPassword))) {
            return Result.error("交易密码错误");
        }
        return Result.ok("支付密码验证成功");
    }


    /**
     * 设置用户交易密码
     * <p>
     * 张靠勤    2021-4-2
     *
     * @param transactionPassword
     * @param memberId
     * @return
     */
    @RequestMapping("settingTransactionPassword")
    @ResponseBody
    public Result<?> settingTransactionPassword(String transactionPassword
            , @RequestAttribute("memberId") String memberId) {
        //参数验证
        if (StringUtils.isBlank(transactionPassword)) {
            return Result.error("交易密码参数不能为空");
        }
        //交易密码领取积分
        iMarketingIntegralTaskService.transactionPassword(memberId);
        MemberList memberList = iMemberListService.getById(memberId);
        memberList.setTransactionPassword(PasswordUtil.encrypt(transactionPassword, transactionPassword, PasswordUtil.Salt));
        iMemberListService.saveOrUpdate(memberList);
        return Result.ok("交易密码设置成功");
    }


    /**
     * 获取会员信息
     *
     * @param
     * @return
     */
    @RequestMapping("getMemberInfo")
    @ResponseBody
    public Result<Map<String, Object>> getMemberInfo(@RequestHeader(defaultValue = "") String sysUserId,
                                                     @RequestAttribute("memberId") String memberId,
                                                     @RequestHeader(name = "softModel", required = false, defaultValue = "") String softModel) {
        Result<Map<String, Object>> result = new Result<>();
        if (StrUtil.isBlank(memberId)) {
            return result;
        }

        Map<String, Object> memberObjectMap = iMemberListService.getMemberInfo(sysUserId, memberId, softModel);
        
        result.setResult(memberObjectMap);
        result.success("获取会员信息成功");
        return result;
    }

    /**
     * 查询用户佣金信息
     *
     * @return
     */
    @RequestMapping("findMemberPromoter")
    @ResponseBody
    public Result<Map<String, Object>> findMemberPromoter(@RequestAttribute("memberId") String memberId) {
        Result<Map<String, Object>> result = new Result<>();

        Map<String, Object> objectMap = Maps.newHashMap();

        //佣金笔数
        objectMap.put("memberRechargeRecordProtomerCoun", iMemberRechargeRecordService.findMemberRechargeRecordProtomerCount(memberId));

        //提现笔数

        objectMap.put("memberRechargeRecordDepositCount", iMemberRechargeRecordService.findMemberRechargeRecordDepositCount(memberId));

        //团队总人数

        MemberListVO memberDistributionCount = iMemberListService.findMemberDistributionCount(memberId);
        if (oConvertUtils.isEmpty(memberDistributionCount)) {
            objectMap.put("memberDistributionCount", 0);
        } else {
            MarketingDistributionSetting marketingDistributionSetting = iMarketingDistributionSettingService.getOne(new LambdaQueryWrapper<MarketingDistributionSetting>().eq(MarketingDistributionSetting::getStatus, "1"));
            if (marketingDistributionSetting != null && marketingDistributionSetting.getDistributionLevel().equals("1")) {
                MemberDistributionLevel memberDistributionLevel = iMemberDistributionLevelService.getOne(new LambdaQueryWrapper<MemberDistributionLevel>().eq(MemberDistributionLevel::getMemberListId, memberId));
                if (memberDistributionLevel != null && StringUtils.isNotBlank(memberDistributionLevel.getMarketingDistributionLevelId())) {
                    objectMap.put("memberDistributionCount", memberDistributionLevel.getUpgradeTeamNumber());
                } else {
                    objectMap.put("memberDistributionCount", 0);
                }
            } else {
                objectMap.put("memberDistributionCount", memberDistributionCount.getMlSum());
            }
        }
        //海报
        QueryWrapper<MarketingDistributionSetting> marketingDistributionSettingQueryWrapper = new QueryWrapper<>();
        marketingDistributionSettingQueryWrapper.eq("status", "1");
        marketingDistributionSettingQueryWrapper.orderByDesc("create_time");
        if (iMarketingDistributionSettingService.count(marketingDistributionSettingQueryWrapper) > 0) {
            MarketingDistributionSetting marketingDistributionSetting = iMarketingDistributionSettingService.list(marketingDistributionSettingQueryWrapper).get(0);
            objectMap.put("distributionPosters", marketingDistributionSetting.getDistributionPosters());
        } else {
            objectMap.put("distributionPosters", "");
        }


        Map<String, Object> paramObjectMap = Maps.newHashMap();
        paramObjectMap.put("pattern", "0");
        paramObjectMap.put("memberId", memberId);
        //待付款
        objectMap.put("obligation", iMemberRechargeRecordService.findMemberRechargeRecordSum(paramObjectMap));

        paramObjectMap.put("pattern", "2");
        //已付款
        objectMap.put("accountPaid", iMemberRechargeRecordService.findMemberRechargeRecordSum(paramObjectMap));

        paramObjectMap.put("pattern", "1");
        //待审核
        objectMap.put("pending", iMemberRechargeRecordService.findMemberRechargeRecordWithdrawSum(paramObjectMap));
        paramObjectMap.put("pattern", "3");
        //待打款
        objectMap.put("playwith", iMemberRechargeRecordService.findMemberRechargeRecordWithdrawSum(paramObjectMap));
        paramObjectMap.put("pattern", "5");
        //已打款
        objectMap.put("haveMoney", iMemberRechargeRecordService.findMemberRechargeRecordWithdrawSum(paramObjectMap));
        paramObjectMap.put("pattern", "7");
        //无效
        objectMap.put("invalid", iMemberRechargeRecordService.findMemberRechargeRecordWithdrawSum(paramObjectMap));
        result.setResult(objectMap);
        result.success("获取会员佣金信息成功");
        return result;
    }

    /**
     * 增加分享次数
     *
     * @param
     * @return
     */
    @RequestMapping("addMemberShareTimes")
    @ResponseBody
    public Result<String> addMemberShareTimes(@RequestAttribute("memberId") String memberId) {
        Result<String> result = new Result<>();
        MemberList memberList = iMemberListService.getById(memberId);
        //增加分享次数
        memberList.setShareTimes(memberList.getShareTimes().add(new BigDecimal(1)));
        iMemberListService.saveOrUpdate(memberList);
        result.setResult(memberList.getShareTimes().toString());
        result.success("分享次数增加成功");
        return result;
    }

    /**
     * 查询用户的级别按照时间排序
     *
     * @param
     * @return
     */
    @RequestMapping("findMemberLevelList")
    @ResponseBody
    @Deprecated
    public Result<IPage<Map<String, Object>>> findMemberLevelList(@RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                                  @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                                                  @RequestAttribute("memberId") String memberId) {
        Result<IPage<Map<String, Object>>> result = new Result<>();
        //组织查询参数
        Page<Map<String, Object>> page = new Page<Map<String, Object>>(pageNo, pageSize);

        result.setResult(iMemberListService.findMemberLevelList(page, memberId));

        result.success("查询会员级别成功");
        return result;
    }

    /**
     * 查询我的订单不同状态的数量,逛好店,收藏接口
     *
     * @param request
     * @return
     */
    @RequestMapping("memberGoodAndOrderCount")
    @ResponseBody
    public Result<Map<String, Object>> memberGoodAndOrderCount(HttpServletRequest request) {
        String memberId = request.getAttribute("memberId").toString();
        Result<Map<String, Object>> result = new Result<>();
        Map<String, Object> map = Maps.newLinkedHashMap();
        if (StringUtils.isBlank(memberId)) {
            return result.error500("memberId不能为空!");
        }

        //订单状态；0：待付款；1：待发货（已付款、部分发货）；2：待收货（已发货）；3：交易成功；4：交易失败
        //待付款(平台)
        long obligationCount = iOrderListService.count(new LambdaQueryWrapper<OrderList>().and(or -> or
                .eq(OrderList::getDelFlag, "0")
                .eq(OrderList::getMemberListId, memberId)
                .eq(OrderList::getStatus, "0")));
        //待付款(店铺)
        long obligationCountStore = iOrderStoreListService.count(new LambdaQueryWrapper<OrderStoreList>().and(or -> or
                .eq(OrderStoreList::getDelFlag, "0")
                .eq(OrderStoreList::getMemberListId, memberId)
                .eq(OrderStoreList::getStatus, "0")));
        map.put("obligationCount", obligationCount + obligationCountStore);

        //待付款(平台)
        long waitDeliveryCount = iOrderListService.count(new LambdaQueryWrapper<OrderList>().and(or -> or
                .eq(OrderList::getDelFlag, "0")
                .eq(OrderList::getMemberListId, memberId)
                .eq(OrderList::getStatus, "1")));
        //待付款(店铺)
        long waitDeliveryCountStore = iOrderStoreListService.count(new LambdaQueryWrapper<OrderStoreList>().and(or -> or
                .eq(OrderStoreList::getDelFlag, "0")
                .eq(OrderStoreList::getMemberListId, memberId)
                .eq(OrderStoreList::getStatus, "1")));
        map.put("waitDeliveryCount", waitDeliveryCount + waitDeliveryCountStore);

        //待收货（已发货）(平台)
        long waitForReceivingCount = iOrderListService.count(new LambdaQueryWrapper<OrderList>().and(or -> or
                .eq(OrderList::getDelFlag, "0")
                .eq(OrderList::getMemberListId, memberId)
                .eq(OrderList::getStatus, "2")));
        //待收货（已发货）(店铺)
        long waitForReceivingCountStore = iOrderStoreListService.count(new LambdaQueryWrapper<OrderStoreList>().and(or -> or
                .eq(OrderStoreList::getDelFlag, "0")
                .eq(OrderStoreList::getMemberListId, memberId)
                .eq(OrderStoreList::getStatus, "2")));
        map.put("waitForReceivingCount", waitForReceivingCount + waitForReceivingCountStore);
        //交易成功(平台)
        long dealsAreDoneCount = iOrderListService.count(new LambdaQueryWrapper<OrderList>().and(or -> or
                .eq(OrderList::getDelFlag, "0")
                .eq(OrderList::getMemberListId, memberId)
                .eq(OrderList::getStatus, "3")
                .eq(OrderList::getIsEvaluate, "0")));
        //交易成功(店铺)
        long dealsAreDoneCountStore = iOrderStoreListService.count(new LambdaQueryWrapper<OrderStoreList>().and(or -> or
                .eq(OrderStoreList::getDelFlag, "0")
                .eq(OrderStoreList::getMemberListId, memberId)
                .eq(OrderStoreList::getStatus, "3")
                .eq(OrderStoreList::getIsEvaluate, "0")));
        map.put("dealsAreDoneCount", dealsAreDoneCount + dealsAreDoneCountStore);

        // 退款售后数量 - 只统计用户需要关注的售后单（不包括已拒绝的状态5）
        LambdaQueryWrapper<OrderRefundList> orderRefundListLambdaQueryWrapper = new LambdaQueryWrapper<>();
        orderRefundListLambdaQueryWrapper
                .eq(OrderRefundList::getDelFlag, "0")
                .eq(OrderRefundList::getMemberId, memberId)
                .in(OrderRefundList::getStatus,"0","1","2","3");
        long refundCount = orderRefundListService.count(orderRefundListLambdaQueryWrapper);
        map.put("refundCount", refundCount);

        result.setResult(map);
        result.success("查询成功!");
        return result;
    }

    /**
     * 通过id查询会员信息
     *
     * @param id
     * @return
     */
    @RequestMapping("findMemberById")
    @ResponseBody
    public Result<Map<String, Object>> findMemberById(String id) {
        Result<Map<String, Object>> result = new Result<>();
        HashMap<String, Object> map = new HashMap<>();
        MemberList memberList = iMemberListService.getById(id);
        if (oConvertUtils.isNotEmpty(memberList)) {
            map.put("phone", memberList.getPhone());
            map.put("id", memberList.getId());
            map.put("nickName", memberList.getNickName());
            MemberGiveWelfarePayments memberGiveWelfarePayments = iMemberGiveWelfarePaymentsService.getOne(new LambdaQueryWrapper<MemberGiveWelfarePayments>()
                    .eq(MemberGiveWelfarePayments::getMemberListId, map.get("id"))
                    .orderByDesc(MemberGiveWelfarePayments::getCreateTime)
                    .last("limit 1"));
            if (memberGiveWelfarePayments == null) {
                map.put("giveWelfarePayments", 0);
            } else {
                map.put("giveWelfarePayments", memberGiveWelfarePayments.getGiveWelfarePayments());
            }
            result.setResult(map);
            result.success("返回会员信息");
        } else {
            result.error500("会员信息异常,请联系管理员");
        }
        return result;
    }

    /**
     * 查看我的团队信息
     *
     * @param request
     * @param id
     * @param memberDesignationGroupId
     * @param pageNo
     * @param pageSize
     * @return
     */
    @RequestMapping("getMemberManageInfo")
    @ResponseBody
    public Result<Map<String, Object>> getMemberManageInfo(HttpServletRequest request,
                                                           String id,
                                                           String memberDesignationGroupId,
                                                           @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                           @RequestParam(name = "pageSize", defaultValue = "1000") Integer pageSize) {
        Result<Map<String, Object>> result = new Result<>();
        String memberId = request.getAttribute("memberId").toString();
        Page<Map<String, Object>> page = new Page<>(pageNo, pageSize);
        log.info("memberListId++++++++++" + id);
        if (StringUtils.isBlank(id)) {
            result.setResult(memberManageMap(memberId, page, memberId, memberDesignationGroupId));
        } else {
            if (id.equals(memberId)) {
                result.setResult(memberManageMap(memberId, page, memberId, memberDesignationGroupId));
            } else {
                result.setResult(memberManageMap(id, page, memberId, memberDesignationGroupId));
            }
        }
        result.success("我的团队");
        return result;
    }

    private Map<String, Object> memberManageMap(String id, Page<Map<String, Object>> page, String memberId, String memberDesignationGroupId) {

        log.info("memberDesignationGroupId++++++++++" + memberDesignationGroupId);

        HashMap<String, Object> map = new HashMap<>();

        MemberList memberList = iMemberListService.getById(id);

        MemberDesignationMemberList designationMemberList = iMemberDesignationMemberListService.getById(memberDesignationGroupId);

        MemberDesignationMemberList memberDesignationMemberList = iMemberDesignationMemberListService.list(new LambdaQueryWrapper<MemberDesignationMemberList>()
                .eq(MemberDesignationMemberList::getDelFlag, "0")
                .eq(MemberDesignationMemberList::getMemberListId, memberList.getId())
                .eq(MemberDesignationMemberList::getMemberDesignationGroupId, designationMemberList.getMemberDesignationGroupId())
        ).get(0);

        map.put("id", memberList.getId());
        map.put("logo", memberList.getHeadPortrait());
        map.put("nickName", memberList.getNickName());
        map.put("phone", memberList.getPhone());

        map.put("tManageId", memberDesignationMemberList.getTManageId());
        map.put("isChange", memberDesignationMemberList.getIsChange());

        MemberDesignation memberDesignation = iMemberDesignationService.getById(memberDesignationMemberList.getMemberDesignationId());
        map.put("name", memberDesignation.getName());
        map.put("logoAddr", memberDesignation.getLogoAddr());
        map.put("totalMembers", memberDesignationMemberList.getTotalMembers());

        long count = iMemberDesignationMemberListService.count(new LambdaQueryWrapper<MemberDesignationMemberList>()
                .eq(MemberDesignationMemberList::getMemberDesignationGroupId, memberDesignationMemberList.getMemberDesignationGroupId())
                .eq(MemberDesignationMemberList::getTManageId, memberList.getId())
        );
        if (oConvertUtils.isEmpty(memberDesignationMemberList.getMemberJoinTime())) {
            map.put("buyGiftTime", "");
        } else {
            map.put("buyGiftTime", memberDesignationMemberList.getMemberJoinTime().getTime());
        }
        if (memberDesignationMemberList.getIsChange().equals("1")) {
            map.put("isViewDesignate", "0");
        } else {
            if (oConvertUtils.isNotEmpty(memberDesignationMemberList.getMemberJoinTime())) {
                //当日23点59时59分
                Calendar calendar2 = Calendar.getInstance();
                calendar2.set(calendar2.get(Calendar.YEAR), calendar2.get(Calendar.MONTH), calendar2.get(Calendar.DAY_OF_MONTH),
                        23, 59, 59);
                //当日0点
                Calendar calendar1 = Calendar.getInstance();
                calendar1.set(calendar1.get(Calendar.YEAR), calendar1.get(Calendar.MONTH), calendar1.get(Calendar.DAY_OF_MONTH),
                        0, 0, 0);
                if (memberDesignationMemberList.getMemberJoinTime().getTime() > calendar1.getTime().getTime() && memberDesignationMemberList.getMemberJoinTime().getTime() < calendar2.getTime().getTime() && count <= 0) {
                    if (StringUtils.isNotBlank(memberDesignationMemberList.getTManageId())) {
                        if (!memberDesignationMemberList.getTManageId().equals(memberId)) {
                            map.put("isViewDesignate", "0");
                        } else {
                            map.put("isViewDesignate", "1");
                        }
                    }
                } else {
                    map.put("isViewDesignate", "0");
                }
            } else {
                map.put("isViewDesignate", "0");
            }
        }

        map.put("memberDirect", count);
        //团队礼包销售总额
        map.put("totalGiftSales", memberDesignationMemberList.getTotalGiftSales());

        List<MemberDesignationCountVO> listByMemberId = iMemberDesignationCountService.findListByMemberId(memberList.getId(), memberDesignationMemberList.getMemberDesignationGroupId());
        ArrayList<Map<String, Object>> maps = new ArrayList<>();
        listByMemberId.forEach(lbm -> {
            HashMap<String, Object> objectHashMap = new HashMap<>();
            objectHashMap.put("designationName", lbm.getName());
            objectHashMap.put("designationTotalMembers", lbm.getTotalMembers());
            maps.add(objectHashMap);
        });
        map.put("designationList", maps);
        map.put("levelDownList", iMemberListService.getmemberListByTManageId(page, memberList.getId(), memberDesignationMemberList.getMemberDesignationGroupId()));
        return map;
    }

    /**
     * 推荐人列表信息
     *
     * @param memberId
     * @param pageNo
     * @param pageSize
     * @return
     */
    @RequestMapping("pushingNumber")
    @ResponseBody
    public Result<?> pushingNumber(@RequestAttribute("memberId") String memberId, @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                   @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize) {
        IPage<Map<String, Object>> resultMap = iMemberListService.pushingNumber(new Page<>(pageNo, pageSize), memberId);
        MarketingDistributionSetting marketingDistributionSetting = iMarketingDistributionSettingService.getOne(new LambdaQueryWrapper<MarketingDistributionSetting>().eq(MarketingDistributionSetting::getStatus, "1"));
        resultMap.getRecords().forEach(r -> {
            //会员等级
            if (r.get("memberType").toString().equals("1")) {
                if (r.get("memberGradeId") != null) {
                    MemberGrade memberGrade = iMemberGradeService.getById(r.get("memberGradeId").toString());
                    if (memberGrade == null) {
                        r.put("memberGradeIcon", "");
                    } else {
                        r.put("memberGradeIcon", memberGrade.getGradeLogo());
                    }
                } else {
                    r.put("memberGradeIcon", "");
                }
            } else {
                r.put("memberGradeIcon", "");
            }
            //推荐人信息
            if (r.get("promoter") != null) {
                r.put("promoterMan", memberUtils.getPromoterMan(r.get("promoterType").toString(), r.get("promoter").toString()));
            } else {
                r.put("promoterMan", "无");
            }
            //归属店铺
            if (r.get("sysUserId") != null && !r.get("sysUserId").toString().equals("")) {
                StoreManage storeManage = iStoreManageService.getOne(new LambdaQueryWrapper<StoreManage>()
                        .eq(StoreManage::getSysUserId, r.get("sysUserId").toString())
                        .in(StoreManage::getPayStatus, "1", "2")
                        .last("limit 1"));
                if (storeManage.getSubStoreName() == null) {
                    r.put("storeName", storeManage.getStoreName());
                } else {
                    r.put("storeName", storeManage.getStoreName() + "(" + storeManage.getSubStoreName() + ")");
                }
            } else {
                r.put("storeName", "无");
            }
            //分销等级
            MemberDistributionLevel memberDistributionLevel = iMemberDistributionLevelService.getOne(new LambdaQueryWrapper<MemberDistributionLevel>().eq(MemberDistributionLevel::getMemberListId, r.get("id").toString()).orderByDesc(MemberDistributionLevel::getTeamNumber).last("limit 1"));
            if (memberDistributionLevel != null && StringUtils.isNotBlank(memberDistributionLevel.getMarketingDistributionLevelId())) {
                MarketingDistributionLevel marketingDistributionLevel = iMarketingDistributionLevelService.getById(memberDistributionLevel.getMarketingDistributionLevelId());
                if (marketingDistributionLevel != null) {
                    r.put("distributionLevelIcon", marketingDistributionLevel.getIcon());
                } else {
                    r.put("distributionLevelIcon", "");
                }
            } else {
                r.put("distributionLevelIcon", "");
            }
            //好友人数
            if (marketingDistributionSetting != null && marketingDistributionSetting.getDistributionLevel().equals("1")) {
                if (memberDistributionLevel != null) {
                    r.put("distributionCount", memberDistributionLevel.getUpgradeTeamNumber());
                } else {
                    r.put("distributionCount", 0);
                }
            } else {
                if (r.get("promoter") != null) {
                    r.put("distributionCount", iMemberListService.count(new LambdaQueryWrapper<MemberList>().eq(MemberList::getPromoter, r.get("id").toString())));
                } else {
                    r.put("distributionCount", 0);
                }
            }
        });
        return Result.ok(resultMap);
    }

    @RequestMapping("getDistributionCount")
    @ResponseBody
    public Result<?> getDistributionCount(@RequestAttribute("memberId") String memberId) {
        Map<String, Object> resultMap = Maps.newHashMap();
        MarketingDistributionSetting marketingDistributionSetting = iMarketingDistributionSettingService.getOne(new LambdaQueryWrapper<MarketingDistributionSetting>().eq(MarketingDistributionSetting::getStatus, "1"));
        if (marketingDistributionSetting != null && marketingDistributionSetting.getDistributionLevel().equals("1")) {
            MemberDistributionLevel memberDistributionLevel = iMemberDistributionLevelService.getOne(new LambdaQueryWrapper<MemberDistributionLevel>().eq(MemberDistributionLevel::getMemberListId, memberId));
            if (memberDistributionLevel != null && StringUtils.isNotBlank(memberDistributionLevel.getMarketingDistributionLevelId())) {
                resultMap.put("pushingNumber", memberDistributionLevel.getUpgradeDirect());
                resultMap.put("betweenNumber", memberDistributionLevel.getUpgradeTeamNumber().subtract(memberDistributionLevel.getUpgradeDirect()));
                resultMap.put("totalNumber", memberDistributionLevel.getUpgradeTeamNumber());
            } else {
                resultMap.put("pushingNumber", 0);
                resultMap.put("betweenNumber", 0);
                resultMap.put("totalNumber", 0);
            }
        } else {
            resultMap.put("pushingNumber", iMemberListService.count(new LambdaQueryWrapper<MemberList>().eq(MemberList::getPromoter, memberId)));
            resultMap.put("betweenNumber", iMemberListService.betweenPush(memberId));
            resultMap.put("totalNumber", Integer.parseInt(resultMap.get("pushingNumber").toString()) + Integer.parseInt(resultMap.get("betweenNumber").toString()));
        }
        return Result.ok(resultMap);
    }

    /**
     * 修改密码
     *
     * @return
     */
    @RequestMapping("updatePasswd")
    @ResponseBody
    public Result<?> updatePasswd(@RequestAttribute("memberId") String memberId, String password, String phone, String captcha) {
        if (StringUtils.isBlank(password)) {
            return Result.error("密码不能为空");
        }
        if (StringUtils.isBlank(phone)) {
            return Result.error("手机号不能为空");
        }
        if (StringUtils.isBlank(captcha)) {
            return Result.error("验证码不能为空");
        }
        //核对验证码
        Object captchaContent = redisUtil.get(phone);
        if (captchaContent == null || !captchaContent.toString().equals(captcha)) {
            return Result.error("验证码不正确");
        }
        MemberList memberList = iMemberListService.getById(memberId);
        password = StringUtils.trim(password);
        memberList.setPassword(PasswordUtil.encrypt(password, password, PasswordUtil.Salt));
        iMemberListService.saveOrUpdate(memberList);
        return Result.ok();
    }
    @RequestMapping("getMemberContribution")
    @ResponseBody
    public Result<Map<String, Object>> getMemberContribution(){
        HashMap<String, Object> map = new HashMap<>();
        map.put("contribution",new BigDecimal(999999999.32));
        return Result.ok(map);
    }

    /**
     * 获取创业业绩统计
     * @param memberId 会员ID
     * @param startDate 开始日期（可选）
     * @param endDate 结束日期（可选）
     * @param dateRange 日期范围快捷选择（可选）：7=近7天，30=近30天
     * @return 业绩统计结果
     */
    @GetMapping("/businessPerformance")
    public Result<BusinessPerformanceDTO> getBusinessPerformance(
            @RequestAttribute("memberId") String memberId,
            @RequestParam(value = "startDate", required = false) String startDate,
            @RequestParam(value = "endDate", required = false) String endDate,
            @RequestParam(value = "dateRange", required = false) Integer dateRange) {

        // 处理快捷日期范围
        if (dateRange != null) {
            if (dateRange == 7 || dateRange == 30) {
                // 计算近 7 天或近 30 天的日期范围
                endDate = DateUtils.formatDate(new Date());
                startDate = DateUtils.formatDate(DateUtil.offsetDay(new Date(),-dateRange));
            }
        }

        Map<String, BigDecimal> performanceMap = iMemberListService.getMemberBusinessPerformance(memberId, startDate, endDate);
        BusinessPerformanceDTO performanceDTO = BusinessPerformanceDTO.fromMap(performanceMap);
        return Result.OK(performanceDTO);
    }

    /**
     * 获取分销佣金统计
     * @param memberId 会员ID
     * @param startDate 开始日期（可选）
     * @param endDate 结束日期（可选）
     * @param dateRange 日期范围快捷选择（可选）：7=近7天，30=近30天
     * @return 分销佣金和订单数量统计结果
     */
    @GetMapping("/distributionCommission")
    public Result<DistributionCommissionDTO> getDistributionCommission(
            @RequestAttribute("memberId") String memberId,
            @RequestParam(value = "startDate", required = false) String startDate,
            @RequestParam(value = "endDate", required = false) String endDate,
            @RequestParam(value = "dateRange", required = false) Integer dateRange) {

        // 处理快捷日期范围
//        if (dateRange != null) {
//            if (dateRange == 7 || dateRange == 30) {
//                // 计算近 7 天或近 30 天的日期范围
//                endDate = DateUtils.formatDate(new Date());
//                startDate = DateUtils.formatDate(DateUtil.offsetDay(new Date(),-dateRange));
//            }
//        }

        Map<String, Object> commissionMap = iMemberListService.getMemberDistributionCommission(memberId, startDate, endDate);
        DistributionCommissionDTO commissionDTO = DistributionCommissionDTO.fromMap(commissionMap);
        return Result.OK(commissionDTO);
    }

    /**
     * 获取分销订单列表
     * @param memberId 会员ID
     * @param pageNo 页码
     * @param pageSize 每页数量
     * @param startDate 开始日期（可选）
     * @param endDate 结束日期（可选）
     * @param dateRange 日期范围快捷选择（可选）：7=近7天，30=近30天
     * @return 分销订单列表分页结果
     */
    @GetMapping("/distributionOrders")
    public Result<PageDTO<DistributionOrderDTO>> getDistributionOrders(
            @RequestAttribute("memberId") String memberId,
            @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
            @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
            @RequestParam(value = "startDate", required = false) String startDate,
            @RequestParam(value = "endDate", required = false) String endDate,
            @RequestParam(value = "dateRange", required = false) Integer dateRange) {

        // 处理快捷日期范围
//        if (dateRange != null) {
//            if (dateRange == 7 || dateRange == 30) {
//                // 计算近 7 天或近 30 天的日期范围
//                endDate = DateUtils.formatDate(new Date());
//                startDate = DateUtils.formatDate(DateUtil.offsetDay(new Date(),-dateRange));
//            }
//        }

        Page<Map<String, Object>> page = new Page<>(pageNo, pageSize);
        IPage<Map<String, Object>> orderList = iMemberListService.getMemberDistributionOrders(page, memberId, startDate, endDate, "-1");

        // 将 IPage<Map<String, Object>> 转换为 PageDTO<DistributionOrderDTO>
        PageDTO<DistributionOrderDTO> orderListDTO = PageDTO.fromMapPage(orderList, DistributionOrderDTO::fromMap);
        return Result.OK(orderListDTO);
    }

    /**
     * 获取团队明细列表
     * @param memberId 会员ID
     * @param pageNo 页码
     * @param pageSize 每页数量
     * @param startDate 开始日期（可选）
     * @param endDate 结束日期（可选）
     * @param dateRange 日期范围快捷选择（可选）：7=近7天，30=近30天
     * @return 团队明细列表分页结果
     */
    @GetMapping("/teamDetail")
    public Result<PageDTO<TeamDetailDTO>> getTeamDetailList(
            @RequestAttribute("memberId") String memberId,
            @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
            @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
            @RequestParam(value = "startDate", required = false) String startDate,
            @RequestParam(value = "endDate", required = false) String endDate,
            @RequestParam(value = "dateRange", required = false) Integer dateRange) {

        // 处理快捷日期范围
//        if (dateRange != null) {
//            if (dateRange == 7 || dateRange == 30) {
//                // 计算近 7 天或近 30 天的日期范围
//                endDate = DateUtils.formatDate(new Date());
//                startDate = DateUtils.formatDate(DateUtil.offsetDay(new Date(),-dateRange));
//            }
//        }

        Page<Map<String, Object>> page = new Page<>(pageNo, pageSize);
        IPage<Map<String, Object>> teamList = iMemberListService.getTeamDetailList(page, memberId, startDate, endDate);

        // 将 IPage<Map<String, Object>> 转换为 PageDTO<TeamDetailDTO>
        PageDTO<TeamDetailDTO> teamListDTO = PageDTO.fromMapPage(teamList, TeamDetailDTO::fromMap);
        return Result.OK(teamListDTO);
    }

    /**
     * 获取用户在指定店铺的补助金信息
     * @param memberId 会员ID
     * @param storeId 店铺ID
     * @return 店铺补助金信息
     */
    @GetMapping("/getMemberStoreSubsidyInfo")
    public Result<Map<String, Object>> getMemberStoreSubsidyInfo(
            @RequestAttribute("memberId") String memberId,
            @RequestParam("storeId") String storeId) {
        
        log.info("获取会员[{}]在店铺[{}]的补助金信息", memberId, storeId);
        
        try {
            Map<String, Object> result = new HashMap<>();
            
            // 获取用户在指定店铺的可用补助金总金额和最近过期时间
            Map<String, Object> subsidyInfo = iMemberListService.getMemberStoreSubsidyInfo(memberId, storeId);
            
            if (subsidyInfo != null) {
                result.put("totalAmount", subsidyInfo.get("totalAmount"));
                result.put("nearestExpireTime", subsidyInfo.get("nearestExpireTime"));
            } else {
                result.put("totalAmount", BigDecimal.ZERO);
                result.put("nearestExpireTime", "");
            }
            
            return Result.ok(result);
            
        } catch (Exception e) {
            log.error("获取会员店铺补助金信息失败", e);
            return Result.error("获取补助金信息失败：" + e.getMessage());
        }
    }
}
