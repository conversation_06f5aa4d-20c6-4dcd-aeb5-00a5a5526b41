package org.jeecg.modules.storeLevelConfigurations.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.storeLevelConfigurations.entity.StoreLevelConfigurations;
import com.github.yulichang.base.MPJBaseMapper;

/**
 * @Description: 店铺等级评定配置表
 * @Author: jeecg-boot
 * @Date:   2025-05-26
 * @Version: V1.0
 */
public interface StoreLevelConfigurationsMapper extends MPJBaseMapper<StoreLevelConfigurations> {

}
