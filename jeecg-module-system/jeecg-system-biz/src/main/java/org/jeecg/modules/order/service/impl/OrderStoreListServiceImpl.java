package org.jeecg.modules.order.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.comparator.CompareUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Maps;
import com.google.zxing.BarcodeFormat;
import com.google.zxing.qrcode.decoder.ErrorCorrectionLevel;
import com.huifu.adapay.core.exception.BaseAdaPayException;
import com.ijpay.core.kit.QrCodeKit;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.exception.JeecgBootException;
import org.jeecg.common.system.api.ISysBaseAPI;
import org.jeecg.common.util.DateUtils;
import org.jeecg.common.util.OrderNoUtils;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.good.entity.GoodStoreList;
import org.jeecg.modules.good.entity.GoodStoreSpecification;
import org.jeecg.modules.good.service.IGoodStoreListService;
import org.jeecg.modules.good.service.IGoodStoreSpecificationService;
import org.jeecg.modules.map.utils.TengxunMapUtils;
import org.jeecg.modules.marketing.entity.*;
import org.jeecg.modules.marketing.service.*;
import org.jeecg.modules.marketing.store.prefecture.service.IMarketingStorePrefectureGoodService;
import org.jeecg.modules.member.entity.*;
import org.jeecg.modules.member.service.*;
import org.jeecg.modules.order.dto.OrderStoreGoodRecordDTO;
import org.jeecg.modules.order.dto.OrderStoreListDTO;
import org.jeecg.modules.order.dto.OrderStoreListExportDTO;
import org.jeecg.modules.order.dto.OrderStoreSubListDTO;
import org.jeecg.modules.order.entity.OrderRefundList;
import org.jeecg.modules.order.entity.OrderStoreGoodRecord;
import org.jeecg.modules.order.entity.OrderStoreList;
import org.jeecg.modules.order.entity.OrderStoreSubList;
import org.jeecg.modules.order.mapper.OrderStoreGoodRecordMapper;
import org.jeecg.modules.order.mapper.OrderStoreListMapper;
import org.jeecg.modules.order.service.*;
import org.jeecg.modules.order.utils.PayUtils;
import org.jeecg.modules.order.vo.OrderStoreListVO;
import org.jeecg.modules.pay.entity.PayOrderCarLog;
import org.jeecg.modules.pay.utils.HftxPayUtils;
import org.jeecg.modules.store.dto.StoreAddressDTO;
import org.jeecg.modules.store.entity.*;
import org.jeecg.modules.store.service.*;
import org.jeecg.modules.store.util.StoreUtils;
import org.jeecg.modules.storeAgency.entity.StoreAgency;
import org.jeecg.modules.storeAgency.service.IStoreAgencyService;
import org.jeecg.modules.system.entity.SysUser;
import org.jeecg.modules.system.service.ISysDictService;
import org.jeecg.modules.system.service.ISysUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: 商品订单列表
 * @Author: jeecg-boot
 * @Date: 2019-11-30
 * @Version: V1.0
 */
@Service
@Slf4j
public class OrderStoreListServiceImpl extends ServiceImpl<OrderStoreListMapper, OrderStoreList> implements IOrderStoreListService {
    @Autowired(required = false)
    private OrderStoreListMapper orderStoreListMapper;

    @Autowired
    @Lazy
    private IMemberListService iMemberListService;

    @Autowired
    @Lazy
    private IMarketingDiscountCouponService iMarketingDiscountCouponService;

    @Autowired
    @Lazy
    private IStoreManageService iStoreManageService;

    @Autowired
    @Lazy
    private IStoreRechargeRecordService iStoreRechargeRecordService;

    @Autowired
    @Lazy
    private IOrderStoreSubListService iOrderStoreSubListService;

    @Autowired
    @Lazy
    private IGoodStoreSpecificationService iGoodStoreSpecificationService;

    @Autowired
    @Lazy
    private IGoodStoreListService iGoodStoreListService;

    @Autowired
    @Lazy
    private IOrderStoreGoodRecordService iOrderStoreGoodRecordService;

    @Autowired
    @Lazy
    private IOrderStoreGoodRecordService orderStoreGoodRecordService;
    @Autowired
    @Lazy
    private ISysUserService sysUserService;
    @Autowired
    @Lazy
    private IStoreAddressService storeAddressService;

    @Autowired
    @Lazy
    private IStoreTemplateService iStoreTemplateService;
    @Autowired
    @Lazy
    private ISysDictService iSysDictService;
    @Autowired
    @Lazy
    private IMarketingStoreDistributionSettingService iMarketingStoreDistributionSettingService;
    @Autowired
    @Lazy
    private IMemberRechargeRecordService iMemberRechargeRecordService;
    @Autowired
    @Lazy
    private IMemberDistributionRecordService iMemberDistributionRecordService;
    @Autowired
    @Lazy
    private IMemberAccountCapitalService iMemberAccountCapitalService;

    @Autowired
    private IStoreAccountCapitalService iStoreAccountCapitalService;

    @Autowired
    @Lazy
    private IMemberGradeService iMemberGradeService;

    @Autowired
    @Lazy
    private IMemberGrowthRecordService iMemberGrowthRecordService;
    @Autowired
    @Lazy
    private IOrderStoreTemplateService iOrderStoreTemplateService;

    @Autowired
    @Lazy
    private IMarketingWelfarePaymentsSettingService iMarketingWelfarePaymentsSettingService;

    @Autowired
    @Lazy
    private IMarketingStoreGiftCardMemberListService iMarketingStoreGiftCardMemberListService;

    @Autowired
    private IMemberWelfarePaymentsService iMemberWelfarePaymentsService;


    @Autowired
    private PayUtils payUtils;

    @Autowired
    private HftxPayUtils hftxPayUtils;

    @Autowired
    @Lazy
    private IStoreCashierRoutingService iStoreCashierRoutingService;

    @Autowired
    private IStoreOrderSettingService iStoreOrderSettingService;

    @Autowired
    private IMarketingStorePrefectureGoodService iMarketingStorePrefectureGoodService;

    @Autowired
    private IMarketingDiscountGoodService marketingDiscountGoodService;

    @Autowired
    private IMarketingDiscountCouponRecordService marketingDiscountCouponRecordService;

    @Autowired
    private IOrderStoreSubListService orderStoreSubListService;

    @Autowired
    private IOrderRefundListService orderRefundListService;

    @Autowired
    private IMemberWelfarePaymentsService memberWelfarePaymentsService;

    @Value(value = "${jeecg.path.upload}")
    private String uploadpath;

    @Autowired
    private ISysBaseAPI sysBaseAPI;

    @Autowired
    private IStoreManageService storeManageService;

    @Autowired
    private TengxunMapUtils tengxunMapUtils;

    @Autowired
    private IStoreAgencyService storeAgencyService;

    @Autowired
    private IStoreFranchiserService storeFranchiserService;

    @Autowired
    private IMarketingStoreGiftCardMemberListService marketingStoreGiftCardMemberListService;

    @Autowired
    private StoreUtils storeUtils;

    @Autowired
    @Lazy
    private IMarketingDistributionSettingService iMarketingDistributionSettingService;

    @Autowired
    private OrderStoreGoodRecordMapper orderStoreGoodRecordMapper;

    @Override
    public IPage<OrderStoreListDTO> getOrderStoreListDto(Page<OrderStoreList> page, OrderStoreListVO orderListVO, String sysUserId) {
        boolean searchCount = page.searchCount(); // false 表示为导出调用
        IPage<OrderStoreListDTO> page1 = orderStoreListMapper.getOrderStoreListDto(page, orderListVO);
        //批量查询会员信息
        List<String> memberListIds = page1.getRecords().stream().map(OrderStoreListDTO::getMemberListId).collect(Collectors.toList());
        Map<String, MemberList> memberListMap = new HashMap<>();
        if (CollUtil.isNotEmpty(memberListIds)) {
            memberListMap = iMemberListService.listByIds(memberListIds).stream().collect(Collectors.toMap(MemberList::getId, m -> m));
        }
//        Map<String, MarketingDiscountCoupon> marketingDiscountCouponMap = new HashMap<>();
        Map<String, List<OrderStoreSubListDTO>> orderStoreSubListDTOMap1 = new HashMap<>();
        Map<String, List<OrderStoreSubListDTO>> orderStoreSubListDTOMap2 = new HashMap<>();
        Map<String, List<OrderStoreGoodRecord>> orderStoreGoodRecordListMap = new HashMap<>();
        Map<String, List<OrderStoreGoodRecordDTO>> orderStoreGoodRecordDTOMap = new HashMap<>();
        Map<String, SysUser> sysUserMap = new HashMap<>();
        if (searchCount) {
            //批量查询优惠券信息
//            List<String> marketingDiscountCouponIds = page1.getRecords().stream().map(OrderStoreListDTO::getMarketingDiscountCouponId).collect(Collectors.toList());
//            if (CollUtil.isNotEmpty(marketingDiscountCouponIds)) {
//                marketingDiscountCouponMap = iMarketingDiscountCouponService.listByIds(marketingDiscountCouponIds).stream().collect(Collectors.toMap(MarketingDiscountCoupon::getId, m -> m));
//            }
            //批量查询包裹信息：发货后的商品信息
            List<String> orderStoreIds1 = page1.getRecords().stream().filter(ol -> ("2".equals(ol.getStatus()) || "3".equals(ol.getStatus()) || "5".equals(ol.getStatus())) && StrUtil.containsAny(ol.getDistribution(), "0", "2")).map(OrderStoreListDTO::getId).collect(Collectors.toList());
            //未发货的商品信息
            String orderStoreIds2 = page1.getRecords().stream().map(OrderStoreListDTO::getId).filter(id -> !orderStoreIds1.contains(id)).collect(Collectors.joining(","));

            if (CollUtil.isNotEmpty(orderStoreIds1)) {
                orderStoreSubListDTOMap1 = iOrderStoreSubListService.selectorderStoreListId(CollUtil.join(orderStoreIds1, ","), sysUserId, null, "0").stream().collect(Collectors.groupingBy(OrderStoreSubListDTO::getOrderStoreListId));
            }
            if (StrUtil.isNotBlank(orderStoreIds2)) {
                orderStoreSubListDTOMap2 = iOrderStoreSubListService.selectorderStoreListId(orderStoreIds2, sysUserId, "0", null).stream().collect(Collectors.groupingBy(OrderStoreSubListDTO::getOrderStoreListId));
            }
            List<OrderStoreSubListDTO> orderStoreSubListDTOs = CollUtil.union(orderStoreSubListDTOMap1.values(), orderStoreSubListDTOMap2.values()).stream().flatMap(Collection::stream).collect(Collectors.toList());
            List<String> sysUserIds = orderStoreSubListDTOs.stream().map(OrderStoreSubListDTO::getSysUserId).collect(Collectors.toList());
            List<String> orderStoreSubListIds = orderStoreSubListDTOs.stream().map(OrderStoreSubListDTO::getId).collect(Collectors.toList());
            // 发货地址id
//        List<String> storeAddressIdSenders = orderStoreSubListDTOs.stream().map(OrderStoreSubListDTO::getStoreAddressIdSender).filter(StrUtil::isNotBlank).collect(Collectors.toList());
            // 退货地址ids
//        List<String> storeAddressIdTuis = orderStoreSubListDTOs.stream().map(OrderStoreSubListDTO::getStoreAddressIdTui).filter(StrUtil::isNotBlank).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(sysUserIds)) {
                sysUserMap = sysUserService.listByIds(sysUserIds).stream().collect(Collectors.toMap(SysUser::getId, s -> s));
            }
            if (CollUtil.isNotEmpty(orderStoreSubListIds)) {
                orderStoreGoodRecordListMap = orderStoreGoodRecordService.selectOrderStoreSubListId(CollUtil.join(orderStoreSubListIds, ",")).stream().collect(Collectors.groupingBy(OrderStoreGoodRecord::getOrderStoreSubListId));
                orderStoreGoodRecordDTOMap = orderStoreGoodRecordService.selectOrderStoreSubListIdDTO(CollUtil.join(orderStoreSubListIds, ",")).stream().collect(Collectors.groupingBy(OrderStoreGoodRecordDTO::getOrderStoreSubListId));
            }
        }
        Map<String, MemberList> finalMemberListMap = memberListMap;
//        Map<String, MarketingDiscountCoupon> finalMarketingDiscountCouponMap = marketingDiscountCouponMap;
        Map<String, List<OrderStoreSubListDTO>> finalOrderStoreSubListDTOMap = orderStoreSubListDTOMap1;
        Map<String, List<OrderStoreSubListDTO>> finalOrderStoreSubListDTOMap1 = orderStoreSubListDTOMap2;
        Map<String, SysUser> finalSysUserMap = sysUserMap;
        Map<String, List<OrderStoreGoodRecord>> finalOrderStoreGoodRecordListMap = orderStoreGoodRecordListMap;
        Map<String, List<OrderStoreGoodRecordDTO>> finalOrderStoreGoodRecordDTOMap = orderStoreGoodRecordDTOMap;

        page1.getRecords().forEach(ol -> {
            //会员信息
            MemberList memberList = finalMemberListMap.get(ol.getMemberListId());
            if (memberList != null) {
                ol.setMemberList(memberList);
            }
            if (searchCount) {
//                if (StringUtils.isNotBlank(ol.getMarketingDiscountCouponId())) {
//                    //优惠券信息
//                    MarketingDiscountCoupon marketingDiscountCoupon = finalMarketingDiscountCouponMap.get(ol.getMarketingDiscountCouponId());
//                    if (marketingDiscountCoupon != null) {
//                        ol.setMarketingDiscountCoupon(marketingDiscountCoupon);
//                    }
//                }
                List<OrderStoreSubListDTO> orderProviderLists;
                if (("2".equals(ol.getStatus()) || "3".equals(ol.getStatus()) || "5".equals(ol.getStatus())) && StrUtil.containsAny(ol.getDistribution(), "0", "2")) {
                    //发货后的商品信息
                    orderProviderLists = finalOrderStoreSubListDTOMap.get(ol.getId());
                } else {
                    //未发货的商品信息
                    //查询供应商订单信息 sysUserId：null为平台登录，不为null为供应商登录
                    orderProviderLists = finalOrderStoreSubListDTOMap1.get(ol.getId());
                }
                if (CollUtil.isNotEmpty(orderProviderLists)) {
                    orderProviderLists.forEach(opl -> {
                        SysUser sysUser = finalSysUserMap.get(opl.getSysUserId());
                        if (sysUser != null) {
                            opl.setSysUserName(sysUser.getRealname());
                        }
                        List<OrderStoreGoodRecord> orderProviderGoodRecords = finalOrderStoreGoodRecordListMap.get(opl.getId());
                        //添加供应商订单商品记录
                        opl.setOrderStoreGoodRecords(orderProviderGoodRecords);

                        List<OrderStoreGoodRecordDTO> orderStoreGoodRecordDTOList = finalOrderStoreGoodRecordDTOMap.get(opl.getId());
                        //添加供应商订单商品记录
                        opl.setOrderStoreGoodRecordDTOList(orderStoreGoodRecordDTOList);

                        //供应商发货信息
                        Map<String, String> paramMap = Maps.newHashMap();
                        paramMap.put("id", opl.getStoreAddressIdSender());
                        if (opl.getStoreAddressIdSender() == null || "".equals(opl.getStoreAddressIdSender())) {
                            paramMap.put("sysUserId", opl.getSysUserId());
                            paramMap.put("isDeliver", "1");//发货默认
                            paramMap.put("isReturn", "");//退货
                        }
                        List<StoreAddressDTO> listStoreAddressDTO = storeAddressService.getlistStoreAddress(paramMap);
                        if (listStoreAddressDTO.size() > 0) {
                            opl.setStoreAddressDTOFa(listStoreAddressDTO.get(0));
                        }
                        //供应商退信息
                        Map<String, String> paramMaptui = Maps.newHashMap();
                        paramMaptui.put("id", opl.getStoreAddressIdTui());
                        if (opl.getStoreAddressIdTui() == null || "".equals(opl.getStoreAddressIdTui())) {
                            paramMaptui.put("sysUserId", opl.getSysUserId());
                            paramMaptui.put("isDeliver", "");//发货默认
                            paramMaptui.put("isReturn", "1");//退货
                        }
                        List<StoreAddressDTO> listStoreAddressDTOTui = storeAddressService.getlistStoreAddress(paramMaptui);
                        if (listStoreAddressDTOTui.size() > 0) {
                            opl.setStoreAddressDTOTui(listStoreAddressDTOTui.get(0));
                        }


                    });

                    //添加供应商订单列表
                    ol.setOrderStoreSubListDTOs(orderProviderLists);

                    List<String> OrderStoreSubIdList = new ArrayList<>();
                    //查询运费模板信息
                    for (OrderStoreSubListDTO opl : orderProviderLists) {
                        OrderStoreSubIdList.add(opl.getId());
                    }
                    List<Map<String, Object>> providerTemplateMaps = new ArrayList<>();
                    OrderStoreSubIdList = OrderStoreSubIdList.stream().distinct().collect(Collectors.toList());
                    //查询运费模板信息
                    providerTemplateMaps = iOrderStoreTemplateService.getOrderStoreTemplateMaps(OrderStoreSubIdList);
                    if (providerTemplateMaps.size() == 0) {
                        //如果未生成运费模板信息,查询运费模板匹配信息
                        providerTemplateMaps = iStoreTemplateService.getStoreTemplateMaps(OrderStoreSubIdList);
                    }
                    //添加运费模板信息
                    ol.setStoreTemplateMaps(providerTemplateMaps);
                }
            }

        });
        return page1;
    }

    @Override
    public OrderStoreListDTO getOrderStoreListDtoDetail(String orderNo, String sysUserId) {
        OrderStoreListDTO ol = orderStoreListMapper.getOrderStoreListDtoDetail(orderNo);
        if (ol == null) {
            throw new JeecgBootException("订单不存在");
        }
        return processOrderDetail(ol, sysUserId);
    }

    @Override
    public OrderStoreListDTO getOrderStoreListDtoDetailById(String id, String sysUserId) {
        OrderStoreListDTO ol = orderStoreListMapper.getOrderStoreListDtoDetailById(id);
        if (ol == null) {
            throw new JeecgBootException("订单不存在");
        }
        return processOrderDetail(ol, sysUserId);
    }

    /**
     * 处理订单详情通用逻辑
     *
     * @param ol 订单详情DTO
     * @param sysUserId 系统用户ID
     * @return 处理后的订单详情
     */
    private OrderStoreListDTO processOrderDetail(OrderStoreListDTO ol, String sysUserId) {
        //会员信息
        MemberList memberList = iMemberListService.getMemberListById(ol.getMemberListId());
        if (memberList != null) {
            ol.setMemberList(memberList);
        }
        if (StringUtils.isNotBlank(ol.getMarketingDiscountCouponId())) {
            //优惠券信息
            MarketingDiscountCoupon marketingDiscountCoupon = iMarketingDiscountCouponService.getById(ol.getMarketingDiscountCouponId());
            if (marketingDiscountCoupon != null) {
                ol.setMarketingDiscountCoupon(marketingDiscountCoupon);
            }
        }
        List<OrderStoreSubListDTO> orderProviderLists;
        if (("2".equals(ol.getStatus()) || "3".equals(ol.getStatus()) || "5".equals(ol.getStatus())) && StrUtil.equals(ol.getDistribution(), "0")) {
            //发货后的商品信息
            orderProviderLists = iOrderStoreSubListService.selectorderStoreListId(ol.getId(), sysUserId, null, "0");
        } else {
            //未发货的商品信息
            //查询供应商订单信息 sysUserId：null为平台登录，不为null为供应商登录
            orderProviderLists = iOrderStoreSubListService.selectorderStoreListId(ol.getId(), sysUserId, "0", null);
        }

        orderProviderLists.forEach(opl -> {
            SysUser sysUser = sysUserService.getById(opl.getSysUserId());
            if (sysUser != null) {
                opl.setSysUserName(sysUser.getRealname());
            }
            List<OrderStoreGoodRecord> orderProviderGoodRecords = orderStoreGoodRecordService.selectOrderStoreSubListId(opl.getId());
            //添加供应商订单商品记录
            opl.setOrderStoreGoodRecords(orderProviderGoodRecords);

            List<OrderStoreGoodRecordDTO> orderStoreGoodRecordDTOList = orderStoreGoodRecordService.selectOrderStoreSubListIdDTO(opl.getId());
            //添加供应商订单商品记录
            opl.setOrderStoreGoodRecordDTOList(orderStoreGoodRecordDTOList);


            //供应商发货信息
            Map<String, String> paramMap = Maps.newHashMap();
            paramMap.put("id", opl.getStoreAddressIdSender());
            if (opl.getStoreAddressIdSender() == null || "".equals(opl.getStoreAddressIdSender())) {
                paramMap.put("sysUserId", opl.getSysUserId());
                paramMap.put("isDeliver", "1");//发货默认
                paramMap.put("isReturn", "");//退货
            }
            List<StoreAddressDTO> listStoreAddressDTO = storeAddressService.getlistStoreAddress(paramMap);
            if (listStoreAddressDTO.size() > 0) {
                opl.setStoreAddressDTOFa(listStoreAddressDTO.get(0));
            }
            //供应商退信息
            Map<String, String> paramMaptui = Maps.newHashMap();
            paramMaptui.put("id", opl.getStoreAddressIdTui());
            if (opl.getStoreAddressIdTui() == null || "".equals(opl.getStoreAddressIdTui())) {
                paramMaptui.put("sysUserId", opl.getSysUserId());
                paramMaptui.put("isDeliver", "");//发货默认
                paramMaptui.put("isReturn", "1");//退货
            }
            List<StoreAddressDTO> listStoreAddressDTOTui = storeAddressService.getlistStoreAddress(paramMaptui);
            if (listStoreAddressDTOTui.size() > 0) {
                opl.setStoreAddressDTOTui(listStoreAddressDTOTui.get(0));
            }


        });
        //添加供应商订单列表
        if (orderProviderLists.size() > 0) {
            ol.setOrderStoreSubListDTOs(orderProviderLists);

            List<String> OrderStoreSubIdList = new ArrayList<>();
            //查询运费模板信息
            for (OrderStoreSubListDTO opl : orderProviderLists) {
                OrderStoreSubIdList.add(opl.getId());
            }
            List<Map<String, Object>> providerTemplateMaps = new ArrayList<>();
            OrderStoreSubIdList = OrderStoreSubIdList.stream().distinct().collect(Collectors.toList());
            //查询运费模板信息
            providerTemplateMaps = iOrderStoreTemplateService.getOrderStoreTemplateMaps(OrderStoreSubIdList);
            if (providerTemplateMaps.size() == 0) {
                //如果未生成运费模板信息,查询运费模板匹配信息
                providerTemplateMaps = iStoreTemplateService.getStoreTemplateMaps(OrderStoreSubIdList);
            }
            //添加运费模板信息
            ol.setStoreTemplateMaps(providerTemplateMaps);
        }
        return ol;
    }

    //发货后信息
    @Override
    public IPage<OrderStoreListDTO> getOrderListDtoWaitForReceiving(Page<OrderStoreList> page, OrderStoreListVO orderListVO, String sysUserId) {
        boolean searchCount = page.searchCount(); // false 表示为导出调用
        IPage<OrderStoreListDTO> page1 = orderStoreListMapper.getOrderStoreListDto(page, orderListVO);
        //批量查询会员信息
        List<String> memberListIds = page1.getRecords().stream().map(OrderStoreListDTO::getMemberListId).collect(Collectors.toList());
        Map<String, MemberList> memberListMap = new HashMap<>();
        if (CollUtil.isNotEmpty(memberListIds)) {
            memberListMap = iMemberListService.listByIds(memberListIds).stream().collect(Collectors.toMap(MemberList::getId, m -> m));
        }
        Map<String, MemberList> finalMemberListMap = memberListMap;
        page1.getRecords().forEach(ol -> {
            //会员信息
            MemberList memberList = finalMemberListMap.get(ol.getMemberListId());
            if (memberList != null) {
                ol.setMemberList(memberList);
            }
            if (searchCount) {
                //优惠券信息
//                MarketingDiscountCoupon marketingDiscountCoupon = iMarketingDiscountCouponService.getById(ol.getMarketingDiscountCouponId());
//                if (marketingDiscountCoupon != null) {
//                    ol.setMarketingDiscountCoupon(marketingDiscountCoupon);
//                }
                //查询供应商订单信息 sysUserId：null为平台登录，不为null为供应商登录
                List<OrderStoreSubListDTO> orderProviderLists = new ArrayList<>();
                if (StrUtil.equals(ol.getDistribution(), "0")) {
                    orderProviderLists  = iOrderStoreSubListService.selectorderStoreListId(ol.getId(), sysUserId, null, "0");
                } else if (StrUtil.equals(ol.getDistribution(), "1")) {
                    orderProviderLists = iOrderStoreSubListService.selectorderStoreListId(ol.getId(), sysUserId, "0", null);
                } else {
                    orderProviderLists = iOrderStoreSubListService.selectorderStoreListId(ol.getId(), sysUserId, null, "0");
                }
                orderProviderLists.forEach(opl -> {
                    SysUser sysUser = sysUserService.getById(opl.getSysUserId());
                    if (sysUser != null) {
                        opl.setSysUserName(sysUser.getRealname());
                    }
                    List<OrderStoreGoodRecord> orderProviderGoodRecords = orderStoreGoodRecordService.selectOrderStoreSubListId(opl.getId());
                    //添加供应商订单商品记录
                    opl.setOrderStoreGoodRecords(orderProviderGoodRecords);
                    List<OrderStoreGoodRecordDTO> orderStoreGoodRecordDTOList = orderStoreGoodRecordService.selectOrderStoreSubListIdDTO(opl.getId());
                    //添加供应商订单商品记录
                    opl.setOrderStoreGoodRecordDTOList(orderStoreGoodRecordDTOList);

                    //供应商发货信息
                    Map<String, String> paramMap = Maps.newHashMap();
                    paramMap.put("id", opl.getStoreAddressIdSender());
                    if (opl.getStoreAddressIdSender() == null || "".equals(opl.getStoreAddressIdSender())) {
                        paramMap.put("sysUserId", opl.getSysUserId());
                        paramMap.put("isDeliver", "1");//发货默认
                        paramMap.put("isReturn", "");//退货
                    }
                    List<StoreAddressDTO> listStoreAddressDTO = storeAddressService.getlistStoreAddress(paramMap);
                    if (listStoreAddressDTO.size() > 0) {
                        opl.setStoreAddressDTOFa(listStoreAddressDTO.get(0));
                    }
                    //供应商退信息
                    Map<String, String> paramMaptui = Maps.newHashMap();
                    paramMaptui.put("id", opl.getStoreAddressIdTui());
                    if (opl.getStoreAddressIdTui() == null || "".equals(opl.getStoreAddressIdTui())) {
                        paramMaptui.put("sysUserId", opl.getSysUserId());
                        paramMaptui.put("isDeliver", "");//发货默认
                        paramMaptui.put("isReturn", "1");//退货
                    }
                    List<StoreAddressDTO> listStoreAddressDTOTui = storeAddressService.getlistStoreAddress(paramMaptui);
                    if (listStoreAddressDTOTui.size() > 0) {
                        opl.setStoreAddressDTOTui(listStoreAddressDTOTui.get(0));
                    }
                });
                //添加供应商订单列表
                if (orderProviderLists.size() > 0) {
                    ol.setOrderStoreSubListDTOs(orderProviderLists);
                    List<String> OrderStoreSubIdList = new ArrayList<>();
                    //查询运费模板信息
                    for (OrderStoreSubListDTO opl : orderProviderLists) {
                        OrderStoreSubIdList.add(opl.getId());
                    }
                    OrderStoreSubIdList = OrderStoreSubIdList.stream().distinct().collect(Collectors.toList());
                    List<Map<String, Object>> providerTemplateMaps = new ArrayList<>();
                    //查询运费模板信息
                    providerTemplateMaps = iOrderStoreTemplateService.getOrderStoreTemplateMaps(OrderStoreSubIdList);
                    if (providerTemplateMaps.size() == 0) {
                        //如果未生成运费模板信息,查询运费模板匹配信息
                        providerTemplateMaps = iStoreTemplateService.getStoreTemplateMaps(OrderStoreSubIdList);
                    }
                    //添加运费模板信息
                    ol.setStoreTemplateMaps(providerTemplateMaps);
                }
            }
        });

        return page1;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public OrderStoreList submitOrderStoreGoods(Map<String, Object> storeGood,
                                                String memberId, String orderJson,
                                                MemberShippingAddress memberShippingAddress,
                                                String longitude, String latitude, String tMemberId) {

        //总运费
        BigDecimal freight = new BigDecimal(0);
        //价格
        BigDecimal totalPrice = new BigDecimal(0);
        //店铺成本价
        BigDecimal costPrice = new BigDecimal(0);
        //件数
        BigDecimal allNumberUnits = new BigDecimal(0);
        //分享折扣总金额
        BigDecimal shareDiscountAmount = BigDecimal.ZERO;
        //分享折扣比例
        BigDecimal shareDiscountRatio = (BigDecimal) storeGood.getOrDefault("shareDiscountRatio", BigDecimal.ZERO);

        //获取用户信息
        MemberList memberList = iMemberListService.getById(memberId);
        //解析订单json
        JSONObject orderJsonObject = JSON.parseObject(orderJson);
        JSONArray storeGoods = orderJsonObject.getJSONArray("storeGoods");
        JSONObject jsonGoods = null;
        for (Object s : storeGoods) {
            JSONObject ss = (JSONObject) s;
            if (storeGood.get("id").toString().equals(ss.getString("id"))) {
                jsonGoods = ss;
                break;
            }
        }

        if (jsonGoods == null) {
            throw new JeecgBootException("传入的店铺id有误~");
        }
        //获取店铺信息
        String storeSysUserId = storeGood.get("id").toString();
        StoreManage storeManage = storeManageService.getStoreManageBySysUserId(storeSysUserId);

        //快递配送方式则需要传收货地址
        String distribution = StrUtil.blankToDefault(Convert.toStr(storeGood.get("distribution")), "0");
        if (StrUtil.containsAny(distribution, "0", "2")) {
            if (memberShippingAddress == null) {
                throw new JeecgBootException("收货地址不能为空！！！");
            }
        }

        //建立店铺订单
        OrderStoreList orderStoreList = new OrderStoreList();
        orderStoreList.setDelFlag("0");
        orderStoreList.setOrderType(storeManage.getStoreType());
        //订单号
        orderStoreList.setOrderNo(OrderNoUtils.getOrderNo());
        //会员设置
        orderStoreList.setMemberListId(memberId);
        orderStoreList.setMemberUniqueId(memberList.getUniqueId());
        //收货地址设置
        if (memberShippingAddress != null) {
            orderStoreList.setConsignee(memberShippingAddress.getLinkman());
            orderStoreList.setContactNumber(memberShippingAddress.getPhone());
            orderStoreList.setShippingAddress(memberShippingAddress.getAreaExplan() + memberShippingAddress.getAreaAddress());
            orderStoreList.setHouseNumber(memberShippingAddress.getHouseNumber());
        }
        //设置留言
        orderStoreList.setMessage(jsonGoods.getString("message"));
        orderStoreList.setCoupon(new BigDecimal(0));
        //设置无修改地址
        orderStoreList.setIsUpdateAddr("0");
        orderStoreList.setStatus("0");

        //推广人默认绑定系统运营人员
        MemberList systemOperation = iMemberListService.getOne(new LambdaQueryWrapper<MemberList>()
                .eq(MemberList::getIsSystemOperation, "1"),false);
        if (systemOperation != null) {
            orderStoreList.setPromoter(systemOperation.getId())
                    .setPromoterType("2")
                    .setMemberLevel(systemOperation.getMemberLevel() + 1)
                    .setMemberPath(systemOperation.getMemberPath() + "->" + memberList.getUniqueId());
        }

        if (StrUtil.isNotBlank(memberList.getPromoter())) {
            MemberList tMemberList = iMemberListService.getById(memberList.getPromoter());
            if (tMemberList != null) {
                orderStoreList.setPromoter(tMemberList.getId())
                        .setPromoterType("1")
                        .setMemberLevel(tMemberList.getMemberLevel() + 1)
                        .setMemberPath(tMemberList.getMemberPath() + "->" + memberList.getUniqueId());
            }
        } else {
            //自然客，还未绑定实际推广人：判断当前推广用户是否为助梦家，助梦家则绑定实际推广人
            if (StrUtil.equals(memberList.getPromoterType(),"2")
                    && StrUtil.equals(memberList.getMemberType(),"0") && StrUtil.isBlank(memberList.getPromoter())) {
                if (StrUtil.isNotBlank(tMemberId)) {
                    //获取推广人信息
                    MemberList tMemberList = iMemberListService.getById(tMemberId);
                    if (tMemberList != null && StrUtil.equals(tMemberList.getIsLoveAmbassador(),"1")) {
                        orderStoreList.setPromoter(tMemberId)
                                .setPromoterType("1")
                                .setMemberLevel(tMemberList.getMemberLevel() + 1)
                                .setMemberPath(tMemberList.getMemberPath() + "->" + memberList.getUniqueId());
                    }
                }
            }
        }

        String promoter = orderStoreList.getPromoter();
        MemberList firstPromoterMemberList = null;
        MarketingStoreDistributionSetting marketingStoreDistributionSetting = null;
        if (StringUtils.isNotBlank(promoter)) {
            //一级推广人——>计算推广佣金
            firstPromoterMemberList = iMemberListService.getById(promoter);
            if (firstPromoterMemberList != null) {
                //店铺分销设置：分销比例优先取商品配置，如无，则取公共配置
                LambdaQueryWrapper<MarketingStoreDistributionSetting> marketingStoreDistributionSettingLambdaQueryWrapper = new LambdaQueryWrapper<MarketingStoreDistributionSetting>()
                        .eq(MarketingStoreDistributionSetting::getDelFlag, "0")
                        .eq(MarketingStoreDistributionSetting::getStatus, "1")
                        .eq(MarketingStoreDistributionSetting::getSysUserId, orderStoreList.getSysUserId());
                //获取分销比例
                marketingStoreDistributionSetting = iMarketingStoreDistributionSettingService.getOne(marketingStoreDistributionSettingLambdaQueryWrapper,false);
            }
        }

        List<Map<String, Object>> myStoreGoods = (List<Map<String, Object>>) storeGood.get("myStoreGoods");

        //归属店铺
        orderStoreList.setSysUserId(storeSysUserId);


        //配送方式
        orderStoreList.setDistribution(distribution);

        //是否部分发货
        orderStoreList.setIsSender("0");

        //设置未评价
        orderStoreList.setIsEvaluate("0");

        //保存订单信息
        this.saveOrUpdate(orderStoreList);

        //建立sub子订单信息

        //商品总价(成本价)
        BigDecimal goodsTotal = new BigDecimal(0);
        //建立供应商订单
        OrderStoreSubList orderStoreSubList = new OrderStoreSubList();
        orderStoreSubList.setDelFlag("0");
        orderStoreSubList.setMemberListId(memberList.getId());
        //设置订单id
        orderStoreSubList.setOrderStoreListId(orderStoreList.getId());
        //设置店铺id
        orderStoreSubList.setSysUserId(storeSysUserId);
        //设置订单号
        orderStoreSubList.setOrderNo(OrderNoUtils.getOrderNo());
        orderStoreSubList.setDistribution(orderStoreList.getDistribution());
        orderStoreSubList.setParentId("0");
        orderStoreSubList.setStatus("0");
        orderStoreSubList.setShipFee(orderStoreList.getShipFee());
        //保存订单信息
        iOrderStoreSubListService.saveOrUpdate(orderStoreSubList);
        //建立店铺商品快照

        BigDecimal distance = BigDecimal.ZERO;
        //todo 有地址计算运费
        if (memberShippingAddress != null) {
            if (StrUtil.equals(distribution, "0")) {
                freight = iStoreTemplateService.calculateFreight(myStoreGoods, memberShippingAddress.getSysAreaId());
                //添加店铺运费模板信息
                iStoreTemplateService.addOrderStoreTemplate(myStoreGoods, memberShippingAddress.getSysAreaId(), orderStoreSubList);
            } else if (StrUtil.equals(distribution, "2")) {
                freight = ObjectUtil.defaultIfNull(jsonGoods.getBigDecimal("freight"), BigDecimal.ZERO);
                distance = ObjectUtil.defaultIfNull(jsonGoods.getBigDecimal("distance"), BigDecimal.ZERO);
            }
        }
        orderStoreList.setShipDistance(distance);
        orderStoreList.setShipFee(freight);
        orderStoreSubList.setShipFee(freight);

        //遍历店铺商品，计算总价
        for (Map<String, Object> m : myStoreGoods) {
            String goodId = Convert.toStr(m.get("goodId"));
            GoodStoreSpecification goodStoreSpecification = iGoodStoreSpecificationService.getById(m.get("goodSpecificationId").toString());
            GoodStoreList goodStoreList = iGoodStoreListService.getById(goodId);
            goodsTotal = goodsTotal.add(goodStoreSpecification.getCostPrice());
            BigDecimal total = NumberUtil.mul((BigDecimal) m.get("price"), ((BigDecimal) m.get("quantity")));

            //添加商品记录
            OrderStoreGoodRecord orderStoreGoodRecord = new OrderStoreGoodRecord();
            orderStoreGoodRecord.setDelFlag("0");
            orderStoreGoodRecord.setOrderStoreSubListId(orderStoreSubList.getId());
            orderStoreGoodRecord.setOrderNo(orderStoreList.getOrderNo());
            orderStoreGoodRecord.setMainPicture(goodStoreList.getMainPicture());
            orderStoreGoodRecord.setGoodStoreListId(goodStoreList.getId());
            orderStoreGoodRecord.setGoodStoreSpecificationId(goodStoreSpecification.getId());
            orderStoreGoodRecord.setGoodName(goodStoreList.getGoodName());
            orderStoreGoodRecord.setSpecification(goodStoreSpecification.getSpecification());
            orderStoreGoodRecord.setPrice(goodStoreSpecification.getPrice());
            orderStoreGoodRecord.setVipPrice(goodStoreSpecification.getVipPrice());
            orderStoreGoodRecord.setCostPrice(goodStoreSpecification.getCostPrice());
            orderStoreGoodRecord.setUnitPrice((BigDecimal) m.get("price"));
            orderStoreGoodRecord.setAmount((BigDecimal) m.get("quantity"));
            orderStoreGoodRecord.setTotal(total);
            orderStoreGoodRecord.setCustomaryDues(total);
            orderStoreGoodRecord.setActualPayment(total);
            orderStoreGoodRecord.setCoupon(BigDecimal.ZERO);
            orderStoreGoodRecord.setGiftCardCoupon(BigDecimal.ZERO);
            orderStoreGoodRecord.setTotalCoupon(BigDecimal.ZERO);
            orderStoreGoodRecord.setTMemberId(promoter);
            orderStoreGoodRecord.setTQuantity(Convert.toBigDecimal(m.get("tQuantity")));
            orderStoreGoodRecord.setPlatformCommission(NumberUtil.mul(total, new BigDecimal("10")).divide(new BigDecimal(100), 2, RoundingMode.DOWN));

            // 佣金计算逻辑已移至 processMemberCommission 方法中
            //商品总重量
            orderStoreGoodRecord.setWeight(goodStoreSpecification.getWeight().multiply(new BigDecimal(m.get("quantity").toString())).setScale(3, RoundingMode.DOWN));
            iOrderStoreGoodRecordService.save(orderStoreGoodRecord);

            // 扣减规格库存
            goodStoreSpecification.setRepertory(goodStoreSpecification.getRepertory().subtract(orderStoreGoodRecord.getAmount()));
            iGoodStoreSpecificationService.saveOrUpdate(goodStoreSpecification);

            // 同步更新商品主表库存
            iGoodStoreListService.updateGoodStoreListRepertory(goodStoreList.getId());

            //计算总价格
            totalPrice = totalPrice.add(orderStoreGoodRecord.getTotal());
            m.put("totalPrice", orderStoreGoodRecord.getTotal());

            //店铺成本价
            costPrice = costPrice.add(goodStoreSpecification.getCostPrice().multiply((BigDecimal) m.get("quantity")));
            //商品总件数
            allNumberUnits = allNumberUnits.add((BigDecimal) m.get("quantity"));
        }
        //商品总价(成本价)
        orderStoreSubList.setGoodsTotal(goodsTotal);
        //应付款
        orderStoreSubList.setCustomaryDues(orderStoreSubList.getGoodsTotal().add(orderStoreSubList.getShipFee()));
        //实付款
        orderStoreSubList.setActualPayment(orderStoreSubList.getGoodsTotal().add(orderStoreSubList.getShipFee()));
        //保存订单信息
        iOrderStoreSubListService.saveOrUpdate(orderStoreSubList);

        //子订单数(拆分供应商后写入)
        orderStoreList.setChildOrder(new BigDecimal(1));
        //设置商品总价
        orderStoreList.setGoodsTotal(totalPrice);
        //运费
        orderStoreList.setShipFee(freight);
        //商品总件数
        orderStoreList.setAllNumberUnits(allNumberUnits);
        //成本价
        costPrice = costPrice.add(freight);
        orderStoreList.setCostPrice(costPrice);

        // 计算分享折扣金额 - 基于原始商品总价
        // 如果有分享折扣比例且大于0
        if (shareDiscountRatio != null && shareDiscountRatio.compareTo(BigDecimal.ZERO) > 0) {
            // 计算折扣金额
            shareDiscountAmount = totalPrice.multiply(shareDiscountRatio).divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP);
        }

        // 设置分享折扣相关字段
        orderStoreList.setShareDiscountAmount(shareDiscountAmount);
        orderStoreList.setShareDiscountRatio(shareDiscountRatio);

        //应付款 (减去分享折扣)
        orderStoreList.setCustomaryDues(totalPrice.subtract(ObjectUtil.defaultIfNull(orderStoreList.getCoupon(), new BigDecimal("0"))).subtract(shareDiscountAmount));
        //实付款 (减去分享折扣)
        orderStoreList.setActualPayment(totalPrice.subtract(ObjectUtil.defaultIfNull(orderStoreList.getCoupon(), new BigDecimal("0"))).subtract(shareDiscountAmount));

        //实付款小于等于，就设置为0
        if (orderStoreList.getActualPayment().doubleValue() <= 0) {
            orderStoreList.setActualPayment(new BigDecimal(0));
        }

        //给应付款和支付款加上运费
        orderStoreList.setCustomaryDues(orderStoreList.getCustomaryDues().add(freight));
        //实付款
        orderStoreList.setActualPayment(orderStoreList.getActualPayment().add(freight));
        this.saveOrUpdate(orderStoreList);

        // 处理佣金逻辑
        BigDecimal actualBrokerage = processMemberCommission(orderStoreList.getOrderNo(), myStoreGoods, firstPromoterMemberList, marketingStoreDistributionSetting, orderStoreList.getMemberListId());
        orderStoreList.setDistributionCommission(actualBrokerage);
        log.info("实际分销金额：" + actualBrokerage);

        //商品利润
        orderStoreList.setProfit(orderStoreList.getActualPayment().subtract(costPrice));

        //净利润
        orderStoreList.setRetainedProfits(orderStoreList.getProfit().subtract(actualBrokerage));

        // 处理店铺实际到账金额
        BigDecimal lasteActuallyReceivedAmount = processStoreActuallyReceivedAmount(orderStoreList, storeManage, actualBrokerage);

        orderStoreList.setActuallyReceivedAmount(lasteActuallyReceivedAmount);
        //保存订单信息
        this.saveOrUpdate(orderStoreList);
        return orderStoreList;
    }

    @Override
    @Transactional
    public Boolean paySuccessOrder(String id, PayOrderCarLog payOrderCarLog) {
        //修改订单成功状态信息
        OrderStoreList orderStoreList = this.getById(id);
        if (orderStoreList == null) {
            log.error("找不到店铺订单，ID: {}", id);
            return false;
        }
        
        String distribution = orderStoreList.getDistribution();
        if (StrUtil.containsAny(distribution, "0", "2") && orderStoreList.getStatus().equals("1")) {
            return true;
        }
        // 同城自提订单没有待发货状态，付款成功就是已发货
        if (StrUtil.equals(distribution, "1") && orderStoreList.getStatus().equals("2")) {
            return true;
        }

        if (StrUtil.containsAny(distribution, "0", "2")) {
            // 发送系统通知
            orderStoreList.setStatus("1");
        } else if (StrUtil.equals(distribution, "1")) {
            // 门店自提生成到店核销码 @张少林
            String ctxPath = uploadpath;
            String fileName = null;
            String bizPath = "files";
            String nowday = new SimpleDateFormat("yyyyMMdd").format(new Date());
            File file = new File(ctxPath + File.separator + bizPath + File.separator + nowday);
            if (!file.exists()) {
                file.mkdirs();// 创建文件根目录
            }

            String orgName = "qrOrderStorePickUp.png";// 获取文件名
            fileName = orgName.substring(0, orgName.lastIndexOf(".")) + "_" + System.currentTimeMillis() + orgName.substring(orgName.indexOf("."));
            String savePath = file.getPath() + File.separator + fileName;

            Boolean encode = QrCodeKit.encode(orderStoreList.getId(), BarcodeFormat.QR_CODE, 3, ErrorCorrectionLevel.H, "png", 200, 200,
                    savePath);
            String dbpath = bizPath + File.separator + nowday + File.separator + fileName;
            //券二维码
            orderStoreList.setStatus("2");
            orderStoreList.setPickUpQrAddr(dbpath);
            orderStoreList.setPickUpQrCode(OrderNoUtils.getOrderNo());
            orderStoreList.setPickUpVerificationStatus("0");
            orderStoreList.setShipmentsTime(new Date());
        }

        orderStoreList.setModePayment(payOrderCarLog.getPayModel());//支付方式的设定
        log.info("支付成功后日志内容：" + JSON.toJSONString(payOrderCarLog));
        BigDecimal integralValue = iMarketingWelfarePaymentsSettingService.getIntegralValue();
        if (payOrderCarLog.getPayPrice().doubleValue() != 0) {
            orderStoreList.setPayPrice(orderStoreList.getActualPayment().divide(payOrderCarLog.getAllTotalPrice(), 2, RoundingMode.HALF_UP).multiply(payOrderCarLog.getPayPrice()));
        }
        
        // 处理余额支付：拆分通用余额和店铺补助金
        if (payOrderCarLog.getBalance().doubleValue() != 0) {
            // 计算该订单在总支付中的比例
            BigDecimal orderRatio = orderStoreList.getActualPayment().divide(payOrderCarLog.getAllTotalPrice(), 2, RoundingMode.HALF_UP);
            
            // 计算该订单的总余额支付金额（通用余额 + 店铺补助金）
            BigDecimal totalBalanceForOrder = orderRatio.multiply(payOrderCarLog.getBalance());
            
            // 计算该订单的店铺补助金支付金额
            BigDecimal storeSubsidyForOrder = BigDecimal.ZERO;
            if (payOrderCarLog.getStoreSubsidyAmount() != null && payOrderCarLog.getStoreSubsidyAmount().doubleValue() != 0) {
                storeSubsidyForOrder = orderRatio.multiply(payOrderCarLog.getStoreSubsidyAmount());
            }
            
            // 计算该订单的通用余额支付金额 = 总余额支付 - 店铺补助金支付
            BigDecimal generalBalanceForOrder = totalBalanceForOrder.subtract(storeSubsidyForOrder);
            
            // 设置订单的支付金额字段
            orderStoreList.setBalance(generalBalanceForOrder);
            orderStoreList.setStoreSubsidyAmount(storeSubsidyForOrder);
            
            log.info("订单[{}]支付金额分解：总余额支付={}, 通用余额支付={}, 店铺补助金支付={}", 
                    orderStoreList.getOrderNo(), totalBalanceForOrder, generalBalanceForOrder, storeSubsidyForOrder);
        }
        
        if (payOrderCarLog.getWelfarePayments().doubleValue() != 0) {
            orderStoreList.setPayWelfarePayments(orderStoreList.getActualPayment().divide(payOrderCarLog.getAllTotalPrice(), 2, RoundingMode.HALF_UP).multiply(payOrderCarLog.getWelfarePayments()));
            orderStoreList.setPayWelfarePaymentsPrice(orderStoreList.getPayWelfarePayments().multiply(integralValue));
        }


        orderStoreList.setPayTime(new Date());
        //设置交易流水号
        orderStoreList.setSerialNumber(payOrderCarLog.getId());
        orderStoreList.setHftxSerialNumber(payOrderCarLog.getSerialNumber());

        //赠送积分，移除赠送积分。 2023-07-22 15:15:22 by 张少林

//        iStoreOrderSettingService.success(orderStoreList);

     //绑定实际推广人
     MemberList orderMemberList = iMemberListService.getById(orderStoreList.getMemberListId());
     if (StrUtil.equals(orderStoreList.getPromoterType(),"1") && StrUtil.isBlank(orderMemberList.getPromoter())) {
         // 获取分销设置
         MarketingDistributionSetting distributionSetting = iMarketingDistributionSettingService.lambdaQuery()
                 .eq(MarketingDistributionSetting::getStatus, "1")
                 .orderByDesc(MarketingDistributionSetting::getCreateTime)
                 .last("limit 1")
                 .one();

         // 检查是否配置了"被推荐人兑换产品"场景（值为1）
         if (distributionSetting != null &&
                 StringUtils.isNotBlank(distributionSetting.getDistributionBuild()) &&
                 distributionSetting.getDistributionBuild().contains("1")) {
             this.iMemberListService.setPromoter(orderMemberList, orderStoreList.getPromoter(),"1");
         }
     }

        this.saveOrUpdate(orderStoreList);

        //修改子订单状态
        OrderStoreSubList orderStoreSubList = new OrderStoreSubList();
        orderStoreSubList.setStatus(StrUtil.equals(distribution, "0") ? "1" : "2");
        UpdateWrapper<OrderStoreSubList> orderProviderListUpdateWrapper = new UpdateWrapper<>();
        orderProviderListUpdateWrapper.eq("order_store_list_id", orderStoreList.getId());
        iOrderStoreSubListService.update(orderStoreSubList, orderProviderListUpdateWrapper);
        //分销记录条件构造器
        LambdaQueryWrapper<MemberRechargeRecord> memberRechargeRecordLambdaQueryWrapper = new LambdaQueryWrapper<MemberRechargeRecord>()
                .eq(MemberRechargeRecord::getTradeNo, orderStoreList.getOrderNo())
                .eq(MemberRechargeRecord::getTradeType, "0")
                .eq(MemberRechargeRecord::getTradeStatus, "0");
        //查出分销记录
        if (iMemberRechargeRecordService.count(memberRechargeRecordLambdaQueryWrapper) > 0) {
            List<MemberRechargeRecord> memberRechargeRecords = iMemberRechargeRecordService.list(memberRechargeRecordLambdaQueryWrapper);
            memberRechargeRecords.forEach(mrrs -> {
                //修改分销记录状态
                iMemberRechargeRecordService.saveOrUpdate(mrrs.setTradeStatus("2"));
                MemberList memberList = iMemberListService.getById(mrrs.getMemberListId());
                if (StrUtil.equals(mrrs.getPayType(), "3")) {
                    // 分配分销佣金
                    iMemberListService.saveOrUpdate(memberList
                            .setAccountFrozen(memberList.getAccountFrozen().add(mrrs.getAmount())));
                } else if (StrUtil.equals(mrrs.getPayType(), "49")) {
                    // 分配企业家佣金
                    iMemberListService.saveOrUpdate(memberList
                            .setAccountFrozen(memberList.getAccountFrozen().add(mrrs.getAmount())));
                }
            });
        }

        //店铺到账金额明细
        LambdaQueryWrapper<StoreRechargeRecord> storeRechargeRecordLambdaQueryWrapper = new LambdaQueryWrapper<StoreRechargeRecord>()
                .eq(StoreRechargeRecord::getTradeNo, orderStoreList.getOrderNo())
                .eq(StoreRechargeRecord::getTradeType, "0")
                .eq(StoreRechargeRecord::getTradeStatus, "0")
                .eq(StoreRechargeRecord::getPayType, "0");
        iStoreRechargeRecordService.list(storeRechargeRecordLambdaQueryWrapper).forEach(srr -> {
            iStoreRechargeRecordService.saveOrUpdate(srr.setTradeStatus("2"));
            StoreManage storeManage = iStoreManageService.getById(srr.getStoreManageId());
            iStoreManageService.saveOrUpdate(storeManage
                    .setAccountFrozen(storeManage.getAccountFrozen().add(srr.getAmount())));
        });
        
        // 记录订单使用了店铺专用补助金
        log.info("订单[{}]支付成功，店铺[{}]", orderStoreList.getOrderNo(), orderStoreList.getSysUserId());
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void abrogateOrder(String id, String CloseExplain, String closeType) {
        OrderStoreList orderStoreList = this.getById(id);

        //买家关闭
        orderStoreList.setCloseType(closeType);
        orderStoreList.setCloseExplain(CloseExplain);
        orderStoreList.setCloseTime(new Date());
        //交易关闭
        orderStoreList.setStatus("4");
        if (StrUtil.equals(orderStoreList.getDistribution(), "1")) {
            orderStoreList.setPickUpVerificationStatus("2");
        }
        this.saveOrUpdate(orderStoreList);

        QueryWrapper<OrderStoreSubList> orderStoreSubListQueryWrapper = new QueryWrapper<>();
        orderStoreSubListQueryWrapper.eq("order_store_list_id", id);
        List<OrderStoreSubList> orderStoreSubLists = iOrderStoreSubListService.list(orderStoreSubListQueryWrapper);
        orderStoreSubLists.forEach(oss -> {
            oss.setStatus("4");
            iOrderStoreSubListService.saveOrUpdate(oss);
            //退回库存
            QueryWrapper<OrderStoreGoodRecord> orderStoreGoodRecordQueryWrapper = new QueryWrapper<>();
            orderStoreGoodRecordQueryWrapper.eq("order_store_sub_list_id", oss.getId());
            List<OrderStoreGoodRecord> orderStoreGoodRecords = iOrderStoreGoodRecordService.list(orderStoreGoodRecordQueryWrapper);
            orderStoreGoodRecords.forEach(osgr -> {
                String goodStoreSpecificationId = osgr.getGoodStoreSpecificationId();
                GoodStoreSpecification goodStoreSpecification = iGoodStoreSpecificationService.getById(goodStoreSpecificationId);
                if (goodStoreSpecification != null) {
                    // 恢复规格库存
                    goodStoreSpecification.setRepertory(goodStoreSpecification.getRepertory().add(osgr.getAmount()));
                    iGoodStoreSpecificationService.saveOrUpdate(goodStoreSpecification);

                    // 同步更新商品主表库存
                    iGoodStoreListService.updateGoodStoreListRepertory(goodStoreSpecification.getGoodStoreListId());
                }
            });
        });

        //分销资金到账关闭
        List<MemberRechargeRecord> memberRechargeRecords = iMemberRechargeRecordService.list(new LambdaQueryWrapper<MemberRechargeRecord>()
                .eq(MemberRechargeRecord::getTradeNo, orderStoreList.getOrderNo())
                .eq(MemberRechargeRecord::getTradeType, "0")
                .eq(MemberRechargeRecord::getTradeStatus, "0")
                .eq(MemberRechargeRecord::getPayType, "3"));
        if (oConvertUtils.isNotEmpty(memberRechargeRecords)) {
            //遍历出分销用户
            memberRechargeRecords.forEach(mrrs -> {
                //交易关闭
                iMemberRechargeRecordService.saveOrUpdate(mrrs
                        .setTradeStatus("7"));
            });
        }

        //店铺到账金额关闭
        LambdaQueryWrapper<StoreRechargeRecord> storeRechargeRecordLambdaQueryWrapper = new LambdaQueryWrapper<StoreRechargeRecord>()
                .eq(StoreRechargeRecord::getTradeNo, orderStoreList.getOrderNo())
                .eq(StoreRechargeRecord::getTradeType, "0")
                .eq(StoreRechargeRecord::getTradeStatus, "0")
                .eq(StoreRechargeRecord::getPayType, "0");
        iStoreRechargeRecordService.list(storeRechargeRecordLambdaQueryWrapper).forEach(srr -> {
            iStoreRechargeRecordService.saveOrUpdate(srr.setTradeStatus("7"));
        });
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<?> refundAndAbrogateOrder(String id, String closeExplain, String closeType) {
        OrderStoreList orderStoreList = this.getById(id);

        String status = orderStoreList.getStatus();
        if (StrUtil.equals(status,"2")) {
            closeType = "5";
        }

        if (StrUtil.equals(orderStoreList.getDistribution(), "0") && !StrUtil.equals(orderStoreList.getStatus(),"1")) {
            return Result.error("待发货订单才能取消并退款");
        }

        if (StrUtil.equals(orderStoreList.getDistribution(), "1") && !StrUtil.equals(orderStoreList.getStatus(),"2")) {
            return Result.error("待收货订单才能取消并退款");
        }

        // fix 在后台操作，取消并退款，福利金没有回收。by zhangshaolin
        if (CompareUtil.compare(orderStoreList.getGiveWelfarePayments(), BigDecimal.ZERO) > 0) {
            MemberList memberList = iMemberListService.getById(orderStoreList.getMemberListId());
            if (CompareUtil.compare(memberList.getWelfarePayments(), orderStoreList.getGiveWelfarePayments()) < 0) {
                return Result.error("会员福利金小于订单赠送福利金，无法退款");
            }
        }

        //现金支付退款
        if (orderStoreList.getPayPrice().doubleValue() > 0.0D) {
            Map balanceMap;
            try {
                balanceMap = this.hftxPayUtils.getSettleAccountBalance();
                log.info(JSON.toJSONString("账户余额信息：" + balanceMap));
                if (!balanceMap.get("status").equals("succeeded")) {
                    return Result.error("汇付账户的余额查询出错");
                }

                if (Double.parseDouble(balanceMap.get("avl_balance").toString()) < orderStoreList.getPayPrice().doubleValue()) {
                    Object var10000 = balanceMap.get("avl_balance");
                    return Result.error("汇付账户的余额：" + var10000 + "；需退金额：" + orderStoreList.getPayPrice());
                }
            } catch (BaseAdaPayException var6) {
                var6.printStackTrace();
            }

            balanceMap = this.payUtils.refund(orderStoreList.getPayPrice(), orderStoreList.getSerialNumber(), orderStoreList.getHftxSerialNumber());
            if (balanceMap.get("status").equals("failed")) {
                return Result.error("现金退款失败");
            }

            orderStoreList.setRefundJson(JSON.toJSONString(balanceMap));
        }
        this.saveOrUpdate(orderStoreList);
        
        //退回通用余额
        if (orderStoreList.getBalance() != null && orderStoreList.getBalance().compareTo(BigDecimal.ZERO) > 0) {
            iMemberListService.addBlance(orderStoreList.getMemberListId(), orderStoreList.getBalance(), orderStoreList.getOrderNo(), "2");
            log.info("订单[{}]退回通用余额：{}", orderStoreList.getOrderNo(), orderStoreList.getBalance());
        }
        
        //退回店铺补助金
        if (orderStoreList.getStoreSubsidyAmount() != null && orderStoreList.getStoreSubsidyAmount().compareTo(BigDecimal.ZERO) > 0) {
            BigDecimal restoredSubsidyAmount = iMemberListService.restoreStoreSubsidyForRefund(
                orderStoreList.getMemberListId(), orderStoreList.getOrderNo());
            log.info("订单[{}]恢复店铺补助金：预期金额={}，实际恢复金额={}", 
                    orderStoreList.getOrderNo(), orderStoreList.getStoreSubsidyAmount(), restoredSubsidyAmount);
        }
        
//        //退回交易积分
//        iMemberWelfarePaymentsService.addWelfarePayments(orderStoreList.getMemberListId(), orderStoreList.getPayWelfarePayments(), "20", orderStoreList.getOrderNo(), "");
        //取消订单
        this.abrogateOrder(id, closeExplain, closeType);


        //分销记录条件构造器
        LambdaQueryWrapper<MemberRechargeRecord> memberRechargeRecordLambdaQueryWrapper = new LambdaQueryWrapper<MemberRechargeRecord>()
                .eq(MemberRechargeRecord::getTradeNo, orderStoreList.getOrderNo())
                .eq(MemberRechargeRecord::getTradeType, "0")
                .eq(MemberRechargeRecord::getTradeStatus, "2");
        //查出分销记录
        if (iMemberRechargeRecordService.count(memberRechargeRecordLambdaQueryWrapper) > 0) {
            List<MemberRechargeRecord> memberRechargeRecords = iMemberRechargeRecordService.list(memberRechargeRecordLambdaQueryWrapper);
            memberRechargeRecords.forEach(mrrs -> {
                //修改分销记录状态
                iMemberRechargeRecordService.saveOrUpdate(mrrs.setTradeStatus("7"));
                MemberList memberList = iMemberListService.getById(mrrs.getMemberListId());
                if (StrUtil.equals(mrrs.getPayType(), "3")) {
                    // 分配分销佣金
                    iMemberListService.saveOrUpdate(memberList
                            .setAccountFrozen(memberList.getAccountFrozen().subtract(mrrs.getAmount())));
                } else if (StrUtil.equals(mrrs.getPayType(), "49")) {
                    // 分配代言人佣金
                    iMemberListService.saveOrUpdate(memberList
                            .setAccountFrozen(memberList.getAccountFrozen().subtract(mrrs.getAmount())));
                }
            });
        }

        //店铺到账金额明细
        LambdaQueryWrapper<StoreRechargeRecord> storeRechargeRecordLambdaQueryWrapper = new LambdaQueryWrapper<StoreRechargeRecord>()
                .eq(StoreRechargeRecord::getTradeNo, orderStoreList.getOrderNo())
                .eq(StoreRechargeRecord::getTradeType, "0")
                .eq(StoreRechargeRecord::getTradeStatus, "2")
                .eq(StoreRechargeRecord::getPayType, "0");
        iStoreRechargeRecordService.list(storeRechargeRecordLambdaQueryWrapper).forEach(srr -> {
            iStoreRechargeRecordService.saveOrUpdate(srr.setTradeStatus("7"));
            StoreManage storeManage = iStoreManageService.getById(srr.getStoreManageId());
            iStoreManageService.saveOrUpdate(storeManage
                    .setAccountFrozen(storeManage.getAccountFrozen().subtract(srr.getAmount())));
        });

        return Result.ok("取消并退款成功");
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void affirmOrder(String id) {

        OrderStoreList orderStoreList = getById(id);
        this.updateById(orderStoreList.setStatus("3").setDeliveryTime(new Date()));
        iOrderStoreSubListService.update(new OrderStoreSubList().setStatus("3"), new LambdaQueryWrapper<OrderStoreSubList>().eq(OrderStoreSubList::getOrderStoreListId, id));

        //判断分销
        LambdaQueryWrapper<MemberRechargeRecord> memberRechargeRecordLambdaQueryWrapper = new LambdaQueryWrapper<MemberRechargeRecord>()
                .eq(MemberRechargeRecord::getTradeNo, orderStoreList.getOrderNo())
                .eq(MemberRechargeRecord::getDelFlag,"0")
                .eq(MemberRechargeRecord::getGoAndCome, "0")
                .eq(MemberRechargeRecord::getTradeStatus, "2")
                .eq(MemberRechargeRecord::getTradeType, "0");
        //获取分销集合
        List<MemberRechargeRecord> memberRechargeRecords = iMemberRechargeRecordService.list(memberRechargeRecordLambdaQueryWrapper);
        //不为空时遍历
        if (oConvertUtils.isNotEmpty(memberRechargeRecords)) {
            memberRechargeRecords.forEach(mrrs -> {
                //获取分销会员
                MemberList memberList = iMemberListService.getById(mrrs.getMemberListId());
                // 更新一级分销佣金
                if (StrUtil.equals(mrrs.getPayType(),"3")) {
                    memberList.setFirstLevelCommission(memberList.getFirstLevelCommission().add(mrrs.getAmount()));
                }
                // 更新企业家佣金
                else if (StrUtil.equals(mrrs.getPayType(),"49")) {
                    memberList.setEntrepreneurCommission(memberList.getEntrepreneurCommission().add(mrrs.getAmount()));
                }

                // 更新总佣金
                memberList.setTotalCommission(memberList.getTotalCommission().add(mrrs.getAmount()));
                //将分销冻结金额转为金额
                iMemberListService.saveOrUpdate(memberList
                        .setAccountFrozen(memberList.getAccountFrozen().subtract(mrrs.getAmount()))
                        .setBalance(memberList.getBalance().add(mrrs.getAmount())));
                //交易完成
                mrrs.setTradeStatus("5");
                iMemberRechargeRecordService.saveOrUpdate(mrrs);
                //生成资金流水记录
                iMemberAccountCapitalService.save(new MemberAccountCapital()
                        .setDelFlag("0")
                        .setMemberListId(mrrs.getMemberListId())
                        .setPayType(mrrs.getPayType())
                        .setGoAndCome("0")
                        .setAmount(mrrs.getAmount())
                        .setOrderNo(mrrs.getOrderNo())
                        .setBalance(memberList.getBalance())
                        .setTradeNo(mrrs.getTradeNo())
                        .setSysUserId(orderStoreList.getSysUserId())); // 添加归属店铺ID
                log.info(" 店铺 确认收货后7天完成订单:分销会员:" + mrrs.getOrderNo());
            });
        }

        //店铺资金到账
        LambdaQueryWrapper<StoreRechargeRecord> storeRechargeRecordLambdaQueryWrapper = new LambdaQueryWrapper<StoreRechargeRecord>()
                .eq(StoreRechargeRecord::getTradeNo, orderStoreList.getOrderNo())
                .eq(StoreRechargeRecord::getGoAndCome, "0")
                .eq(StoreRechargeRecord::getTradeStatus, "2")
                .eq(StoreRechargeRecord::getTradeType, "0")
                .eq(StoreRechargeRecord::getPayType,"0");
        iStoreRechargeRecordService.list(storeRechargeRecordLambdaQueryWrapper).forEach(srr -> {
            //交易完成
            iStoreRechargeRecordService.saveOrUpdate(srr.setTradeStatus("5"));

            //将订单冻结金额转为金额
            StoreManage storeManage = iStoreManageService.getById(srr.getStoreManageId());
            iStoreManageService.saveOrUpdate(storeManage
                    .setAccountFrozen(storeManage.getAccountFrozen().subtract(srr.getAmount()))
                    .setBalance(storeManage.getBalance().add(srr.getAmount()))
                    .setTotalPerformance(NumberUtil.add(storeManage.getTotalPerformance(),srr.getAmount())));

            //生成资金流水记录
            iStoreAccountCapitalService.save(new StoreAccountCapital()
                    .setDelFlag("0")
                    .setStoreManageId(srr.getStoreManageId())
                    .setPayType(srr.getPayType())
                    .setGoAndCome("0")
                    .setAmount(srr.getAmount())
                    .setOrderNo(srr.getOrderNo())
                    .setBalance(storeManage.getBalance()));
        });

        //订单余额分账
//        iStoreCashierRoutingService.independentAccountOrderBalance(orderStoreList);

        //更新商品销量
        List<Map<String, Object>> orderStoreGoodRecordByOrderId = orderStoreGoodRecordMapper.getOrderStoreGoodRecordByOrderId(orderStoreList.getId());
        for (Map<String, Object> stringObjectMap : orderStoreGoodRecordByOrderId) {
            String specificationId = Convert.toStr(stringObjectMap.get("specificationId"));
            BigDecimal amount = Convert.toBigDecimal(stringObjectMap.get("amount"));
            GoodStoreSpecification goodStoreSpecification = iGoodStoreSpecificationService.getById(specificationId);
            if (goodStoreSpecification != null) {
                goodStoreSpecification.setSalesVolume(NumberUtil.add(goodStoreSpecification.getSalesVolume(),amount));
                BigDecimal bigDecimal = NumberUtil.sub(Convert.toBigDecimal(stringObjectMap.get("total")), Convert.toBigDecimal(stringObjectMap.get("shareCommission")), Convert.toBigDecimal(stringObjectMap.get("platformCommission")));
                goodStoreSpecification.setTotalPerformance(NumberUtil.add(goodStoreSpecification.getTotalPerformance(),bigDecimal));
                iGoodStoreSpecificationService.saveOrUpdate(goodStoreSpecification);

                GoodStoreList goodStoreList = iGoodStoreListService.getById(goodStoreSpecification.getGoodStoreListId());
                if (goodStoreList != null) {
                    goodStoreList.setTotalPerformance(NumberUtil.add(goodStoreList.getTotalPerformance(),bigDecimal));
                    iGoodStoreListService.saveOrUpdate(goodStoreList);
                }
            }
        }
    }

    /**
     * 待支付订单超时数据
     *
     * @param hour
     * @return
     */
    @Override
    public List<OrderStoreList> getCancelOrderStoreList(String hour) {
        return baseMapper.getCancelOrderStoreList(hour);
    }

    /**
     * 定时器取消订单
     */
    @Override
    public void cancelOrderStoreListJob() {
        //过期时间(小时)
        String hour = iSysDictService
                .queryTableDictTextByKey("sys_dict_item", "item_value", "item_text", "common_order_payment_timeout");
        if (StringUtils.isNotBlank(hour)) {
            List<OrderStoreList> orderStoreListList = baseMapper.getCancelOrderStoreList(hour);
            orderStoreListList.forEach(ol -> {
                log.info("定时器取消 店铺 待支付订单超时未支付:订单编号:" + ol.getOrderNo());
                abrogateOrder(ol.getId(), "0", "0");
            });

        }

    }

    /**
     * 返回待支付订单计时器(秒)
     *
     * @param id
     * @param hour
     * @return
     */
    @Override
    public String getOrderStoreListTimer(String id, String hour) {
        return baseMapper.getOrderStoreListTimer(id, hour);
    }

    /**
     * 确认订单超时数据
     *
     * @param hour
     * @return
     */
    @Override
    public List<OrderStoreList> getStoreConfirmReceiptOrderList(String hour) {
        return baseMapper.getStoreConfirmReceiptOrderList(hour);
    }

    /**
     * 定时器确认收货
     */
    @Override
    public void storeConfirmReceiptOrderList() {
        //确认收货时间(小时)
        String hour = iSysDictService
                .queryTableDictTextByKey("sys_dict_item", "item_value", "item_text", "order_confirmation_receiving_timeout");
        if (StringUtils.isNotBlank(hour)) {
            List<OrderStoreList> orderStoreListList = baseMapper.getStoreConfirmReceiptOrderList(hour);
            orderStoreListList.forEach(ol -> {
                log.info("定时器 店铺 确认收货超时:订单编号:" + ol.getOrderNo());
                affirmOrder(ol.getId());
            });

        }

    }

    @Override
    public void confirmReceiptOrderJob() {
        //确认收货时间超过7天后完成订单
        //获取售后时间(单位:小时)
        String hour = iSysDictService
                .queryTableDictTextByKey("sys_dict_item", "item_value", "item_text", "order_after_service_timeout");
        if (StringUtils.isNotBlank(hour)) {
            //查询出超过售后时间的订单(50条上限) 取店铺订单
            List<OrderStoreList> orderCompletion = baseMapper.getOrderCompletion(hour);
            //遍历店铺订单
            orderCompletion.forEach(oc -> {
                log.info(" 店铺 确认收货后7天完成订单:订单编号:" + oc.getOrderNo());
                this.accomplishStoreOrder(oc.getId());
            });
        }
    }


    /**
     * 返回确认收货订单计时器(秒)
     *
     * @param id
     * @param hour
     * @return
     */
    @Override
    public String getStoreConfirmReceiptTimer(String id, String hour) {
        return baseMapper.getStoreConfirmReceiptTimer(id, hour);
    }


    /**
     * 确认收货后7天完成订单,分销资金到账  fix 分销资金到账调整为确认收货后即到账，没有待评价状态
     * @param id
     */
    @Override
    @Transactional
    public void accomplishStoreOrder(String id) {
        OrderStoreList orderStoreList = this.getById(id);
        //写入订单完成时间,状态为交易完成
        this.saveOrUpdate(orderStoreList
                .setCompletionTime(new Date())
                .setStatus("5"));
    }

    /**
     * 订单导出列表
     *
     * @param orderStoreListVO
     * @return
     */
    @Override
    public List<OrderStoreListExportDTO> getOrderStoreListDtoExport(Map<String, Object> orderStoreListVO) {
        return baseMapper.getOrderStoreListDtoExport(orderStoreListVO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<String> ordereDlivery(String listMap) {
        Result<String> result = new Result<String>();
        JSONObject jsonObject = JSONObject.parseObject(new String(listMap));
        List<Map<String, Object>> listObjectSec = jsonObject.getJSONArray("listMap").toJavaObject(List.class);
        OrderStoreSubList orderStoreSubList = new OrderStoreSubList();
        OrderStoreGoodRecord orderStoreGoodRecord;
        //OrderStoreSubList orderStoreSubList;
        List<Map<String, Object>> orderProviderGoodRecordInfoList;
        String addorderProviderId;
        if (!listObjectSec.isEmpty()) {
            for (Map<String, Object> map : listObjectSec) {
                System.out.println("map++++" + map);
                //包裹数据
                List<Map<String, Object>> listParcelMapSS = (List<Map<String, Object>>) (List) map.get("listParcel");
                for (Map<String, Object> listParcelMap : listParcelMapSS) {
                    System.out.println("listParcelMap++++" + listParcelMap);
                    //包裹内的商品修改供应商订单id
                    orderProviderGoodRecordInfoList = (List<Map<String, Object>>) (List) listParcelMap.get("orderProviderGoodRecordInfo");
                    System.out.println("orderProviderGoodRecordInfoList++++" + orderProviderGoodRecordInfoList);
                    if (!orderProviderGoodRecordInfoList.isEmpty()) {
                        //查询供应商订单信息
                        orderStoreSubList = orderStoreSubListService.getById(listParcelMap.get("id").toString());
                        //添加包裹,并返回新增ID
                        addorderProviderId = addorderStoreSubList(orderStoreSubList, listParcelMap.get("logisticsCompany").toString(), listParcelMap.get("trackingNumber").toString());

                        for (Map<String, Object> orderProviderGoodRecordId : orderProviderGoodRecordInfoList) {
                            //订单商品信息
                            orderStoreGoodRecord = orderStoreGoodRecordService.getById(orderProviderGoodRecordId.get("id").toString());
                            if (orderStoreGoodRecord == null) {
                                throw new JeecgBootException("订单商品信息不存在");
                            }
                            QueryWrapper<OrderRefundList> orderRefundListLambdaQueryWrapper = new QueryWrapper<>();
                            orderRefundListLambdaQueryWrapper.select("IFNULL(sum(refund_amount),0) as ongoingRefundCount");
                            orderRefundListLambdaQueryWrapper.eq("order_good_record_id", orderStoreGoodRecord.getId());
                            orderRefundListLambdaQueryWrapper.in("status", "0", "1", "2", "3", "4");
                            Map<String, Object> refundMap = orderRefundListService.getMap(orderRefundListLambdaQueryWrapper);
                            if (Convert.toLong(refundMap.get("ongoingRefundCount")) > 0) {
                                throw new JeecgBootException("售后待处理订单，处理售后才可进行发货");
                            }
                            //修改商品的OrderProviderListId为包裹的已发货包裹
                            orderStoreGoodRecord.setOrderStoreSubListId(addorderProviderId);
                            orderStoreGoodRecord.setStatus("1");
                            orderStoreGoodRecordService.updateById(orderStoreGoodRecord);
                        }
                    }
                }

            }
            //调用方法
            //是否全部发货,修改orderList的状态内容
            ShipmentOrderModification(orderStoreSubList);
            result.success("添加成功!");
        }
        return result;
    }

    /**
     * 添加已发货的供应商包裹信息
     *
     * @param orderStoreSubList
     * @param logisticsCompany  物流公司
     * @param trackingNumber    快递单号
     */
    public String addorderStoreSubList(OrderStoreSubList orderStoreSubList, String logisticsCompany, String
            trackingNumber) {
        try {
            OrderStoreSubList opl = new OrderStoreSubList();
            // opl.setDelFlag(orderStoreSubList.getDelFlag());
            //opl = orderStoreSubList;
            opl.setDelFlag(orderStoreSubList.getDelFlag());
            opl.setMemberListId(orderStoreSubList.getMemberListId());
            opl.setOrderStoreListId(orderStoreSubList.getOrderStoreListId());
            opl.setSysUserId(orderStoreSubList.getSysUserId());
            opl.setOrderNo(orderStoreSubList.getOrderNo());
            opl.setDistribution(orderStoreSubList.getDistribution());
            opl.setStoreTemplateId(orderStoreSubList.getStoreTemplateId());
            opl.setTemplateName(orderStoreSubList.getTemplateName());
            opl.setAccountingRules(orderStoreSubList.getAccountingRules());
            opl.setChargeMode(orderStoreSubList.getChargeMode());
            opl.setAmount(orderStoreSubList.getAmount());
            opl.setShipFee(orderStoreSubList.getShipFee());
            opl.setStoreAddressIdSender(orderStoreSubList.getStoreAddressIdSender());
            opl.setStoreAddressIdTui(orderStoreSubList.getStoreAddressIdTui());
            opl.setLogisticsTracking(orderStoreSubList.getLogisticsTracking());
            opl.setGoodsTotal(orderStoreSubList.getGoodsTotal());
            //opl.setGoodsTotalCost(orderStoreSubList.getGoodsTotalCost());
            opl.setCustomaryDues(orderStoreSubList.getCustomaryDues());
            opl.setActualPayment(orderStoreSubList.getActualPayment());
            //修改数据
            opl.setParentId(orderStoreSubList.getId());
            opl.setLogisticsCompany(logisticsCompany);
            opl.setTrackingNumber(trackingNumber);
            opl.setStatus("2");
            orderStoreSubListService.save(opl);
            String id = opl.getId();
            return id;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * //是否全部发货,修改orderList的状态内容
     *
     * @param orderStoreSubList
     * @return
     */
    public void ShipmentOrderModification(OrderStoreSubList orderStoreSubList) {
        if (orderStoreSubList != null) {
            List<OrderStoreGoodRecord> listOrderStoreGoodRecord;
            Boolean bl = false;
            OrderStoreList orderStoreList = getById(orderStoreSubList.getOrderStoreListId());
            QueryWrapper<OrderStoreSubList> queryWrapper = new QueryWrapper<OrderStoreSubList>();
            QueryWrapper<OrderStoreGoodRecord> queryWrapperOPGR = new QueryWrapper<OrderStoreGoodRecord>();
            if (orderStoreList != null) {
                queryWrapper.eq("order_store_list_id", orderStoreList.getId());
                //queryWrapper.ne("parent_id","0");
                //判断是否有发货
                List<OrderStoreSubList> listOrderStoreSubList = orderStoreSubListService.list(queryWrapper);
                if (!listOrderStoreSubList.isEmpty()) {
                    for (OrderStoreSubList oplfh : listOrderStoreSubList) {

                        if (!"0".equals(oplfh.getParentId())) {
                            //部分发货
                            orderStoreList.setIsSender("1");
                        } else {
                            //是否全部发货
                            queryWrapperOPGR = new QueryWrapper<OrderStoreGoodRecord>();
                            queryWrapperOPGR.eq("order_store_sub_list_id", oplfh.getId());
                            queryWrapperOPGR.in("status", "0", "2", "4", "5");
                            listOrderStoreGoodRecord = orderStoreGoodRecordService.list(queryWrapperOPGR);
                            if (!listOrderStoreGoodRecord.isEmpty()) {
                                //说明还没为发货商品
                                bl = true;
                            }
                        }
                    }
                    //bl = false 为已全部发货，改变状态为待收货
                    if (!bl) {
                        orderStoreList.setStatus("2");
                    }
                    if (orderStoreList.getShipmentsTime() == null || "".equals(orderStoreList.getShipmentsTime())) {
                        //第一次发货时间
                        orderStoreList.setShipmentsTime(new Date());
                    }
                    //子订单数量
                    orderStoreList.setChildOrder(new BigDecimal(listOrderStoreSubList.size()));
                    //修改订单信息
                    updateById(orderStoreList);
                }
            }
        }
    }

    /**
     * 通过会员ID查询会员业绩
     * 包含会员自身订单和下级已完成订单的业绩
     *
     * @param page 分页对象
     * @param orderStoreListVO 查询条件
     * @param memberId 会员ID
     * @return 会员业绩列表
     */
    @Override
    public IPage<OrderStoreListDTO> getMemberPerformanceByMemberId(Page<OrderStoreList> page, OrderStoreListVO orderStoreListVO, String memberId) {
        // 1. 查询会员信息，获取会员的唯一标识
        MemberList member = iMemberListService.getById(memberId);
        if (member == null || member.getUniqueId() == null) {
            return new Page<>();
        }

        // 2. 设置查询条件
        String uniqueId = member.getUniqueId().toString();

        // 3. 调用Mapper查询会员业绩
        return orderStoreListMapper.getMemberPerformanceByMemberId(page, orderStoreListVO, memberId, uniqueId);
    }

    /**
     * 通过会员手机号查询会员业绩
     * 包含会员自身订单和下级已完成订单的业绩
     *
     * @param page 分页对象
     * @param orderStoreListVO 查询条件
     * @param phone 会员手机号
     * @return 会员业绩列表
     */
    @Override
    public IPage<OrderStoreListDTO> getMemberPerformanceByPhone(Page<OrderStoreList> page, OrderStoreListVO orderStoreListVO, String phone) {
        // 1. 通过手机号查询会员信息
        MemberList member = iMemberListService.getOne(
            new LambdaQueryWrapper<MemberList>()
                .eq(MemberList::getPhone, phone)
                .eq(MemberList::getDelFlag, "0")
        );

        // 2. 如果未找到会员，返回空结果
        if (member == null || member.getUniqueId() == null) {
            return new Page<>();
        }

        // 3. 调用通过会员ID查询的方法
        return getMemberPerformanceByMemberId(page, orderStoreListVO, member.getId());
    }

    /**
     * 处理店铺实际到账金额
     * @param orderStoreList 订单信息
     * @param storeManage 店铺信息
     * @param actualBrokerage 分销佣金
     * @return 店铺最终实际到账金额
     */
    private BigDecimal processStoreActuallyReceivedAmount(OrderStoreList orderStoreList,
                                                        StoreManage storeManage,
                                                        BigDecimal actualBrokerage) {
        // 设置平台佣金比例固定为10%
        BigDecimal platformCommissionRate = new BigDecimal("10.00");

        // 计算平台佣金金额 = 商品总价 * 10%
        BigDecimal platformCommissionAmount = NumberUtil.mul(orderStoreList.getGoodsTotal(), platformCommissionRate.divide(new BigDecimal("100")))
                .setScale(2, RoundingMode.DOWN);

        // 设置平台佣金金额和比例字段
        orderStoreList.setPlatformCommissionRate(platformCommissionRate);
        orderStoreList.setPlatformCommissionAmount(platformCommissionAmount);

        // 实际到账金额 = 商品总价 - 平台佣金 - 分销佣金 - 分享折扣
        // 注意：分销佣金已经在processMemberCommission方法中考虑了分享折扣
        BigDecimal actuallyReceivedAmount = orderStoreList.getGoodsTotal()
                .subtract(platformCommissionAmount)
                .subtract(actualBrokerage)
                .subtract(orderStoreList.getShareDiscountAmount())
                .setScale(2, RoundingMode.DOWN);

        // 店铺最终实际到账金额
        BigDecimal lasteActuallyReceivedAmount = actuallyReceivedAmount;

        // 店铺实际到账金额还需给企业家分佣 - 分账设置
        List<StoreCashierRouting> storeCashierRoutings = iStoreCashierRoutingService.list(new LambdaQueryWrapper<StoreCashierRouting>()
                .eq(StoreCashierRouting::getStoreManageId, storeManage.getId())
                .eq(StoreCashierRouting::getFashionableType, "1")
                .eq(StoreCashierRouting::getAccountType, "2")
                .orderByDesc(StoreCashierRouting::getCreateTime));

        // 企业家分销佣金金额总计
        BigDecimal entrepreneurCommissionAmount = BigDecimal.ZERO;

        if (!storeCashierRoutings.isEmpty()) {
            // 当前实时到账金额，随着企业家分佣而减少
            BigDecimal currentAmount = lasteActuallyReceivedAmount;

            for (StoreCashierRouting storeCashierRouting : storeCashierRoutings) {
                if (storeCashierRouting.getFashionableWay().equals("0")) {
                    // 根据当前实时到账金额计算企业家分佣
                    BigDecimal div = currentAmount
                            .multiply(storeCashierRouting.getFashionableProportion().divide(new BigDecimal(100)))
                            .setScale(2, RoundingMode.DOWN);

                    if (div.doubleValue() <= 0) {
                        continue;
                    }

                    if (storeCashierRouting.getRoleType().equals("0")) {
                        // 企业家分佣记录
                        MemberRechargeRecord memberRechargeRecord = new MemberRechargeRecord()
                                .setDelFlag("0")
                                .setMemberListId(storeCashierRouting.getMemberListId())
                                .setPayType("49")
                                .setGoAndCome("0")
                                .setAmount(div)
                                .setTradeStatus("0")
                                .setOrderNo(OrderNoUtils.getOrderNo())
                                .setOperator("系统")
                                .setRemark("店铺订单企业家分佣奖励 (一) [" + orderStoreList.getOrderNo() + "]")
                                .setPayment("0")
                                .setTradeNo(orderStoreList.getOrderNo())
                                .setTradeType("0")
                                .setMemberLevel("1")
                                .setTMemberListId(orderStoreList.getMemberListId())
                                .setSysUserId(orderStoreList.getSysUserId()); //归属店铺id

                        iMemberRechargeRecordService.save(memberRechargeRecord);

                        // 记录日志
                        log.info("企业家[{}]分佣金额: {}, 分佣前金额: {}, 分佣后金额: {}",
                                storeCashierRouting.getMemberListId(), div, currentAmount, currentAmount.subtract(div));

                        // 累计企业家分销佣金金额
                        entrepreneurCommissionAmount = entrepreneurCommissionAmount.add(div);
                    }

                    // 更新当前实时到账金额和最终到账金额
                    currentAmount = currentAmount.subtract(div);
                    lasteActuallyReceivedAmount = lasteActuallyReceivedAmount.subtract(div);
                }
            }
        }

        // 设置企业家分销佣金金额字段
        orderStoreList.setEntrepreneurCommissionAmount(entrepreneurCommissionAmount);

        // 创建店铺实际到账金额记录
        StoreRechargeRecord storeRechargeRecord = new StoreRechargeRecord()
                .setDelFlag("0")
                .setStoreManageId(storeManage.getId())
                .setPayType("0")
                .setGoAndCome("0")
                .setAmount(lasteActuallyReceivedAmount)
                .setTradeStatus("0")
                .setOrderNo(OrderNoUtils.getOrderNo())
                .setOperator("系统")
                .setRemark("订单交易到账金额 (一) [" + orderStoreList.getOrderNo() + "]")
                .setPayment("0")
                .setTradeNo(orderStoreList.getOrderNo())
                .setTradeType("0");

        iStoreRechargeRecordService.save(storeRechargeRecord);

        // 更新订单信息
        orderStoreList.setActuallyReceivedAmount(lasteActuallyReceivedAmount);
        updateById(orderStoreList);

        return lasteActuallyReceivedAmount;
    }

    /**
     * 处理会员佣金
     * @param orderNo 订单号
     * @param myStoreGoods 订单商品列表
     * @param firstPromoterMemberList 一级推广会员
     * @param marketingStoreDistributionSetting 分销设置
     * @param memberListId 会员ID
     * @return 计算后的总佣金
     */
    private BigDecimal processMemberCommission(String orderNo,
                                              List<Map<String, Object>> myStoreGoods,
                                              MemberList firstPromoterMemberList,
                                              MarketingStoreDistributionSetting marketingStoreDistributionSetting,
                                              String memberListId) {
        // 如果没有推广人或没有商品，直接返回0
        if (firstPromoterMemberList == null || CollUtil.isEmpty(myStoreGoods)) {
            return BigDecimal.ZERO;
        }

        // 创建分销记录列表
        List<MemberDistributionRecord> memberDistributionRecords = new ArrayList<>();

        // 获取订单信息，用于获取分享折扣比例
        OrderStoreList orderStoreList = this.getOne(new LambdaQueryWrapper<OrderStoreList>()
            .eq(OrderStoreList::getOrderNo, orderNo)
            .eq(OrderStoreList::getDelFlag, "0"));

        // 遍历商品，计算每个商品的分享佣金
        for (Map<String, Object> m : myStoreGoods) {
            String goodId = Convert.toStr(m.get("goodId"));
            GoodStoreSpecification goodStoreSpecification = iGoodStoreSpecificationService.getById(m.get("goodSpecificationId").toString());
            GoodStoreList goodStoreList = iGoodStoreListService.getById(goodId);
            BigDecimal price = (BigDecimal) m.get("price");
            BigDecimal quantity = (BigDecimal) m.get("quantity");
            BigDecimal total = NumberUtil.mul(price, quantity);

            // 获取分享折扣比例
            BigDecimal shareDiscountRatio = BigDecimal.ZERO;
            if (orderStoreList != null && orderStoreList.getShareDiscountRatio() != null &&
                orderStoreList.getShareDiscountRatio().compareTo(BigDecimal.ZERO) > 0) {
                shareDiscountRatio = orderStoreList.getShareDiscountRatio();
            }

            // 分佣比例
            BigDecimal shareCommissionRate = goodStoreList.getShareCommissionRate();
            if (shareCommissionRate == null || CompareUtil.compare(shareCommissionRate, BigDecimal.ZERO) <= 0) {
                if (marketingStoreDistributionSetting != null) {
                    shareCommissionRate = marketingStoreDistributionSetting.getCommonFirst();
                }
            }

            // 计算商品的分享折扣金额 - 基于原始商品总价
            BigDecimal itemShareDiscountAmount = BigDecimal.ZERO;
            if (shareDiscountRatio.compareTo(BigDecimal.ZERO) > 0) {
                itemShareDiscountAmount = total.multiply(shareDiscountRatio)
                    .divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP);
            }

            // 计算实际分佣比例 - 分享折扣是从分享佣金中得来的
            BigDecimal actualCommissionRate = shareCommissionRate;
            if (shareCommissionRate != null && shareDiscountRatio.compareTo(BigDecimal.ZERO) > 0) {
                // 实际分佣比例 = 原分佣比例 - 分享折扣比例
                actualCommissionRate = shareCommissionRate.subtract(shareDiscountRatio);
                // 确保分佣比例不小于0
                if (actualCommissionRate.compareTo(BigDecimal.ZERO) < 0) {
                    actualCommissionRate = BigDecimal.ZERO;
                }
            }

            // 计算一级推广佣金 - 基于原始商品总价
            if (actualCommissionRate != null && CompareUtil.compare(actualCommissionRate, BigDecimal.ZERO) > 0) {
                MemberDistributionRecord memberDistributionRecord = new MemberDistributionRecord()
                    .setDelFlag("0")
                    .setGoodId(goodStoreList.getId())
                    .setGoodSpecificationId(goodStoreSpecification.getId())
                    .setGoodPicture(goodStoreList.getMainPicture())
                    .setGoodName(goodStoreList.getGoodName())
                    .setGoodSpecification(goodStoreSpecification.getSpecification())
                    .setCommission(NumberUtil.mul(total, actualCommissionRate).divide(new BigDecimal(100), 2, RoundingMode.DOWN));

                memberDistributionRecords.add(memberDistributionRecord);

                // 更新商品记录中的分享佣金，根据订单号和商品信息精确定位
                List<OrderStoreGoodRecord> goodRecords = iOrderStoreGoodRecordService.list(
                    new LambdaQueryWrapper<OrderStoreGoodRecord>()
                        .eq(OrderStoreGoodRecord::getOrderNo, orderNo)
                        .eq(OrderStoreGoodRecord::getGoodStoreListId, goodId)
                        .eq(OrderStoreGoodRecord::getGoodStoreSpecificationId, goodStoreSpecification.getId())
                );

                if (CollUtil.isNotEmpty(goodRecords)) {
                    for (OrderStoreGoodRecord record : goodRecords) {
                        record.setShareCommission(memberDistributionRecord.getCommission());
                        iOrderStoreGoodRecordService.updateById(record);
                    }
                }
            }
        }

        // 计算订单实际分销佣金
        BigDecimal actualBrokerage = memberDistributionRecords.stream()
            .map(MemberDistributionRecord::getCommission)
            .reduce(BigDecimal.ZERO, NumberUtil::add);

        // 创建佣金记录
        if (actualBrokerage.compareTo(BigDecimal.ZERO) > 0) {
            // 查询订单对应的店铺ID
            OrderStoreList orderInfo = this.getOne(new LambdaQueryWrapper<OrderStoreList>()
                .eq(OrderStoreList::getOrderNo, orderNo)
                .eq(OrderStoreList::getDelFlag, "0"));
            String storeSysUserId = orderInfo != null ? orderInfo.getSysUserId() : null;

            MemberRechargeRecord memberRechargeRecord = new MemberRechargeRecord()
                .setDelFlag("0")
                .setMemberListId(firstPromoterMemberList.getId())
                .setPayType("3")
                .setGoAndCome("0")
                .setAmount(actualBrokerage)
                .setTradeStatus("0")
                .setOrderNo(OrderNoUtils.getOrderNo())
                .setOperator("系统")
                .setRemark("订单分销奖励 (一) [" + orderNo + "]")
                .setPayment("0")
                .setTradeNo(orderNo)
                .setTradeType("0")
                .setMemberLevel("1")
                .setTMemberListId(memberListId)
                .setSysUserId(storeSysUserId); // 设置归属店铺ID

            iMemberRechargeRecordService.save(memberRechargeRecord);

            // 更新分销记录中的会员充值记录ID
            memberDistributionRecords.forEach(mdr -> mdr.setMemberRechargeRecordId(memberRechargeRecord.getId()));
            iMemberDistributionRecordService.saveBatch(memberDistributionRecords);
        }

        return actualBrokerage;
    }
}
