package org.jeecg.modules.storeLevelChangeLogs.service.impl;

import org.jeecg.modules.storeLevelChangeLogs.entity.StoreLevelChangeLogs;
import org.jeecg.modules.storeLevelChangeLogs.mapper.StoreLevelChangeLogsMapper;
import org.jeecg.modules.storeLevelChangeLogs.service.IStoreLevelChangeLogsService;
import org.springframework.stereotype.Service;

import com.github.yulichang.base.MPJBaseServiceImpl;

/**
 * @Description: 店铺等级变更及奖励日志表
 * @Author: jeecg-boot
 * @Date:   2025-05-26
 * @Version: V1.0
 */
@Service
public class StoreLevelChangeLogsServiceImpl extends MPJBaseServiceImpl<StoreLevelChangeLogsMapper, StoreLevelChangeLogs> implements IStoreLevelChangeLogsService {

}
