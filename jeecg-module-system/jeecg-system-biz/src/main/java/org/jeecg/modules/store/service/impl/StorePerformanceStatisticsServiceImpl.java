package org.jeecg.modules.store.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.store.service.IStorePerformanceStatisticsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.jeecg.modules.order.entity.OrderStoreList;
import org.jeecg.modules.order.service.IOrderStoreListService;
import org.jeecg.modules.store.entity.StoreCashierRouting;
import org.jeecg.modules.store.service.IStoreCashierRoutingService;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

/**
 * 店铺业绩统计服务实现类
 * 
 * <AUTHOR>
 * @date 2024-03-21
 */
@Service
@Slf4j
public class StorePerformanceStatisticsServiceImpl implements IStorePerformanceStatisticsService {
    
    @Autowired
    private IOrderStoreListService orderStoreListService;
    
    @Autowired
    private IStoreCashierRoutingService storeCashierRoutingService;
    
    @Override
    public BigDecimal calculateMonthlyPerformance(String storeId, String assessmentMonth) {
        log.info("开始计算店铺[{}]在[{}]月份的业绩", storeId, assessmentMonth);
        
        // 解析评估月份
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM");
        LocalDate assessmentDate = LocalDate.parse(assessmentMonth + "-01");
        LocalDate startDate = assessmentDate.withDayOfMonth(1);
        LocalDate endDate = assessmentDate.plusMonths(1).withDayOfMonth(1);
        
        // 构建查询条件
        LambdaQueryWrapper<OrderStoreList> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OrderStoreList::getSysUserId, storeId)
                   .in(OrderStoreList::getStatus, "3", "5") // 交易成功或交易完成
                   .ge(OrderStoreList::getDeliveryTime, startDate)
                   .lt(OrderStoreList::getDeliveryTime, endDate);
        
        // 统计业绩总额
        BigDecimal totalAmount = orderStoreListService.list(queryWrapper)
                .stream()
                .map(OrderStoreList::getActuallyReceivedAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        
        log.info("店铺[{}]在[{}]月份的业绩统计完成，总额：{}", storeId, assessmentMonth, totalAmount);
        return totalAmount;
    }
    
    @Override
    public Long calculateEntrepreneurCount(String storeId) {
        log.info("开始统计店铺[{}]的合作企业家数量", storeId);
        
        // 构建查询条件
        LambdaQueryWrapper<StoreCashierRouting> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StoreCashierRouting::getStoreManageId, storeId)
                   .eq(StoreCashierRouting::getDelFlag, "0")
                   .isNotNull(StoreCashierRouting::getMemberListId)
                   .groupBy(StoreCashierRouting::getMemberListId);
        
        // 统计去重后的企业家数量
        long count = storeCashierRoutingService.count(queryWrapper);
        
        log.info("店铺[{}]的合作企业家数量统计完成，总数：{}", storeId, count);
        return count;
    }
} 