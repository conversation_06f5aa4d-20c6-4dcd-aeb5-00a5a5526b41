package org.jeecg.modules.store.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.system.util.JwtUtil;
import org.jeecg.modules.storeLevelConfigurations.entity.StoreLevelConfigurations;
import org.jeecg.modules.storeLevelConfigurations.service.IStoreLevelConfigurationsService;
import org.jeecg.modules.storeLevelChangeLogs.entity.StoreLevelChangeLogs;
import org.jeecg.modules.storeLevelChangeLogs.service.IStoreLevelChangeLogsService;
import org.jeecg.modules.store.entity.StoreManage;
import org.jeecg.modules.store.model.StoreLevelAssessmentResult;
import org.jeecg.modules.store.service.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;

/**
 * 店铺等级评定服务实现类
 * 
 * <AUTHOR>
 * @date 2024-03-21
 */
@Service
@Slf4j
public class StoreLevelAssessmentServiceImpl implements IStoreLevelAssessmentService {
    
    @Autowired
    private IStorePerformanceStatisticsService performanceStatisticsService;
    
    @Autowired
    private IStoreLevelConfigurationsService levelConfigurationService;
    
    @Autowired
    private IStoreManageService storeManageService;
    
    @Autowired
    private IStoreLevelChangeLogsService changeLogService;
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public StoreLevelAssessmentResult assessStoreLevel(String storeId, String assessmentPeriod) {
        log.info("开始执行店铺[{}]在[{}]月份的等级评定", storeId, assessmentPeriod);
        
        // 1. 获取店铺当前等级
        String currentLevel = getCurrentStoreLevel(storeId);
        
        // 2. 统计上月业绩和企业家数量
        BigDecimal actualSales = performanceStatisticsService.calculateMonthlyPerformance(storeId, assessmentPeriod);
        Long actualEntrepreneurs = performanceStatisticsService.calculateEntrepreneurCount(storeId);
        
        // 3. 维持当前等级评估
        StoreLevelAssessmentResult result;
        if (canMaintainCurrentLevel(currentLevel, actualSales, actualEntrepreneurs)) {
            // 4. 尝试晋升评估
            String nextLevel = getNextLevel(currentLevel);
            if (nextLevel != null && canUpgradeToNextLevel(nextLevel, actualSales, actualEntrepreneurs)) {
                result = upgradeStore(storeId, currentLevel, nextLevel, actualSales, actualEntrepreneurs, assessmentPeriod);
            } else {
                result = maintainStore(storeId, currentLevel, actualSales, actualEntrepreneurs, assessmentPeriod);
            }
        } else {
            // 5. 执行降级
            String previousLevel = getPreviousLevel(currentLevel);
            result = downgradeStore(storeId, currentLevel, previousLevel, actualSales, actualEntrepreneurs, assessmentPeriod);
        }
        
        log.info("店铺[{}]等级评定完成，结果：{}", storeId, result);
        return result;
    }
    
    @Override
    public String getCurrentStoreLevel(String storeId) {
        StoreManage store = storeManageService.getById(storeId);
        return store != null ? String.valueOf(store.getStoreLevel()) : "0";
    }
    
    @Override
    public String getNextLevel(String currentLevel) {
        List<StoreLevelConfigurations> configurations = getActiveConfigurations();
        Optional<StoreLevelConfigurations> currentConfig = configurations.stream()
                .filter(config -> config.getLevelValue().equals(currentLevel))
                .findFirst();
        
        if (currentConfig.isPresent()) {
            int currentIndex = configurations.indexOf(currentConfig.get());
            if (currentIndex < configurations.size() - 1) {
                return configurations.get(currentIndex + 1).getLevelValue();
            }
        }
        return null;
    }
    
    @Override
    public String getPreviousLevel(String currentLevel) {
        List<StoreLevelConfigurations> configurations = getActiveConfigurations();
        Optional<StoreLevelConfigurations> currentConfig = configurations.stream()
                .filter(config -> config.getLevelValue().equals(currentLevel))
                .findFirst();
        
        if (currentConfig.isPresent()) {
            int currentIndex = configurations.indexOf(currentConfig.get());
            if (currentIndex > 0) {
                return configurations.get(currentIndex - 1).getLevelValue();
            }
        }
        return "0";
    }
    
    @Override
    public boolean canMaintainCurrentLevel(String currentLevel, BigDecimal actualSales, Long actualEntrepreneurs) {
        StoreLevelConfigurations config = getConfigurationByLevel(currentLevel);
        if (config == null) {
            return false;
        }
        
        return actualSales.compareTo(config.getMinMonthlySales()) >= 0 
            && actualEntrepreneurs >= config.getMinEntrepreneurCount();
    }
    
    @Override
    public boolean canUpgradeToNextLevel(String nextLevel, BigDecimal actualSales, Long actualEntrepreneurs) {
        StoreLevelConfigurations config = getConfigurationByLevel(nextLevel);
        if (config == null) {
            return false;
        }
        
        return actualSales.compareTo(config.getMinMonthlySales()) >= 0 
            && actualEntrepreneurs >= config.getMinEntrepreneurCount();
    }
    
    /**
     * 获取所有启用的等级配置，按等级值排序
     */
    private List<StoreLevelConfigurations> getActiveConfigurations() {
        LambdaQueryWrapper<StoreLevelConfigurations> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StoreLevelConfigurations::getIsActive, true)
                   .eq(StoreLevelConfigurations::getDelFlag, "0")
                   .orderBy(true, true, StoreLevelConfigurations::getSortOrder);
        
        return levelConfigurationService.list(queryWrapper);
    }
    
    /**
     * 根据等级值获取配置
     */
    private StoreLevelConfigurations getConfigurationByLevel(String level) {
        LambdaQueryWrapper<StoreLevelConfigurations> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StoreLevelConfigurations::getLevelValue, level)
                   .eq(StoreLevelConfigurations::getIsActive, true)
                   .eq(StoreLevelConfigurations::getDelFlag, "0");
        
        return levelConfigurationService.getOne(queryWrapper);
    }
    
    /**
     * 执行店铺升级
     */
    private StoreLevelAssessmentResult upgradeStore(String storeId, String currentLevel, String nextLevel,
                                                  BigDecimal actualSales, Long actualEntrepreneurs,
                                                  String assessmentPeriod) {
        // 1. 更新店铺等级
        StoreManage store = new StoreManage();
        store.setId(storeId);
        store.setStoreLevel(Long.valueOf(nextLevel));
        storeManageService.updateById(store);
        
        // 2. 获取新等级的奖励金额
        StoreLevelConfigurations newConfig = getConfigurationByLevel(nextLevel);
        BigDecimal rewardAmount = newConfig.getMonthlyRewardBalanceAmount();
        
        // 3. 发放奖励
        if (rewardAmount.compareTo(BigDecimal.ZERO) > 0) {
            String orderNo = "LEVEL_REWARD_" + storeId + "_" + assessmentPeriod;
            storeManageService.addStoreBlance(storeId, rewardAmount, orderNo, "LEVEL_REWARD");
        }
        
        // 4. 记录变更日志
        StoreLevelChangeLogs log = new StoreLevelChangeLogs();
        log.setStoreManageId(storeId);
        log.setAssessmentPeriod(assessmentPeriod);
        log.setPreviousLevel(currentLevel);
        log.setCurrentLevel(nextLevel);
        log.setAssessedSales(actualSales);
        log.setAssessedEntrepreneurCount(actualEntrepreneurs.intValue());
        log.setChangeType("UPGRADE");
        log.setRewardBalanceAmountGiven(rewardAmount);
        log.setRemarks("店铺等级升级");
        log.setCreateBy("system");
        changeLogService.save(log);
        
        // 5. 返回评定结果
        return StoreLevelAssessmentResult.builder()
                .storeId(storeId)
                .assessmentPeriod(assessmentPeriod)
                .previousLevel(currentLevel)
                .currentLevel(nextLevel)
                .assessedSales(actualSales)
                .assessedEntrepreneurCount(actualEntrepreneurs)
                .changeType("UPGRADE")
                .rewardBalanceAmount(rewardAmount)
                .remarks("店铺等级升级")
                .build();
    }
    
    /**
     * 执行店铺降级
     */
    private StoreLevelAssessmentResult downgradeStore(String storeId, String currentLevel, String previousLevel,
                                                    BigDecimal actualSales, Long actualEntrepreneurs,
                                                    String assessmentPeriod) {
        // 1. 更新店铺等级
        StoreManage store = new StoreManage();
        store.setId(storeId);
        store.setStoreLevel(Long.valueOf(previousLevel));
        storeManageService.updateById(store);
        
        // 2. 获取新等级的奖励金额
        StoreLevelConfigurations newConfig = getConfigurationByLevel(previousLevel);
        BigDecimal rewardAmount = newConfig.getMonthlyRewardBalanceAmount();
        
        // 3. 发放奖励（如果有）
        if (rewardAmount.compareTo(BigDecimal.ZERO) > 0) {
            String orderNo = "LEVEL_REWARD_" + storeId + "_" + assessmentPeriod;
            storeManageService.addStoreBlance(storeId, rewardAmount, orderNo, "LEVEL_REWARD");
        }
        
        // 4. 记录变更日志
        StoreLevelChangeLogs log = new StoreLevelChangeLogs();
        log.setStoreManageId(storeId);
        log.setAssessmentPeriod(assessmentPeriod);
        log.setPreviousLevel(currentLevel);
        log.setCurrentLevel(previousLevel);
        log.setAssessedSales(actualSales);
        log.setAssessedEntrepreneurCount(actualEntrepreneurs.intValue());
        log.setChangeType("DOWNGRADE");
        log.setRewardBalanceAmountGiven(rewardAmount);
        log.setRemarks("店铺等级降级");
        log.setCreateBy("system");
        changeLogService.save(log);
        
        // 5. 返回评定结果
        return StoreLevelAssessmentResult.builder()
                .storeId(storeId)
                .assessmentPeriod(assessmentPeriod)
                .previousLevel(currentLevel)
                .currentLevel(previousLevel)
                .assessedSales(actualSales)
                .assessedEntrepreneurCount(actualEntrepreneurs)
                .changeType("DOWNGRADE")
                .rewardBalanceAmount(rewardAmount)
                .remarks("店铺等级降级")
                .build();
    }
    
    /**
     * 维持店铺等级
     */
    private StoreLevelAssessmentResult maintainStore(String storeId, String currentLevel,
                                                   BigDecimal actualSales, Long actualEntrepreneurs,
                                                   String assessmentPeriod) {
        // 1. 获取当前等级的奖励金额
        StoreLevelConfigurations config = getConfigurationByLevel(currentLevel);
        BigDecimal rewardAmount = config.getMonthlyRewardBalanceAmount();
        
        // 2. 发放奖励（如果有）
        if (rewardAmount.compareTo(BigDecimal.ZERO) > 0) {
            String orderNo = "LEVEL_REWARD_" + storeId + "_" + assessmentPeriod;
            storeManageService.addStoreBlance(storeId, rewardAmount, orderNo, "LEVEL_REWARD");
        }
        
        // 3. 记录变更日志
        StoreLevelChangeLogs log = new StoreLevelChangeLogs();
        log.setStoreManageId(storeId);
        log.setAssessmentPeriod(assessmentPeriod);
        log.setPreviousLevel(currentLevel);
        log.setCurrentLevel(currentLevel);
        log.setAssessedSales(actualSales);
        log.setAssessedEntrepreneurCount(actualEntrepreneurs.intValue());
        log.setChangeType("MAINTAIN");
        log.setRewardBalanceAmountGiven(rewardAmount);
        log.setRemarks("店铺等级维持");
        log.setCreateBy("system");
        changeLogService.save(log);
        
        // 4. 返回评定结果
        return StoreLevelAssessmentResult.builder()
                .storeId(storeId)
                .assessmentPeriod(assessmentPeriod)
                .previousLevel(currentLevel)
                .currentLevel(currentLevel)
                .assessedSales(actualSales)
                .assessedEntrepreneurCount(actualEntrepreneurs)
                .changeType("MAINTAIN")
                .rewardBalanceAmount(rewardAmount)
                .remarks("店铺等级维持")
                .build();
    }
} 