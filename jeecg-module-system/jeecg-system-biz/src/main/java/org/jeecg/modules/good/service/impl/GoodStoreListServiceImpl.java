package org.jeecg.modules.good.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.common.util.OrderNoUtils;
import org.jeecg.modules.good.dto.*;
import org.jeecg.modules.good.entity.GoodList;
import org.jeecg.modules.good.entity.GoodStoreList;
import org.jeecg.modules.good.entity.GoodStoreSpecification;
import org.jeecg.modules.good.entity.GoodStoreType;
import org.jeecg.modules.good.mapper.GoodStoreListMapper;
import org.jeecg.modules.good.service.IGoodStoreListService;
import org.jeecg.modules.good.service.IGoodStoreSpecificationService;
import org.jeecg.modules.good.service.IGoodStoreTypeService;
import org.jeecg.modules.good.util.GoodUtils;
import org.jeecg.modules.good.vo.GoodStoreListSpecificationVO;
import org.jeecg.modules.good.vo.GoodStoreListVo;
import org.jeecg.modules.good.vo.SearchTermsVO;
import org.jeecg.modules.good.vo.SpecificationsPicturesVO;
import org.jeecg.modules.store.entity.StoreManage;
import org.jeecg.modules.store.service.IStoreManageService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @Description: 店铺商品列表
 * @Author: jeecg-boot
 * @Date: 2019-10-25
 * @Version: V1.0
 */
@Service
@Slf4j
public class GoodStoreListServiceImpl extends ServiceImpl<GoodStoreListMapper, GoodStoreList> implements IGoodStoreListService {

    @Autowired
    private IGoodStoreSpecificationService goodStoreSpecificationService;
    @Autowired
    private IGoodStoreTypeService goodStoreTypeService;
    @Autowired
    private GoodUtils goodUtils;
    @Autowired
    @Lazy
    private IGoodStoreSpecificationService iGoodStoreSpecificationService;
    @Autowired
    @Lazy
    private IStoreManageService iStoreManageService;


    @Override
    public IPage<Map<String, Object>> queryPageList(Page<Map<String, Object>> page, Map<String, Object> paramMap, QueryWrapper wrapper) {
        return baseMapper.queryPageList(page, paramMap, wrapper);
    }

    @Override
    public IPage<Map<String, Object>> selectGood(Page<Map<String, Object>> page, Map<String, Object> paramMap) {
        return baseMapper.selectGood(page, paramMap);
    }

    @Override
    public GoodStoreList getGoodStoreListById(String id) {

        List<GoodStoreList> listStoreGoodList = baseMapper.getGoodStoreListById(id);
        if (listStoreGoodList.size() > 0) {
            GoodStoreList goodStoreList = listStoreGoodList.get(0);
            return goodStoreList;
        }
        return null;
    }

    ;

    @Override
    public void updateDelFalg(GoodStoreList goodStoreList, String delFlag) {
        baseMapper.updateDelFalg(goodStoreList.getId(), delFlag);
    }

    ;


    /**
     * 根据id获取商品
     *
     * @param id
     * @return
     */
    @Override
    public GoodStoreListDto selectById(String id) {
        GoodStoreList goodList = baseMapper.selectById(id);
        GoodStoreListDto goodStoreListDto = new GoodStoreListDto();
        List<GoodStoreSpecification> goodStoreSpecifications = goodStoreSpecificationService.getGoodStoreSpecificationByGoodListId(id);
        BeanUtils.copyProperties(goodList, goodStoreListDto);
        goodStoreListDto.setGoodListSpecificationVOs(goodStoreSpecifications);
        return goodStoreListDto;
    }

    /**
     * 保存商品
     *
     * @param goodListVo
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveOrUpdate(GoodStoreListVo goodListVo) {
        //获取当前登录人信息
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        GoodStoreList goodStoreList = new GoodStoreList();
        BeanUtils.copyProperties(goodListVo, goodStoreList);
        goodStoreList.setShareCommissionRate(Convert.toBigDecimal(goodListVo.getShareCommissionRate()));
        goodStoreList.setSharePicture(goodListVo.getSharePicture());
        List<GoodStoreListSpecificationVO> listGoodStoreListSpecificationVO = null;
        if (goodListVo.getGoodListSpecificationVOs1() != null) {
            listGoodStoreListSpecificationVO = JSONObject.parseArray(goodListVo.getGoodListSpecificationVOs1()).toJavaList(GoodStoreListSpecificationVO.class);
            //添加规格图
            if (goodListVo.getSpecificationsPictures() != null) {
                List<SpecificationsPicturesVO> listSpecificationsPicturesVO = JSONObject.parseArray(goodListVo.getSpecificationsPictures()).toJavaList(SpecificationsPicturesVO.class);
                //List<Map> list= JSONObject.parseObject(goodListVo.getSpecificationsPictures(),List.class);
                for (SpecificationsPicturesVO specificationsPicturesVO : listSpecificationsPicturesVO) {
                    for (GoodStoreListSpecificationVO goodStoreListSpecificationVO : listGoodStoreListSpecificationVO) {
                        if (goodStoreListSpecificationVO.getSpecification().contains(specificationsPicturesVO.getName())) {
                            if (specificationsPicturesVO.getUrl() != null) {
                                goodStoreListSpecificationVO.setSpecificationPicture(specificationsPicturesVO.getUrl());
                            }
                        }
                    }
                }
            }
        }
        goodStoreList.setSpecification(goodListVo.getSpecification());
        int i = 0;
        int result1;
        boolean isHaveId = true;
        //如果没有选择供应商就存入当前登录人id
        if (StringUtils.isBlank(goodListVo.getSysUserId())) {
            goodStoreList.setSysUserId(sysUser.getId());
        }

        // 计算总库存
        BigDecimal totalRepertory = BigDecimal.ZERO;

        // 从规格信息中计算总库存
        if (listGoodStoreListSpecificationVO != null && !listGoodStoreListSpecificationVO.isEmpty()) {
            // 有规格商品，汇总所有规格的库存
            for (GoodStoreListSpecificationVO specVO : listGoodStoreListSpecificationVO) {
                if (specVO.getRepertory() != null) {
                    totalRepertory = totalRepertory.add(specVO.getRepertory());
                }
            }
        } else {
            // 无规格商品，直接使用商品的库存
            totalRepertory = goodListVo.getRepertory();
        }

        // 设置商品主表的库存字段
        goodStoreList.setRepertory(totalRepertory);

        if (StringUtils.isBlank(goodStoreList.getId())) {
            isHaveId = false;
            i = baseMapper.insert(goodStoreList);
        } else {
            i = baseMapper.updateById(goodStoreList);
        }
        if (i > 0) {
            if (isHaveId) {
                //删除原有规格
                QueryWrapper<GoodStoreSpecification> queryWrapper = new QueryWrapper<>();
                queryWrapper.eq("good_store_list_id", goodStoreList.getId());
                result1 = goodStoreSpecificationService.remove(queryWrapper) ? 1 : 0;
            } else {
                result1 = 1;
            }
            if (listGoodStoreListSpecificationVO != null && CollUtil.isNotEmpty(listGoodStoreListSpecificationVO)) {
                List<GoodStoreSpecification> goodStoreSpecificationList = listGoodStoreListSpecificationVO.stream().map(goodStoreListSpecificationVO -> {
                    GoodStoreSpecification goodStoreSpecification = new GoodStoreSpecification();
                    goodStoreSpecification.setGoodStoreListId(goodStoreList.getId());
                    goodStoreSpecification.setSpecification(goodStoreListSpecificationVO.getSpecification());
                    goodStoreSpecification.setPrice(goodStoreListSpecificationVO.getPrice());
                    goodStoreSpecification.setVipPrice(goodStoreListSpecificationVO.getVipPrice());
                    goodStoreSpecification.setCostPrice(goodStoreListSpecificationVO.getCostPrice());
                    goodStoreSpecification.setRepertory(goodStoreListSpecificationVO.getRepertory());
                    goodStoreSpecification.setSkuNo(StrUtil.blankToDefault(goodStoreListSpecificationVO.getSkuNo(), OrderNoUtils.getOrderNo()));
                    goodStoreSpecification.setWeight(goodStoreListSpecificationVO.getWeight());
                    goodStoreSpecification.setSpecificationPicture(goodStoreListSpecificationVO.getSpecificationPicture());
                    goodStoreSpecification.setPSpecification(CollUtil.get(StrUtil.split(goodStoreListSpecificationVO.getSpecification(), ','), 0));
                    return goodStoreSpecification;
                }).collect(Collectors.toList());
                goodStoreSpecificationService.saveBatch(goodStoreSpecificationList);
            } else {
                //无规格
                GoodStoreSpecification goodStoreSpecification = new GoodStoreSpecification();
                goodStoreSpecification.setGoodStoreListId(goodStoreList.getId());
                goodStoreSpecification.setSpecification("无");
                goodStoreSpecification.setPrice(Convert.toBigDecimal(goodListVo.getPrice()));
                goodStoreSpecification.setVipPrice(Convert.toBigDecimal(goodListVo.getVipPrice()));
                goodStoreSpecification.setCostPrice(Convert.toBigDecimal(goodListVo.getCostPrice()));
                goodStoreSpecification.setRepertory(goodListVo.getRepertory());
                goodStoreSpecification.setSkuNo(goodListVo.getGoodNo());
                goodStoreSpecification.setWeight(Convert.toBigDecimal(goodListVo.getWeight()));
//                goodStoreSpecification.setSpecificationPicture(goodListVo.getSpecificationPicture());
//                goodStoreSpecification.setPSpecification(CollUtil.get(StrUtil.split(goodStoreListSpecificationVO.getSpecification(), ','),0));
                goodStoreSpecificationService.save(goodStoreSpecification);
            }
        }
        return i > 0;
    }

    @Override
    public IPage<GoodStoreListDto> getGoodListDto(Page<GoodStoreList> page, GoodStoreListVo goodListVo, String notauditStatus) {
        IPage<GoodStoreListDto> pageList = baseMapper.getGoodListDto(page, goodListVo, notauditStatus);
        List<String> ids = pageList.getRecords().stream().map(GoodStoreListDto::getId).collect(Collectors.toList());
        Map<String, List<GoodStoreSpecification>> listMap = Maps.newHashMap();
        if (CollUtil.isNotEmpty(ids)) {
            QueryWrapper<GoodStoreSpecification> queryWrapper1 = new QueryWrapper<>();
            queryWrapper1.in("good_store_list_id", ids);
            listMap  = goodStoreSpecificationService.list(queryWrapper1).stream().collect(Collectors.groupingBy(GoodStoreSpecification::getGoodStoreListId));
        }
        try {
            for (GoodStoreListDto gd : pageList.getRecords()) {
                List<GoodStoreSpecification> listGoodSpecification = listMap.getOrDefault(gd.getId(), Lists.newArrayList());
                //规格图的数据
                List<SpecificationsPicturesDTO> listSpecificationPicture = goodStoreSpecificationService.selectByspecificationPicture(gd.getId());
                if (!listSpecificationPicture.isEmpty()) {
                    //添加有规格图数据，去除无规格情况
                    if (!listSpecificationPicture.get(0).getName().equals("无")) {
                        gd.setListSpecificationsPicturesDTO(listSpecificationPicture);
                    }
                }
                gd.setGoodListSpecificationVOs(listGoodSpecification);
            }
        } catch (Exception e) {
           log.error(e.getMessage());
        }
        return pageList;
    }

    @Override
    public IPage<GoodStoreListDto> getGoodListDtoDelFlag(Page<GoodStoreList> page, GoodStoreListVo goodListVo) {
        //查询添加goodTypeId 处理
        if (goodListVo != null) {
            if (goodListVo.getGoodTypeIdTwoevel() != null && !goodListVo.getGoodTypeIdTwoevel().equals("")) {
                goodListVo.setGoodStoreTypeId(goodListVo.getGoodTypeIdTwoevel());
            }
        }
        IPage<GoodStoreListDto> pageList = baseMapper.getGoodListDtoDelFlag(page, goodListVo);
        GoodStoreType goodType1;
        GoodStoreType goodType2;
        String string;
        List<GoodStoreSpecification> listGoodSpecification;
        QueryWrapper<GoodStoreSpecification> queryWrapper1 = new QueryWrapper<>();
        try {
            for (GoodStoreListDto gd : pageList.getRecords()) {
                queryWrapper1 = new QueryWrapper<>();
                queryWrapper1.eq("good_store_list_id", gd.getId());
                listGoodSpecification = goodStoreSpecificationService.list(queryWrapper1);
                if (listGoodSpecification.size() > 0) {
                    gd.setGoodListSpecificationVOs(listGoodSpecification);
                }
                string = "";
                goodType1 = goodStoreTypeService.getById(gd.getGoodStoreTypeId());//二级
                if (goodType1 != null) {
                    goodType2 = goodStoreTypeService.getById(goodType1.getParentId());//一级
                    if (goodType2 != null) {
                        string = goodType2.getName();
                    }
                    string = string + "-" + goodType1.getName();
                    gd.setGoodTypeNameSan(string);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return pageList;
    }

    @Override
    public IPage<Map<String, Object>> findGoodListByGoodType(Page<Map<String, Object>> page, Map<String, Object> paramMap) {
        IPage<Map<String, Object>> result = baseMapper.findGoodListByGoodType(page, paramMap);
        
        // 为每个商品添加规格明细数据
        result.getRecords().forEach(goodMap -> {
            String goodId = goodMap.get("id").toString();
            String isSpecification = String.valueOf(goodMap.get("isSpecification"));
            
            // 如果商品有规格，查询规格明细数据
            if ("1".equals(isSpecification)) {
                List<Map<String, Object>> specifications = iGoodStoreSpecificationService.listMaps(
                    new QueryWrapper<GoodStoreSpecification>()
                        .select("id", "specification", "price", "vip_price", "cost_price", "repertory", "specification_picture")
                        .lambda()
                        .eq(GoodStoreSpecification::getGoodStoreListId, goodId)
                        .eq(GoodStoreSpecification::getDelFlag, "0")
                );
                goodMap.put("specifications", specifications);
            } else {
                goodMap.put("specifications", null);
            }
        });
        
        return result;
    }

    @Override
    public Map<String, Object> findGoodListByGoodId(String goodId) {
        return baseMapper.findGoodListByGoodId(goodId);
    }

    @Override
    public IPage<Map<String, Object>> findGoodListBySysUserId(Page<Map<String, Object>> page, String sysUserId) {
        return baseMapper.findGoodListBySysUserId(page, sysUserId);
    }

    @Override
    public List<Map<String, Object>> findGoodListBySysUserIds(List<String> sysUserIds) {
        return baseMapper.findGoodListBySysUserIds(sysUserIds);
    }

    @Override
    public IPage<Map<String, Object>> searchGoodList(Page<Map<String, Object>> page, Map<String, Object> paramMap) {
        return baseMapper.searchGoodList(page, paramMap);
    }

    /**
     * 每日上新返回goodTypeId 集合 根据个数倒叙
     *
     * @param createTime
     * @param limit
     * @return
     */
    @Override
    public List<Map<String, Object>> getEverydayGoodStoreTypeId(String sysUserId, String createTime, Integer limit) {
        return baseMapper.getEverydayGoodStoreTypeId(sysUserId, createTime, limit);
    }

    ;

    /**
     * 每周特惠查询
     *
     * @param page
     * @param paramMap
     * @return
     */
    @Override
    public IPage<Map<String, Object>> getEveryWeekPreferential(Page<Map<String, Object>> page, Map<String, Object> paramMap) {
        return baseMapper.getEveryWeekPreferential(page, paramMap);
    }

    @Override
    public List<GoodStoreDiscountDTO> findStoreGoodList(GoodStoreListVo goodListVo) {
        List<GoodStoreDiscountDTO> pageList = baseMapper.findStoreGoodList(goodListVo);
        return pageList;
    }

    /**
     * 查询规格商品里有0库存的商品ID和目前总库存
     *
     * @return
     */
    @Override
    public List<Map<String, Object>> getGoodStoreListIdAndRepertory() {
        return baseMapper.getGoodStoreListIdAndRepertory();
    }

    ;

    /**
     * 店铺商品回收站商品个数
     *
     * @param sysUserId
     * @return
     */
    @Override
    public Integer getGoodStoreListdelFlag(String sysUserId) {
        return baseMapper.getGoodStoreListdelFlag(sysUserId);
    }

    ;

    /**
     * 商家端店铺商品列表
     *
     * @param page
     * @param paramMap
     * @return
     */
    @Override
    public IPage<Map<String, Object>> getGoodStoreListMaps(Page<GoodStoreList> page, Map<String, Object> paramMap) {
        return baseMapper.getGoodStoreListMaps(page, paramMap);
    }

    ;

    /**
     * 商家端普通商品查询列表
     *
     * @param page
     * @param searchTermsVO
     * @return
     */
    public IPage<Map<String, Object>> searchGoodListStore(Page<Map<String, Object>> page, SearchTermsVO searchTermsVO) {
        return baseMapper.searchGoodListStore(page, searchTermsVO);
    }

    @Override
    public IPage<Map<String, Object>> queryPageListNew(GoodStoreList goodList, String sysUserId, String level, String typeId, Integer pageNo, Integer pageSize, HttpServletRequest req) {
        QueryWrapper<GoodStoreList> queryWrapper = QueryGenerator.initQueryWrapper(goodList, req.getParameterMap());
        if (StrUtil.isNotBlank(sysUserId)) {
            queryWrapper.eq("sys_user_id", sysUserId);
        }

        // 这里不需要获取排序参数，在后面传递给queryPageList方法时会处理

        // 处理特殊的查询条件
        // 处理库存大于0的条件
        if (req.getAttribute("repertory_gt") != null) {
            queryWrapper.gt("repertory", new BigDecimal(0));
        }

        // 处理库存小于等于0或为NULL的条件
        if (req.getAttribute("repertory_le") != null) {
            queryWrapper.and(wrapper ->
                wrapper.le("repertory", new BigDecimal(0))
                    .or()
                    .isNull("repertory")
            );
        }

        // 处理审核状态不等于某值的条件
        if (req.getAttribute("auditStatus_ne") != null) {
            queryWrapper.ne("audit_status", req.getAttribute("auditStatus_ne").toString());
        }

        Map<String, Object> paramMap = Maps.newHashMap();
        paramMap.put("level", level);
        paramMap.put("typeId", typeId);

        // 添加排序参数
        String patternStr = req.getParameter("pattern");
        if (StringUtils.isNotBlank(patternStr)) {
            paramMap.put("pattern", Integer.parseInt(patternStr));
        }

        IPage<Map<String, Object>> pageList = this.queryPageList(new Page<>(pageNo, pageSize), paramMap, queryWrapper);
        //组织返回的数据
        pageList.getRecords().forEach(g -> {
            //图片数据格式转换
            if (g.get("mainPicture") != null) {
                g.put("mainPicture", goodUtils.imgTransition(g.get("mainPicture").toString()));
            }
            if (g.get("detailsGoods") != null) {
                g.put("detailsGoods", goodUtils.imgTransition(g.get("detailsGoods").toString()));
            }
            if (g.get("sharePicture") != null) {
                g.put("sharePicture", goodUtils.imgTransition(g.get("sharePicture").toString()));
            }

            // 添加30日销量字段
            if (g.get("salesVolume") != null) {
                //ZSL TODO 2025/4/25: 这里简化处理，将30日销量设为总销量的30%左右
                BigDecimal salesVolume = new BigDecimal(g.get("salesVolume").toString());
                BigDecimal monthlySales = salesVolume.multiply(new BigDecimal("0.3")).setScale(0, BigDecimal.ROUND_HALF_UP);
                g.put("monthlySales", monthlySales);
            } else {
                g.put("monthlySales", new BigDecimal(0));
            }

            if (g.get("specifications") == null) {
                if (g.get("specification") != null) {
                    g.put("specifications", goodUtils.oldSpecificationToNew(g.get("specification").toString(), false));
                } else {
                    g.put("specifications", goodUtils.oldSpecificationToNew("", false));
                }
            }
            List<Map<String, Object>> shopInfo = Lists.newArrayList();
            List<Map<String, Object>> specificationsDecribes = Lists.newArrayList();

            List<GoodStoreSpecification> goodSpecifications = iGoodStoreSpecificationService.list(new LambdaQueryWrapper<GoodStoreSpecification>()
                    .eq(GoodStoreSpecification::getGoodStoreListId, g.get("id"))
                    .orderByDesc(GoodStoreSpecification::getCreateTime));

            Map<String, Object> shopInfoMap = Maps.newHashMap();

            if (!goodSpecifications.isEmpty()) {

                if (g.get("isSpecification").toString().equals("0")) {
                    GoodStoreSpecification goodSpecification = goodSpecifications.get(0);
                    shopInfoMap.put("salesPrice", goodSpecification.getPrice());
                    shopInfoMap.put("vipPrice", goodSpecification.getVipPrice());
                    shopInfoMap.put("ambassadorPrice", goodSpecification.getAmbassadorPrice());
                    shopInfoMap.put("costPrice", goodSpecification.getCostPrice());
                    shopInfoMap.put("repertory", goodSpecification.getRepertory());
                    shopInfoMap.put("weight", goodSpecification.getWeight());
                    shopInfoMap.put("skuNo", goodSpecification.getSkuNo());
                    shopInfoMap.put("minWholesaleNum", goodSpecification.getMinWholesaleNum());
                }
                if (g.get("isSpecification").toString().equals("1")) {

                    BigDecimal repertory = new BigDecimal(0);

                    for (GoodStoreSpecification goodSpecification : goodSpecifications) {
                        Map<String, Object> specificationsDecribeMap = Maps.newHashMap();

                        specificationsDecribeMap.put("pName", goodSpecification.getSpecification());
                        specificationsDecribeMap.put("salesPrice", goodSpecification.getPrice());
                        specificationsDecribeMap.put("vipPrice", goodSpecification.getVipPrice());
                        specificationsDecribeMap.put("ambassadorPrice", goodSpecification.getAmbassadorPrice());
                        specificationsDecribeMap.put("costPrice", goodSpecification.getCostPrice());
                        specificationsDecribeMap.put("weight", goodSpecification.getWeight());
                        specificationsDecribeMap.put("repertory", goodSpecification.getRepertory());
                        specificationsDecribeMap.put("skuNo", goodSpecification.getSkuNo());
                        specificationsDecribeMap.put("imgUrl", goodSpecification.getSpecificationPicture());
                        specificationsDecribeMap.put("minWholesaleNum", goodSpecification.getMinWholesaleNum());
                        repertory = repertory.add(goodSpecification.getRepertory());
                        specificationsDecribes.add(specificationsDecribeMap);
                    }
                    shopInfoMap.put("salesPrice", g.get("minPrice") + "-" + g.get("maxPrice"));
                    shopInfoMap.put("vipPrice", g.get("minVipPrice") + "-" + g.get("maxVipPrice"));
                    shopInfoMap.put("ambassadorPrice", g.get("minAmbassadorPrice") + "-" + g.get("maxAmbassadorPrice"));
                    shopInfoMap.put("costPrice", g.get("minCostPrice") + "-" + g.get("maxCostPrice"));
                    shopInfoMap.put("repertory", repertory);
                    shopInfoMap.put("weight", "0");
                    shopInfoMap.put("skuNo", "0");
                    shopInfoMap.put("minWholesaleNum", g.get("speMinWholesaleNum") + "-" + g.get("speMaxWholesaleNum"));
                }
            }

            shopInfo.add(shopInfoMap);


            g.put("shopInfo", JSON.toJSONString(shopInfo));
            g.put("specificationsDecribes", JSON.toJSONString(specificationsDecribes));

            StoreManage storeManage = iStoreManageService.getStoreManageBySysUserId(g.get("sysUserId").toString());
            g.put("storeManageId", storeManage.getId());
            if (storeManage.getSubStoreName() == null) {
                g.put("storeName", storeManage.getStoreName());
            } else {
                g.put("storeName", storeManage.getStoreName() + "(" + storeManage.getSubStoreName() + ")");
            }
        });
        return pageList;
    }

    @Override
    public boolean add(GoodStoreListNewDTO goodStoreListNewDTO) {
        GoodStoreList goodList = new GoodStoreList();
        /*上下架状态*/
        goodList.setFrameStatus(goodStoreListNewDTO.getFrameStatus());

        //审核状态
        goodList.setAuditStatus(goodStoreListNewDTO.getAuditStatus());

        goodList.setSysUserId(goodStoreListNewDTO.getSysUserId());

        goodList.setDistribution(goodStoreListNewDTO.getDistribution());

        goodList.setStoreTemplateId(goodStoreListNewDTO.getStoreTemplateId());

        goodList.setMarketPrice(goodStoreListNewDTO.getMarketPrice().toString());

        goodList.setIsWholesale(goodStoreListNewDTO.getIsWholesale());
        goodList.setIsSelectedProducts(goodStoreListNewDTO.getIsSelectedProducts());
        goodList.setProductStoreCommissionRate(goodStoreListNewDTO.getProductStoreCommissionRate());
        goodList.setProductAgencyCommissionRate(goodStoreListNewDTO.getProductAgencyCommissionRate());
        goodList.setProductAgencyCommissionRateTwo(goodStoreListNewDTO.getProductAgencyCommissionRateTwo());
        goodList.setProductAgencyCommissionRateMyself(goodStoreListNewDTO.getProductAgencyCommissionRateMyself());
        goodList.setShareCommissionRate(goodStoreListNewDTO.getShareCommissionRate());
        goodList.setSelectedStoreCommissionRate(goodStoreListNewDTO.getSelectedStoreCommissionRate());

        //商品编号
        JSONArray shopInfo = JSON.parseArray(goodStoreListNewDTO.getShopInfo());
        JSONObject shopInfoo = (JSONObject) shopInfo.get(0);

        //商品编码
        goodList.setGoodNo(goodStoreListNewDTO.getGoodNo());

        goodList.setGoodName(goodStoreListNewDTO.getGoodName());

        goodList.setGoodStoreTypeId(goodStoreListNewDTO.getGoodTypeId());
        goodList.setGoodDescribe(goodStoreListNewDTO.getGoodDescribe());
        goodList.setMainPicture(goodStoreListNewDTO.getMainImages());
        goodList.setDetailsGoods(goodStoreListNewDTO.getDetailsImages());
        goodList.setSharePicture(goodStoreListNewDTO.getSharePictureImages());
        goodList.setCommitmentCustomers(goodStoreListNewDTO.getCommitmentCustomers());
        //是否有规格
        JSONArray specifications = JSON.parseArray(goodStoreListNewDTO.getSpecifications());
        goodList.setSpecifications(goodStoreListNewDTO.getSpecifications());
        goodList.setStatus(goodStoreListNewDTO.getStatus());

        /*添加搜索内容*/
        List<String> searchInfos = Lists.newArrayList();
        searchInfos.add(org.apache.commons.lang3.StringUtils.defaultString(goodList.getGoodName()));
        searchInfos.add(org.apache.commons.lang3.StringUtils.defaultString(goodList.getNickName()));
        searchInfos.add(org.apache.commons.lang3.StringUtils.defaultString(goodList.getGoodDescribe()));
        searchInfos.add(goodStoreTypeService.getById(goodList.getGoodStoreTypeId()).getName());

        if (specifications.size() == 0) {
            //无规格
            goodList.setIsSpecification("0");
        } else {
            //有规格
            goodList.setIsSpecification("1");
        }

        goodList.setSearchInfo(JSON.toJSONString(searchInfos));

        // 计算总库存
        BigDecimal totalRepertory = BigDecimal.ZERO;
        if (specifications.isEmpty()) {
            // 无规格商品，直接获取库存
            totalRepertory = shopInfoo.getBigDecimal("repertory");
        } else {
            // 有规格商品，汇总所有规格的库存
            JSONArray specificationsDecribes = JSON.parseArray(goodStoreListNewDTO.getSpecificationsDecribes());
            for (Object s : specificationsDecribes) {
                JSONObject jsonObject = (JSONObject) s;
                BigDecimal repertory = jsonObject.getBigDecimal("repertory");
                if (repertory != null) {
                    totalRepertory = totalRepertory.add(repertory);
                }
            }
        }

        // 设置商品主表的库存字段
        goodList.setRepertory(totalRepertory);

        //保存数据
        if (this.save(goodList)) {
            if (specifications.isEmpty()) {
                //无规格
                GoodStoreSpecification goodSpecification = new GoodStoreSpecification();
                goodSpecification.setGoodStoreListId(goodList.getId());
                goodSpecification.setSpecification("无");
                goodSpecification.setPrice(shopInfoo.getBigDecimal("salesPrice"));
                goodSpecification.setVipPrice(shopInfoo.getBigDecimal("vipPrice"));
                goodSpecification.setCostPrice(shopInfoo.getBigDecimal("costPrice"));
                goodSpecification.setRepertory(shopInfoo.getBigDecimal("repertory"));
                goodSpecification.setSkuNo(shopInfoo.getString("skuNo"));
                goodSpecification.setWeight(new BigDecimal(shopInfoo.getString("weight")));
                if (org.apache.commons.lang3.StringUtils.isNotBlank(shopInfoo.getString("minWholesaleNum"))) {
                    goodSpecification.setMinWholesaleNum(new BigDecimal(shopInfoo.getString("minWholesaleNum")));
                }
                if (org.apache.commons.lang3.StringUtils.isNotBlank(shopInfoo.getString("ambassadorPrice"))) {
                    goodSpecification.setAmbassadorPrice(shopInfoo.getBigDecimal("ambassadorPrice"));
                }
                iGoodStoreSpecificationService.save(goodSpecification);
            } else {
                //有规格
                JSONArray specificationsDecribes = JSON.parseArray(goodStoreListNewDTO.getSpecificationsDecribes());
                for (Object s : specificationsDecribes) {
                    JSONObject jsonObject = (JSONObject) s;
                    GoodStoreSpecification goodSpecification = new GoodStoreSpecification();
                    goodSpecification.setGoodStoreListId(goodList.getId());
                    goodSpecification.setSpecification(jsonObject.getString("pName"));
                    goodSpecification.setPrice(jsonObject.getBigDecimal("salesPrice"));
                    goodSpecification.setVipPrice(jsonObject.getBigDecimal("vipPrice"));
                    goodSpecification.setCostPrice(jsonObject.getBigDecimal("costPrice"));
                    goodSpecification.setRepertory(jsonObject.getBigDecimal("repertory"));
                    goodSpecification.setSkuNo(jsonObject.getString("skuNo"));
                    goodSpecification.setWeight(new BigDecimal(jsonObject.getString("weight")));
                    goodSpecification.setSpecificationPicture(jsonObject.getString("imgUrl"));
                    if (org.apache.commons.lang3.StringUtils.isNotBlank(jsonObject.getString("minWholesaleNum"))) {
                        goodSpecification.setMinWholesaleNum(new BigDecimal(jsonObject.getString("minWholesaleNum")));
                    }
                    if (org.apache.commons.lang3.StringUtils.isNotBlank(jsonObject.getString("ambassadorPrice"))) {
                        goodSpecification.setAmbassadorPrice(jsonObject.getBigDecimal("ambassadorPrice"));
                    }
                    iGoodStoreSpecificationService.save(goodSpecification);
                }
            }
            return true;
        } else {
            return false;
        }
    }

    @Override
    public boolean edit(GoodStoreListNewDTO goodStoreListNewDTO, GoodStoreList goodList) {
        /*上下架状态*/
        goodList.setFrameStatus(goodStoreListNewDTO.getFrameStatus());

        //审核状态
        goodList.setAuditStatus("2");

        goodList.setDistribution(goodStoreListNewDTO.getDistribution());

        goodList.setStoreTemplateId(goodStoreListNewDTO.getStoreTemplateId());

        goodList.setMarketPrice(goodStoreListNewDTO.getMarketPrice().toString());

        goodList.setIsWholesale(goodStoreListNewDTO.getIsWholesale());
        goodList.setIsSelectedProducts(goodStoreListNewDTO.getIsSelectedProducts());
        goodList.setProductStoreCommissionRate(goodStoreListNewDTO.getProductStoreCommissionRate());
        goodList.setProductAgencyCommissionRate(goodStoreListNewDTO.getProductAgencyCommissionRate());
        goodList.setProductAgencyCommissionRateTwo(goodStoreListNewDTO.getProductAgencyCommissionRateTwo());
        goodList.setProductAgencyCommissionRateMyself(goodStoreListNewDTO.getProductAgencyCommissionRateMyself());
        goodList.setShareCommissionRate(goodStoreListNewDTO.getShareCommissionRate());
        goodList.setSelectedStoreCommissionRate(goodStoreListNewDTO.getSelectedStoreCommissionRate());

        //商品编号
        JSONArray shopInfo = JSON.parseArray(goodStoreListNewDTO.getShopInfo());
        JSONObject shopInfoo = (JSONObject) shopInfo.get(0);

        //商品编码
        goodList.setGoodNo(goodStoreListNewDTO.getGoodNo());

        goodList.setGoodName(goodStoreListNewDTO.getGoodName());

        goodList.setGoodStoreTypeId(goodStoreListNewDTO.getGoodTypeId());
        goodList.setGoodDescribe(goodStoreListNewDTO.getGoodDescribe());
        goodList.setMainPicture(goodStoreListNewDTO.getMainImages());
        goodList.setSharePicture(goodStoreListNewDTO.getSharePictureImages());
        goodList.setDetailsGoods(goodStoreListNewDTO.getDetailsImages());
        goodList.setCommitmentCustomers(goodStoreListNewDTO.getCommitmentCustomers());
        //是否有规格
        JSONArray specifications = JSON.parseArray(goodStoreListNewDTO.getSpecifications());
        goodList.setSpecifications(goodStoreListNewDTO.getSpecifications());
        goodList.setStatus(goodStoreListNewDTO.getStatus());

        /*添加搜索内容*/
        List<String> searchInfos = Lists.newArrayList();
        searchInfos.add(org.apache.commons.lang3.StringUtils.defaultString(goodList.getGoodName()));
        searchInfos.add(org.apache.commons.lang3.StringUtils.defaultString(goodList.getNickName()));
        searchInfos.add(org.apache.commons.lang3.StringUtils.defaultString(goodList.getGoodDescribe()));
        searchInfos.add(goodStoreTypeService.getById(goodList.getGoodStoreTypeId()).getName());

        if (specifications.size() == 0) {
            //无规格
            goodList.setIsSpecification("0");
        } else {
            //有规格
            goodList.setIsSpecification("1");
        }

        // 计算总库存
        BigDecimal totalRepertory = BigDecimal.ZERO;
        if (specifications.isEmpty()) {
            // 无规格商品，直接获取库存
            totalRepertory = shopInfoo.getBigDecimal("repertory");
        } else {
            // 有规格商品，汇总所有规格的库存
            JSONArray specificationsDecribes = JSON.parseArray(goodStoreListNewDTO.getSpecificationsDecribes());
            for (Object s : specificationsDecribes) {
                JSONObject jsonObject = (JSONObject) s;
                BigDecimal repertory = jsonObject.getBigDecimal("repertory");
                if (repertory != null) {
                    totalRepertory = totalRepertory.add(repertory);
                }
            }
        }

        // 设置商品主表的库存字段
        goodList.setRepertory(totalRepertory);

        goodList.setSearchInfo(JSON.toJSONString(searchInfos));
        if (this.saveOrUpdate(goodList)) {
            //查询所有商品的规格信息
            Map<String, GoodStoreSpecification> goodSpecificationMap = Maps.newHashMap();
            iGoodStoreSpecificationService.list(new LambdaQueryWrapper<GoodStoreSpecification>()
                    .eq(GoodStoreSpecification::getGoodStoreListId, goodList.getId())).forEach(gs -> {
                goodSpecificationMap.put(gs.getSpecification(), gs);
            });

            if (specifications.size() == 0) {
                //无规格
                GoodStoreSpecification goodSpecification = new GoodStoreSpecification();
                if (goodSpecificationMap.containsKey("无")) {
                    goodSpecification = goodSpecificationMap.get("无");
                    goodSpecificationMap.remove("无");
                }
                goodSpecification.setGoodStoreListId(goodList.getId());
                goodSpecification.setSpecification("无");
                goodSpecification.setPrice(shopInfoo.getBigDecimal("salesPrice"));
                goodSpecification.setVipPrice(shopInfoo.getBigDecimal("vipPrice"));
                goodSpecification.setCostPrice(shopInfoo.getBigDecimal("costPrice"));
                goodSpecification.setRepertory(shopInfoo.getBigDecimal("repertory"));
                goodSpecification.setSkuNo(shopInfoo.getString("skuNo"));
                goodSpecification.setWeight(new BigDecimal(shopInfoo.getString("weight")));
                if (org.apache.commons.lang3.StringUtils.isNotBlank(shopInfoo.getString("minWholesaleNum"))) {
                    goodSpecification.setMinWholesaleNum(new BigDecimal(shopInfoo.getString("minWholesaleNum")));
                }
                if (org.apache.commons.lang3.StringUtils.isNotBlank(shopInfoo.getString("ambassadorPrice"))) {
                    goodSpecification.setAmbassadorPrice(shopInfoo.getBigDecimal("ambassadorPrice"));
                }

                iGoodStoreSpecificationService.saveOrUpdate(goodSpecification);
            } else {
                //有规格
                JSONArray specificationsDecribes = JSON.parseArray(goodStoreListNewDTO.getSpecificationsDecribes());
                for (Object s : specificationsDecribes) {
                    JSONObject jsonObject = (JSONObject) s;
                    GoodStoreSpecification goodSpecification = new GoodStoreSpecification();
                    if (goodSpecificationMap.containsKey(jsonObject.getString("pName"))) {
                        goodSpecification = goodSpecificationMap.get(jsonObject.getString("pName"));
                        goodSpecificationMap.remove(jsonObject.getString("pName"));
                    }
                    goodSpecification.setGoodStoreListId(goodList.getId());
                    goodSpecification.setSpecification(jsonObject.getString("pName"));
                    goodSpecification.setPrice(jsonObject.getBigDecimal("salesPrice"));
                    goodSpecification.setVipPrice(jsonObject.getBigDecimal("vipPrice"));
                    goodSpecification.setCostPrice(jsonObject.getBigDecimal("costPrice"));
                    goodSpecification.setRepertory(jsonObject.getBigDecimal("repertory"));
                    goodSpecification.setSkuNo(jsonObject.getString("skuNo"));
                    goodSpecification.setWeight(new BigDecimal(jsonObject.getString("weight")));
                    goodSpecification.setSpecificationPicture(jsonObject.getString("imgUrl"));
                    if (org.apache.commons.lang3.StringUtils.isNotBlank(jsonObject.getString("minWholesaleNum"))) {
                        goodSpecification.setMinWholesaleNum(new BigDecimal(jsonObject.getString("minWholesaleNum")));
                    }
                    if (org.apache.commons.lang3.StringUtils.isNotBlank(jsonObject.getString("ambassadorPrice"))) {
                        goodSpecification.setAmbassadorPrice(jsonObject.getBigDecimal("ambassadorPrice"));
                    }
                    iGoodStoreSpecificationService.saveOrUpdate(goodSpecification);
                }
            }
            //删除规格信息
            if (!goodSpecificationMap.isEmpty()) {
                goodSpecificationMap.forEach((key, value) -> iGoodStoreSpecificationService.removeById(value.getId()));
            }
            return true;
        } else {
            return false;
        }

    }

    /**
     * 获取指定状态的商品数量
     * @param paramMap 包含查询条件的参数Map
     * @return 商品数量
     */
    @Override
    public Integer getGoodStoreListCount(Map<String, Object> paramMap) {
        // 创建查询条件
        LambdaQueryWrapper<GoodStoreList> queryWrapper = new LambdaQueryWrapper<>();

        // 添加系统用户ID条件
        if (paramMap.containsKey("sysUserId") && paramMap.get("sysUserId") != null) {
            queryWrapper.eq(GoodStoreList::getSysUserId, paramMap.get("sysUserId"));
        }

        // 添加搜索条件
        if (paramMap.containsKey("search") && paramMap.get("search") != null) {
            String search = paramMap.get("search").toString();
            queryWrapper.and(wrapper ->
                wrapper.like(GoodStoreList::getGoodName, search)
                    .or()
                    .like(GoodStoreList::getGoodDescribe, search)
            );
        }

        // 添加商品分类条件
        if (paramMap.containsKey("goodStoreTypeId") && paramMap.get("goodStoreTypeId") != null) {
            queryWrapper.eq(GoodStoreList::getGoodStoreTypeId, paramMap.get("goodStoreTypeId"));
        }

        // 根据商品状态添加不同的查询条件
        String goodStatus = paramMap.containsKey("goodStatus") ? paramMap.get("goodStatus").toString() : "0";

        switch (goodStatus) {
            case "0": // 所有店铺商品
                queryWrapper.eq(GoodStoreList::getDelFlag, "0")
                        .ne(GoodStoreList::getAuditStatus, "0");
                break;
            case "1": // 在售中商品
                queryWrapper.eq(GoodStoreList::getDelFlag, "0")
                        .eq(GoodStoreList::getAuditStatus, "2")
                        .eq(GoodStoreList::getStatus, "1")
                        .eq(GoodStoreList::getFrameStatus, "1")
                        .gt(GoodStoreList::getRepertory, new BigDecimal(0));
                break;
            case "2": // 已下架商品
                queryWrapper.eq(GoodStoreList::getDelFlag, "0")
                        .eq(GoodStoreList::getAuditStatus, "2")
                        .eq(GoodStoreList::getStatus, "1")
                        .eq(GoodStoreList::getFrameStatus, "0");
                break;
            case "3": // 已售罄商品
                queryWrapper.eq(GoodStoreList::getDelFlag, "0")
                        .eq(GoodStoreList::getAuditStatus, "2")
                        .eq(GoodStoreList::getStatus, "1")
                        .eq(GoodStoreList::getFrameStatus, "1")
                        .and(wrapper -> wrapper.le(GoodStoreList::getRepertory, new BigDecimal(0))
                                .or().isNull(GoodStoreList::getRepertory));
                break;
            case "4": // 发布中商品
                queryWrapper.eq(GoodStoreList::getDelFlag, "0")
                        .eq(GoodStoreList::getAuditStatus, "1")
                        .eq(GoodStoreList::getStatus, "1");
                break;
            case "5": // 已驳回商品
                queryWrapper.eq(GoodStoreList::getDelFlag, "0")
                        .eq(GoodStoreList::getAuditStatus, "3")
                        .eq(GoodStoreList::getStatus, "1");
                break;
            case "6": // 草稿箱商品
                queryWrapper.eq(GoodStoreList::getDelFlag, "0")
                        .eq(GoodStoreList::getAuditStatus, "0");
                break;
            case "7": // 回收站商品
                queryWrapper.eq(GoodStoreList::getDelFlag, "1");
                break;
            default:
                queryWrapper.eq(GoodStoreList::getDelFlag, "0");
                break;
        }

        // 返回符合条件的记录数
        return Math.toIntExact(count(queryWrapper));
    }

    /**
     * 更新商品主表库存
     * 根据商品规格表中的库存总和更新商品主表的库存字段
     * @param goodStoreListId 商品ID
     * @return 是否更新成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateGoodStoreListRepertory(String goodStoreListId) {
        if (StringUtils.isBlank(goodStoreListId)) {
            return false;
        }

        try {
            // 查询所有规格的库存并汇总
            BigDecimal totalRepertory = iGoodStoreSpecificationService.list(
                new LambdaQueryWrapper<GoodStoreSpecification>()
                    .eq(GoodStoreSpecification::getGoodStoreListId, goodStoreListId)
                    .eq(GoodStoreSpecification::getDelFlag, "0")
            ).stream()
            .map(GoodStoreSpecification::getRepertory)
            .filter(Objects::nonNull)
            .reduce(BigDecimal.ZERO, BigDecimal::add);

            // 更新商品主表库存
            GoodStoreList goodStoreList = this.getById(goodStoreListId);
            if (goodStoreList != null) {
                goodStoreList.setRepertory(totalRepertory);
                return this.updateById(goodStoreList);
            }
            return false;
        } catch (Exception e) {
            log.error("更新商品主表库存失败：" + e.getMessage(), e);
            return false;
        }
    }
}
