# 订单售后申请接口说明文档

## 接口概述

- **接口名称**：申请订单售后
- **接口路径**：`/after/orderRefund/apply`
- **请求方式**：POST
- **接口描述**：用于用户申请订单商品的售后服务，包括仅退款、退货退款和换货三种类型。

## 请求参数

### 请求头

| 参数名 | 类型 | 必填 | 描述 |
| ------ | ---- | ---- | ---- |
| memberId | String | 是 | 会员ID，通过token解析获取 |

### 请求体

请求体为JSON格式，包含以下字段：

| 参数名 | 类型 | 必填 | 描述 |
| ------ | ---- | ---- | ---- |
| isPlatform | String | 是 | 订单类型：0=店铺订单，1=平台订单 |
| refundType | String | 是 | 售后类型：0=仅退款，1=退货退款，2=换货 |
| orderId | String | 是 | 订单ID |
| remarks | String | 否 | 售后申请说明 |
| refundCertificate | String | 否 | 退款/换货凭证图片，多张图片按照顺序逗号隔开 |
| memberShippingAddressId | String | 条件必填 | 买家换货收货地址ID，当refundType=2时必填 |
| orderRefundListDtos | Array | 是 | 售后单商品列表 |

#### orderRefundListDtos 数组元素结构

| 参数名 | 类型 | 必填 | 描述 |
| ------ | ---- | ---- | ---- |
| orderGoodRecordId | String | 是 | 订单商品记录ID |
| refundReason | Object | 条件必填 | 退款原因对象，平台订单(isPlatform=1)必填，店铺订单(isPlatform=0)不需要传递 |
| remarks | String | 否 | 售后申请说明（商品级别） |
| refundCertificate | String | 否 | 退款凭证图片（商品级别），多张图片按照顺序逗号隔开 |
| refundPrice | BigDecimal | 否 | 申请退款金额，不填则默认为商品实际支付金额 |
| refundAmount | BigDecimal | 否 | 申请退款数量，不填则默认为商品购买数量 |
| exchangeGoodSpecificationId | String | 否 | 换货商品规格ID，不传时使用当前订单商品规格 |
| exchangeGoodSpecification | String | 否 | 换货规格名称，按照顺序逗号隔开，不传时使用当前订单商品规格 |

#### refundReason 对象结构

| 参数名 | 类型 | 必填 | 描述 |
| ------ | ---- | ---- | ---- |
| id | Long | 是 | 退款原因ID |
| name | String | 是 | 退款原因名称 |
| needVoucher | Boolean | 否 | 凭证是否必须上传 |
| noRefundCarriage | Boolean | 否 | 是否支持退运费 |
| tip | String | 否 | 提示信息 |

### 请求示例

```json
{
  "isPlatform": "0",
  "refundType": "0",
  "orderId": "1234567890",
  "remarks": "商品质量不符合预期",
  "refundCertificate": "image1.jpg,image2.jpg",
  "orderRefundListDtos": [
    {
      "orderGoodRecordId": "9876543210",
      "refundReason": {
        "id": 1,
        "name": "商品质量问题",
        "needVoucher": true,
        "noRefundCarriage": false,
        "tip": "请上传商品质量问题的照片"
      },
      "remarks": "商品有明显瑕疵",
      "refundCertificate": "image3.jpg",
      "refundPrice": 100.00,
      "refundAmount": 1
    }
  ]
}
```

换货请求示例（店铺订单）：

```json
{
  "isPlatform": "0",
  "refundType": "2",
  "orderId": "1234567890",
  "remarks": "商品尺寸不合适，需要更换",
  "memberShippingAddressId": "5678901234",
  "orderRefundListDtos": [
    {
      "orderGoodRecordId": "9876543210",
      "remarks": "需要更换大一号的尺寸"
      // 店铺订单换货不需要传递refundReason对象
      // 不传exchangeGoodSpecificationId和exchangeGoodSpecification时，系统会自动使用当前订单商品规格
    }
  ]
}
```

## 响应参数

### 响应体

| 参数名 | 类型 | 描述 |
| ------ | ---- | ---- |
| code | Integer | 状态码：200=成功，其他=失败 |
| message | String | 响应消息 |
| result | Array | 创建的售后单列表 |
| success | Boolean | 是否成功 |

#### result 数组元素结构

| 参数名 | 类型 | 描述 |
| ------ | ---- | ---- |
| id | String | 售后单ID |
| orderNo | String | 订单编号 |
| orderListId | String | 订单ID |
| orderGoodRecordId | String | 订单商品记录ID |
| refundType | String | 售后类型：0=仅退款，1=退货退款，2=换货 |
| status | String | 售后状态：0=待处理，1=待买家退回，2=换货中，3=退款中，4=退款成功，5=已拒绝，6=退款关闭，7=换货关闭，8=换货完成 |
| refundPrice | BigDecimal | 申请退款金额 |
| refundAmount | BigDecimal | 申请退款数量 |
| applyTime | Date | 申请时间 |
| remarks | String | 售后申请说明 |
| refundCertificate | String | 退款凭证图片 |
| isPlatform | String | 订单类型：0=店铺订单，1=平台订单 |
| exchangeGoodSpecificationId | String | 换货商品规格ID |
| exchangeGoodSpecification | String | 换货规格名称 |
| exchangeMemberShippingAddress | String | 买家换货收货地址JSON |

### 响应示例

```json
{
  "code": 200,
  "message": "操作成功",
  "result": [
    {
      "id": "1598765432100",
      "orderNo": "ORDER2023042001",
      "orderListId": "1234567890",
      "orderGoodRecordId": "9876543210",
      "refundType": "0",
      "status": "0",
      "refundPrice": 100.00,
      "refundAmount": 1,
      "applyTime": "2023-04-20 15:30:45",
      "remarks": "商品有明显瑕疵",
      "refundCertificate": "image3.jpg",
      "isPlatform": "0"
    }
  ],
  "success": true
}
```

## 业务规则

### 参数校验规则

1. **基本参数校验**：
   - isPlatform、refundType、orderId 不能为空
   - orderRefundListDtos 不能为空数组
   - 对于平台订单(isPlatform=1)，每个 orderRefundListDtos 元素中的 refundReason 对象必须提供
   - 对于店铺订单(isPlatform=0)，refundReason 对象可以不提供

2. **换货特殊校验**：
   - 当 refundType=2（换货）时：
     - 只能选择一种商品进行换货（orderRefundListDtos 数组长度必须为1）
     - memberShippingAddressId 必须提供
     - exchangeGoodSpecificationId 和 exchangeGoodSpecification 可以不提供，不提供时系统会自动使用当前订单商品规格

### 订单状态校验规则

1. **不允许申请售后的订单状态**：
   - 待付款订单（status=0）
   - 交易失败订单（status=4）

2. **退货退款和换货的额外限制**：
   - 待发货订单（status=1）不允许申请退货退款或换货

### 售后限制校验规则

1. **换货与退款冲突校验**：
   - 如果商品已有进行中的换货申请，则不能再申请退款或退货退款
   - 如果商品已有进行中的退款或退货退款申请，则不能再申请换货

2. **退款数量校验**：
   - 已申请售后的商品数量 + 本次申请数量 ≤ 商品购买总数量
   - 防止重复提交售后申请

3. **退款金额校验**：
   - 申请退款金额不能超过商品实际支付金额

### 特殊处理

1. **1688订单处理**：
   - 对于1688平台的订单，系统会自动调用1688接口创建对应的退款单
   - 将1688退款单ID保存到售后单中

## 错误码说明

| 错误码 | 错误信息 | 描述 |
| ------ | -------- | ---- |
| 500 | isPlatform 不能为空 | 未提供订单类型参数 |
| 500 | refundType 不能为空 | 未提供售后类型参数 |
| 500 | orderId 不能为空 | 未提供订单ID参数 |
| 500 | orderStoreRefundLists 不能为空 | 未提供售后单商品列表 |
| 500 | 换货只能选择一种商品 | 换货时选择了多种商品 |
| 500 | 收货地址id不能为空 | 换货时未提供收货地址ID |
| 500 | 收货地址不存在 | 提供的收货地址ID无效 |
| 500 | 订单不存在 | 提供的订单ID无效 |
| 500 | 待付款/交易失败订单无法发起售后申请 | 订单状态不允许申请售后 |
| 500 | 待发货订单无法发起退货、换货售后申请 | 订单状态不允许申请退货或换货 |
| 500 | 订单商品不存在 | 提供的订单商品记录ID无效 |
| 500 | 退款原因不能为空 | 未提供退款原因对象 |
| 500 | 该订单商品已申请换货，无法发起退款申请 | 存在冲突的售后申请 |
| 500 | 该订单商品已申请退款，无法发起换货申请 | 存在冲突的售后申请 |
| 500 | 该订单已申请售后，请勿重复提交 | 申请数量超过限制 |

## 售后流程说明

1. **仅退款流程**：
   - 用户提交仅退款申请 → 商家审核 → 审核通过后退款 → 退款成功

2. **退货退款流程**：
   - 用户提交退货退款申请 → 商家审核 → 审核通过后用户退货 → 商家确认收货 → 退款 → 退款成功

3. **换货流程**：
   - 用户提交换货申请 → 商家审核 → 审核通过后用户退货 → 商家确认收货 → 商家发货 → 用户确认收货 → 换货完成
