# 商家端商品列表接口文档

## 1. 接口基本信息

- **接口URL**: `/back/goodList/queryPageListNew`
- **请求方式**: GET
- **接口描述**: 获取商家端商品列表，支持多种状态筛选和排序方式

## 2. 请求参数

| 参数名 | 类型 | 必填 | 描述 |
| --- | --- | --- | --- |
| pageNo | Integer | 否 | 页码，默认为1 |
| pageSize | Integer | 否 | 每页记录数，默认为10 |
| level | String | 否 | 分类级别，默认为-1 |
| typeId | String | 否 | 分类ID，默认为空 |
| goodStatus | String | 否 | 商品状态，默认为0 |
| pattern | Integer | 否 | 排序方式，默认为0 |

### 2.1 商品状态说明

| goodStatus值 | 状态名称 | 描述 |
| --- | --- | --- |
| 0 | 全部商品 | 所有未删除的商品 |
| 1 | 在售中 | 已上架且有库存的商品 |
| 2 | 已下架 | 已下架的商品 |
| 3 | 已售罄 | 已上架但库存为0的商品 |
| 4 | 发布中 | 待审核的商品 |
| 5 | 已驳回 | 审核被驳回的商品 |
| 6 | 草稿箱 | 未提交审核的商品 |
| 7 | 回收站 | 已删除的商品 |

### 2.2 排序方式说明

| pattern值 | 排序方式 | 描述 |
| --- | --- | --- |
| 0 | 综合排序 | 按销量降序，然后按最新上架时间降序 |
| 1 | 销量排序 | 按销量降序 |
| 2 | 最新排序 | 按上架时间降序 |
| 3 | 价格降序 | 按价格从高到低排序 |
| 4 | 价格升序 | 按价格从低到高排序 |

## 3. 响应参数

| 参数名 | 类型 | 描述 |
| --- | --- | --- |
| success | Boolean | 请求是否成功 |
| message | String | 提示信息 |
| code | Integer | 状态码 |
| result | Object | 结果对象 |
| result.records | Array | 商品列表 |
| result.records[].id | String | 商品ID |
| result.records[].mainPicture | String | 商品主图 |
| result.records[].sharePicture | String | 商品分享图 |
| result.records[].goodName | String | 商品名称 |
| result.records[].minPrice | BigDecimal | 最低价格 |
| result.records[].maxPrice | BigDecimal | 最高价格 |
| result.records[].minVipPrice | BigDecimal | 最低会员价格 |
| result.records[].maxVipPrice | BigDecimal | 最高会员价格 |
| result.records[].salesVolume | Integer | 总销量 |
| result.records[].monthlySales | Integer | 30天销量 |
| result.records[].repertory | BigDecimal | 库存 |
| result.records[].auditStatus | String | 审核状态 |
| result.records[].frameStatus | String | 上架状态 |
| result.records[].status | String | 商品状态 |
| result.total | Integer | 总记录数 |
| result.size | Integer | 每页记录数 |
| result.current | Integer | 当前页码 |
| result.pages | Integer | 总页数 |

## 4. 响应示例

```json
{
  "success": true,
  "message": "操作成功！",
  "code": 200,
  "result": {
    "records": [
      {
        "id": "1234567890abcdef",
        "mainPicture": "https://example.com/images/product1.jpg",
        "sharePicture": "https://example.com/images/share1.jpg",
        "goodName": "高品质商品",
        "minPrice": 99.00,
        "maxPrice": 199.00,
        "minVipPrice": 89.00,
        "maxVipPrice": 179.00,
        "salesVolume": 1000,
        "monthlySales": 300,
        "repertory": 500,
        "auditStatus": "2",
        "frameStatus": "1",
        "status": "1"
      },
      {
        "id": "abcdef1234567890",
        "mainPicture": "https://example.com/images/product2.jpg",
        "sharePicture": "https://example.com/images/share2.jpg",
        "goodName": "热销商品",
        "minPrice": 199.00,
        "maxPrice": 299.00,
        "minVipPrice": 179.00,
        "maxVipPrice": 269.00,
        "salesVolume": 800,
        "monthlySales": 240,
        "repertory": 300,
        "auditStatus": "2",
        "frameStatus": "1",
        "status": "1"
      }
    ],
    "total": 100,
    "size": 10,
    "current": 1,
    "pages": 10
  }
}
```

## 5. 业务逻辑说明

### 5.1 商品状态筛选逻辑

根据 `goodStatus` 参数的不同值，设置不同的查询条件：

- **全部商品(goodStatus=0)**：`del_flag = '0'` 且 `audit_status != '0'`
- **在售中(goodStatus=1)**：`del_flag = '0'` 且 `audit_status = '2'` 且 `status = '1'` 且 `frame_status = '1'` 且 `repertory > 0`
- **已下架(goodStatus=2)**：`del_flag = '0'` 且 `audit_status = '2'` 且 `status = '1'` 且 `frame_status = '0'`
- **已售罄(goodStatus=3)**：`del_flag = '0'` 且 `audit_status = '2'` 且 `status = '1'` 且 `frame_status = '1'` 且 `repertory <= 0`
- **发布中(goodStatus=4)**：`del_flag = '0'` 且 `audit_status = '1'` 且 `status = '1'`
- **已驳回(goodStatus=5)**：`del_flag = '0'` 且 `audit_status = '3'` 且 `status = '1'`
- **草稿箱(goodStatus=6)**：`del_flag = '0'` 且 `audit_status = '0'`
- **回收站(goodStatus=7)**：`del_flag = '1'`

### 5.2 排序逻辑

根据 `pattern` 参数的不同值，采用不同的排序方式：

- **综合排序(pattern=0)**：先按销量降序，再按更新时间降序
- **销量排序(pattern=1)**：按销量降序
- **最新排序(pattern=2)**：按更新时间降序
- **价格降序(pattern=3)**：按价格从高到低排序
- **价格升序(pattern=4)**：按价格从低到高排序

**注意**：由于店铺商品表中没有直接的销量和最低价格字段，这些排序是通过子查询优化实现的：

```sql
-- 优化后的查询结构
SELECT * FROM (
    SELECT
        -- 其他字段...
        (SELECT IFNULL(SUM(sales_volume), 0)
         FROM good_store_specification
         WHERE good_store_list_id = gsl.id AND del_flag = '0') AS salesVolume,
        (SELECT IFNULL(MIN(price), 0)
         FROM good_store_specification
         WHERE good_store_list_id = gsl.id AND del_flag = '0') AS minPrice,
        gsl.update_time AS updateTime
    FROM good_store_list gsl
    -- 其他表连接和条件...
) AS result
ORDER BY result.salesVolume DESC, result.updateTime DESC  -- 综合排序
-- 或者
ORDER BY result.salesVolume DESC  -- 销量排序
-- 或者
ORDER BY result.updateTime DESC  -- 最新排序
-- 或者
ORDER BY result.minPrice DESC  -- 价格降序
-- 或者
ORDER BY result.minPrice ASC  -- 价格升序
```

这种实现方式将子查询放在外层查询的 SELECT 子句中，而不是 ORDER BY 子句中，可以显著提高查询性能，特别是在数据量大的情况下。

## 6. 使用场景

此接口主要用于商家端商品管理页面，商家可以通过不同的状态筛选和排序方式查看自己的商品，例如：

1. **全部商品**：查看所有商品
2. **在售中**：查看正在销售的商品
3. **已下架**：查看已下架的商品
4. **已售罄**：查看库存为0的商品，提醒商家及时补货
5. **发布中**：查看正在审核的商品
6. **已驳回**：查看审核被驳回的商品，提醒商家修改后重新提交
7. **草稿箱**：查看未提交审核的商品
8. **回收站**：查看已删除的商品，可以恢复或彻底删除

## 7. 注意事项

1. 接口需要用户登录后才能访问
2. 只能查询当前登录用户所属店铺的商品
3. 价格相关字段均以元为单位，保留两位小数
4. 商品主图字段 `mainPicture` 和分享图字段 `sharePicture` 返回的是相对路径，需要与图片服务器地址拼接后才能访问
5. 30天销量字段 `monthlySales` 是根据总销量估算得出，仅供参考
