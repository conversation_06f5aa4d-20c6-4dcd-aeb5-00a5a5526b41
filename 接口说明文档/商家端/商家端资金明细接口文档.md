# 商家端资金明细接口文档

## 1. 接口基本信息

- **接口URL**: `/back/storeAccountCapital/findStoreAccountCapitalInfo`
- **请求方式**: GET
- **接口描述**: 获取商家资金流水明细，包括可用余额明细和冻结余额明细

## 2. 请求参数

| 参数名 | 类型 | 必填 | 描述 |
| --- | --- | --- | --- |
| pageNo | Integer | 否 | 页码，默认为1 |
| pageSize | Integer | 否 | 每页记录数，默认为10 |
| isPlatform | Integer | 是 | 查询类型：2-可用余额明细，3-冻结余额明细 |
| id | String | 是 | 店铺ID |

## 3. 响应参数

| 参数名 | 类型 | 描述 |
| --- | --- | --- |
| records | Array | 资金明细记录列表 |
| records[].goAndCome | String | 收支类型：0-收入，1-支出 |
| records[].payType | String | 交易类型，如"订单交易"、"余额提现"等 |
| records[].createTime | String | 交易时间，格式为"yyyy-MM-dd HH:mm:ss" |
| records[].amount | String | 交易金额，带正负号 |
| records[].balance | String | 交易后余额，带"余额"前缀 |
| records[].remarks | String | 交易备注，可能为空 |
| total | Integer | 总记录数 |
| size | Integer | 每页记录数 |
| current | Integer | 当前页码 |
| pages | Integer | 总页数 |

## 4. 响应示例

### 4.1 可用余额明细查询响应示例

```json
{
  "success": true,
  "message": "返回可用余额明细成功!",
  "code": 200,
  "result": {
    "records": [
      {
        "goAndCome": "0",
        "payType": "订单交易",
        "createTime": "2023-04-13 14:29:17",
        "amount": "+98.60",
        "balance": "余额 123.25",
        "remarks": "订单号：202304131429170001"
      },
      {
        "goAndCome": "1",
        "payType": "余额提现",
        "createTime": "2023-04-13 10:15:32",
        "amount": "-500.00",
        "balance": "余额 24.65",
        "remarks": "提现到微信钱包"
      }
    ],
    "total": 100,
    "size": 10,
    "current": 1,
    "pages": 10
  }
}
```

### 4.2 冻结余额明细查询响应示例

```json
{
  "success": true,
  "message": "返回冻结余额明细成功!",
  "code": 200,
  "result": {
    "records": [
      {
        "goAndCome": "0",
        "payType": "订单冻结",
        "createTime": "2023-04-13 14:29:17",
        "amount": "+98.60",
        "balance": "余额 123.25",
        "remarks": "订单号：202304131429170001"
      },
      {
        "goAndCome": "1",
        "payType": "冻结解除",
        "createTime": "2023-04-12 22:56:53",
        "amount": "-24.65",
        "balance": "余额 24.65",
        "remarks": "订单完成解除冻结"
      }
    ],
    "total": 50,
    "size": 10,
    "current": 1,
    "pages": 5
  }
}
```

## 5. 交易类型说明

### 5.1 可用余额明细交易类型

| 交易类型 | 描述 |
| --- | --- |
| 订单交易 | 订单完成后的收入 |
| 余额提现 | 商家提现到银行卡或微信钱包 |
| 退款 | 订单退款导致的支出 |
| 系统充值 | 平台管理员为商家充值 |
| 系统扣除 | 平台管理员从商家账户扣款 |
| 冻结解除 | 冻结资金解除后转入可用余额 |

### 5.2 冻结余额明细交易类型

| 交易类型 | 描述 |
| --- | --- |
| 订单冻结 | 订单支付后资金暂时冻结 |
| 冻结解除 | 订单完成或取消后解除冻结 |
| 系统冻结 | 平台管理员手动冻结商家资金 |
| 系统解冻 | 平台管理员手动解冻商家资金 |

## 6. 业务逻辑说明

### 6.1 可用余额明细查询逻辑

查询条件：
- 店铺ID等于请求参数中的id
- 资金类型为可用余额(`capital_type = '0'`)
- 未被删除(`del_flag = '0'`)

### 6.2 冻结余额明细查询逻辑

查询条件：
- 店铺ID等于请求参数中的id
- 资金类型为冻结余额(`capital_type = '1'`)
- 未被删除(`del_flag = '0'`)

## 7. 使用场景

此接口主要用于商家端资金管理页面，商家可以查看自己的资金流水明细，包括：

1. **可用余额明细**：查看账户可用资金的收支记录，了解资金来源和去向
2. **冻结余额明细**：查看账户被冻结资金的记录，了解资金冻结和解冻情况

## 8. 注意事项

1. 接口需要用户登录后才能访问
2. 只能查询当前登录用户所属店铺的资金明细
3. 金额字段(`amount`)带有正负号，正号表示收入，负号表示支出
4. 余额字段(`balance`)表示交易后的账户余额，带有"余额"前缀
5. 交易类型(`payType`)是中文描述，前端可以直接显示
6. 交易时间(`createTime`)格式为"yyyy-MM-dd HH:mm:ss"
7. 备注字段(`remarks`)可能为空，前端需要做空值处理
