# 商品查询接口文档

## 1. 接口基本信息

- **接口URL**: `/back/goodList/searchGoodList`
- **请求方式**: GET
- **接口描述**: 根据条件查询商品列表，支持多种排序方式

## 2. 请求参数

| 参数名 | 类型 | 必填 | 描述 |
| --- | --- | --- | --- |
| pageNo | Integer | 否 | 页码，默认为1 |
| pageSize | Integer | 否 | 每页记录数，默认为10 |
| isPlatform | Integer | 是 | 查询类型：0-店铺商品，1-平台商品 |
| search | String | 否 | 搜索关键词，支持商品名称和商品描述搜索 |
| goodTypeId | String | 否 | 商品一级分类ID |
| goodTypeIdTwo | String | 否 | 商品二级分类ID |
| goodTypeIdThree | String | 否 | 商品三级分类ID |
| minPrice | BigDecimal | 否 | 最低价格 |
| maxPrice | BigDecimal | 否 | 最高价格 |
| pattern | Integer | 否 | 排序方式，默认为0 |
| sourceType | String | 否 | 商品来源类型 |
| marketingDiscountGoodCount | Integer | 否 | 优惠券数量筛选 |

### 2.1 排序方式说明

| pattern值 | 排序方式 | 描述 |
| --- | --- | --- |
| 0 | 综合排序 | 按销量降序，然后按最新上架时间降序 |
| 1 | 销量排序 | 按销量降序 |
| 2 | 最新排序 | 按上架时间降序 |
| 3 | 价格降序 | 按价格从高到低排序 |
| 4 | 价格升序 | 按价格从低到高排序 |

## 3. 响应参数

| 参数名 | 类型 | 描述 |
| --- | --- | --- |
| success | Boolean | 请求是否成功 |
| message | String | 提示信息 |
| code | Integer | 状态码 |
| result | Object | 结果对象 |
| result.records | Array | 商品列表 |
| result.records[].id | String | 商品ID |
| result.records[].mainPicture | String | 商品主图 |
| result.records[].isPlatform | Integer | 是否平台商品：0-店铺商品，1-平台商品 |
| result.records[].goodName | String | 商品名称 |
| result.records[].smallPrice | BigDecimal | 最低价格 |
| result.records[].smallVipPrice | BigDecimal | 最低会员价格 |
| result.records[].activitiesType | String | 活动类型 |
| result.records[].activityPrice | BigDecimal | 活动价格 |
| result.records[].storeName | String | 店铺名称 |
| result.records[].discount | Integer | 优惠券数量 |
| result.records[].isViewVipPrice | String | 是否显示会员价格 |
| result.records[].sourceType | String | 商品来源类型 |
| result.records[].marketingDiscountGoodCount | Integer | 优惠券数量 |
| result.total | Integer | 总记录数 |
| result.size | Integer | 每页记录数 |
| result.current | Integer | 当前页码 |
| result.pages | Integer | 总页数 |

## 4. 响应示例

```json
{
  "success": true,
  "message": "查询商品列表成功",
  "code": 200,
  "result": {
    "records": [
      {
        "id": "1234567890abcdef",
        "mainPicture": "https://example.com/images/product1.jpg",
        "isPlatform": 1,
        "goodName": "高品质商品",
        "smallPrice": 99.00,
        "smallVipPrice": 89.00,
        "activitiesType": "0",
        "activityPrice": 0,
        "storeName": "",
        "discount": 2,
        "isViewVipPrice": "1",
        "sourceType": "1",
        "marketingDiscountGoodCount": 2
      },
      {
        "id": "abcdef1234567890",
        "mainPicture": "https://example.com/images/product2.jpg",
        "isPlatform": 1,
        "goodName": "热销商品",
        "smallPrice": 199.00,
        "smallVipPrice": 179.00,
        "activitiesType": "0",
        "activityPrice": 0,
        "storeName": "",
        "discount": 1,
        "isViewVipPrice": "1",
        "sourceType": "1",
        "marketingDiscountGoodCount": 1
      }
    ],
    "total": 100,
    "size": 10,
    "current": 1,
    "pages": 10
  }
}
```

## 5. 业务逻辑说明

### 5.1 店铺商品查询逻辑

当 `isPlatform=0` 时，查询店铺商品表 `good_store_list`，查询条件包括：
- 未删除的商品 (`del_flag = '0'`)
- 已上架的商品 (`frame_status = '1'`)
- 状态正常的商品 (`status = '1'`)
- 审核通过的商品 (`audit_status = '2'`)
- 有主图的商品 (`main_picture IS NOT NULL`)

### 5.2 平台商品查询逻辑

当 `isPlatform=1` 时，查询平台商品表 `good_list`，查询条件包括：
- 未删除的商品 (`del_flag = '0'`)
- 已上架的商品 (`frame_status = '1'`)
- 状态正常的商品 (`status = '1'`)
- 审核通过的商品 (`audit_status = '2'`)
- 有主图的商品 (`main_picture IS NOT NULL`)

### 5.3 排序逻辑

根据 `pattern` 参数的不同值，采用不同的排序方式：

- **综合排序(pattern=0)**：先按销量降序，再按更新时间降序
- **销量排序(pattern=1)**：按销量降序
- **最新排序(pattern=2)**：按更新时间降序
- **价格降序(pattern=3)**：按价格从高到低排序
- **价格升序(pattern=4)**：按价格从低到高排序

**注意**：由于店铺商品表中没有直接的销量和最低价格字段，这些排序是通过子查询优化实现的：

```sql
-- 优化后的查询结构
SELECT * FROM (
    SELECT
        -- 其他字段...
        (SELECT IFNULL(SUM(sales_volume), 0)
         FROM good_store_specification
         WHERE good_store_list_id = g.id AND del_flag = '0') AS salesVolume,
        (SELECT IFNULL(MIN(price), 0)
         FROM good_store_specification
         WHERE good_store_list_id = g.id AND del_flag = '0') AS minPrice,
        g.update_time AS updateTime
    FROM good_store_list g
    -- 其他表连接和条件...
) AS result
ORDER BY result.salesVolume DESC, result.updateTime DESC  -- 综合排序
-- 或者
ORDER BY result.salesVolume DESC  -- 销量排序
-- 或者
ORDER BY result.updateTime DESC  -- 最新排序
-- 或者
ORDER BY result.minPrice DESC  -- 价格降序
-- 或者
ORDER BY result.minPrice ASC  -- 价格升序
```

这种实现方式将子查询放在外层查询的 SELECT 子句中，而不是 ORDER BY 子句中，可以显著提高查询性能，特别是在数据量大的情况下。

## 6. 使用场景

此接口主要用于商品列表页面，用户可以通过不同的排序方式查看商品，例如：

1. **综合排序**：默认排序方式，展示热销且新上架的商品
2. **销量排序**：查看最热销的商品
3. **最新排序**：查看最新上架的商品
4. **价格降序**：查看高价商品
5. **价格升序**：查看低价商品

## 7. 注意事项

1. 接口需要用户登录后才能访问
2. 价格相关字段均以元为单位，保留两位小数
3. 商品主图字段 `mainPicture` 返回的是相对路径，需要与图片服务器地址拼接后才能访问
4. 当 `isPlatform=0` 时，`storeName` 字段有值；当 `isPlatform=1` 时，`storeName` 字段为空字符串
