# 商家端商品接口说明文档

## 目录

1. [商品列表查询接口](#1-商品列表查询接口)
2. [商品统计接口](#2-商品统计接口)
3. [商品上下架接口](#3-商品上下架接口)
4. [商品删除接口](#4-商品删除接口)
5. [商品价格与库存修改接口](#5-商品价格与库存修改接口)
   1. [查询修改价格商品信息接口](#51-查询修改价格商品信息接口)
   2. [商品价格库存修改接口](#52-商品价格库存修改接口)

## 前端开发注意事项

1. **参数传递方式**：
   - 请严格按照接口文档中指定的参数传递方式发送请求
   - 简单参数的接口通常使用Form表单参数（application/x-www-form-urlencoded）
   - 复杂参数（如嵌套对象、数组）的接口可能需要使用JSON格式（application/json）
   - 每个接口的参数传递方式会在接口文档中明确说明

2. **请求方式**：
   - 严格按照接口文档中指定的请求方式（GET/POST/PUT/DELETE）发送请求
   - GET请求参数通常通过URL查询字符串传递
   - POST/PUT请求参数通过请求体传递，内容类型根据接口要求可能是form-urlencoded或json

3. **参数命名**：
   - 参数名称区分大小写，必须与接口文档中的完全一致
   - 不要添加额外的参数，可能导致后端解析错误

## 1. 商品列表查询接口

### 1.1 接口基本信息

- **接口URL**: `/back/goodList/queryPageListNew`
- **请求方式**: GET
- **接口描述**: 获取商家端商品列表，支持多种状态筛选和排序方式

### 1.2 请求参数

**参数传递方式**：Form表单参数（application/x-www-form-urlencoded）

| 参数名 | 类型 | 必填 | 描述 |
| --- | --- | --- | --- |
| pageNo | Integer | 否 | 页码，默认为1 |
| pageSize | Integer | 否 | 每页记录数，默认为10 |
| level | String | 否 | 分类级别，默认为-1 |
| typeId | String | 否 | 分类ID，默认为空 |
| goodStatus | String | 否 | 商品状态，默认为0 |
| pattern | Integer | 否 | 排序方式，默认为0 |

#### 1.2.1 商品状态说明

| goodStatus值 | 状态名称 | 描述 |
| --- | --- | --- |
| 0 | 全部商品 | 所有未删除的商品 |
| 1 | 在售中 | 已上架且有库存的商品 |
| 2 | 已下架 | 已下架的商品 |
| 3 | 已售罄 | 已上架但库存为0的商品 |
| 4 | 发布中 | 待审核的商品 |
| 5 | 已驳回 | 审核被驳回的商品 |
| 6 | 草稿箱 | 未提交审核的商品 |
| 7 | 回收站 | 已删除的商品 |

#### 1.2.2 排序方式说明

| pattern值 | 排序方式 | 描述 |
| --- | --- | --- |
| 0 | 综合排序 | 按销量降序，然后按最新上架时间降序 |
| 1 | 销量排序 | 按销量降序 |
| 2 | 最新排序 | 按上架时间降序 |
| 3 | 价格降序 | 按价格从高到低排序 |
| 4 | 价格升序 | 按价格从低到高排序 |

### 1.3 响应参数

| 参数名 | 类型 | 描述 |
| --- | --- | --- |
| success | Boolean | 请求是否成功 |
| message | String | 提示信息 |
| code | Integer | 状态码 |
| result | Object | 结果对象 |
| result.records | Array | 商品列表 |
| result.records[].id | String | 商品ID |
| result.records[].mainPicture | String | 商品主图 |
| result.records[].sharePicture | String | 商品分享图 |
| result.records[].goodName | String | 商品名称 |
| result.records[].minPrice | BigDecimal | 最低价格 |
| result.records[].maxPrice | BigDecimal | 最高价格 |
| result.records[].minVipPrice | BigDecimal | 最低会员价格 |
| result.records[].maxVipPrice | BigDecimal | 最高会员价格 |
| result.records[].salesVolume | Integer | 总销量 |
| result.records[].monthlySales | Integer | 30天销量 |
| result.records[].repertory | BigDecimal | 库存 |
| result.records[].auditStatus | String | 审核状态 |
| result.records[].frameStatus | String | 上架状态 |
| result.records[].status | String | 商品状态 |
| result.total | Integer | 总记录数 |
| result.size | Integer | 每页记录数 |
| result.current | Integer | 当前页码 |
| result.pages | Integer | 总页数 |

### 1.4 响应示例

```json
{
  "success": true,
  "message": "操作成功！",
  "code": 200,
  "result": {
    "records": [
      {
        "id": "**********abcdef",
        "mainPicture": "https://example.com/images/product1.jpg",
        "sharePicture": "https://example.com/images/share1.jpg",
        "goodName": "高品质商品",
        "minPrice": 99.00,
        "maxPrice": 199.00,
        "minVipPrice": 89.00,
        "maxVipPrice": 179.00,
        "salesVolume": 1000,
        "monthlySales": 300,
        "repertory": 500,
        "auditStatus": "2",
        "frameStatus": "1",
        "status": "1"
      },
      {
        "id": "abcdef**********",
        "mainPicture": "https://example.com/images/product2.jpg",
        "sharePicture": "https://example.com/images/share2.jpg",
        "goodName": "热销商品",
        "minPrice": 199.00,
        "maxPrice": 299.00,
        "minVipPrice": 179.00,
        "maxVipPrice": 269.00,
        "salesVolume": 800,
        "monthlySales": 240,
        "repertory": 300,
        "auditStatus": "2",
        "frameStatus": "1",
        "status": "1"
      }
    ],
    "total": 100,
    "size": 10,
    "current": 1,
    "pages": 10
  }
}
```

### 1.5 业务逻辑说明

#### 1.5.1 商品状态筛选逻辑

根据 `goodStatus` 参数的不同值，设置不同的查询条件：

- **全部商品(goodStatus=0)**：`del_flag = '0'` 且 `audit_status != '0'`
- **在售中(goodStatus=1)**：`del_flag = '0'` 且 `audit_status = '2'` 且 `status = '1'` 且 `frame_status = '1'` 且 `repertory > 0`
- **已下架(goodStatus=2)**：`del_flag = '0'` 且 `audit_status = '2'` 且 `status = '1'` 且 `frame_status = '0'`
- **已售罄(goodStatus=3)**：`del_flag = '0'` 且 `audit_status = '2'` 且 `status = '1'` 且 `frame_status = '1'` 且 `repertory <= 0`
- **发布中(goodStatus=4)**：`del_flag = '0'` 且 `audit_status = '1'` 且 `status = '1'`
- **已驳回(goodStatus=5)**：`del_flag = '0'` 且 `audit_status = '3'` 且 `status = '1'`
- **草稿箱(goodStatus=6)**：`del_flag = '0'` 且 `audit_status = '0'`
- **回收站(goodStatus=7)**：`del_flag = '1'`

#### 1.5.2 排序逻辑

根据 `pattern` 参数的不同值，采用不同的排序方式：

- **综合排序(pattern=0)**：先按销量降序，再按更新时间降序
- **销量排序(pattern=1)**：按销量降序
- **最新排序(pattern=2)**：按更新时间降序
- **价格降序(pattern=3)**：按价格从高到低排序
- **价格升序(pattern=4)**：按价格从低到高排序

**注意**：由于店铺商品表中没有直接的销量和最低价格字段，这些排序是通过子查询优化实现的：

```sql
-- 优化后的查询结构
SELECT * FROM (
    SELECT
        -- 其他字段...
        (SELECT IFNULL(SUM(sales_volume), 0)
         FROM good_store_specification
         WHERE good_store_list_id = gsl.id AND del_flag = '0') AS salesVolume,
        (SELECT IFNULL(MIN(price), 0)
         FROM good_store_specification
         WHERE good_store_list_id = gsl.id AND del_flag = '0') AS minPrice,
        gsl.update_time AS updateTime
    FROM good_store_list gsl
    -- 其他表连接和条件...
) AS result
ORDER BY result.salesVolume DESC, result.updateTime DESC  -- 综合排序
-- 或者
ORDER BY result.salesVolume DESC  -- 销量排序
-- 或者
ORDER BY result.updateTime DESC  -- 最新排序
-- 或者
ORDER BY result.minPrice DESC  -- 价格降序
-- 或者
ORDER BY result.minPrice ASC  -- 价格升序
```

这种实现方式将子查询放在外层查询的 SELECT 子句中，而不是 ORDER BY 子句中，可以显著提高查询性能，特别是在数据量大的情况下。

### 1.6 使用场景

此接口主要用于商家端商品管理页面，商家可以通过不同的状态筛选和排序方式查看自己的商品，例如：

1. **全部商品**：查看所有商品
2. **在售中**：查看正在销售的商品
3. **已下架**：查看已下架的商品
4. **已售罄**：查看库存为0的商品，提醒商家及时补货
5. **发布中**：查看正在审核的商品
6. **已驳回**：查看审核被驳回的商品，提醒商家修改后重新提交
7. **草稿箱**：查看未提交审核的商品
8. **回收站**：查看已删除的商品，可以恢复或彻底删除

### 1.7 注意事项

1. 接口需要用户登录后才能访问
2. 只能查询当前登录用户所属店铺的商品
3. 价格相关字段均以元为单位，保留两位小数
4. 商品主图字段 `mainPicture` 和分享图字段 `sharePicture` 返回的是相对路径，需要与图片服务器地址拼接后才能访问
5. 30天销量字段 `monthlySales` 是根据总销量估算得出，仅供参考

## 2. 商品统计接口

### 2.1 接口基本信息

- **接口URL**: `/back/goodList/getGoodStoreCount`
- **请求方式**: GET
- **接口描述**: 获取商家端各状态商品的数量统计，用于商品管理页面的状态标签显示

### 2.2 请求参数

**参数传递方式**：Form表单参数（application/x-www-form-urlencoded）

此接口不需要额外的请求参数，系统会自动获取当前登录用户的 `sysUserId`。

### 2.3 响应参数

| 参数名 | 类型 | 描述 |
| --- | --- | --- |
| success | Boolean | 请求是否成功 |
| message | String | 提示信息 |
| code | Integer | 状态码 |
| result | Object | 结果对象 |
| result.goodSumCount | Integer | 全部商品数量（未删除且非草稿状态的商品） |
| result.goodOnSaleCount | Integer | 在售中商品数量（已上架且有库存的商品） |
| result.goodSoldOutCount | Integer | 已下架商品数量 |
| result.goodSoldOutOfStockCount | Integer | 已售罄商品数量（已上架但库存为0的商品） |
| result.goodPublishingCount | Integer | 发布中商品数量（审核中的商品） |
| result.goodRejectedCount | Integer | 已驳回商品数量（审核被驳回的商品） |
| result.goodDraftsCount | Integer | 草稿箱商品数量（未提交审核的商品） |
| result.goodRecycleBinCount | Integer | 回收站商品数量（已删除的商品） |
| result.goodSumSaleCount | Integer | 在售商品数量（兼容旧版前端，与goodOnSaleCount相同） |
| result.goodAuditCount | Integer | 审核中商品总数（兼容旧版前端，等于goodPublishingCount + goodRejectedCount） |

### 2.4 响应示例

```json
{
  "success": true,
  "message": "请求成功!",
  "code": 200,
  "result": {
    "goodSumCount": 100,
    "goodOnSaleCount": 50,
    "goodSoldOutCount": 10,
    "goodSoldOutOfStockCount": 5,
    "goodPublishingCount": 15,
    "goodRejectedCount": 8,
    "goodDraftsCount": 7,
    "goodRecycleBinCount": 5,
    "goodSumSaleCount": 50,
    "goodAuditCount": 23
  }
}
```

### 2.5 业务逻辑说明

#### 2.5.1 商品状态统计逻辑

接口会根据不同的商品状态条件统计数量：

- **全部商品(goodStatus=0)**：`del_flag = '0'` 且 `audit_status != '0'`
- **在售中(goodStatus=1)**：`del_flag = '0'` 且 `audit_status = '2'` 且 `status = '1'` 且 `frame_status = '1'` 且 `repertory > 0`
- **已下架(goodStatus=2)**：`del_flag = '0'` 且 `audit_status = '2'` 且 `status = '1'` 且 `frame_status = '0'`
- **已售罄(goodStatus=3)**：`del_flag = '0'` 且 `audit_status = '2'` 且 `status = '1'` 且 `frame_status = '1'` 且 `repertory <= 0`
- **发布中(goodStatus=4)**：`del_flag = '0'` 且 `audit_status = '1'` 且 `status = '1'`
- **已驳回(goodStatus=5)**：`del_flag = '0'` 且 `audit_status = '3'` 且 `status = '1'`
- **草稿箱(goodStatus=6)**：`del_flag = '0'` 且 `audit_status = '0'`
- **回收站(goodStatus=7)**：`del_flag = '1'`

#### 2.5.2 兼容旧版前端的字段

为了兼容旧版前端，接口还提供了以下汇总字段：

- **goodSumSaleCount**：与 `goodOnSaleCount` 相同，表示在售商品数量
- **goodAuditCount**：等于 `goodPublishingCount + goodRejectedCount`，表示所有处于审核流程中的商品数量（包括审核中和已驳回）

### 2.6 使用场景

此接口主要用于商家端商品管理页面的状态标签显示，商家可以通过此接口获取各状态下的商品数量，例如：

1. **全部商品**：显示商家所有未删除且非草稿状态的商品总数
2. **在售中**：显示正在销售的商品数量，帮助商家了解当前可售商品情况
3. **已下架**：显示已下架的商品数量，提醒商家可重新上架的商品数量
4. **已售罄**：显示库存为0的商品数量，提醒商家及时补货
5. **发布中**：显示正在审核的商品数量，帮助商家了解审核进度
6. **已驳回**：显示审核被驳回的商品数量，提醒商家修改后重新提交
7. **草稿箱**：显示未提交审核的商品数量，提醒商家完成编辑并提交
8. **回收站**：显示已删除的商品数量，可以恢复或彻底删除

### 2.7 注意事项

1. 接口需要用户登录后才能访问
2. 只能查询当前登录用户所属店铺的商品数量
3. 此接口不返回具体的商品信息，仅返回各状态的商品数量统计
4. 商品状态的判断逻辑与商品列表查询接口 `/back/goodList/queryPageListNew` 中的状态筛选逻辑保持一致
5. 接口响应速度较快，适合在页面初始化时调用，为用户提供商品管理的整体概览

## 3. 商品上下架接口

### 3.1 接口基本信息

## 4. 商品删除接口

### 4.1 接口基本信息

- **接口URL**: `/back/goodList/delete`
- **请求方式**: DELETE
- **接口描述**: 删除商家端商品，将商品移入回收站

### 4.2 请求参数

**参数传递方式**：Form表单参数（application/x-www-form-urlencoded）

| 参数名 | 类型 | 必填 | 描述 |
| --- | --- | --- | --- |
| id | String | 是 | 商品ID，支持批量删除（多个ID以英文逗号分隔，如"id1,id2,id3"） |

### 4.3 响应参数

| 参数名 | 类型 | 描述 |
| --- | --- | --- |
| success | Boolean | 请求是否成功 |
| message | String | 提示信息 |
| code | Integer | 状态码 |
| result | Object | 结果对象，删除接口通常为null |

### 4.4 响应示例

**单个商品删除成功：**

```json
{
  "success": true,
  "message": "删除成功!",
  "code": 200,
  "result": null
}
```

**批量删除成功：**

```json
{
  "success": true,
  "message": "批量删除成功，共删除3个商品",
  "code": 200,
  "result": null
}
```

**批量删除部分成功：**

```json
{
  "success": true,
  "message": "批量删除完成，成功：2，失败：1",
  "code": 200,
  "result": null
}
```

### 4.5 业务逻辑说明

删除商品接口实际上是将商品的删除标志(`del_flag`)设置为"1"，将商品移入回收站，而不是物理删除数据库中的记录。这样做的好处是：

1. 商家可以从回收站恢复误删的商品
2. 保留历史数据，便于后续分析和审计
3. 避免误操作导致的数据丢失

#### 4.5.1 单个商品删除流程

1. 验证当前登录用户是否有权限删除该商品（检查商品是否属于当前用户的店铺）
2. 如果有权限，则将商品的删除标志设置为"1"
3. 更新商品的删除时间和删除原因（如果提供）
4. 返回删除成功的消息

#### 4.5.2 批量删除流程

1. 解析传入的ID字符串，获取所有需要删除的商品ID
2. 对每个商品ID执行以下操作：
   - 验证当前登录用户是否有权限删除该商品
   - 如果有权限，则将商品的删除标志设置为"1"
   - 统计成功和失败的数量
3. 根据成功和失败的数量返回相应的消息：
   - 全部成功：返回"批量删除成功，共删除X个商品"
   - 部分成功：返回"批量删除完成，成功：X，失败：Y"

### 4.6 使用场景

此接口主要用于商家端商品管理页面，当商家需要删除不再销售的商品时使用。删除后的商品会被移入回收站，可以通过回收站页面查看和恢复。

常见的删除场景包括：

1. 商品已经停产，不再销售
2. 商品信息录入错误，需要重新创建
3. 临时性商品活动结束后的清理
4. 店铺调整商品结构，下架并删除部分商品

### 4.7 注意事项

1. 接口需要用户登录后才能访问
2. 只能删除当前登录用户所属店铺的商品
3. 删除操作不可逆，但可以通过回收站恢复
4. 删除商品前，建议先将商品下架，避免正在进行中的订单受到影响
5. 如果商品已经有关联的订单，删除操作不会影响已有订单数据
6. 批量删除时，建议一次不要超过50个商品ID，以避免请求超时
7. 批量删除时，即使部分商品删除失败，接口仍会返回成功状态码(200)，但会在消息中说明成功和失败的数量

- **接口URL**: `/back/goodList/updateFrameStatus`
- **请求方式**: POST
- **接口描述**: 批量修改商品的上下架状态

### 3.2 请求参数

**参数传递方式**：Form表单参数（application/x-www-form-urlencoded）

| 参数名 | 类型 | 必填 | 描述 |
| --- | --- | --- | --- |
| ids | String | 是 | 商品ID列表，多个ID用逗号分隔 |
| frameStatus | String | 是 | 上架状态：0=下架，1=上架 |
| frameExplain | String | 否 | 上下架说明/原因 |

### 3.3 响应参数

| 参数名 | 类型 | 描述 |
| --- | --- | --- |
| success | Boolean | 请求是否成功 |
| message | String | 提示信息 |
| code | Integer | 状态码 |
| result | null | 无返回数据 |

### 3.4 响应示例

```json
{
  "success": true,
  "message": "修改成功!",
  "code": 200,
  "result": null
}
```

### 3.5 业务逻辑说明

接口会根据传入的商品ID列表，批量修改这些商品的上下架状态：

1. 首先验证参数有效性，确保 `frameStatus` 和 `ids` 不为空
2. 将 `ids` 参数按逗号分隔，得到商品ID列表
3. 遍历商品ID列表，对每个商品执行以下操作：
   - 根据ID查询商品信息
   - 如果商品存在，则更新其上架状态(`frameStatus`)和上架说明(`frameExplain`)
   - 如果商品不存在，则返回错误信息

### 3.6 使用场景

此接口主要用于商家端商品管理页面，商家可以通过此接口批量上架或下架商品，常见场景包括：

1. **批量上架商品**：商家选择多个商品，点击"上架"按钮，将商品设置为可售状态
2. **批量下架商品**：商家选择多个商品，点击"下架"按钮，将商品设置为不可售状态
3. **临时下架商品**：当商品需要修改信息或暂停销售时，商家可以先将商品下架

### 3.7 注意事项

1. 接口需要用户登录后才能访问
2. 只能修改当前登录用户所属店铺的商品状态
3. 上架商品时，如果商品库存为0，虽然状态会变为上架，但在前端会显示为"已售罄"状态
4. 下架商品后，该商品将不会在前端商城中显示，但不会影响已经下单的商品
5. 商品上下架操作会立即生效，请谨慎操作

## 5. 商品价格与库存修改接口

### 5.1 查询修改价格商品信息接口

#### 5.1.1 接口基本信息

- **接口URL**: `/back/goodList/getGoodPriceAndRepertory`
- **请求方式**: GET
- **接口描述**: 获取商品的价格和库存信息，用于初始化修改价格库存页面

#### 5.1.2 请求参数

**参数传递方式**：Form表单参数（application/x-www-form-urlencoded）

| 参数名 | 类型 | 必填 | 描述 |
| --- | --- | --- | --- |
| goodId | String | 是 | 商品ID |

#### 5.1.3 响应参数

| 参数名 | 类型 | 描述 |
| --- | --- | --- |
| success | Boolean | 请求是否成功 |
| message | String | 提示信息 |
| code | Integer | 状态码 |
| result | Object | 结果对象 |
| result.goods | Object | 商品信息对象 |
| result.goods.id | String | 商品ID |
| result.goods.mainPicture | String | 商品主图 |
| result.goods.goodName | String | 商品名称 |
| result.goods.repertory | BigDecimal | 商品库存（规格表汇总） |
| result.goods.marketPrice | String | 市场价 |
| result.goods.price | String | 销售价 |
| result.goods.vipPrice | String | 会员价 |
| result.goods.costPrice | String | 成本价 |
| result.goods.smallPrice | String | 最低销售价 |
| result.goods.smallVipPrice | String | 最低会员价 |
| result.goods.smallCostPrice | String | 最低成本价 |
| result.goods.isSpecification | String | 是否有规格（0：无规格；1：有规格） |
| result.goods.goodStoreSpecificationList | Array | 商品规格列表 |
| result.goods.goodStoreSpecificationList[].id | String | 规格ID |
| result.goods.goodStoreSpecificationList[].price | BigDecimal | 规格销售价 |
| result.goods.goodStoreSpecificationList[].vipPrice | BigDecimal | 规格会员价 |
| result.goods.goodStoreSpecificationList[].costPrice | BigDecimal | 规格成本价 |
| result.goods.goodStoreSpecificationList[].repertory | BigDecimal | 规格库存 |
| result.goods.goodStoreSpecificationList[].specification | String | 规格名称 |
| result.goods.goodStoreSpecificationList[].specificationPicture | String | 规格图片 |
| result.goods.goodStoreSpecificationList[].weight | BigDecimal | 规格重量 |

#### 5.1.4 响应示例

```json
{
  "success": true,
  "message": "查询商品价格库存信息成功!",
  "code": 200,
  "result": {
    "goods": {
      "id": "**********",
      "mainPicture": "[\"http://example.com/images/product1.jpg\"]",
      "goodName": "不粘套装pan烤盘固底蛋糕模具烘焙工具加厚baking布朗尼方形",
      "repertory": 87436,
      "marketPrice": "199.00",
      "price": "99.00",
      "vipPrice": "89.00",
      "costPrice": "50.00",
      "smallPrice": "99.00",
      "smallVipPrice": "89.00",
      "smallCostPrice": "50.00",
      "isSpecification": "0",
      "goodStoreSpecificationList": [
        {
          "id": "spec123456",
          "price": 99.00,
          "vipPrice": 89.00,
          "costPrice": 50.00,
          "repertory": 87436,
          "specification": "彩盒装",
          "specificationPicture": "http://example.com/images/spec1.jpg",
          "weight": 0.5
        }
      ]
    }
  }
}
```

#### 5.1.5 业务逻辑说明

接口会根据传入的商品ID，查询商品的价格和库存信息：

1. 首先查询商品主表信息，包括商品ID、名称、主图、市场价、是否有规格等基本信息
2. 然后查询商品规格表信息，包括规格ID、规格名称、销售价、会员价、成本价、库存等
3. 将规格信息添加到商品信息中，一并返回给前端

#### 5.1.6 使用场景

此接口主要用于商家端商品管理页面的价格库存修改功能，当商家点击"修改价格"或"修改库存"按钮时，系统会调用此接口获取商品的价格和库存信息，用于初始化修改页面。

根据原型图，接口支持两种场景：

1. **修改价格场景**：
   - **无规格商品**：
     - 显示商品名称和图片
     - 显示当前价格
     - 提供修改销售价的输入框
     - 显示改后销售价

   - **有规格商品**：
     - 显示商品名称和图片
     - 显示商品价格区间
     - 显示各个规格的名称、图片、当前价格
     - 提供各规格的价格修改输入框
     - 提供批量修改功能，支持"价格加减"和"统一价"两种修改方式

2. **修改库存场景**：
   - **无规格商品**：
     - 显示商品名称和图片
     - 显示当前库存
     - 提供增减库存的输入框
     - 显示改后库存

   - **有规格商品**：
     - 显示商品名称和图片
     - 显示商品总库存
     - 显示各个规格的名称、图片、当前库存
     - 提供各规格的增减库存输入框
     - 显示各规格的改后库存
     - 提供批量设置功能

**重点备注**：虽然原型图中包含优惠促销信息和多种价格类型，但本项目实际只需要修改销售价即可，其他促销相关功能可以忽略。

#### 5.1.7 注意事项

1. 接口需要用户登录后才能访问
2. 只能查询当前登录用户所属店铺的商品
3. 商品主表中的`repertory`字段是所有规格库存的总和
4. **前端如何判断商品是否有规格**：
   - 通过返回的`isSpecification`字段判断：`"0"`表示无规格，`"1"`表示有规格
   - 也可以通过`goodStoreSpecificationList`的长度判断：长度为1通常是无规格商品，大于1一定是有规格商品
5. **前端如何获取价格和库存数据**：
   - **无规格商品**：直接使用`goodStoreSpecificationList`中唯一一条记录的`price`和`repertory`字段
   - **有规格商品**：遍历`goodStoreSpecificationList`，获取每个规格的`price`和`repertory`字段
6. 对于无规格商品，`goodStoreSpecificationList`中只有一条记录
7. 对于有规格商品，`goodStoreSpecificationList`中有多条记录，每条记录对应一个规格
8. 规格图片字段`specificationPicture`可能为空，此时应使用商品主图
9. 规格数据（`goodStoreSpecificationList`）按照价格升序排列，即价格从低到高排序

### 5.2 商品价格库存修改接口

#### 5.2.1 接口基本信息

- **接口URL**: `/back/goodList/updateGoodPriceAndRepertory`
- **请求方式**: POST
- **接口描述**: 修改商家端店铺商品的价格和库存信息

#### 5.2.2 请求参数

**参数传递方式**：JSON格式（application/json）

**注意**：请求体需要传递JSON格式的数据，例如：

```json
{
  "id": "**********",
  "marketPrice": 199.00,
  "goodStoreSpecificationList": [
    {
      "id": "spec123456",
      "price": 99.00,
      "repertory": 100
    },
    {
      "id": "spec789012",
      "price": 199.00,
      "repertory": 50
    }
  ]
}
```

| 参数名 | 类型 | 必填 | 描述 |
| --- | --- | --- | --- |
| id | String | 是 | 商品ID |
| marketPrice | BigDecimal | 是 | 商品市场价 |
| goodStoreSpecificationList | Array | 是 | 商品规格列表 |
| goodStoreSpecificationList[].id | String | 是 | 规格ID |
| goodStoreSpecificationList[].price | BigDecimal | 是 | 规格销售价格 |
| goodStoreSpecificationList[].repertory | BigDecimal | 是 | 规格库存 |

**特别说明**：

- 无论是修改价格还是修改库存，都使用同一个接口，只需传入修改后的具体值即可
- 对于无规格商品，`goodStoreSpecificationList`中只会有一条记录
- 对于有规格商品，`goodStoreSpecificationList`中会有多条记录，每条对应一个规格
- 请务必使用JSON格式提交参数，不要使用Form表单格式

#### 5.2.3 响应参数

| 参数名 | 类型 | 描述 |
| --- | --- | --- |
| success | Boolean | 请求是否成功 |
| message | String | 提示信息 |
| code | Integer | 状态码 |
| result | Object | 结果对象，通常为null |

#### 5.2.4 响应示例

```json
{
  "success": true,
  "message": "修改成功!",
  "code": 200,
  "result": null
}
```

#### 5.2.5 业务逻辑说明

接口会根据传入的商品ID和规格信息，更新商品的价格和库存：

1. 首先验证商品是否存在，如果不存在则返回错误信息
2. 更新商品主表的市场价（marketPrice）
3. 遍历规格列表，对每个规格执行以下操作：
   - 根据规格ID查询规格信息
   - 如果规格存在，则直接使用提交的价格和库存值更新规格信息
4. 调用`updateGoodStoreListRepertory`方法，计算并更新商品主表的总库存：
   - 查询所有规格的库存并汇总
   - 更新商品主表的库存字段（repertory）

**注意**：虽然接口支持修改会员价格和成本价，但根据项目需求，实际只需要关注销售价的修改，其他价格类型可以忽略。

#### 5.2.6 使用场景

此接口主要用于商家端商品管理页面，商家可以通过此接口快速修改商品的价格和库存信息，常见场景包括：

1. **修改价格场景**：
   - **无规格商品**：商家直接输入新的销售价，点击确认提交修改
   - **有规格商品**：商家可以为每个规格单独设置新的销售价

2. **修改库存场景**：
   - **无规格商品**：商家直接输入新的库存数量，点击确认提交修改
   - **有规格商品**：商家可以为每个规格单独设置新的库存数量

**重点备注**：

- 虽然原型图中包含参考价、满件折扣等促销信息，但本项目实际只需要修改销售价即可，其他促销相关功能可以忽略
- 修改价格时，只需关注销售价的修改，不需要处理会员价、成本价等其他价格类型
- 批量修改功能（如"价格加减"、"统一价"等）由前端实现，后端只接收最终修改后的价格或库存值

#### 5.2.7 注意事项

1. 接口需要用户登录后才能访问
2. 只能修改当前登录用户所属店铺的商品
3. 价格相关字段均以元为单位，保留两位小数
4. 库存修改会立即生效，如果将库存修改为0，商品会自动变为"已售罄"状态
5. 如果商品有多个规格，必须传入所有规格的信息，否则未传入的规格信息将保持不变
6. 商品主表的库存会自动更新为所有规格库存的总和，无需手动计算
7. 价格修改可能会影响商品在专区中的状态，如果修改后的价格不符合专区限制条件，商品可能会被自动从专区中移除
8. 虽然原型图中显示了参考价、满件折扣等促销信息，但本项目实际只需要修改销售价即可，其他促销相关功能可以忽略
9. 修改价格时，只需关注销售价的修改，不需要处理会员价、成本价等其他价格类型
10. **重要**：此接口必须使用JSON格式（application/json）提交参数，不能使用Form表单格式
