# 商家端接口文档汇总

## 1. 概述

本文档汇总了商家端小程序和管理后台使用的主要接口，包括首页数据、订单管理、商品管理、资金管理等模块的接口说明。

## 2. 接口列表

### 2.1 首页相关接口

| 接口名称 | 接口URL | 请求方式 | 接口描述 |
| --- | --- | --- | --- |
| [首页数据接口](#首页数据接口) | /back/storeManage/findUseInfo | GET | 获取商家端首页展示的账户余额、销售数据等信息 |

### 2.2 订单管理相关接口

| 接口名称 | 接口URL | 请求方式 | 接口描述 |
| --- | --- | --- | --- |
| [订单统计接口](#订单统计接口) | /back/order/storeOrderCount | GET | 获取商家端不同状态订单的数量统计 |
| [订单列表接口](#订单列表接口) | /back/order/getOrderStoreListDto | GET | 分页获取商家订单列表 |
| [订单详情接口](#订单详情接口) | /back/order/getOrderStoreListDtoDetail | GET | 获取订单详细信息 |

### 2.3 资金管理相关接口

| 接口名称 | 接口URL | 请求方式 | 接口描述 |
| --- | --- | --- | --- |
| [资金明细接口](#资金明细接口) | /back/storeAccountCapital/findStoreAccountCapitalInfo | GET | 获取商家资金流水明细 |
| [提现申请接口](#提现申请接口) | /back/storeRechargeRecord/cashOut | POST | 商家申请提现 |
| [提现记录接口](#提现记录接口) | /back/storeRechargeRecord/findPayRecord | GET | 获取商家提现记录 |

## 3. 接口详细说明

### <span id="首页数据接口">3.1 首页数据接口</span>

#### 3.1.1 接口基本信息

- **接口URL**: `/back/storeManage/findUseInfo`
- **请求方式**: GET
- **接口描述**: 获取商家端首页展示的账户余额、销售数据等信息

#### 3.1.2 请求参数

无需额外参数，接口通过当前登录用户的 `sysUserId` 自动获取相关数据。

#### 3.1.3 响应参数

| 参数名 | 类型 | 描述 |
| --- | --- | --- |
| id | String | 店铺ID |
| balance | BigDecimal | 账户余额 |
| accountFrozen | BigDecimal | 冻结金额 |
| unusableFrozen | BigDecimal | 不可用冻结金额 |
| isOpenWelfarePayments | String | 是否开启福利金支付 |
| moneyReceivingCode | String | 收款码 |
| haveWithdrawal | BigDecimal | 已提现金额 |
| withdrawingAmount | BigDecimal | 提现中金额 |
| todaySalesAmount | BigDecimal | 今日销售额 |
| todayOrderCount | Integer | 今日订单数量 |
| totalPerformance | BigDecimal | 累计业绩 |

#### 3.1.4 响应示例

```json
{
  "success": true,
  "message": "操作成功！",
  "code": 200,
  "result": {
    "id": "1234567890abcdef",
    "balance": 8562.00,
    "accountFrozen": 1280.00,
    "unusableFrozen": 0.00,
    "isOpenWelfarePayments": "1",
    "moneyReceivingCode": "https://example.com/qrcode/123456",
    "haveWithdrawal": 12680.00,
    "withdrawingAmount": 3450.00,
    "todaySalesAmount": 3562.00,
    "todayOrderCount": 86,
    "totalPerformance": 152680.00
  }
}
```

#### 3.1.5 字段说明

##### 账户资金相关字段

- **账户余额(balance)**: 商家当前可用的资金余额
- **冻结金额(accountFrozen)**: 因订单未完成等原因暂时冻结的资金
- **不可用冻结金额(unusableFrozen)**: 因特殊原因被冻结且不可用的资金
- **已提现金额(haveWithdrawal)**: 商家历史累计已成功提现的金额总和，从提现申请表中汇总
- **提现中金额(withdrawingAmount)**: 商家当前正在提现中的金额总和，从提现申请表中汇总

##### 销售数据相关字段

- **今日销售额(todaySalesAmount)**: 当天产生的所有订单的实际收款金额总和
- **今日订单数量(todayOrderCount)**: 当天产生的订单总数
- **累计业绩(totalPerformance)**: 所有已完成订单的实际收款金额总和

#### 3.1.6 业务逻辑说明

##### 提现相关金额计算逻辑

###### 提现中金额计算逻辑

提现中金额是通过查询 `store_withdraw_deposit` 表中符合以下条件的记录总和计算得出：
- 提现状态为待审核或待打款(`status IN ('0', '1')`)
- 未被删除(`del_flag = '0'`)

###### 已提现金额计算逻辑

已提现金额是通过查询 `store_withdraw_deposit` 表中符合以下条件的记录总和计算得出：
- 提现状态为已付款(`status = '2'`)
- 未被删除(`del_flag = '0'`)

##### 今日销售额计算逻辑

今日销售额是通过查询 `order_store_list` 表中符合以下条件的记录总和计算得出：
- 订单状态为交易成功或已收货(`status IN ('3', '5')`)
- 创建时间为当天(`DATE(create_time) = CURDATE()`)
- 未被删除(`del_flag = '0'`)

##### 今日订单数量计算逻辑

今日订单数量是通过查询 `order_store_list` 表中符合以下条件的记录数量计算得出：
- 创建时间为当天(`DATE(create_time) = CURDATE()`)
- 未被删除(`del_flag = '0'`)

##### 累计业绩计算逻辑

累计业绩是通过查询 `order_store_list` 表中符合以下条件的记录总和计算得出：
- 订单状态为交易成功或已收货(`status IN ('3', '5')`)
- 未被删除(`del_flag = '0'`)

### <span id="订单统计接口">3.2 订单统计接口</span>

#### 3.2.1 接口基本信息

- **接口URL**: `/back/order/storeOrderCount`
- **请求方式**: GET
- **接口描述**: 获取商家端不同状态订单的数量统计，用于订单管理页面的状态标签显示

#### 3.2.2 请求参数

无需额外参数，接口通过当前登录用户的 `sysUserId` 自动获取相关数据。

#### 3.2.3 响应参数

| 参数名 | 类型 | 描述 |
| --- | --- | --- |
| allCountStore | Integer | 全部订单数量 |
| obligationCount | Integer | 待付款订单数量 |
| waitDeliveryCount | Integer | 待发货订单数量 |
| waitForReceivingCount | Integer | 待收货订单数量 |
| doneCountStore | Integer | 已完成订单数量 |
| cancelCountStore | Integer | 已关闭订单数量 |
| dealsAreDoneCount | Integer | 交易成功但未评价订单数量 |
| waitHandleRefundCount | Integer | 待处理退款售后数量 |

#### 3.2.4 响应示例

```json
{
  "success": true,
  "message": "查询成功!",
  "code": 200,
  "result": {
    "allCountStore": 125,
    "obligationCount": 15,
    "waitDeliveryCount": 30,
    "waitForReceivingCount": 25,
    "doneCountStore": 40,
    "cancelCountStore": 10,
    "dealsAreDoneCount": 5,
    "waitHandleRefundCount": 8
  }
}
```

#### 3.2.5 订单状态说明

系统中订单状态定义如下：

| 状态码 | 状态名称 | 说明 |
| --- | --- | --- |
| 0 | 待付款 | 订单已创建但尚未支付 |
| 1 | 待发货 | 订单已支付，等待商家发货 |
| 2 | 待收货 | 商家已发货，等待买家确认收货 |
| 3 | 交易成功 | 买家已确认收货，交易完成 |
| 4 | 交易关闭 | 订单已取消或交易失败 |
| 5 | 已完成 | 订单全部流程已完成 |

### <span id="资金明细接口">3.3 资金明细接口</span>

#### 3.3.1 接口基本信息

- **接口URL**: `/back/storeAccountCapital/findStoreAccountCapitalInfo`
- **请求方式**: GET
- **接口描述**: 获取商家资金流水明细

#### 3.3.2 请求参数

| 参数名 | 类型 | 必填 | 描述 |
| --- | --- | --- | --- |
| pageNo | Integer | 否 | 页码，默认为1 |
| pageSize | Integer | 否 | 每页记录数，默认为10 |
| isPlatform | Integer | 是 | 查询类型：2-可用余额明细，3-冻结余额明细 |
| id | String | 是 | 店铺ID |

#### 3.3.3 响应参数

| 参数名 | 类型 | 描述 |
| --- | --- | --- |
| records | Array | 资金明细记录列表 |
| records[].goAndCome | String | 收支类型：0-收入，1-支出 |
| records[].payType | String | 交易类型，如"订单交易"、"余额提现"等 |
| records[].createTime | String | 交易时间，格式为"yyyy-MM-dd HH:mm:ss" |
| records[].amount | String | 交易金额，带正负号 |
| records[].balance | String | 交易后余额，带"余额"前缀 |
| total | Integer | 总记录数 |
| size | Integer | 每页记录数 |
| current | Integer | 当前页码 |
| pages | Integer | 总页数 |

#### 3.3.4 响应示例

```json
{
  "success": true,
  "message": "返回可用余额明细成功!",
  "code": 200,
  "result": {
    "records": [
      {
        "goAndCome": "0",
        "payType": "订单交易",
        "createTime": "2023-04-13 14:29:17",
        "amount": "+98.60",
        "balance": "余额 123.25"
      },
      {
        "goAndCome": "0",
        "payType": "订单交易",
        "createTime": "2023-04-12 22:56:53",
        "amount": "+24.65",
        "balance": "余额 24.65"
      }
    ],
    "total": 100,
    "size": 10,
    "current": 1,
    "pages": 10
  }
}
```

## 4. 通用状态码说明

| 状态码 | 说明 |
| --- | --- |
| 200 | 请求成功 |
| 500 | 服务器内部错误 |
| 401 | 未授权，需要登录 |
| 403 | 禁止访问，权限不足 |

## 5. 注意事项

1. 所有接口都需要用户登录后才能访问
2. 金额类型数据均以元为单位，保留两位小数
3. 日期类型数据格式为"yyyy-MM-dd HH:mm:ss"
4. 所有接口只能查询当前登录用户所属店铺的数据
