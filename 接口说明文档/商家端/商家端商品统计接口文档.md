# 商家端商品统计接口文档

## 1. 接口基本信息

- **接口URL**: `/back/goodList/getGoodStoreCount`
- **请求方式**: GET
- **接口描述**: 获取商家端各状态商品的数量统计，用于商品管理页面的状态标签显示

## 2. 请求参数

此接口不需要额外的请求参数，系统会自动获取当前登录用户的 `sysUserId`。

## 3. 响应参数

| 参数名 | 类型 | 描述 |
| --- | --- | --- |
| success | Boolean | 请求是否成功 |
| message | String | 提示信息 |
| code | Integer | 状态码 |
| result | Object | 结果对象 |
| result.goodSumCount | Integer | 全部商品数量（未删除且非草稿状态的商品） |
| result.goodOnSaleCount | Integer | 在售中商品数量（已上架且有库存的商品） |
| result.goodSoldOutCount | Integer | 已下架商品数量 |
| result.goodSoldOutOfStockCount | Integer | 已售罄商品数量（已上架但库存为0的商品） |
| result.goodPublishingCount | Integer | 发布中商品数量（审核中的商品） |
| result.goodRejectedCount | Integer | 已驳回商品数量（审核被驳回的商品） |
| result.goodDraftsCount | Integer | 草稿箱商品数量（未提交审核的商品） |
| result.goodRecycleBinCount | Integer | 回收站商品数量（已删除的商品） |
| result.goodSumSaleCount | Integer | 在售商品数量（兼容旧版前端，与goodOnSaleCount相同） |
| result.goodAuditCount | Integer | 审核中商品总数（兼容旧版前端，等于goodPublishingCount + goodRejectedCount） |

## 4. 响应示例

```json
{
  "success": true,
  "message": "请求成功!",
  "code": 200,
  "result": {
    "goodSumCount": 100,
    "goodOnSaleCount": 50,
    "goodSoldOutCount": 10,
    "goodSoldOutOfStockCount": 5,
    "goodPublishingCount": 15,
    "goodRejectedCount": 8,
    "goodDraftsCount": 7,
    "goodRecycleBinCount": 5,
    "goodSumSaleCount": 50,
    "goodAuditCount": 23
  }
}
```

## 5. 业务逻辑说明

### 5.1 商品状态统计逻辑

接口会根据不同的商品状态条件统计数量：

- **全部商品(goodStatus=0)**：`del_flag = '0'` 且 `audit_status != '0'`
- **在售中(goodStatus=1)**：`del_flag = '0'` 且 `audit_status = '2'` 且 `status = '1'` 且 `frame_status = '1'` 且 `repertory > 0`
- **已下架(goodStatus=2)**：`del_flag = '0'` 且 `audit_status = '2'` 且 `status = '1'` 且 `frame_status = '0'`
- **已售罄(goodStatus=3)**：`del_flag = '0'` 且 `audit_status = '2'` 且 `status = '1'` 且 `frame_status = '1'` 且 `repertory <= 0`
- **发布中(goodStatus=4)**：`del_flag = '0'` 且 `audit_status = '1'` 且 `status = '1'`
- **已驳回(goodStatus=5)**：`del_flag = '0'` 且 `audit_status = '3'` 且 `status = '1'`
- **草稿箱(goodStatus=6)**：`del_flag = '0'` 且 `audit_status = '0'`
- **回收站(goodStatus=7)**：`del_flag = '1'`

### 5.2 兼容旧版前端的字段

为了兼容旧版前端，接口还提供了以下汇总字段：

- **goodSumSaleCount**：与 `goodOnSaleCount` 相同，表示在售商品数量
- **goodAuditCount**：等于 `goodPublishingCount + goodRejectedCount`，表示所有处于审核流程中的商品数量（包括审核中和已驳回）

## 6. 使用场景

此接口主要用于商家端商品管理页面的状态标签显示，商家可以通过此接口获取各状态下的商品数量，例如：

1. **全部商品**：显示商家所有未删除且非草稿状态的商品总数
2. **在售中**：显示正在销售的商品数量，帮助商家了解当前可售商品情况
3. **已下架**：显示已下架的商品数量，提醒商家可重新上架的商品数量
4. **已售罄**：显示库存为0的商品数量，提醒商家及时补货
5. **发布中**：显示正在审核的商品数量，帮助商家了解审核进度
6. **已驳回**：显示审核被驳回的商品数量，提醒商家修改后重新提交
7. **草稿箱**：显示未提交审核的商品数量，提醒商家完成编辑并提交
8. **回收站**：显示已删除的商品数量，可以恢复或彻底删除

## 7. 注意事项

1. 接口需要用户登录后才能访问
2. 只能查询当前登录用户所属店铺的商品数量
3. 此接口不返回具体的商品信息，仅返回各状态的商品数量统计
4. 商品状态的判断逻辑与商品列表查询接口 `/back/goodList/queryPageListNew` 中的状态筛选逻辑保持一致
5. 接口响应速度较快，适合在页面初始化时调用，为用户提供商品管理的整体概览
