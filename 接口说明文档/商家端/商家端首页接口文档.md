# 商家端首页接口文档

## 1. 商家端首页数据接口

### 1.1 接口基本信息

- **接口URL**: `/back/storeManage/findUseInfo`
- **请求方式**: GET
- **接口描述**: 获取商家端首页展示的账户余额、销售数据等信息

### 1.2 请求参数

无需额外参数，接口通过当前登录用户的 `sysUserId` 自动获取相关数据。

### 1.3 响应参数

| 参数名 | 类型 | 描述 |
| --- | --- | --- |
| id | String | 店铺ID |
| balance | BigDecimal | 账户余额 |
| accountFrozen | BigDecimal | 冻结金额 |
| unusableFrozen | BigDecimal | 不可用冻结金额 |
| isOpenWelfarePayments | String | 是否开启福利金支付 |
| moneyReceivingCode | String | 收款码 |
| haveWithdrawal | BigDecimal | 已提现金额 |
| withdrawingAmount | BigDecimal | 提现中金额 |
| todaySalesAmount | BigDecimal | 今日销售额 |
| todayOrderCount | Integer | 今日订单数量 |
| totalPerformance | BigDecimal | 累计业绩 |

### 1.4 响应示例

```json
{
  "success": true,
  "message": "操作成功！",
  "code": 200,
  "result": {
    "id": "1234567890abcdef",
    "balance": 8562.00,
    "accountFrozen": 1280.00,
    "unusableFrozen": 0.00,
    "isOpenWelfarePayments": "1",
    "moneyReceivingCode": "https://example.com/qrcode/123456",
    "haveWithdrawal": 12680.00,
    "withdrawingAmount": 3450.00,
    "todaySalesAmount": 3562.00,
    "todayOrderCount": 86,
    "totalPerformance": 152680.00
  }
}
```

## 2. 字段说明

### 2.1 账户资金相关字段

- **账户余额(balance)**: 商家当前可用的资金余额
- **冻结金额(accountFrozen)**: 因订单未完成等原因暂时冻结的资金
- **不可用冻结金额(unusableFrozen)**: 因特殊原因被冻结且不可用的资金
- **已提现金额(haveWithdrawal)**: 商家历史累计已成功提现的金额总和，从提现申请表中汇总
- **提现中金额(withdrawingAmount)**: 商家当前正在提现中的金额总和，从提现申请表中汇总

### 2.2 销售数据相关字段

- **今日销售额(todaySalesAmount)**: 当天产生的所有订单的实际收款金额总和
- **今日订单数量(todayOrderCount)**: 当天产生的订单总数
- **累计业绩(totalPerformance)**: 所有已完成订单的实际收款金额总和

## 3. 业务逻辑说明

### 3.1 提现相关金额计算逻辑

#### 3.1.1 提现中金额计算逻辑

提现中金额是通过查询 `store_withdraw_deposit` 表中符合以下条件的记录总和计算得出：
- 提现状态为待审核或待打款(`status IN ('0', '1')`)
- 未被删除(`del_flag = '0'`)

#### 3.1.2 已提现金额计算逻辑

已提现金额是通过查询 `store_withdraw_deposit` 表中符合以下条件的记录总和计算得出：
- 提现状态为已付款(`status = '2'`)
- 未被删除(`del_flag = '0'`)

### 3.2 今日销售额计算逻辑

今日销售额是通过查询 `order_store_list` 表中符合以下条件的记录总和计算得出：
- 订单状态为交易成功或已收货(`status IN ('3', '5')`)
- 创建时间为当天(`DATE(create_time) = CURDATE()`)
- 未被删除(`del_flag = '0'`)

### 3.3 今日订单数量计算逻辑

今日订单数量是通过查询 `order_store_list` 表中符合以下条件的记录数量计算得出：
- 创建时间为当天(`DATE(create_time) = CURDATE()`)
- 未被删除(`del_flag = '0'`)

### 3.4 累计业绩计算逻辑

累计业绩是通过查询 `order_store_list` 表中符合以下条件的记录总和计算得出：
- 订单状态为交易成功或已收货(`status IN ('3', '5')`)
- 未被删除(`del_flag = '0'`)

## 4. 注意事项

1. 所有金额字段均以元为单位，保留两位小数
2. 今日销售额和今日订单数量每天零点会自动重置
3. 提现中金额会在提现成功后自动转入已提现金额
4. 累计业绩只统计已完成的订单，不包含已取消、已退款的订单
