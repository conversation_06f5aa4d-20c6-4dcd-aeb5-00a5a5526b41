# 整体架构设计
## 技术栈

- **IDE**: IDEA (必须安装 Lombok 插件)
- **编程语言**: Java 8+ (支持 Java 17)
- **依赖管理**: Maven
- **基础框架**: Spring Boot 2.7.18
- **微服务框架**: Spring Cloud Alibaba 2021.0.1.0
- **持久层框架**: MybatisPlus 3.5.3.2
- **报表工具**: JimuReport 1.8.1
- **安全框架**: Apache Shiro 1.12.0, JWT 3.11.0
- **微服务技术栈**: Spring Cloud Alibaba, Nacos, Gateway, Sentinel, Skywalking
- **数据库连接池**: 阿里巴巴 Druid 1.1.22
- **日志打印**: Logback
- **缓存**: Redis
- **其他工具**: AutoPOI, Fastjson, POI, Swagger-ui, Quartz, Lombok 等
- **默认数据库**: MySQL 5.7+

## 项目结构

```
├─jeecg-boot-parent（父POM：管理项目依赖、模块组织）
│  ├─jeecg-boot-base-core（共通模块：工具类、配置、权限、查询过滤器、注解等）
│  ├─jeecg-module-demo（示例代码）
│  ├─jeecg-module-system（系统管理模块）
│  │  ├─jeecg-system-biz（业务代码模块：负责系统管理权限等核心业务逻辑）
│  │  ├─jeecg-system-start（启动模块：单体应用启动入口，端口8080）
│  │  ├─jeecg-system-api（系统管理模块对外API）
│  │  │  ├─jeecg-system-cloud-api（微服务风格的对外API接口）
│  │  │  ├─jeecg-system-local-api（单体应用风格的对外API接口）
├─jeecg-server-cloud（微服务模块）
│  ├─jeecg-cloud-gateway（微服务网关模块，端口9999）
│  ├─jeecg-cloud-nacos（Nacos服务模块，端口8848）
│  ├─jeecg-system-cloud-start（System微服务启动模块，端口7001）
│  ├─jeecg-demo-cloud-start（Demo微服务启动模块，端口7002）
│  ├─jeecg-visual
│  │  ├─jeecg-cloud-monitor（微服务监控模块，端口9111）
│  │  ├─jeecg-cloud-xxljob（微服务xxljob定时任务服务端，端口9080）
│  │  ├─jeecg-cloud-sentinel（微服务sentinel服务端，端口9000）
│  │  ├─jeecg-cloud-test（微服务测试示例，涵盖各种例子）
│  │  │  ├─jeecg-cloud-test-more（微服务测试示例，涵盖feign、熔断降级、xxljob、分布式锁等）
│  │  │  ├─jeecg-cloud-test-rabbitmq（微服务测试示例，RabbitMQ集成）
│  │  │  ├─jeecg-cloud-test-seata（微服务测试示例，Seata分布式事务）
│  │  │  ├─jeecg-cloud-test-shardingsphere（微服务测试示例，分库分表）
```

## 重点说明

1. **开发模式**：该项目使用单体模式开发，核心业务代码位于 `jeecg-system-biz` 模块，负责系统管理、权限管理等功能的实现。
2. **启动模块**：单体应用的启动模块为 `jeecg-system-start`，用于启动整个应用，监听端口为 8080。
3. **业务逻辑模块**：`jeecg-system-biz` 模块专注于实现系统管理相关的业务逻辑，其他模块如 `jeecg-system-api` 则用于提供对外的 API 接口。

通过这种描述，AI 工具能够精确识别和理解项目的架构，特别是单体应用开发模式的特点。

## 业务表分包说明

假设有一张名为 `LcReserveOrder` 的业务表，代码包应该按以下方式组织：

```
├─LcReserveOrder（业务表代码包）
│  ├─api（对外API接口：开放给用户端，接口前缀固定位 `after` 表示需要登录才能调用）
│  ├─controller（管理后台数据接口：用于接收和处理管理员操作的请求）
│  ├─service（服务层：包含业务逻辑）
│  ├─mapper（持久层：数据库操作接口）
│  ├─entity（实体类：映射数据库表的类）
│  ├─vo（视图对象：可能需要返回给前端的实体类）
│  ├─dto（数据传输对象：用于接收复杂 JSON 参数的实体类）
```

## 说明

- **api**：开放的 API 接口供用户端调用。接口前缀 `after` 表示该接口必须先进行用户登录验证后才能访问。
  
- **controller**：处理前端管理后台的请求，接收数据并返回处理结果，主要针对管理后台的数据接口。

- **service**：处理业务逻辑，通常会调用 `mapper` 层与数据库交互，或者根据需要调用其他业务服务。

- **mapper**：对应数据库操作，封装了与数据库交互的逻辑，通常与 `entity` 层的类关联。

- **entity**：定义与数据库表对应的实体类，每个类的字段对应表中的列。

- **vo**：用于与前端交互的数据格式，通常包含需要展示给前端的字段。

- **dto**：接收复杂的 JSON 参数的实体类，用于接收来自前端或者外部接口的数据。

### 异常处理最佳实践

1. **优先使用自定义异常**：在业务逻辑中，应优先使用自定义异常（如 [JeecgBootException](cci:2://file:///Users/<USER>/Dev/SideWork/heartful-mall/heartful-mall/jeecg-boot-base-core/src/main/java/org/jeecg/common/exception/JeecgBootException.java:8:0-38:1)）而非直接抛出 `RuntimeException`，便于统一处理和维护。
2. **统一错误码**：自定义异常应携带错误码，便于前端和日志系统统一处理。
3. **日志记录**：异常发生时记录日志，便于后续排查问题。
4. **异常分类**：根据业务场景定义不同的异常类型，如业务异常、权限异常、数据库异常等。
5. **全局处理**：在全局异常处理器中统一处理自定义异常，确保返回一致的错误响应。

#### 代码示例
```java
// 抛出异常示例
if (user == null) {
    throw new JeecgBootException("用户不存在", CommonConstant.SC_NOT_FOUND_404);
}

// 处理异常示例
@ExceptionHandler(JeecgBootException.class)
public Result<?> handleJeecgBootException(JeecgBootException e) {
    log.error(e.getMessage(), e);
    addSysLog(e);
    return Result.error(e.getErrCode(), e.getMessage());
}
```

### RESTful 接口统一返回格式

#### 1. 返回数据结构
```java
@Data
@ApiModel(value="接口返回对象", description="接口返回对象")
public class Result implements Serializable {
    private boolean success = true; // 成功标志
    private String message = "";    // 返回处理消息
    private Integer code = 0;       // 返回代码
    private T result;               // 返回数据对象
    private long timestamp = System.currentTimeMillis(); // 时间戳
}
```

#### 2. 成功返回示例
```java
// 返回成功状态
Result.ok();

// 返回成功状态和数据
Result.ok(data);

// 返回成功状态、消息和数据
Result.ok("操作成功", data);
```

#### 3. 错误返回示例
```java
// 返回错误状态和消息
Result.error("操作失败");

// 返回错误状态、错误码和消息
Result.error(500, "服务器内部错误");

// 返回无权限访问结果
Result.noauth("无权限访问");
```

#### 4. 最佳实践
1. **统一返回格式**：所有接口返回 [Result](cci:2://file:///Users/<USER>/Dev/SideWork/heartful-mall/heartful-mall/jeecg-boot-base-core/src/main/java/org/jeecg/common/api/vo/Result.java:17:0-178:1) 对象，确保前端接收到一致的响应结构。
2. **错误码机制**：通过 `code` 字段返回错误码，便于前端根据错误码进行特定处理。
3. **日志记录**：异常发生时记录日志，便于后续排查问题。
4. **时间戳**：返回 `timestamp` 字段，便于调试和追踪请求。
5. **异常处理**：在全局异常处理器中统一处理自定义异常，确保返回一致的错误响应。

#### 5. 代码示例
```java
// 成功示例
@GetMapping("/success")
public Result success() {
    return Result.ok("操作成功");
}

// 失败示例
@GetMapping("/error")
public Result error() {
    return Result.error("操作失败");
}

// 数据返回示例
@GetMapping("/data")
public Result<List> data() {
    List users = userService.list();
    return Result.ok(users);
}
```

### 接口数据规范

1. **返回数据结构**
   - 所有接口返回的数据必须使用 DTO/VO 实体类，禁止直接返回 Map 或其他非结构化数据
   - DTO/VO 实体类应包含清晰的字段注释和类型定义
   - 分页数据统一使用 `PageDTO<T>` 封装，其中 T 为具体的 DTO 类型
   - 每个 DTO 类应提供从 Map 转换为 DTO 的静态方法，确保数据转换的一致性

2. **参数接收规范**
   - 简单参数（如单个字段）可以直接使用 `@RequestParam` 接收
   - 复杂参数（包含多个字段）必须创建 DTO 类接收
   - 更复杂的参数（如包含列表、嵌套对象等）使用实体类配合 `@RequestBody` 接收 JSON 参数
   - 所有 DTO 类应包含清晰的字段注释和类型定义

3. **命名规范**
   - DTO 类命名以 `DTO` 结尾，如 `BusinessPerformanceDTO`
   - VO 类命名以 `VO` 结尾，如 `MemberInfoVO`
   - 分页 DTO 统一命名为 `PageDTO<T>`
   - 方法命名应清晰表达其功能，如 [getBusinessPerformance](cci:1://file:///Users/<USER>/Dev/SideWork/heartful-mall/heartful-mall/jeecg-module-system/jeecg-system-biz/src/main/java/org/jeecg/modules/member/api/AfterMemberController.java:792:4-819:5)

4. **其他要求**
   - 所有 DTO/VO 类必须实现 `Serializable` 接口
   - 所有字段必须添加 `@ApiModelProperty` 注解，描述字段含义
   - 日期字段需添加 `@JsonFormat` 和 `@DateTimeFormat` 注解，统一日期格式
   - 金额字段统一使用 `BigDecimal` 类型，避免精度问题

5. **异常处理**
   - 所有接口必须处理可能出现的异常情况
   - 返回的错误信息应使用统一的错误码和错误信息格式
   - 敏感信息（如密码、手机号等）在日志中必须脱敏处理

### 字典数据配置

- 字典配置SQL模板和示例请参考：`@[docs/业务架构文档/数据库设计/字典数据配置示例.sql]`
- 常见字典类型：
  - 状态字典：`sys_status`
  - 类型字典：`order_type`
  - 操作字典：`order_operation`
  - 结果字典：`order_result`
- 使用说明：
  1. 复制模板SQL
  2. 修改相应的字典编码和名称
  3. 添加所需的字典项
  4. 执行SQL语句
- 注意事项：
  - `dict_code`：必须全局唯一，建议使用小写字母和下划线
  - `item_value`：建议使用数字或简单字符串，避免特殊字符
  - `sort_order`：数字越小越靠前
  - `status`：1=启用，0=禁用
  - `item_color`：可选，格式为#RRGGBB