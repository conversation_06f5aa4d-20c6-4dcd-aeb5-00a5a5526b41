<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>八闽助业集市系统架构图</title>
    <style>
        body {
            font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f8f9fa;
            color: #333;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        h1 {
            text-align: center;
            color: #3c6382;
            margin-bottom: 20px;
        }
        .architecture-diagram {
            width: 100%;
            height: calc(100vh - 100px);
            min-height: 600px;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
            overflow: hidden;
        }
        svg {
            width: 100%;
            height: 100%;
        }
        /* SVG样式 */
        .layer-title {
            font-size: 14px;
            font-weight: bold;
            fill: #333;
        }
        .module-title {
            font-size: 12px;
            fill: #333;
        }
        .layer-rect {
            stroke-width: 1;
        }
        .module-rect {
            stroke-width: 1;
        }
        .arrow {
            stroke-width: 1.5;
            stroke: #666;
            fill: none;
        }
        .arrow-head {
            fill: #666;
        }
        .service-title {
            font-size: 11px;
            fill: #333;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="architecture-diagram">
            <svg viewBox="0 0 1000 650" xmlns="http://www.w3.org/2000/svg">
                <!-- 标题 -->
                <text x="500" y="30" font-size="20" font-weight="bold" text-anchor="middle" fill="#3c6382">八闽助业集市系统架构图</text>

                <!-- 用户交互层 -->
                <g transform="translate(50, 60)">
                    <rect width="900" height="80" rx="4" class="layer-rect" fill="#4b6584" fill-opacity="0.1" stroke="#4b6584"></rect>
                    <text x="10" y="25" class="layer-title" fill="#4b6584">用户交互层</text>
                    
                    <g transform="translate(50, 35)">
                        <rect width="240" height="35" rx="4" class="module-rect" fill="#4b6584" fill-opacity="0.7" stroke="#4b6584"></rect>
                        <text x="120" y="22" class="module-title" fill="white" text-anchor="middle">用户端小程序</text>
                    </g>
                    
                    <g transform="translate(330, 35)">
                        <rect width="240" height="35" rx="4" class="module-rect" fill="#4b6584" fill-opacity="0.7" stroke="#4b6584"></rect>
                        <text x="120" y="22" class="module-title" fill="white" text-anchor="middle">商家端小程序</text>
                    </g>
                    
                    <g transform="translate(610, 35)">
                        <rect width="240" height="35" rx="4" class="module-rect" fill="#4b6584" fill-opacity="0.7" stroke="#4b6584"></rect>
                        <text x="120" y="22" class="module-title" fill="white" text-anchor="middle">系统管理后台PC端</text>
                    </g>
                </g>

                <!-- 网关层 -->
                <g transform="translate(50, 170)">
                    <rect width="900" height="60" rx="4" class="layer-rect" fill="#7ed6df" fill-opacity="0.1" stroke="#7ed6df"></rect>
                    <text x="10" y="25" class="layer-title" fill="#7ed6df">网关层</text>
                    
                    <g transform="translate(250, 20)">
                        <rect width="400" height="30" rx="4" class="module-rect" fill="#7ed6df" fill-opacity="0.7" stroke="#7ed6df"></rect>
                        <text x="200" y="20" class="module-title" fill="white" text-anchor="middle">Spring Cloud Gateway（路由转发、鉴权过滤、限流熔断）</text>
                    </g>
                </g>

                <!-- 业务服务层 -->
                <g transform="translate(50, 250)">
                    <rect width="900" height="80" rx="4" class="layer-rect" fill="#e66767" fill-opacity="0.1" stroke="#e66767"></rect>
                    <text x="10" y="25" class="layer-title" fill="#e66767">业务服务层</text>
                    
                    <g transform="translate(20, 35)">
                        <rect width="160" height="35" rx="4" class="module-rect" fill="#e66767" fill-opacity="0.7" stroke="#e66767"></rect>
                        <text x="80" y="22" class="module-title" fill="white" text-anchor="middle">会员服务</text>
                    </g>
                    
                    <g transform="translate(190, 35)">
                        <rect width="160" height="35" rx="4" class="module-rect" fill="#e66767" fill-opacity="0.7" stroke="#e66767"></rect>
                        <text x="80" y="22" class="module-title" fill="white" text-anchor="middle">订单服务</text>
                    </g>
                    
                    <g transform="translate(360, 35)">
                        <rect width="160" height="35" rx="4" class="module-rect" fill="#e66767" fill-opacity="0.7" stroke="#e66767"></rect>
                        <text x="80" y="22" class="module-title" fill="white" text-anchor="middle">商品服务</text>
                    </g>
                    
                    <g transform="translate(530, 35)">
                        <rect width="160" height="35" rx="4" class="module-rect" fill="#e66767" fill-opacity="0.7" stroke="#e66767"></rect>
                        <text x="80" y="22" class="module-title" fill="white" text-anchor="middle">分佣服务</text>
                    </g>
                    
                    <g transform="translate(700, 35)">
                        <rect width="160" height="35" rx="4" class="module-rect" fill="#e66767" fill-opacity="0.7" stroke="#e66767"></rect>
                        <text x="80" y="22" class="module-title" fill="white" text-anchor="middle">资金账户服务</text>
                    </g>
                </g>

                <!-- 技术架构层 -->
                <g transform="translate(50, 350)">
                    <rect width="900" height="80" rx="4" class="layer-rect" fill="#f5cd79" fill-opacity="0.1" stroke="#f5cd79"></rect>
                    <text x="10" y="25" class="layer-title" fill="#f5cd79">技术架构层</text>
                    
                    <g transform="translate(20, 35)">
                        <rect width="160" height="35" rx="4" class="module-rect" fill="#f5cd79" fill-opacity="0.7" stroke="#f5cd79"></rect>
                        <text x="80" y="22" class="module-title" fill="white" text-anchor="middle">Spring Cloud Alibaba</text>
                    </g>
                    
                    <g transform="translate(190, 35)">
                        <rect width="160" height="35" rx="4" class="module-rect" fill="#f5cd79" fill-opacity="0.7" stroke="#f5cd79"></rect>
                        <text x="80" y="22" class="module-title" fill="white" text-anchor="middle">Apache Shiro + JWT</text>
                    </g>
                    
                    <g transform="translate(360, 35)">
                        <rect width="160" height="35" rx="4" class="module-rect" fill="#f5cd79" fill-opacity="0.7" stroke="#f5cd79"></rect>
                        <text x="80" y="22" class="module-title" fill="white" text-anchor="middle">MybatisPlus + Druid</text>
                    </g>
                    
                    <g transform="translate(530, 35)">
                        <rect width="160" height="35" rx="4" class="module-rect" fill="#f5cd79" fill-opacity="0.7" stroke="#f5cd79"></rect>
                        <text x="80" y="22" class="module-title" fill="white" text-anchor="middle">Redis + RabbitMQ</text>
                    </g>
                    
                    <g transform="translate(700, 35)">
                        <rect width="160" height="35" rx="4" class="module-rect" fill="#f5cd79" fill-opacity="0.7" stroke="#f5cd79"></rect>
                        <text x="80" y="22" class="module-title" fill="white" text-anchor="middle">JimuReport + ECharts</text>
                    </g>
                </g>

                <!-- 数据层 -->
                <g transform="translate(50, 450)">
                    <rect width="900" height="60" rx="4" class="layer-rect" fill="#786fa6" fill-opacity="0.1" stroke="#786fa6"></rect>
                    <text x="10" y="25" class="layer-title" fill="#786fa6">数据层</text>
                    
                    <g transform="translate(100, 20)">
                        <rect width="200" height="30" rx="4" class="module-rect" fill="#786fa6" fill-opacity="0.7" stroke="#786fa6"></rect>
                        <text x="100" y="20" class="module-title" fill="white" text-anchor="middle">MySQL（主库+从库）</text>
                    </g>
                    
                    <g transform="translate(350, 20)">
                        <rect width="200" height="30" rx="4" class="module-rect" fill="#786fa6" fill-opacity="0.7" stroke="#786fa6"></rect>
                        <text x="100" y="20" class="module-title" fill="white" text-anchor="middle">Redis（集群）</text>
                    </g>
                    
                    <g transform="translate(600, 20)">
                        <rect width="200" height="30" rx="4" class="module-rect" fill="#786fa6" fill-opacity="0.7" stroke="#786fa6"></rect>
                        <text x="100" y="20" class="module-title" fill="white" text-anchor="middle">Elasticsearch</text>
                    </g>
                </g>

                <!-- 基础设施层 -->
                <g transform="translate(50, 530)">
                    <rect width="900" height="60" rx="4" class="layer-rect" fill="#63cdda" fill-opacity="0.1" stroke="#63cdda"></rect>
                    <text x="10" y="25" class="layer-title" fill="#63cdda">基础设施层</text>
                    
                    <g transform="translate(100, 20)">
                        <rect width="200" height="30" rx="4" class="module-rect" fill="#63cdda" fill-opacity="0.7" stroke="#63cdda"></rect>
                        <text x="100" y="20" class="module-title" fill="white" text-anchor="middle">Docker/Kubernetes</text>
                    </g>
                    
                    <g transform="translate(350, 20)">
                        <rect width="200" height="30" rx="4" class="module-rect" fill="#63cdda" fill-opacity="0.7" stroke="#63cdda"></rect>
                        <text x="100" y="20" class="module-title" fill="white" text-anchor="middle">Jenkins（CI/CD）</text>
                    </g>
                    
                    <g transform="translate(600, 20)">
                        <rect width="200" height="30" rx="4" class="module-rect" fill="#63cdda" fill-opacity="0.7" stroke="#63cdda"></rect>
                        <text x="100" y="20" class="module-title" fill="white" text-anchor="middle">Nginx（负载均衡）</text>
                    </g>
                </g>

                <!-- 第三方服务 - 水平布局 -->
                <g transform="translate(50, 600)">
                    <rect width="900" height="40" rx="4" class="layer-rect" fill="#cf6a87" fill-opacity="0.1" stroke="#cf6a87"></rect>
                    <text x="10" y="25" class="layer-title" fill="#cf6a87">第三方服务</text>
                    
                    <g transform="translate(100, 5)">
                        <rect width="150" height="30" rx="4" class="module-rect" fill="#cf6a87" fill-opacity="0.7" stroke="#cf6a87"></rect>
                        <text x="75" y="20" class="module-title" fill="white" text-anchor="middle">支付服务</text>
                    </g>
                    
                    <g transform="translate(280, 5)">
                        <rect width="150" height="30" rx="4" class="module-rect" fill="#cf6a87" fill-opacity="0.7" stroke="#cf6a87"></rect>
                        <text x="75" y="20" class="module-title" fill="white" text-anchor="middle">物流服务</text>
                    </g>
                    
                    <g transform="translate(460, 5)">
                        <rect width="150" height="30" rx="4" class="module-rect" fill="#cf6a87" fill-opacity="0.7" stroke="#cf6a87"></rect>
                        <text x="75" y="20" class="module-title" fill="white" text-anchor="middle">短信服务</text>
                    </g>
                    
                    <g transform="translate(640, 5)">
                        <rect width="150" height="30" rx="4" class="module-rect" fill="#cf6a87" fill-opacity="0.7" stroke="#cf6a87"></rect>
                        <text x="75" y="20" class="module-title" fill="white" text-anchor="middle">身份认证</text>
                    </g>
                </g>
                
                <!-- 业务服务层到第三方服务的连接线 -->
                <path d="M500,330 L500,600" class="arrow" marker-end="url(#arrowhead)"></path>

                <!-- 连接线 -->
                <!-- 用户交互层到网关层 -->
                <path d="M170,150 L170,170" class="arrow" marker-end="url(#arrowhead)"></path>
                <path d="M450,150 L450,170" class="arrow" marker-end="url(#arrowhead)"></path>
                <path d="M730,150 L730,170" class="arrow" marker-end="url(#arrowhead)"></path>

                <!-- 网关层到业务服务层 -->
                <path d="M450,230 L450,250" class="arrow" marker-end="url(#arrowhead)"></path>

                <!-- 业务服务层到技术架构层 -->
                <path d="M100,330 L100,350" class="arrow" marker-end="url(#arrowhead)"></path>
                <path d="M270,330 L270,350" class="arrow" marker-end="url(#arrowhead)"></path>
                <path d="M440,330 L440,350" class="arrow" marker-end="url(#arrowhead)"></path>
                <path d="M610,330 L610,350" class="arrow" marker-end="url(#arrowhead)"></path>
                <path d="M780,330 L780,350" class="arrow" marker-end="url(#arrowhead)"></path>

                <!-- 技术架构层到数据层 -->
                <path d="M200,430 L200,450" class="arrow" marker-end="url(#arrowhead)"></path>
                <path d="M450,430 L450,450" class="arrow" marker-end="url(#arrowhead)"></path>
                <path d="M700,430 L700,450" class="arrow" marker-end="url(#arrowhead)"></path>

                <!-- 数据层到基础设施层 -->
                <path d="M200,510 L200,530" class="arrow" marker-end="url(#arrowhead)"></path>
                <path d="M450,510 L450,530" class="arrow" marker-end="url(#arrowhead)"></path>
                <path d="M700,510 L700,530" class="arrow" marker-end="url(#arrowhead)"></path>



                <!-- 箭头定义 -->
                <defs>
                    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                        <polygon points="0 0, 10 3.5, 0 7" class="arrow-head"></polygon>
                    </marker>
                </defs>
            </svg>
        </div>
    </div>
</body>
</html>