-- auto-generated definition
create table order_store_list
(
    id                             varchar(50)                     not null comment '主键ID'
        primary key,
    create_by                      varchar(32)                     null comment '创建人',
    create_time                    datetime                        null comment '创建时间',
    update_by                      varchar(32)                     null comment '修改人',
    update_time                    datetime                        null comment '修改时间',
    year                           int                             null comment '创建年',
    month                          int                             null comment '创建月',
    day                            int                             null comment '创建日',
    del_flag                       varchar(1)     default '0'      null comment '删除状态（0，正常，1已删除）',
    member_list_id                 varchar(50)                     null comment '会员列表id',
    order_no                       varchar(80)                     null comment '订单号',
    order_type                     varchar(10)    default '0'      null comment '订单类型；0：普通订单；1：拼团订单；2：抢购订单；3：自选订单；4：兑换订单；数据字典：order_type；',
    consignee                      varchar(20)                     null comment '收货人',
    contact_number                 varchar(13)                     null comment '联系电话',
    shipping_address               varchar(255)                    null comment '收货地址',
    house_number                   varchar(30)                     null comment '门牌号',
    message                        varchar(500)                    null comment '留言',
    goods_total                    decimal(9, 2)  default 0.00     null comment '商品总价',
    coupon                         decimal(9, 2)  default 0.00     null comment '优惠金额',
    ship_fee                       decimal(9, 2)  default 0.00     null comment '配送金额',
    marketing_discount_coupon_id   varchar(100)                    null comment '优惠券id',
    customary_dues                 decimal(9, 2)  default 0.00     null comment '应付款（支付前标准金额）',
    actual_payment                 decimal(9, 2)  default 0.00     null comment '实付款（支付后标准金额）',
    actually_received_amount       decimal(20, 2) default 0.00     null comment '店铺实收金额：买家实付金额 - 平台佣金10% - 分销佣金',
    is_update_addr                 varchar(1)                      null comment '有无修改地址（0：无修改地址；1：有修改地址）',
    status                         varchar(2)                      null comment '订单状态；0：待付款；1：待发货（已付款、部分发货）；2：待收货（已发货）；3：交易成功；4：交易失败；5：交易完成；',
    pay_time                       datetime                        null comment '付款时间',
    shipments_time                 datetime                        null comment '首次发货时间',
    delivery_time                  datetime                        null comment '确认收货时间',
    child_order                    decimal(3)     default 0        null comment '子订单数量',
    close_type                     varchar(2)                      null comment '关闭类型；0：超时关闭；1：买家关闭；2：卖家关闭；数据字典：oder_close_type；',
    close_explain                  varchar(255)                    null comment '订单关闭原因；超时关闭：原因0：超时未付款；买家关闭：原因1：订单不能按预计时间送达；原因2：操作有误（商品、地址等选错）；原因3：重复下单/误下单；原因4：其他渠道价格更低；原因5：不想买；原因6：商品无货；原因7：其他原因；卖家关闭：原因8：未及时付款；原因9：买家不想买了 ；原因10：买家信息填写错误，重新拍；原因11：恶意买家/同行捣乱；原因12：缺货；原因13：同城见面交易；原因14：其他原因；数据字典：oder_close_explain',
    close_time                     datetime                        null comment '关闭时间',
    promoter                       varchar(50)                     null comment '推广人',
    distribution                   varchar(1)                      null comment '订单配送方式；0：快递；1：自提；2：配送；数据字典：oder_distribution',
    completion_time                datetime                        null comment '订单完成时间',
    logistics_star                 decimal(2)     default 0        null comment '物流星级',
    shipping_star                  decimal(2)     default 0        null comment '发货星级',
    service_star                   decimal(2)     default 0        null comment '服务星级',
    evaluate_time                  datetime                        null comment '评价时间',
    is_evaluate                    varchar(1)                      null comment '是否评价；0：未评价；1：已评价',
    cost_price                     decimal(9, 2)  default 0.00     null comment '成本价（供货价）',
    profit                         decimal(9, 2)  default 0.00     null comment '商品利润',
    distribution_commission        decimal(9, 2)  default 0.00     null comment '分销佣金',
    retained_profits               decimal(9, 2)  default 0.00     null comment '净利润',
    sys_user_id                    varchar(50)                     null comment '店铺id',
    is_sender                      varchar(1)                      null comment '是否部分发货；0：否；1：是',
    all_number_units               decimal(5)     default 0        null comment '商品总件数',
    serial_number                  varchar(50)                     null comment '交易流水号',
    longitude                      decimal(10, 6) default 0.000000 null comment '经度',
    latitude                       decimal(10, 6) default 0.000000 null comment '纬度',
    sys_area_id                    varchar(50)                     null comment '区域id',
    promoter_type                  varchar(1)                      null comment '推广人类型;0:店铺；1：会员；2：平台',
    hftx_serial_number             varchar(50)                     null comment '汇付天下交易流水号',
    balance                        decimal(20, 2) default 0.00     null comment '余额',
    mode_payment                   varchar(1)     default '2'      null comment '支付方式；0：微信；1：支付宝；2：余额或积分',
    pay_welfare_payments           decimal(20, 2) default 0.00     null comment '支付的福利金',
    pay_welfare_payments_price     decimal(20, 2) default 0.00     null comment '支付的福利金价值',
    discount                       varchar(300)                    null comment '折扣Json',
    pay_price                      decimal(20, 2) default 0.00     null comment '支付金额',
    gift_card_total                decimal(20, 2) default 0.00     null comment '礼品卡优惠金额',
    active_id                      varchar(50)                     null comment '活动id',
    refund_json                    varchar(2000)                   null comment '退款json返回日志',
    give_welfare_payments          decimal(20, 2) default 0.00     null comment '赠送的积分',
    discount_settle_json           json                            null comment '折扣券结算结果json',
    pick_up_qr_code                varchar(100)   default ''       null comment '到店自提码',
    pick_up_qr_addr                varchar(500)   default ''       null comment ' 到店自提二维码地址',
    pick_up_verification_status    varchar(1)                      null comment '门店自提核销状态（0=未核销 1=已核销 2=已失效）',
    discount_stacking              varchar(1)     default '0'      not null comment '是否折扣券叠加  0=否 1=是',
    ship_distance                  decimal(9, 2)  default 0.00     null comment '配送距离，公里',
    member_unique_id               bigint                          null comment '唯一自增长id,用于拼接层级路径',
    member_level                   int            default 1        null comment '会员分销层级，第一层级为1，递增',
    member_path                    text                            null comment '会员层级路径',
    platform_commission_amount     decimal(10, 2)                  null comment '平台佣金金额',
    entrepreneur_commission_amount decimal(10, 2)                  null comment '企业家分销佣金金额'
)
    comment '店铺订单表' charset = utf8;

-- auto-generated definition
create table order_store_sub_list
(
    id                      varchar(50)                not null comment '主键ID'
        primary key,
    create_by               varchar(32)                null comment '创建人',
    create_time             datetime                   null comment '创建时间',
    update_by               varchar(32)                null comment '修改人',
    update_time             datetime                   null comment '修改时间',
    year                    int                        null comment '创建年',
    month                   int                        null comment '创建月',
    day                     int                        null comment '创建日',
    del_flag                varchar(1)    default '0'  null comment '删除状态（0，正常，1已删除）',
    member_list_id          varchar(50)                null comment '会员id',
    order_store_list_id     varchar(50)                null comment '店铺订单id',
    sys_user_id             varchar(50)                null comment '店铺id',
    order_no                varchar(80)                null comment '订单ID',
    distribution            varchar(50)                null comment '快递',
    store_template_id       varchar(50)                null comment '店铺运费模板id',
    template_name           varchar(200)               null comment '运费模板名称',
    accounting_rules        varchar(200)               null comment '计费规则',
    charge_mode             varchar(50)                null comment '计费方式',
    amount                  decimal(9)    default 0    null comment '总数',
    ship_fee                decimal(9, 2) default 0.00 null comment '配送金额',
    parent_id               varchar(50)                null comment '父订单id；如果没有父订单，显示：0',
    logistics_company       varchar(100)               null comment '物流公司；0：顺丰速运；1：圆通快递；2：申通快递；3：中通快递；4：韵达快递；5：天天快递；6：中国邮政；7：EMS邮政特快专递；8：德邦快递；对应数据字典：logistics_company；',
    tracking_number         varchar(100)               null comment '快递单号',
    store_address_id_sender varchar(50)                null comment '店铺发货地址id',
    store_address_id_tui    varchar(50)                null comment '店铺商退货地址id',
    logistics_tracking      text                       null comment '物流跟踪信息的json保存（每次查询的时候更新）',
    goods_total             decimal(9, 2) default 0.00 null comment '商品总价',
    customary_dues          decimal(9, 2) default 0.00 null comment '应付款（平台应付款项）',
    actual_payment          decimal(9, 2) default 0.00 null comment '平台实付款项',
    status                  varchar(2)                 null comment '订单状态；0：待付款；1：待发货（已付款、部分发货）；2：待收货（已发货）；3：交易成功；4：交易失败；5：交易完成；'
)
    comment '店铺供应商订单表' charset = utf8;

-- auto-generated definition
create table order_store_good_record
(
    id                             varchar(50)                  not null comment '主键ID'
        primary key,
    create_by                      varchar(32)                  null comment '创建人',
    create_time                    datetime                     null comment '创建时间',
    update_by                      varchar(32)                  null comment '修改人',
    update_time                    datetime                     null comment '修改时间',
    year                           int                          null comment '创建年',
    month                          int                          null comment '创建月',
    day                            int                          null comment '创建日',
    del_flag                       varchar(1)     default '0'   null comment '删除状态（0，正常，1已删除）',
    order_store_sub_list_id        varchar(50)                  null comment '供应商订单id',
    main_picture                   varchar(2000)                null comment '商品主图相对地址（以json的形式存储多张）',
    good_store_list_id             varchar(50)                  null comment '平台商品id（只做对象映射）',
    good_store_specification_id    varchar(50)                  null comment '商品规格id（只做对象映射）',
    good_name                      varchar(255)                 null comment '商品名称',
    specification                  varchar(100)                 null comment '规格名称，按照顺序逗号隔开',
    price                          decimal(9, 2)  default 0.00  null comment '商品销售价格',
    vip_price                      decimal(9, 2)  default 0.00  null comment '商品会员价.',
    supply_price                   decimal(9, 2)  default 0.00  null comment '商品供货价',
    cost_price                     decimal(9, 2)  default 0.00  null comment '商品成本价',
    unit_price                     decimal(9, 2)  default 0.00  null comment '销售单价',
    amount                         decimal(9)     default 0     null comment '销售数量',
    total                          decimal(9, 2)  default 0.00  null comment '总计金额（小计）',
    customary_dues                 decimal(9, 2)  default 0.00  null comment '应付款（支付前标准金额）',
    actual_payment                 decimal(9, 2)  default 0.00  null comment '实付款（支付后标准金额）',
    marketing_discount_coupon_id   varchar(1000)  default ''    null comment '优惠券记录id,多个用逗号分隔',
    weight                         decimal(30, 3) default 0.000 null comment '重量',
    coupon                         decimal(9, 2)  default 0.00  null comment '优惠券优惠金额',
    gift_card_coupon               decimal(9, 2)  default 0.00  null comment '礼品卡优惠金额',
    total_coupon                   decimal(9, 2)  default 0.00  null comment '优惠总金额',
    status                         varchar(1)     default '0'   not null comment '订单商品状态 0=下单成功 1=已发货 2=退款中 3=退款成功 4=换货中 5=换货成功',
    give_welfare_payments          decimal(20, 2) default 0.00  null comment '赠送的积分',
    marketing_discount_coupon_json json                         null comment '折扣券折扣json',
    t_member_id                    varchar(50)                  null comment '分享人id',
    t_quantity                     decimal(6)     default 0     null comment '分享购买的商品数量',
    is_wholesale                   varchar(1)     default '0'   null comment '是否产品批发 0=否 1=是',
    is_selected_products           varchar(1)     default '0'   null comment '是否甄品优选（0=否 1=是）',
    pay_price_type                 varchar(1)     default '0'   null comment '支付价格类型 0=普通会员 1=vip会员（黔行者） 2=黔行者大使',
    share_commission               decimal(9, 2)  default 0.00  null comment '分享佣金',
    product_agency_commission      decimal(9, 2)  default 0.00  null comment '批发代理商佣金',
    product_store_commission       decimal(9, 2)  default 0.00  null comment '批发店铺佣金',
    selected_store_commission      decimal(10, 2) default 0.00  null comment '店铺分佣佣金（甄选优品）',
    platform_commission            decimal(20, 2) default 0.00  null comment '平台佣金（固定10%）',
    order_no                       varchar(80)                  null comment '订单编号'
)
    comment '店铺订单商品表' charset = utf8;

-- auto-generated definition
create table order_store_template
(
    id                      varchar(50)                not null comment '主键ID'
        primary key,
    create_by               varchar(32)                null comment '创建人',
    create_time             datetime                   null comment '创建时间',
    update_by               varchar(32)                null comment '修改人',
    update_time             datetime                   null comment '修改时间',
    year                    int                        null comment '创建年',
    month                   int                        null comment '创建月',
    day                     int                        null comment '创建日',
    del_flag                varchar(1)    default '0'  null comment '删除状态（0，正常，1已删除）',
    order_store_list_id     varchar(50)                null comment '店铺订单id',
    sys_user_id             varchar(50)                null comment '店铺id',
    order_store_sub_list_id varchar(50)                null comment '店铺子订单id',
    store_template_id       varchar(50)                null comment '店铺运费模板id',
    template_name           varchar(200)               null comment '运费模板名称',
    accounting_rules        varchar(200)               null comment '计费规则',
    charge_mode             varchar(50)                null comment '计费方式',
    ship_fee                decimal(9, 2) default 0.00 null comment '配送金额',
    quantity                decimal(5)    default 0    null comment '订单商品数量'
)
    comment '店铺订单运费表' charset = utf8;

-- auto-generated definition
create table order_refund_list
(
    id                                         varchar(36)                 not null comment '主键ID'
        primary key,
    create_by                                  varchar(36)    default ''   null comment '创建人',
    create_time                                datetime                    null comment '创建时间',
    update_by                                  varchar(36)    default ''   null comment '修改人',
    update_time                                datetime                    null comment '修改时间',
    apply_time                                 datetime                    null comment '申请时间',
    year                                       int            default -1   null comment '创建年',
    month                                      int            default -1   null comment '创建月',
    day                                        int            default -1   null comment '创建日',
    del_flag                                   varchar(1)     default '0'  null comment '删除状态（0，正常，1已删除）',
    order_sub_list_id                          varchar(36)    default ''   null comment '子订单id（供应商/包裹）',
    order_list_id                              varchar(50)    default ''   null comment '订单id（店铺订单/平台订单）',
    order_no                                   varchar(50)    default ''   null comment '订单号（店铺订单/平台订单）',
    order_type                                 varchar(11)    default ''   null comment '订单类型；0：普通订单；1：拼团订单；2：抢购订单；3：自选订单；4：兑换订单；数据字典：order_type；',
    order_good_record_id                       varchar(50)    default ''   null comment '订单商品记录id',
    member_id                                  varchar(50)    default ''   null comment '会员id',
    sys_user_id                                varchar(50)    default ''   null comment '店铺用户id/平台供应商用户id',
    refund_type                                varchar(1)     default ''   null comment '退款类型 0=仅退款 1=退货退款 2=换货 关联字典：refund_type',
    refund_reason                              varchar(50)    default ''   null comment '退款原因,关联字典：order_store_refund_reason',
    refund_explain                             varchar(500)   default ''   null comment '后台商家拒绝退款理由',
    remarks                                    varchar(500)   default ''   null comment '售后申请说明',
    status                                     varchar(1)     default '0'  null comment '售后状态 0=待处理 1=待买家退回 2=换货中（等待店铺确认收货） 3=退款中 4=退款成功 5=已拒绝 6=退款关闭 7=换货关闭 8=换货完成。 关联字典：order_refund_status',
    good_main_picture                          varchar(1000)  default ''   null comment '订单商品主图相对地址（以json的形式存储多张）',
    good_list_id                               varchar(50)    default ''   null comment '订单商品id（只做对象映射）',
    good_specification_id                      varchar(50)    default ''   null comment '订单商品规格id（只做对象映射）',
    good_name                                  varchar(100)   default ''   null comment '商品名称',
    good_specification                         varchar(500)   default ''   null comment '规格名称，按照顺序逗号隔开',
    refund_certificate                         text                        null comment '申请退款凭证图片，按照顺序逗号隔开',
    refund_price                               decimal(20, 2) default 0.00 null comment '申请退款金额',
    refund_amount                              decimal(20, 2) default 0.00 null comment '申请退款数量',
    is_platform                                varchar(1)     default ''   null comment '0=店铺  1=平台',
    good_record_total                          decimal(20, 2) default 0.00 null comment '订单商品总计金额（小计）',
    good_record_actual_payment                 decimal(20, 2) default 0.00 null comment '订单商品实付款',
    good_record_coupon                         decimal(20, 2) default 0.00 null comment '订单商品优惠卷优惠金额',
    good_record_gift_card_coupon               decimal(20, 2) default 0.00 null comment '订单商品礼品卡优惠金额',
    good_record_marketing_discount_coupon_id   varchar(500)   default ''   null comment '订单商品优惠券记录id,多个用逗号分隔',
    good_record_total_coupon                   decimal(20, 2) default 0.00 null comment '订单商品总优惠金额',
    good_record_amount                         decimal(20, 2) default 0.00 null comment '订单商品购买数量',
    good_unit_price                            decimal(9, 2)  default 0.00 null comment '订单商品销售单价',
    close_explain                              varchar(1)     default ''   null comment '售后单关闭原因，关联字典 refund_close_explain',
    refused_explain                            varchar(500)   default ''   null comment '商家拒绝原因',
    merchant_consignee_name                    varchar(50)    default ''   null comment '退货/换货商家邮寄信息：商家收件人姓名',
    merchant_consignee_address                 varchar(500)   default ''   null comment '退货/换货商家邮寄信息：商家收件地址',
    merchant_consignee_phone                   varchar(50)    default ''   null comment '退货/换货商家邮寄信息：商家收件手机号',
    merchant_consignee_province_id             varchar(50)    default ''   null comment '退货/换货商家邮寄信息：商家收件地址：省',
    merchant_consignee_city_id                 varchar(50)    default ''   null comment '退货/换货商家邮寄信息：商家收件地址：市',
    merchant_consignee_area_id                 varchar(50)    default ''   null comment '退货/换货商家邮寄信息：商家收件地址：区',
    buyer_logistics_company                    varchar(20)    default ''   null comment '买家寄回物流公司；0：顺丰速运；1：圆通快递；2：申通快递；3：中通快递；4：韵达快递；5：天天快递；6：中国邮政；7：EMS邮政特快专递；8：德邦快递；对应数据字典：logistics_company；',
    buyer_tracking_number                      varchar(100)   default ''   null comment '买家寄回快递单号',
    buyer_logistics_tracking                   json                        null comment '买家寄回物流跟踪信息的json保存（每次查询的时候更新）',
    exchange_good_specification_id             varchar(500)   default ''   null comment '买家换货商品规格id，逗号分隔',
    exchange_good_specification                varchar(500)   default ''   null comment '买家换货规格名称，按照顺序逗号隔开',
    exchange_member_shipping_address           json                        null comment '买家换货：买家收货地址json',
    merchant_logistics_company                 varchar(20)    default ''   null comment '换货时卖家寄回：商家物流公司；0：顺丰速运；1：圆通快递；2：申通快递；3：中通快递；4：韵达快递；5：天天快递；6：中国邮政；7：EMS邮政特快专递；8：德邦快递；对应数据字典：logistics_company；',
    merchant_tracking_number                   varchar(100)   default ''   null comment '换货时卖家寄回：商家快递单号',
    merchant_logistics_tracking                json                        null comment '换货时卖家寄回：商家物流跟踪信息的json保存（每次查询的时候更新）',
    refund_json                                json                        null comment '退款json返回日志',
    actual_refund_balance                      decimal(20, 2) default 0.00 null comment '实际退款余额',
    actual_refund_price                        decimal(20, 2) default 0.00 null comment '实际退款现金（汇付微信）',
    actual_refund_gift_card_balance            decimal(20, 2) default 0.00 null comment '实际退还礼品卡金额',
    actual_refund_discount_welfare_payments    decimal(20, 2) default 0.00 null comment '实际退还抵扣的福利金（专区商品）',
    welfare_payments_price                     decimal(20, 2) default 0.00 null comment '订单商品抵扣福利金价值（专区商品）',
    welfare_payments                           decimal(20, 2) default 0.00 null comment '订单商品抵扣福利金（专区商品）',
    actual_refund_marketing_discount_coupon_id varchar(500)   default ''   null comment '实际退款优惠券记录id',
    balance_receive_time                       datetime                    null comment '余额到账时间',
    huifu_receive_time                         datetime                    null comment '微信到账时间',
    good_record_give_welfare_payments          decimal(20, 2) default 0.00 null comment '商品赠送的积分',
    actual_return_welfare_payments             decimal(20, 2) default 0.00 null comment '实际退还积分',
    refund_channel                             varchar(10)                 null comment '退款渠道  0=微信 1=余额。 全部勾选则逗号分隔，如0,1',
    marketing_certificate_record_id            varchar(36)                 null comment '兑换券记录id',
    tao_refund_id                              varchar(36)                 null comment '1688退款id',
    refund_reason_label                        varchar(100)   default ''   null comment '退款原因文本（1688使用）',
    tao_refund_reason_json                     json                        null comment '淘宝退款原因json'
)
    comment '订单售后表' charset = utf8mb4;






