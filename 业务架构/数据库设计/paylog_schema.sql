-- auto-generated definition
create table pay_balance_log
(
    id               varchar(50)                 not null comment '主键ID'
        primary key,
    create_by        varchar(32)                 null comment '创建人',
    create_time      datetime                    null comment '创建时间',
    update_by        varchar(32)                 null comment '修改人',
    update_time      datetime                    null comment '修改时间',
    year             int                         null comment '创建年',
    month            int                         null comment '创建月',
    day              int                         null comment '创建日',
    del_flag         varchar(1)     default '0'  null comment '删除状态（0，正常，1已删除）',
    pay_log          varchar(2000)               null comment '支付json日志',
    pay_status       varchar(1)     default '0'  null comment '支付状态；0:未支付；1：已支付；2：支付失败；3：已分配',
    back_status      varchar(1)     default '0'  null comment '回调状态；0：未回调；1：已回调',
    back_times       decimal(4)     default 0    null comment '回调次数',
    member_list_id   varchar(50)                 null comment '会员id',
    t_member_id      varchar(65)                 null comment '分享人id',
    defeated_explain varchar(100)                null comment '支付失败原因',
    total_fee        decimal(9, 2)  default 0.00 null comment '总金额',
    serial_number    varchar(50)                 null comment '支付单号',
    pay_model        varchar(1)                  null comment '支付方式；0：微信支付，1：支付宝支付',
    pay_price        decimal(20, 2) default 0.00 null comment '支付金额',
    pay_param        varchar(500)                null comment '支付日志'
)
    comment '余额充值支付日志表' charset = utf8;

-- auto-generated definition
create table pay_order_car_log
(
    id               varchar(50)                 not null comment '主键ID'
        primary key,
    create_by        varchar(32)                 null comment '创建人',
    create_time      datetime                    null comment '创建时间',
    update_by        varchar(32)                 null comment '修改人',
    update_time      datetime                    null comment '修改时间',
    year             int                         null comment '创建年',
    month            int                         null comment '创建月',
    day              int                         null comment '创建日',
    del_flag         varchar(1)     default '0'  null comment '删除状态（0，正常，1已删除）',
    pay_order_log    varchar(2000)               null comment '支付json日志',
    pay_param        varchar(500)                null comment '支付日志',
    pay_status       varchar(1)                  null comment '支付状态；0:未支付；1：已支付；2：支付失败；3：已分配',
    back_status      varchar(1)                  null comment '回调状态；0：未回调；1：已回调',
    back_times       decimal(4)     default 0    null comment '回调次数',
    member_list_id   varchar(50)                 null comment '会员id',
    defeated_explain varchar(100)                null comment '支付失败原因',
    serial_number    varchar(50)                 null comment '交易流水号',
    all_total_price  decimal(20, 2) default 0.00 null comment '交易金额',
    balance          decimal(20, 2) default 0.00 null comment '余额',
    welfare_payments decimal(20, 2) default 0.00 null comment '积分',
    pay_model        varchar(1)     default '0'  null comment '支付方式；0：微信支付，1：支付宝支付',
    pay_price        decimal(20, 2) default 0.00 null comment '支付金额',
    integral         decimal(20, 2) default 0.00 null comment '支付积分数',
    integral_price   decimal(20, 2) default 0.00 null comment '免费积分价值',
    t_member_id      varchar(50)                 null comment '推荐人'
)
    comment '订单支付日志表' charset = utf8;

create index index_member_list_id
    on pay_order_car_log (member_list_id);