-- auto-generated definition
create table store_manage
(
    id                                varchar(50)                       not null comment '主键ID'
        primary key,
    create_by                         varchar(32)                       null comment '创建人',
    create_time                       datetime                          null comment '创建时间',
    update_by                         varchar(32)                       null comment '修改人',
    update_time                       datetime                          null comment '修改时间',
    year                              int                               null comment '创建年',
    month                             int                               null comment '创建月',
    day                               int                               null comment '创建日',
    sys_user_id                       varchar(50)                       null comment '关联店铺用户id，审核通过后创建店铺账号进行关联',
    store_name                        varchar(80)                       null comment '门店名称',
    sub_store_name                    varchar(80)                       null comment '分店名称',
    logo_addr                         varchar(255)                      null comment 'logo图片地址',
    store_picture                     varchar(255)                      null comment '门脸照图片地址',
    sys_area_id                       varchar(50)                       null comment '区域id',
    area_address                      varchar(60)                       null comment '城市区域地址说明',
    store_address                     varchar(255)                      null comment '门店详细地址',
    open_type                         varchar(1)                        null comment '开通类型：0：包年；1：终生；数据字典：store_open_type',
    money                             decimal(6, 2)  default 0.00       null comment '开通费用',
    main_type                         varchar(4)                        null comment '主营分类：1：餐饮；2：教育培训，3：丽人；4：医疗；5：休闲娱乐；6：婚庆；7：生活服务；8：亲子；9：宠物；10：爱车；11：运动健身；12：家装；13：购物；14：k歌；15：电脑支架；16：房产；17：汽车；18：住宿；数据字典：store_main_type',
    next_type                         varchar(4)                        null comment '次营分类：1：餐饮；2：教育培训，3：丽人；4：医疗；5：休闲娱乐；6：结婚；7：生活服务；8：亲子；9：宠物；10：爱车；11：运动健身；12：家装；13：购物；14：k歌；数据字典：store_next_type',
    comprehensive_evaluation          decimal(6, 1)  default 0.0        null comment '综合评分',
    boss_name                         varchar(20)                       null comment '老板姓名（联系人名称）',
    boss_phone                        varchar(15)                       null comment '老板手机（联系人手机号，是登录账号）',
    take_out_phone                    varchar(20)                       null comment '客服电话',
    longitude                         decimal(10, 6) default 0.000000   null comment '经度',
    latitude                          decimal(10, 6) default 0.000000   null comment '纬度',
    start_time                        datetime                          null comment '开通时间',
    end_time                          datetime                          null comment '到期时间，如果是终生就为空',
    status                            varchar(1)                        null comment '状态：0:停用；1：启用',
    attestation_status                varchar(2)                        null comment '认证状态：-1：未认证，0：待审核；1：已认证；2：免认证；3：未通过；4：过期；数据字典：store_attest',
    remark                            varchar(255)                      null comment '备注：未通过原因和其他的一些说明',
    license_for_enterprise            varchar(255)                      null comment '企业营业执照图片地址',
    social_credit_code                varchar(30)                       null comment '统一信用代码',
    id_code                           varchar(35)                       null comment '身份证号码',
    id_picture_z                      varchar(255)                      null comment '身份证正面照片地址',
    id_picture_f                      varchar(255)                      null comment '身份证反面照片地址',
    del_flag                          varchar(1)     default '0'        null comment '删除状态（0，正常，1已删除）',
    agent_type                        varchar(1)                        null comment '经办人类型：0：代办人；1：个人；2：法人；数据库字典：store_agent_type',
    agent_name                        varchar(20)                       null comment '经办人姓名',
    id_hand                           varchar(255)                      null comment '手持身份证照片地址',
    agent_authorization               varchar(255)                      null comment '授权书图片地址',
    pay_status                        varchar(1)                        null comment '支付状态：0：未支付；1：已支付；2：免支付',
    pay_type                          varchar(1)                        null comment '支付类型：0：微信；1：支付宝',
    pay_time                          datetime                          null comment '支付时间',
    grade                             decimal(4, 2)  default 0.00       null comment '评分',
    according_store                   varchar(255)                      null comment '店内照',
    straight                          varchar(2)                        null comment '主体类型：0:个人；1：企业或者个体户；数据字典store_straight',
    service_range                     decimal(10, 2) default 5000.00    null comment '服务距离',
    pick_up_status                    varchar(1)     default '0'        null comment '到店自提状态；0：停用；1:启用',
    pick_up_remark                    varchar(300)                      null comment '自提备注',
    distribution_type                 varchar(1)                        null comment '配送方式；0：快递；1：自提；2：同城配送；数据字典store_distribution_type',
    accounting_rules                  varchar(300)                      null comment '配送计费规则',
    city_distribution_type            varchar(1)                        null comment '同城配送类型：0：商家自配送；1：第三方配送',
    distribution_status               varchar(1)     default '0'        null comment '配送状态状态；0：停用；1:启用',
    pick_up_stop_remark               varchar(300)                      null comment '自提停用说明',
    distribution_stop_remark          varchar(300)                      null comment '配送停用说明',
    welfare_payments                  decimal(9, 2)  default 0.00       null comment '店铺福利金',
    balance                           decimal(9, 2)  default 0.00       null comment '店铺余额',
    close_explain                     varchar(50)                       null comment '停用说明',
    account_frozen                    decimal(9, 2)  default 0.00       null comment '冻结金额（待结算）',
    unusable_frozen                   decimal(9, 2)  default 0.00       null comment '不可用金额',
    pay_param                         varchar(500)                      null comment '支付日志',
    back_status                       varchar(1)                        null comment '回调状态；0：未回调；1：已回调',
    back_times                        decimal(4)     default 0          null comment '回调次数',
    member_list_id                    varchar(50)                       null comment '支付前的用户信息',
    sys_smallcode_id                  varchar(50)                       null comment '二维码表',
    if_view_welfare_payments          varchar(1)     default '0'        null comment '送福利金；0：不显示；1：显示',
    is_view_vip_price                 varchar(1)     default '0'        null comment '店铺会员价；0：停用；1：启用',
    good_audit                        tinyint(1)     default 0          null comment '商品审核：0：需审核 1:免审核',
    store_type                        varchar(30)    default '0'        null comment '店铺类型：0：联盟店；1：独立店',
    is_chain                          tinyint(1)     default 0          null comment '是否连锁：0：否；1：是',
    app_id                            varchar(50)                       null comment '微信的appId',
    app_secret                        varchar(80)                       null comment '微信的：appSecret',
    mch_id                            varchar(50)                       null comment '微信的：mchId',
    partner_key                       varchar(100)                      null comment '微信的：partnerKey',
    business_hours_explain            varchar(150)                      null comment '营业时间说明',
    promoter_type                     varchar(1)     default '2'        null comment '推广人类型;0:店铺；1：会员；2：平台:3:加盟商',
    promoter                          varchar(50)                       null comment '推广人',
    alliance_user_id                  varchar(50)                       null comment '归属加盟商',
    is_open_welfare_payments          varchar(1)     default '0'        null comment '是否开启福利金收款；0：关闭；1：开启',
    store_type_id                     varchar(50)                       null comment '店铺类型id',
    sort                              decimal(6)     default 0          null comment '排序',
    is_recommend                      varchar(1)     default '0'        null comment '是否推荐；0：否；1：是',
    introduce                         text                              null comment '介绍',
    floor_name                        varchar(50)                       null comment '楼层名称',
    money_receiving_code              varchar(255)                      null comment '收款码',
    product_wholesale_status          varchar(1)     default '1'        null comment '是否开启产品批发栏目（0=否 1=是）',
    product_wholesale_title           varchar(200)   default '产品批发' null comment '产品批发栏目名称',
    product_wholesale_only_franchiser varchar(1)     default '1'        null comment '仅经销商可查看数据（0=否 1=是）',
    product_wholesale_only_agency     varchar(1)     default '1'        null comment '仅代理商可查看数据（0=否 1=是）',
    products_selected_status          varchar(1)     default '1'        null comment '是否开启臻选优品栏目（0=否 1=是）',
    products_selected_title           varchar(200)   default '臻选优品' null comment '臻选优品栏目名称',
    products_selected_share_picture   varchar(200)   default ''         null comment '臻选优品商品分享背景图',
    product_wholesale_apply_picture   varchar(150)   default ''         null comment '代理商、经销商申请按钮海报图',
    product_wholesale_share_picture   varchar(50)    default ''         null comment '批发商品分享图',
    product_wholesale_apply_content   text                              null comment '批发商品：申请内容',
    product_franchiser_apply_content  text                              null comment '批发商品: 经销商申请内容',
    products_selected_posts_picture   varchar(200)   default ''         null comment '甄选海报图（分享）',
    apply_franchiser_btn              varchar(1)     default '0'        null comment '经销商申请按钮开关',
    apply_agency_btn                  varchar(1)     default '0'        null comment '代理商申请按钮开关',
    store_source                      int            default 0          null comment '会员来源渠道：0 全惠付，1 黔景有约',
    travel_user_id                    varchar(50)                       null comment '黔景有约用户id',
    store_size_type                   int            default 0          null,
    store_size_t_manage_id            varchar(50)                       null,
    gift_award_ratio                  decimal(10, 2) default 0.00       null,
    order_award_ratio                 decimal(10, 2) default 0.00       null,
    capital_pool_percentage           decimal(20, 2)                    null comment '店铺流水分红比例',
    store_level                       bigint         default 1          null comment '店铺等级',
    total_performance                 decimal(20, 2) default 0.00       null comment '累计业绩'
)
    comment '店铺列表' charset = utf8;

create index index_member_list_id
    on store_manage (member_list_id);

create index index_promoter
    on store_manage (promoter);

create index index_sys_user_id
    on store_manage (sys_user_id);

-- auto-generated definition
create table store_recharge_record
(
    id                 varchar(50)                not null comment '主键ID'
        primary key,
    create_by          varchar(32)                null comment '创建人',
    create_time        datetime                   null comment '创建时间',
    update_by          varchar(32)                null comment '修改人',
    update_time        datetime                   null comment '修改时间',
    year               int                        null comment '创建年',
    month              int                        null comment '创建月',
    day                int                        null comment '创建日',
    del_flag           varchar(1)    default '0'  null comment '删除状态（0，正常，1已删除）',
    store_manage_id    varchar(50)                null comment '店铺管理id',
    pay_type           varchar(2)                 null comment '交易类型；0：订单交易；1：余额提现；2：订单退款；3：福利金赠送；4：余额充值；5：归属店铺奖励；6：渠道销售奖励；7：推荐开店奖励；8：礼包推广奖励；9：福利金推广奖励；10：兑换券核销奖励；11：福利金收款；12：采购礼包归属奖励；13：采购礼包渠道奖励；做成数据字典store_deal_type',
    go_and_come        varchar(1)                 null comment '支付和收入；0：收入；1：支出',
    amount             decimal(9, 2) default 0.00 null comment '交易金额',
    trade_status       varchar(2)                 null comment '交易状态：0：未支付；1：进行中；2：待结算：3：待打款；4：待退款；5：交易完成；6：已退款；7：交易关闭；数据字典：trade_status',
    order_no           varchar(50)                null comment '单号',
    store_bank_card_id varchar(50)                null comment '店铺银行卡id',
    operator           varchar(50)                null comment '操作人',
    remark             varchar(100)               null comment '备注',
    payment            varchar(1)                 null comment '支付方式；0:微信支付；1：支付宝支付',
    pay_param          varchar(500)               null comment '支付日志',
    back_status        varchar(1)    default '0'  null comment '回调状态；0：未回调；1：已回调',
    back_times         decimal(4)    default 0    null comment '回调次数',
    trade_no           varchar(50)                null comment '交易单号',
    trade_type         varchar(1)                 null comment '交易类型；0：订单；1：礼包；2：开店；3：福利金',
    bank_card          varchar(40)                null comment '银行卡号(支付宝账号)',
    bank_name          varchar(40)                null comment '开户行名称',
    cardholder         varchar(20)                null comment '持卡人姓名(真实姓名)'
)
    comment '店铺资金记录表' charset = utf8;

-- auto-generated definition
create table store_account_capital
(
    id              varchar(50)                not null comment '主键ID'
        primary key,
    create_by       varchar(32)                null comment '创建人',
    create_time     datetime                   null comment '创建时间',
    update_by       varchar(32)                null comment '修改人',
    update_time     datetime                   null comment '修改时间',
    year            int                        null comment '创建年',
    month           int                        null comment '创建月',
    day             int                        null comment '创建日',
    del_flag        varchar(1)    default '0'  null comment '删除状态（0，正常，1已删除）',
    store_manage_id varchar(50)                null comment '店铺管理id',
    pay_type        varchar(2)                 null comment '交易类型；0：订单交易；1：余额提现；2：订单退款；3：福利金赠送；4：余额充值；5：归属店铺奖励；6：渠道销售奖励；7：推荐开店奖励；8：礼包推广奖励；9：福利金推广奖励；10：兑换券核销奖励；11：福利金收款；12：采购礼包归属奖励；13：采购礼包渠道奖励；做成数据字典store_deal_type',
    go_and_come     varchar(1)                 null comment '支付和收入；0：收入；1：支出',
    amount          decimal(9, 2) default 0.00 null comment '交易金额',
    order_no        varchar(50)                null comment '单号',
    balance         decimal(9, 2) default 0.00 null comment '账户余额'
)
    comment '店铺资金流水表' charset = utf8;

-- auto-generated definition
create table store_withdraw_deposit
(
    id               varchar(50)                not null comment '主键ID'
        primary key,
    create_by        varchar(32)                null comment '创建人',
    create_time      datetime                   null comment '创建时间',
    update_by        varchar(32)                null comment '修改人',
    update_time      datetime                   null comment '修改时间',
    year             int                        null comment '创建年',
    month            int                        null comment '创建月',
    day              int                        null comment '创建日',
    del_flag         varchar(1)    default '0'  null comment '删除状态（0，正常，1已删除）',
    store_manage_id  varchar(50)                null comment '店铺管理id',
    order_no         varchar(50)                null comment '单号',
    phone            varchar(13)                null comment '手机号',
    money            decimal(9, 2) default 0.00 null comment '提现金额',
    withdrawal_type  varchar(1)                 null comment '提现类型；0：微信；1：支付宝；2：银行卡；',
    service_charge   decimal(4, 2) default 0.00 null comment '手续费',
    amount           decimal(9, 2) default 0.00 null comment '实际金额',
    time_application datetime                   null comment '申请时间',
    status           varchar(1)                 null comment '提现状态；0：待审核；1：待打款；2：已付款；3：无效',
    pay_time         datetime                   null comment '打款时间',
    remark           varchar(100)               null comment '备注',
    close_explain    varchar(100)               null comment '无效原因',
    bank_card        varchar(40)                null comment '银行卡号(支付宝账号)',
    bank_name        varchar(40)                null comment '开户行名称',
    cardholder       varchar(20)                null comment '持卡人姓名(真实姓名)',
    audit_time       datetime                   null comment '审核时间',
    boss_phone       varchar(20)                null comment '老板电话',
    opening_bank     varchar(100)               null comment '开户行分支行'
)
    comment '店铺提现记录表' charset = utf8;