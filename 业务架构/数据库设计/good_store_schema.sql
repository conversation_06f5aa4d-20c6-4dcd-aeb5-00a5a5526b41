-- auto-generated definition
create table good_store_list
(
    id                                    varchar(50)                 not null comment '主键ID'
        primary key,
    create_by                             varchar(32)                 null comment '创建人',
    create_time                           datetime                    null comment '创建时间',
    update_by                             varchar(32)                 null comment '修改人',
    update_time                           datetime                    null comment '修改时间',
    year                                  int                         null comment '创建年',
    month                                 int                         null comment '创建月',
    day                                   int                         null comment '创建日',
    sys_user_id                           varchar(50)                 null comment '店铺用户id',
    del_flag                              varchar(1)     default '0'  null comment '删除状态（0，正常，1已删除）',
    main_picture                          varchar(1500)               null comment '商品主图相对地址',
    good_name                             varchar(255)                null comment '商品名称',
    nick_name                             varchar(255)                null comment '商品别名（默认与商品名称相同）',
    good_store_type_id                    varchar(50)                 null comment '店铺商品分类id',
    good_no                               varchar(100)                null comment '商品编号',
    store_template_id                     varchar(50)                 null comment '运费模板',
    market_price                          varchar(50)                 null comment '商品市场价',
    status                                varchar(1)     default '0'  null comment '状态：0：停用；1：启用',
    good_describe                         varchar(300)                null comment '商品描述',
    good_video                            varchar(255)                null comment '商品视频地址',
    details_goods                         text                        null comment '商品详情图多张以json的形式存储',
    frame_status                          varchar(1)                  null comment '上下架；0：下架；1：上架',
    frame_explain                         varchar(255)                null comment '上下架说明',
    status_explain                        varchar(255)                null comment '状态说明，停用说明',
    specification                         varchar(300)                null comment '规格说明按照json的形式保存',
    is_specification                      varchar(1)                  null comment '有无规格；0：无规格；1：有规格',
    commitment_customers                  varchar(100)                null comment '服务承诺，来自数据字段逗号隔开',
    del_explain                           varchar(255)                null comment '删除原因',
    del_time                              datetime                    null comment '删除时间',
    noutoasiakas_status                   varchar(1)     default '0'  null comment '自提状态：0：未启用；1：启用',
    distribution_status                   varchar(1)     default '0'  null comment '配送状态：0：未启用；1：启用',
    audit_status                          varchar(1)     default '0'  null comment '审核状态：0:草稿；1：待审核；2：审核通过；3：审核不通过',
    audit_explain                         varchar(255)                null comment '审核原因',
    specifications                        text                        null comment '规格信息',
    search_info                           text                        null comment '搜索信息',
    remarks                               varchar(200)                null,
    distribution                          varchar(10)    default '0'  null comment '配送方式；0：快递；1：自提；2: 配送',
    sort                                  decimal(20)    default 0    null comment '排序',
    is_wholesale                          varchar(1)     default '0'  null comment '是否产品批发 0=否 1=是',
    is_selected_products                  varchar(1)     default '0'  null comment '是否甄品优选（0=否 1=是）',
    product_store_commission_rate         decimal(10, 2) default 0.00 null comment '店铺分佣比例（产品批发）',
    product_agency_commission_rate        decimal(10, 2) default 0.00 null comment '产品批发：代理商分佣比例',
    share_commission_rate                 decimal(10, 2) default 0.00 null comment '分享佣金比例',
    selected_store_commission_rate        decimal(10, 2) default 0.00 null comment '店铺分佣比例（甄选优品）',
    product_agency_commission_rate_two    decimal(10, 2) default 0.00 null comment '产品批发：二级代理商分销比例',
    product_agency_commission_rate_myself decimal(10, 2) default 0.00 null comment '产品批发：本人分佣比例',
    share_picture                         varchar(3000)               null comment '分享图',
    store_type                            varchar(36)                 null comment '对应店铺的 store_type 字段',
    total_performance                     decimal(20, 2) default 0.00 null comment '累计业绩'
)
    comment '店铺商品列表' charset = utf8;

-- auto-generated definition
create table good_store_specification
(
    id                    varchar(50)                  not null comment '主键ID'
        primary key,
    create_by             varchar(32)                  null comment '创建人',
    create_time           datetime                     null comment '创建时间',
    update_by             varchar(32)                  null comment '修改人',
    update_time           datetime                     null comment '修改时间',
    year                  int                          null comment '创建年',
    month                 int                          null comment '创建月',
    day                   int                          null comment '创建日',
    del_flag              varchar(1)     default '0'   null comment '删除状态（0，正常，1已删除）',
    good_store_list_id    varchar(50)                  null comment '商品id',
    price                 decimal(9, 2)  default 0.00  null comment '商品销售价格',
    vip_price             decimal(9, 2)  default 0.00  null comment '商品会员价，按照利润比例，根据数据字段设置的比例自动填入',
    ambassador_price      decimal(9, 2)  default 0.00  null comment '黔行者大使价（元）',
    cost_price            decimal(9, 2)  default 0.00  null comment '商品成本价',
    repertory             decimal(9)     default 0     null comment '库存',
    p_commodity_style     varchar(100)                 null comment '一级规格类型名称',
    commodity_style       varchar(100)                 null comment '二级规格类型名称',
    p_specification       varchar(100)                 null comment '一级规格名称',
    specification         varchar(100)                 null comment '规格名称，按照顺序逗号隔开',
    sku_no                varchar(50)                  null comment 'sku编码',
    weight                decimal(7, 3)  default 0.000 null comment '重量',
    sales_volume          decimal(9)     default 0     null comment '销量',
    specification_picture varchar(500)                 null comment '商品规格图',
    min_wholesale_num     decimal(9, 2)  default 2.00  null comment '起批数量',
    remarks               varchar(200)   default ''    null comment '备注',
    total_performance     decimal(20, 2) default 0.00  null comment '累计业绩'
)
    comment '店铺商品规格' charset = utf8;

create index index_good_list_id
    on good_store_specification (good_store_list_id);