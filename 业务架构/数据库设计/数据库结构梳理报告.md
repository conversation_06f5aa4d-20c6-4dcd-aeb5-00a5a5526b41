# 数据库结构梳理报告

## 1. 概述

本报告对"暖心商城"(heartful-mall)系统的数据库结构进行全面梳理，包括主要数据表的功能、字段说明以及表间关联关系。系统采用MySQL数据库，主要分为店铺管理、商品管理、订单管理、会员管理、支付管理和系统管理等模块。

## 2. 数据库模块划分

根据业务功能和表结构特点，数据库可划分为以下主要模块：

### 2.1 店铺管理模块

店铺管理模块主要负责商家店铺的信息管理、认证、结算等功能。

**核心表**：
- `store_manage`: 店铺基本信息表
- `store_type`: 店铺类型表
- `store_label_relation`: 店铺标签关联表
- `store_welfare_payments_gathering`: 店铺福利金汇总表
- `store_account_capital`: 店铺资金账户表

### 2.2 商品管理模块

商品管理模块负责商品信息、分类、规格、库存等管理。

**核心表**：
- `good_store_list`: 店铺商品列表
- `good_store_specification`: 商品规格表
- `good_store_type`: 商品分类表
- `good_store_inventory`: 商品库存表

### 2.3 订单管理模块

订单管理模块处理订单创建、支付、发货、退款等流程。

**核心表**：
- `order_store_list`: 店铺订单表
- `order_store_sub_list`: 店铺供应商订单表
- `order_store_good_record`: 店铺订单商品记录表
- `order_store_ship_fee`: 店铺订单运费表

### 2.4 会员管理模块

会员管理模块负责会员信息、等级、积分、分销关系等管理。

**核心表**：
- `member_list`: 会员列表
- `member_designation`: 会员称号表
- `member_designation_group`: 会员称号组表
- `member_designation_member_list`: 会员称号关联表

### 2.5 支付管理模块

支付管理模块处理各类支付交易和资金流水记录。

**核心表**：
- `pay_balance_log`: 余额充值支付日志表
- `pay_order_car_log`: 订单支付日志表

### 2.6 系统管理模块

系统管理模块包含系统基础配置、用户权限等功能。

**核心表**：
- `sys_dict`: 字典配置主表
- `sys_dict_item`: 字典配置项表
- `sys_user`: 系统用户表
- `sys_role`: 角色表
- `sys_permission`: 权限表

## 3. 核心表结构详解

### 3.1 店铺管理模块

#### 3.1.1 store_manage (店铺基本信息表)

**表功能**：存储店铺的基本信息，包括店铺名称、地址、联系方式、认证状态等。

**主要字段**：
- `id`: 主键
- `sys_user_id`: 关联店铺用户id
- `store_name`: 门店名称
- `sub_store_name`: 分店名称
- `logo_addr`: logo图片地址
- `store_picture`: 门脸照图片地址
- `area_address`: 城市区域地址
- `store_address`: 门店详细地址
- `main_type`: 主营分类
- `boss_name`: 老板姓名
- `boss_phone`: 老板手机
- `take_out_phone`: 客服电话
- `longitude`/`latitude`: 经纬度坐标
- `status`: 状态(0:停用；1：启用)
- `attestation_status`: 认证状态
- `balance`: 店铺余额
- `total_performance`: 累计业绩

### 3.2 商品管理模块

#### 3.2.1 good_store_list (店铺商品列表)

**表功能**：存储店铺商品的基本信息。

**主要字段**：
- `id`: 主键
- `sys_user_id`: 店铺用户id
- `main_picture`: 商品主图
- `good_name`: 商品名称
- `nick_name`: 商品别名
- `good_store_type_id`: 店铺商品分类id
- `good_no`: 商品编号
- `store_template_id`: 运费模板
- `market_price`: 商品市场价
- `status`: 状态
- `good_describe`: 商品描述
- `details_goods`: 商品详情图
- `frame_status`: 上下架状态
- `is_specification`: 有无规格
- `audit_status`: 审核状态
- `is_wholesale`: 是否产品批发
- `is_selected_products`: 是否甄品优选
- `total_performance`: 累计业绩

#### 3.2.2 good_store_specification (商品规格表)

**表功能**：存储商品的规格信息。

**主要字段**：
- `id`: 主键
- `good_store_list_id`: 商品ID
- `price`: 价格
- `vip_price`: VIP价格
- `wholesale_price`: 批发价格
- `specification`: 规格名称
- `sku_no`: sku编码
- `weight`: 重量
- `sales_volume`: 销量
- `specification_picture`: 商品规格图
- `min_wholesale_num`: 起批数量
- `total_performance`: 累计业绩

### 3.3 订单管理模块

#### 3.3.1 order_store_list (店铺订单表)

**表功能**：存储店铺订单的基本信息。

**主要字段**：
- `id`: 主键
- `member_list_id`: 会员列表id
- `order_no`: 订单号
- `order_type`: 订单类型
- `consignee`: 收货人
- `contact_number`: 联系电话
- `shipping_address`: 收货地址
- `message`: 留言
- `goods_total`: 商品总价
- `coupon`: 优惠金额
- `ship_fee`: 配送金额
- `customary_dues`: 应付款
- `actual_payment`: 实付款
- `actually_received_amount`: 店铺实收金额
- `status`: 订单状态
- `pay_time`: 付款时间
- `shipments_time`: 首次发货时间
- `delivery_time`: 确认收货时间
- `completion_time`: 订单完成时间
- `promoter`: 推广人
- `distribution`: 订单配送方式
- `cost_price`: 成本价
- `profit`: 商品利润
- `distribution_commission`: 分销佣金
- `retained_profits`: 净利润
- `sys_user_id`: 店铺id
- `member_path`: 会员层级路径

#### 3.3.2 order_store_sub_list (店铺供应商订单表)

**表功能**：存储店铺供应商订单信息。

**主要字段**：
- `id`: 主键
- `member_list_id`: 会员id
- `order_store_list_id`: 店铺订单id
- `sys_user_id`: 店铺id
- `order_no`: 订单ID
- `distribution`: 快递
- `store_template_id`: 店铺运费模板id
- `template_name`: 运费模板名称
- `accounting_rules`: 计费规则
- `charge_mode`: 计费方式
- `amount`: 总数
- `customary_dues`: 应付款
- `actual_payment`: 平台实付款项
- `status`: 订单状态

### 3.4 会员管理模块

#### 3.4.1 member_list (会员列表)

**表功能**：存储会员的基本信息。

**主要字段**：
- `id`: 主键
- `head_portrait`: 头像
- `phone`: 手机号
- `nick_name`: 会员昵称
- `sex`: 性别
- `area_addr`: 地区
- `member_type`: 会员类型
- `welfare_payments`: 福利金
- `balance`: 余额
- `is_open_store`: 是否开店
- `status`: 状态
- `openid`: 微信的openid
- `sys_user_id`: 归属店铺id
- `promoter_type`: 推广人类型
- `promoter`: 店铺或者会员id
- `total_commission`: 累计佣金
- `have_withdrawal`: 已提现金额
- `account_frozen`: 冻结金额
- `growth_value`: 成长值
- `integral`: 积分
- `unique_id`: 唯一自增长id
- `member_level`: 会员分销层级
- `member_path`: 会员层级路径

### 3.5 支付管理模块

#### 3.5.1 pay_balance_log (余额充值支付日志表)

**表功能**：记录余额充值的支付日志。

**主要字段**：
- `id`: 主键
- `pay_log`: 支付json日志
- `pay_status`: 支付状态
- `back_status`: 回调状态
- `back_times`: 回调次数
- `member_list_id`: 会员id
- `t_member_id`: 分享人id
- `defeated_explain`: 支付失败原因
- `total_fee`: 总金额
- `serial_number`: 支付单号
- `pay_model`: 支付方式
- `pay_price`: 支付金额
- `pay_param`: 支付日志

#### 3.5.2 pay_order_car_log (订单支付日志表)

**表功能**：记录订单支付的日志。

**主要字段**：
- `id`: 主键
- `pay_order_log`: 支付json日志
- `pay_param`: 支付日志
- `pay_status`: 支付状态
- `back_status`: 回调状态
- `back_times`: 回调次数
- `member_list_id`: 会员id
- `defeated_explain`: 支付失败原因
- `serial_number`: 交易流水号
- `all_total_price`: 交易金额

### 3.6 系统管理模块

#### 3.6.1 sys_dict (字典配置主表)

**表功能**：存储系统字典配置信息。

**主要字段**：
- `id`: 主键
- `dict_name`: 字典名称
- `dict_code`: 字典编码
- `description`: 描述
- `del_flag`: 删除状态
- `type`: 字典类型

#### 3.6.2 sys_dict_item (字典配置项表)

**表功能**：存储字典配置项信息。

**主要字段**：
- `id`: 主键
- `dict_id`: 字典id
- `item_text`: 字典项文本
- `item_value`: 字典项值
- `item_color`: 字典项颜色
- `description`: 描述
- `sort_order`: 排序
- `status`: 状态

## 4. 表间关联关系

### 4.1 店铺与商品关系

- `store_manage.sys_user_id` → `good_store_list.sys_user_id`: 一个店铺可以有多个商品

### 4.2 商品与规格关系

- `good_store_list.id` → `good_store_specification.good_store_list_id`: 一个商品可以有多个规格

### 4.3 订单与会员关系

- `member_list.id` → `order_store_list.member_list_id`: 一个会员可以有多个订单

### 4.4 订单与店铺关系

- `store_manage.sys_user_id` → `order_store_list.sys_user_id`: 一个店铺可以有多个订单

### 4.5 订单与子订单关系

- `order_store_list.id` → `order_store_sub_list.order_store_list_id`: 一个订单可以有多个子订单

### 4.6 订单与商品记录关系

- `order_store_sub_list.id` → `order_store_good_record.order_store_sub_list_id`: 一个子订单可以有多个商品记录

### 4.7 会员与推广关系

- `member_list.id` → `member_list.promoter`: 会员之间形成推广关系
- `member_list.member_path`: 存储会员的层级路径，用于计算分销关系

### 4.8 字典与字典项关系

- `sys_dict.id` → `sys_dict_item.dict_id`: 一个字典可以有多个字典项

## 5. 业务流程与数据流转

### 5.1 店铺开设流程

1. 创建 `store_manage` 记录，设置基本信息
2. 审核通过后，创建 `sys_user` 账号并关联到店铺
3. 更新 `store_manage.attestation_status` 为已认证状态

### 5.2 商品上架流程

1. 创建 `good_store_list` 记录，设置商品基本信息
2. 如有规格，创建 `good_store_specification` 记录
3. 提交审核，审核通过后更新 `good_store_list.audit_status` 和 `good_store_list.frame_status`

### 5.3 订单处理流程

1. 创建 `order_store_list` 记录，设置订单基本信息
2. 创建 `order_store_sub_list` 子订单记录
3. 创建 `order_store_good_record` 商品记录
4. 创建 `pay_order_car_log` 支付日志
5. 支付成功后，更新订单状态
6. 发货后，更新订单状态
7. 确认收货后，更新订单状态，计算分销佣金
8. 完成订单后，更新店铺业绩和会员积分

### 5.4 会员分销流程

1. 会员注册时，记录推广人信息 `member_list.promoter`
2. 生成会员层级路径 `member_list.member_path`
3. 会员下单时，根据层级路径计算各级分销佣金
4. 订单完成后，更新各级推广人的佣金账户

## 6. 数据库设计特点

### 6.1 通用字段设计

系统中的大多数表都包含以下通用字段：
- `id`: 主键，使用UUID或雪花算法生成
- `create_by`/`create_time`: 创建人和创建时间
- `update_by`/`update_time`: 修改人和修改时间
- `year`/`month`/`day`: 创建的年月日，用于快速查询
- `del_flag`: 逻辑删除标志

### 6.2 业绩统计设计

系统采用多层次的业绩统计机制：
- `store_manage.total_performance`: 店铺总业绩
- `good_store_list.total_performance`: 商品总业绩
- `good_store_specification.total_performance`: 规格总业绩
- `order_store_list.actually_received_amount`: 订单实收金额，用于计算业绩

### 6.3 分销体系设计

系统实现了多级分销体系：
- `member_list.unique_id`: 会员唯一标识
- `member_list.member_level`: 会员分销层级
- `member_list.member_path`: 会员层级路径，存储完整的推广链
- `order_store_list.member_path`: 订单中记录会员路径，用于计算分销佣金

### 6.4 状态流转设计

系统中的各类状态都采用数字编码，并通过字典表进行解释：
- `store_manage.attestation_status`: 店铺认证状态
- `good_store_list.audit_status`: 商品审核状态
- `order_store_list.status`: 订单状态
- `pay_balance_log.pay_status`: 支付状态

## 7. 数据库优化建议

### 7.1 索引优化

建议在以下字段上添加索引：
- `store_manage.sys_user_id`
- `good_store_list.sys_user_id`
- `order_store_list.member_list_id`
- `order_store_list.sys_user_id`
- `order_store_list.order_no`
- `member_list.phone`
- `member_list.openid`
- `member_list.promoter`

### 7.2 表结构优化

1. 考虑将大文本字段（如JSON数据）单独存储，减轻主表负担
2. 对于频繁查询但不常更新的数据，考虑使用物化视图或预计算表
3. 对于历史订单数据，考虑按时间分区或归档处理

### 7.3 查询优化

1. 对于复杂的分销业绩查询，考虑使用存储过程或预计算
2. 对于热点数据（如商品信息、订单状态），考虑使用Redis缓存
3. 对于统计类查询，考虑使用定时任务预计算，减轻实时查询压力

## 8. 总结

"暖心商城"系统的数据库设计整体结构清晰，功能模块划分合理，能够支持电商平台的核心业务流程。系统特别在会员分销、业绩统计、店铺管理等方面有较为完善的设计。

数据库设计体现了以下特点：
1. **模块化设计**：按业务功能划分表结构，便于维护和扩展
2. **通用字段统一**：统一的字段命名和类型设计，提高了代码复用性
3. **分销体系完善**：通过会员路径设计，支持多级分销关系追踪
4. **状态流转清晰**：各业务环节的状态定义明确，便于流程控制
5. **业绩统计全面**：多层次的业绩统计机制，满足不同维度的统计需求

未来可以考虑在数据库性能优化、分库分表、缓存策略等方面进行进一步完善，以支持系统的持续发展和业务规模扩大。
