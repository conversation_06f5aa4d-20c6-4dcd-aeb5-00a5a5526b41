-- 字典配置SQL模板
-- 说明：id 使用雪花算法生成或去除 `-` 的 UUID 字符串

-- 生成字典ID
SET @dict_id = REPLACE(UUID(), '-', '');

-- 添加字典类型
INSERT INTO `sys_dict` (
    `id`, `dict_code`, `dict_name`, `description`, `del_flag`, 
    `create_by`, `create_time`, `update_by`, `update_time`
) VALUES (
    @dict_id, 'your_dict_code', '字典名称', '字典描述', '0',
    'admin', NOW(), 'admin', NOW()
);

-- 生成字典项ID
SET @item_id_1 = REPLACE(UUID(), '-', '');
SET @item_id_2 = REPLACE(UUID(), '-', '');

-- 添加字典项
INSERT INTO `sys_dict_item` (
    `id`, `dict_id`, `item_text`, `item_value`, `item_color`, 
    `description`, `sort_order`, `status`, `create_by`, 
    `create_time`, `update_by`, `update_time`
) VALUES (
    @item_id_1, @dict_id, '选项文本1', '选项值1', '#000000',
    '选项描述1', 1, 1, 'admin', NOW(), 'admin', NOW()
), (
    @item_id_2, @dict_id, '选项文本2', '选项值2', '#000000',
    '选项描述2', 2, 1, 'admin', NOW(), 'admin', NOW()
);

-- 字典配置注意事项
-- dict_code：必须全局唯一，建议使用小写字母和下划线
-- item_value：建议使用数字或简单字符串，避免特殊字符
-- sort_order：数字越小越靠前
-- status：1=启用，0=禁用
-- item_color：可选，格式为#RRGGBB

-- 示例：添加状态字典
-- 生成字典ID
SET @dict_id = REPLACE(UUID(), '-', '');

-- 添加状态字典
INSERT INTO `sys_dict` (
    `id`, `dict_code`, `dict_name`, `description`, `del_flag`, 
    `create_by`, `create_time`, `update_by`, `update_time`
) VALUES (
    @dict_id, 'order_status', '订单状态', '订单状态字典', '0',
    'admin', NOW(), 'admin', NOW()
);

-- 生成字典项ID
SET @item_id_1 = REPLACE(UUID(), '-', '');
SET @item_id_2 = REPLACE(UUID(), '-', '');
SET @item_id_3 = REPLACE(UUID(), '-', '');

-- 添加状态字典项
INSERT INTO `sys_dict_item` (
    `id`, `dict_id`, `item_text`, `item_value`, `item_color`, 
    `description`, `sort_order`, `status`, `create_by`, 
    `create_time`, `update_by`, `update_time`
) VALUES (
    @item_id_1, @dict_id, '待支付', '0', '#FF0000',
    '订单待支付状态', 1, 1, 'admin', NOW(), 'admin', NOW()
), (
    @item_id_2, @dict_id, '已支付', '1', '#00FF00',
    '订单已支付状态', 2, 1, 'admin', NOW(), 'admin', NOW()
), (
    @item_id_3, @dict_id, '已取消', '2', '#FF00FF',
    '订单已取消状态', 3, 1, 'admin', NOW(), 'admin', NOW()
);