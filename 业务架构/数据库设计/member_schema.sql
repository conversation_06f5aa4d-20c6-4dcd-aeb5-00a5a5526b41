-- auto-generated definition
create table member_list
(
    id                        varchar(50) charset utf8                not null comment '主键ID'
        primary key,
    create_by                 varchar(32) charset utf8                null comment '创建人',
    create_time               datetime                                null comment '创建时间',
    update_by                 varchar(32) charset utf8                null comment '修改人',
    update_time               datetime                                null comment '修改时间',
    year                      int                                     null comment '创建年',
    month                     int                                     null comment '创建月',
    day                       int                                     null comment '创建日',
    del_flag                  varchar(1) charset utf8 default '0'     null comment '删除状态（0，正常，1已删除）',
    head_portrait             varchar(255) charset utf8               null comment '头像绝对地址',
    phone                     varchar(13) charset utf8                null comment '手机号',
    nick_name                 varchar(100) collate utf8mb4_general_ci null comment '会员昵称',
    sex                       varchar(10) charset utf8                null comment '性别：0：未知；1：男；2：女；数据字典sex',
    area_addr                 varchar(150) charset utf8               null comment '地区',
    member_type               varchar(1) charset utf8 default '0'     null comment '会员类型：0：普通客户；1：vip会员（暂不启用）2：企业家；数据字典：member_type',
    welfare_payments          decimal(20, 2)          default 0.00    null comment '福利金',
    balance                   decimal(20, 2)          default 0.00    null comment '余额',
    is_open_store             varchar(1) charset utf8                 null comment '是否开店；0：否；1：是',
    vip_time                  datetime                                null comment '加入vip的时间',
    status                    varchar(1) charset utf8                 null comment '状态：0：停用：1：启用',
    stop_remark               varchar(255) charset utf8               null comment '停用说明',
    openid                    varchar(50) charset utf8                null comment '微信的openid',
    session_key               varchar(50) charset utf8                null comment '微信使用',
    sys_user_id               varchar(50) charset utf8                null comment '归属店铺id',
    promoter_type             varchar(1) charset utf8 default '2'     null comment '推广人类型;0:店铺（暂不启用）；1：会员；2：平台',
    promoter                  varchar(50) charset utf8                null comment '店铺或者会员id',
    total_commission          decimal(20, 2)          default 0.00    null comment '累计佣金(总的佣金：一级分销佣金+企业家佣金)',
    have_withdrawal           decimal(20, 2)          default 0.00    null comment '已提现金额',
    account_frozen            decimal(20, 2)          default 0.00    null comment '冻结金额',
    share_times               decimal(4)              default 5       null comment '分享次数',
    sys_smallcode_id          varchar(50) charset utf8                null comment '二维码表',
    qrcode_addr               varchar(100)                            null comment '用户二维码',
    welfare_payments_frozen   decimal(20, 2)          default 0.00    null comment '冻结福利金',
    unusable_frozen           decimal(20, 2)          default 0.00    null comment '不可用金额',
    remark                    varchar(150)                            null comment '备注',
    welfare_payments_unusable decimal(20, 2)          default 0.00    null comment '不可用福利金',
    member_grade_id           varchar(50)                             null comment '会员等级id',
    growth_value              decimal(10, 2)          default 0.00    null comment '成长值',
    app_openid                varchar(50)                             null comment 'app登录的openid',
    union_id                  varchar(50)                             null comment '公共id',
    transaction_password      varchar(100)                            null comment '交易密码',
    integral                  decimal(20, 2)          default 0.00    null comment '积分',
    sign_count                decimal(9)              default 0       null comment '连续签到次数',
    fourth_integral           decimal(20, 2)          default 0.00    null comment '第四积分',
    account_number            varchar(50)                             null comment '账号',
    password                  varchar(200)                            null comment '密码',
    promotion_code            varchar(10)                             null comment '推广码',
    version                   bigint                  default 0       null comment '乐观锁',
    member_source             int                     default 0       null comment '会员来源渠道 0=八闽助业集市',
    mail                      varchar(50)                             null comment '邮箱',
    gz_openid                 varchar(50)                             null comment '微信公众号openid',
    is_recommend_status       int                     default 0       null comment '地推资格：0 没有 1 有',
    unique_id                 bigint auto_increment comment '唯一自增长id,用于拼接层级路径',
    member_level              int                     default 1       null comment '会员分销层级，第一层级为1，递增',
    member_path               text                                    null comment '会员层级路径',
    is_love_ambassador        int                     default 0       null comment '是否助梦家 0=否 1=是',
    total_recharge_balance    decimal(20, 2)          default 0.00    null comment '累计充值金额',
    registration_source       varchar(10)                             null comment '注册来源 0=小程序注册 1=后台添加',
    is_employee               varchar(10)             default '0'     null comment '是否员工 0否1是',
    bind_time                 datetime                                null comment '绑定上下级关系时间',
    first_level_commission    decimal(10, 2)          default 0.00    null comment '一级分销佣金',
    entrepreneur_commission   decimal(10, 2)          default 0.00    null comment '企业家佣金（店铺给企业家的佣金）',
    constraint member_list_pk
        unique (unique_id)
)
    comment '用户端会员列表' charset = utf8mb4;

create index index_account_number
    on member_list (account_number);

create index index_del_flag
    on member_list (del_flag);

create index index_member_grade_id
    on member_list (member_grade_id);

create index index_openid
    on member_list (openid);

create index index_phone
    on member_list (phone);

create index index_promoter
    on member_list (promoter);

create index index_sys_user_id
    on member_list (sys_user_id);

create index index_union_id
    on member_list (union_id);

-- auto-generated definition
create table member_account_capital
(
    id             varchar(50)                 not null comment '主键ID',
    create_by      varchar(32)                 null comment '创建人',
    create_time    datetime                    not null comment '创建时间',
    update_by      varchar(32)                 null comment '修改人',
    update_time    datetime                    null comment '修改时间',
    year           int                         null comment '创建年',
    month          int                         null comment '创建月',
    day            int                         null comment '创建日',
    del_flag       varchar(1)     default '0'  null comment '删除状态（0，正常，1已删除）',
    member_list_id varchar(50)                 null comment '会员id',
    pay_type       varchar(2)                  null comment '交易类型；0：订单交易；1：余额提现；2：订单退款；3：分销奖励；4：推荐开店奖励；5：礼包推广奖励；6：福利金推广奖励；7：兑换券推广奖励；8：称号分红；9：采购礼包直推奖励；10：采购礼包间推奖励，11：提现退款；12：积分挂卖；13：拼券付款；14：拼券失败退款；15：抢券付款；16：兑换券购买；做成数据字典member_deal_type',
    go_and_come    varchar(1)                  null comment '支付和收入；0：收入；1：支出',
    amount         decimal(20, 2) default 0.00 null comment '交易金额',
    order_no       varchar(50)                 null comment '单号',
    balance        decimal(20, 2) default 0.00 null comment '账户余额',
    member_source  int            default 0    null comment '会员来源渠道：0=八闽助业集市',
    primary key (id, create_time)
)
    comment '会员资金流水表' charset = utf8;

create index index_del_flag
    on member_account_capital (del_flag);

create index index_go_and_come
    on member_account_capital (go_and_come);

create index index_member_list_id
    on member_account_capital (member_list_id);

-- auto-generated definition
create table member_recharge_record
(
    id                         varchar(50)                 not null comment '主键ID'
        primary key,
    create_by                  varchar(32)                 null comment '创建人',
    create_time                datetime                    null comment '创建时间',
    update_by                  varchar(32)                 null comment '修改人',
    update_time                datetime                    null comment '修改时间',
    year                       int                         null comment '创建年',
    month                      int                         null comment '创建月',
    day                        int                         null comment '创建日',
    del_flag                   varchar(1)     default '0'  null comment '删除状态（0，正常，1已删除）',
    member_list_id             varchar(50)                 null comment '会员id',
    pay_type                   varchar(2)                  null comment '交易类型；0：订单交易；1：余额提现；2：订单退款；3：分销奖励；4：推荐开店奖励；5：礼包推广奖励；6：福利金推广奖励；7：兑换券推广奖励；8：称号分红；9：采购礼包直推奖励；10：采购礼包间推奖励，11：免单专区,12:回购；做成数据字典member_deal_type',
    go_and_come                varchar(1)                  null comment '支付和收入；0：收入；1：支出',
    amount                     decimal(20, 2) default 0.00 null comment '交易金额',
    trade_status               varchar(2)                  null comment '交易状态：0：未支付；1：进行中；2：待结算：3：待打款；4：待退款；5：交易完成；6：已退款；7：交易关闭；数据字典：trade_status',
    order_no                   varchar(50)                 null comment '单号',
    member_bank_card_id        varchar(50)                 null comment '会员银行卡id',
    operator                   varchar(50)                 null comment '操作人',
    remark                     varchar(100)                null comment '备注',
    payment                    varchar(1)                  null comment '支付方式；0:微信支付；1：支付宝支付',
    trade_no                   varchar(50)                 null comment '交易单号',
    trade_type                 varchar(1)                  null comment '交易类型；0：订单；1：礼包',
    member_level               varchar(1)                  null comment '成员级别；1：一级；2：二级',
    member_withdraw_deposit_id varchar(50)                 null comment '会员提现审批id',
    t_member_list_id           varchar(50)                 null comment '推广会员id'
)
    comment '会员资金记录表' charset = utf8;

create index index_member_bank_card_id
    on member_recharge_record (member_bank_card_id);

create index index_member_list_id
    on member_recharge_record (member_list_id);

create index index_member_withdraw_deposit_id
    on member_recharge_record (member_withdraw_deposit_id);

create index index_t_member_list_id
    on member_recharge_record (t_member_list_id);

-- auto-generated definition
create table member_withdraw_deposit
(
    id                  varchar(50)                 not null comment '主键ID'
        primary key,
    create_by           varchar(32)                 null comment '创建人',
    create_time         datetime                    null comment '创建时间',
    update_by           varchar(32)                 null comment '修改人',
    update_time         datetime                    null comment '修改时间',
    year                int                         null comment '创建年',
    month               int                         null comment '创建月',
    day                 int                         null comment '创建日',
    del_flag            varchar(1)     default '0'  null comment '删除状态（0，正常，1已删除）',
    member_list_id      varchar(50)                 null comment '会员id',
    order_no            varchar(50)                 null comment '单号',
    phone               varchar(13)                 null comment '手机号',
    money               decimal(20, 2) default 0.00 null comment '提现金额',
    withdrawal_type     varchar(1)                  null comment '提现类型；0：微信；1：支付宝;2:银行卡',
    service_charge      decimal(20, 2) default 0.00 null comment '手续费',
    amount              decimal(20, 2) default 0.00 null comment '实际金额',
    time_application    datetime                    null comment '申请时间',
    status              varchar(1)                  null comment '状态；0：待审核；1：待打款；2：已付款；3：无效',
    pay_time            datetime                    null comment '打款时间',
    remark              varchar(100)                null comment '备注',
    close_explain       varchar(100)                null comment '无效原因',
    bank_card           varchar(40)                 null comment '银行卡号(支付宝账号)',
    bank_name           varchar(40)                 null comment '开户行名称',
    cardholder          varchar(100)                null comment '持卡人姓名(真实姓名)',
    audit_time          datetime                    null comment '审核时间',
    receipt_voucher     varchar(500)                null comment '收款凭证',
    process_information varchar(1000)               null comment '处理信息进度json',
    opening_bank        varchar(100)                null comment '开户分支行',
    identity_number     varchar(50)                 null comment '身份证号'
)
    comment '会员提现记录表' charset = utf8;

create index index_member_list_id
    on member_withdraw_deposit (member_list_id);