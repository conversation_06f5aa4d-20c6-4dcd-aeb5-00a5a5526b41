# 交易关闭状态订单详情页面优化

## 问题描述

交易关闭状态的订单详情页面中缺少了两个重要信息的展示：
1. 订单关闭类型（closeType_dictText）："交易取消方向用户"
2. 订单关闭原因（closeExplain）："包裹超时，请重新购买"

## 实现方案

采用了"方案二：订单信息区域整合型"，将关闭类型和关闭原因添加到订单信息区域，与其他订单信息保持一致的展示风格。

## 修改内容

1. 在订单信息区域的"交易关闭"时间下方，添加了两个新的信息行：
   - 关闭类型：显示 `info.closeType_dictText` 字段
   - 关闭原因：显示 `info.closeExplain` 字段

2. 使用 `v-if="judgeTime(5) && info.closeType_dictText"` 和 `v-if="judgeTime(5) && info.closeExplain"` 条件，确保只有在交易关闭状态且有相应数据时才显示这些字段

## 修改文件

- 文件路径：`pages/orderDetail/orderDetail.vue`
- 修改位置：订单信息区域（约第272-295行）

## 效果预期

修改后，交易关闭状态的订单详情页面将在订单信息区域显示以下内容：

```
订单信息
包裹编号     202504131652313539229
收货方式                     快递
交易关闭     2025-04-13 16:54:30
关闭类型     交易取消方向用户
关闭原因     包裹超时，请重新购买
```

这样用户可以清楚地了解订单关闭的具体原因和类型，提升用户体验。

## 备注

此优化已在2023年4月13日实现，如需进一步调整样式或布局，可以在后续迭代中进行。
