# 佣金明细页面状态显示方案

## 需求背景

在佣金明细页面的"全部"选项卡下，虽然已经通过颜色区分了不同状态的佣金（待结算为黄褐色，交易完成为红色），但用户无法直观地看出具体的资金状态。需要添加 `tradeStatus_dictText` 字段来明确显示资金状态。

## 方案一：在金额旁添加状态标签

### 设计思路
在金额的左侧或右侧添加一个小型状态标签，使用 `tradeStatus_dictText` 显示状态文本。

### 视觉效果
```
分销奖励                      [待结算] +1.45
2025-04-13 14:31

分销奖励                      [已完成] +5.8
2025-04-13 14:08
```

### 优点
1. 直观明了，状态和金额在同一视线范围内
2. 不需要大幅改变现有布局
3. 可以使用与金额相同的颜色，保持视觉一致性

### 缺点
1. 可能会使右侧区域显得有些拥挤

## 方案二：在时间下方添加状态文本

### 设计思路
在创建时间下方添加一行小字，显示 `tradeStatus_dictText`。

### 视觉效果
```
分销奖励                        +1.45
2025-04-13 14:31
待结算

分销奖励                        +5.8
2025-04-13 14:08
交易完成
```

### 优点
1. 不影响现有元素的布局
2. 信息层次清晰，状态作为辅助信息显示
3. 左侧区域有足够空间容纳额外文本

### 缺点
1. 可能会增加每个项目的高度
2. 状态文本与金额不在同一视线范围内

## 方案三：使用状态图标 + 悬停文本

### 设计思路
在金额前添加一个状态图标（如圆点、小图标等），图标颜色与金额颜色一致，悬停时显示完整状态文本。

### 视觉效果
```
分销奖励                      ● +1.45
2025-04-13 14:31

分销奖励                      ● +5.8
2025-04-13 14:08
```
(黄色圆点表示待结算，红色圆点表示交易完成)

### 优点
1. 视觉简洁，不占用太多空间
2. 颜色编码与金额保持一致，增强视觉关联
3. 不改变现有布局结构

### 缺点
1. 状态信息不够直观，需要用户理解颜色含义
2. 移动端悬停效果实现可能有限制

## 方案四：在左侧添加状态标签

### 设计思路
在左侧的支付类型上方或下方添加一个状态标签，使用小字号和对应颜色显示 `tradeStatus_dictText`。

### 视觉效果
```
分销奖励 [待结算]              +1.45
2025-04-13 14:31

分销奖励 [交易完成]            +5.8
2025-04-13 14:08
```

### 优点
1. 状态信息与支付类型在一起，逻辑上更合理
2. 左侧区域通常有更多空间
3. 可以使用与金额相同的颜色，保持视觉一致性

### 缺点
1. 可能会使左侧文本显得有些拥挤
2. 需要调整现有文本的布局

## 实施建议

根据不同方案的优缺点分析，建议优先考虑方案二或方案四：

- 如果希望保持现有布局结构不变，可以选择方案二
- 如果希望状态信息更加突出，可以选择方案四

无论选择哪种方案，都建议：

1. 状态文本使用与金额相同的颜色，保持视觉一致性
2. 使用适当的字号和间距，确保信息层次清晰
3. 考虑在不同屏幕尺寸下的显示效果
