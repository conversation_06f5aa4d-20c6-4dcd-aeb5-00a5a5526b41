# myPackage 组件重构方案

## 1. 问题分析

当前 `pages/myPackage/myPackage.vue` 文件存在以下问题：

- 代码行数高达 1566 行，导致维护困难
- 业务逻辑、UI 展示和数据处理混杂在一起
- 组件职责不清晰，功能过于集中
- 缺乏模块化设计，代码复用性差
- 状态管理复杂，数据流转不清晰

## 2. 重构目标

- 将大型组件拆分为多个小型、功能单一的子组件
- 使用 Composition API 重构，提高代码可读性和可维护性
- 严格分离业务逻辑、UI 展示和数据处理
- 建立清晰的数据流转模型
- 提高代码复用性和可测试性
- 优化性能，提升用户体验

## 3. 文件结构设计

重构后的文件结构如下：

```
pages/myPackage/
├── myPackage.vue                    # 主容器组件
├── components/                      # 子组件目录
│   ├── OrderItem.vue                # 订单项组件
│   ├── AfterSaleItem.vue            # 售后项组件
│   ├── TabBar.vue                   # 页签组件
│   ├── EmptyState.vue               # 空状态组件
│   ├── LoadingState.vue             # 加载状态组件
│   └── popups/                      # 弹窗组件目录
│       ├── CancelOrderPopup.vue     # 取消订单弹窗
│       └── ExchangeGoodsPopup.vue   # 换货商品弹窗
├── composables/                     # 组合式函数目录
│   ├── useOrderList.js              # 订单列表相关逻辑
│   ├── useAfterSaleList.js          # 售后列表相关逻辑
│   ├── usePagination.js             # 分页逻辑
│   ├── useTabControl.js             # 页签控制逻辑
│   └── usePopupControl.js           # 弹窗控制逻辑
└── utils/                           # 工具函数目录
    ├── constants.js                 # 常量定义
    ├── formatters.js                # 格式化函数
    └── helpers.js                   # 辅助函数
```

## 4. 组件拆分详情

### 4.1 主容器组件 (myPackage.vue)

职责：
- 组织和协调子组件
- 管理全局状态
- 处理页面生命周期
- 提供子组件所需的数据和方法

```vue
<template>
  <view class="myPackage">
    <!-- 自定义导航栏 -->
    <uni-nav-bar
      title="我的包裹"
      leftIcon="left"
      @clickLeft="toUpPage"
      statusBar
      fixed
      backgroundColor="#f8f8f8"
      color="#333333"
      class="custom-nav-bar"
    />

    <!-- 页签组件 -->
    <tab-bar
      :current="tabSelectIndex"
      :tabs="tabs"
      @change="changeTabSelectIndex"
    />

    <!-- 可滚动内容区域 -->
    <scroll-view
      class="scroll-container"
      scroll-y
      :refresher-enabled="true"
      :refresher-triggered="isRefreshing"
      @refresherrefresh="onRefresh"
      @scrolltolower="onLoadMore"
    >
      <view class="myPackage-container">
        <!-- 订单列表展示 -->
        <template v-if="tabSelectIndex !== 4">
          <order-item
            v-for="(item, index) in list"
            :key="index"
            :item="item"
            :store-type-list="storeTypeList"
            @click="toOrderDetail(item)"
            @take-goods="takeGoods"
            @cancel-order="toggleCancelOrderPop"
            @to-pay="toPay"
            @to-logistics="toLogistics"
            @exchange-goods="showExchangeGoodsPopup"
          />
        </template>

        <!-- 售后列表展示 -->
        <template v-else>
          <after-sale-item
            v-for="(item, index) in list"
            :key="index"
            :item="item"
            :store-type-list="storeTypeList"
            @click="toAfterSaleDetail(item)"
            @delete="deleteAfterSaleOrder"
            @cancel="dealAfterSaleOrder"
          />
        </template>

        <!-- 空状态 -->
        <empty-state v-if="!list?.length" />

        <!-- 底部加载状态 -->
        <loading-state
          v-if="list?.length"
          :is-loading-more="isLoadingMore"
          :is-no-more="isNoMore"
        />
      </view>
    </scroll-view>

    <!-- 弹窗组件 -->
    <uni-popup ref="alertDialog" type="dialog">
      <uni-popup-dialog
        type="info"
        mode="base"
        :title="popupMsg.title"
        :content="popupMsg.content"
        :duration="2000"
        :before-close="false"
        @confirm="confirm"
        :confirmText="popupMsg.confirmText"
        :showClose="popupMsg.showClose"
      />
    </uni-popup>

    <!-- 取消订单弹窗 -->
    <cancel-order-popup
      ref="cancelOrderPop"
      :type="cancelOrderPopType"
      @success="cancelSuccess"
      @close="closeCancelOrderPop"
    />

    <!-- 商品选择弹窗 -->
    <exchange-goods-popup
      ref="exchangeGoodsPopup"
      :order-info="currentOrder"
      :goods-list="currentOrderGoods"
      @confirm="handleExchangeGoodsConfirm"
      @close="handleExchangeGoodsClose"
    />
  </view>
</template>

<script setup>
import { computed, ref, onMounted } from 'vue';
import { onLoad, onUnload } from '@dcloudio/uni-app';
import { toUpPage } from '@/hooks';

// 导入子组件
import TabBar from './components/TabBar.vue';
import OrderItem from './components/OrderItem.vue';
import AfterSaleItem from './components/AfterSaleItem.vue';
import EmptyState from './components/EmptyState.vue';
import LoadingState from './components/LoadingState.vue';
import CancelOrderPopup from './components/popups/CancelOrderPopup.vue';
import ExchangeGoodsPopup from './components/popups/ExchangeGoodsPopup.vue';

// 导入组合式函数
import { useOrderList } from './composables/useOrderList';
import { useAfterSaleList } from './composables/useAfterSaleList';
import { usePagination } from './composables/usePagination';
import { useTabControl } from './composables/useTabControl';
import { usePopupControl } from './composables/usePopupControl';

// 导入常量和工具函数
import { TABS, STATUS_LIST } from './utils/constants';

// 使用组合式函数
const {
  orderList,
  refreshOrderList,
  loadMoreOrders,
  toOrderDetail,
  takeGoods,
  toPay,
  toLogistics
} = useOrderList();

const {
  afterSaleList,
  refreshAfterSaleList,
  loadMoreAfterSales,
  toAfterSaleDetail,
  deleteAfterSaleOrder,
  dealAfterSaleOrder
} = useAfterSaleList();

const {
  page,
  pageSize,
  isRefreshing,
  isLoadingMore,
  isNoMore,
  onRefresh,
  onLoadMore
} = usePagination({
  loadMoreCallback: (tabIndex) => {
    return tabIndex === 4 ? loadMoreAfterSales(page.value) : loadMoreOrders(page.value, STATUS_LIST[tabIndex]);
  },
  refreshCallback: (tabIndex) => {
    return tabIndex === 4 ? refreshAfterSaleList() : refreshOrderList(STATUS_LIST[tabIndex]);
  }
});

const {
  tabs,
  tabSelectIndex,
  changeTabSelectIndex
} = useTabControl({
  tabs: TABS,
  onTabChange: (index) => {
    // 重置分页数据
    page.value = 1;
    isNoMore.value = false;
    isLoadingMore.value = false;

    // 加载对应页签的数据
    if (index === 4) {
      refreshAfterSaleList();
    } else {
      refreshOrderList(STATUS_LIST[index]);
    }
  }
});

const {
  popupMsg,
  alertDialog,
  cancelOrderPop,
  cancelOrderPopType,
  cancelOrderItem,
  currentOrder,
  currentOrderGoods,
  exchangeGoodsPopup,
  showExchangeGoodsPopup,
  toggleCancelOrderPop,
  closeCancelOrderPop,
  cancelSuccess,
  handleExchangeGoodsConfirm,
  handleExchangeGoodsClose,
  confirm
} = usePopupControl();

// 获取店铺类型字典
const storeTypeList = ref([]);
async function getStoreTypeDicts() {
  const { data } = await uni.http.get(`${uni.api.getDicts}?code=store_type`);
  storeTypeList.value = data.result || [];
}

// 计算当前列表数据
const list = computed(() => {
  return tabSelectIndex.value === 4 ? afterSaleList.value : orderList.value;
});

// 生命周期钩子
onLoad((op) => {
  // 处理页面加载参数
  if (op.tabIndex) {
    changeTabSelectIndex({ index: Number(op.tabIndex) });
  } else if (op.status) {
    STATUS_LIST.forEach((item, index) => {
      if (op.status == item) {
        changeTabSelectIndex({ index });
      }
    });
  } else {
    changeTabSelectIndex({ index: 0 });
  }

  // 获取店铺类型字典
  getStoreTypeDicts();

  // 监听刷新事件
  uni.$on('refreshMyOrder', () => {
    onRefresh();
  });
});

onUnload(() => {
  uni.$off('refreshMyOrder');
});
</script>

<style lang="scss">
/* 样式保持不变 */
</style>
```

### 4.2 子组件设计

#### 4.2.1 TabBar.vue

职责：
- 展示页签
- 处理页签切换事件

```vue
<template>
  <view class="tabs-container">
    <uv-tabs
      :current="current"
      :scrollable="false"
      :list="tabs"
      lineWidth="45rpx"
      lineHeight="6rpx"
      lineColor="#1A69D1"
      :activeStyle="{
        color: '#1A69D1',
        fontSize: '28rpx',
        fontWeight: 'bold'
      }"
      :inactiveStyle="{
        color: '#999999',
        fontSize: '28rpx'
      }"
      itemStyle="height:64rpx;"
      @click="onChange"
    />
  </view>
</template>

<script setup>
defineProps({
  current: {
    type: Number,
    default: 0
  },
  tabs: {
    type: Array,
    default: () => []
  }
});

const emit = defineEmits(['change']);

function onChange(e) {
  emit('change', e);
}
</script>

<style lang="scss" scoped>
.tabs-container {
  padding-top: 0;
  margin-top: 0;
  background-color: #f8f8f8;
  position: relative;
  z-index: 100;
  padding-bottom: 16rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}
</style>
```

#### 4.2.2 OrderItem.vue

职责：
- 展示单个订单信息
- 处理订单相关操作

```vue
<template>
  <view class="myPackage-container-wrap" @click="$emit('click')">
    <view class="myPackage-container-wrap-info">
      <view class="myPackage-container-wrap-info-top">
        <view class="myPackage-container-wrap-info-top-left" @click.stop="$emit('nav-to-store')">
          <view class="store-name">
            {{ item.storeName }}
          </view>
          <view class="myPackage-container-wrap-info-top-left-label" v-if="storeTypeList?.length">
            {{ storeTypeList.find(i => item.storeType === i.value)?.title }}
          </view>
          <image class="myPackage-container-wrap-info-top-left-arrow" src="@/static/right-arrow-black.png" mode="" />
        </view>
        <view class="myPackage-container-wrap-info-top-right">
          {{ item.isSender == '1' && item.STATUS == 1 ? '部分发货' : getStatusDetail(item).name }}
        </view>
      </view>

      <view class="myPackage-container-wrap-info-good" v-for="itm in item.goods" :key="itm.id">
        <!-- 商品信息展示 -->
        <!-- ... 省略部分代码 ... -->
      </view>
    </view>

    <view class="myPackage-container-wrap-bottom">
      <!-- 订单底部信息 -->
      <!-- ... 省略部分代码 ... -->
    </view>

    <view class="myPackage-container-wrap-operation">
      <!-- 操作按钮 -->
      <view class="operation-btn confirm-btn" v-if="getStatusDetail(item).type == 6 && !item.pickUpQrCode" @click.stop="$emit('take-goods', item)">
        确认收货
      </view>
      <template v-if="getStatusDetail(item).type == 3">
        <view class="operation-btn cancel-btn" @click.stop="$emit('cancel-order', item)">
          取消订单
        </view>
        <view class="operation-btn pay-btn" @click.stop="$emit('to-pay', item)">
          去付款
        </view>
      </template>
      <view class="operation-btn logistics-btn" @click.stop="$emit('to-logistics', item)"
        v-if="(getStatusDetail(item).type == 1 || getStatusDetail(item).type == 6 || getStatusDetail(item).type == 7) && !item.pickUpQrCode">
        查看物流
      </view>
      <view class="operation-btn exchange-btn" @click.stop="$emit('exchange-goods', item)"
        v-if="getStatusDetail(item).type == 7 && !item.pickUpQrCode && item.goods && item.goods.length > 0">
        申请换货
      </view>
    </view>
  </view>
</template>

<script setup>
import { getStatusDetail, getGoodStatusDetail } from '../utils/helpers';

defineProps({
  item: {
    type: Object,
    required: true
  },
  storeTypeList: {
    type: Array,
    default: () => []
  }
});

defineEmits([
  'click',
  'nav-to-store',
  'take-goods',
  'cancel-order',
  'to-pay',
  'to-logistics',
  'exchange-goods'
]);
</script>

<style lang="scss" scoped>
/* 样式保持不变 */
</style>
```

## 5. 组合式函数设计

### 5.1 usePagination.js

职责：
- 管理分页相关状态和逻辑
- 处理下拉刷新和上拉加载更多

```js
import { ref } from 'vue';

export function usePagination({ loadMoreCallback, refreshCallback }) {
  const page = ref(1);
  const pageSize = ref(10);
  const isRefreshing = ref(false);
  const isLoadingMore = ref(false);
  const isNoMore = ref(false);

  // 下拉刷新处理
  async function onRefresh(tabIndex) {
    console.log('refresh 被调用', {
      tabIndex,
      currentPage: page.value
    });

    isRefreshing.value = true;
    page.value = 1;
    isNoMore.value = false;

    try {
      await refreshCallback(tabIndex);
    } finally {
      isRefreshing.value = false;
      uni.stopPullDownRefresh();
    }
  }

  // 上拉加载更多
  async function onLoadMore(tabIndex) {
    console.log('触发上拉加载更多', {
      isLoadingMore: isLoadingMore.value,
      isNoMore: isNoMore.value,
      page: page.value,
      tabIndex
    });

    // 如果正在加载或已经没有更多数据，直接返回
    if (isLoadingMore.value || isNoMore.value) {
      console.log('已经没有更多数据或正在加载中，不再请求');
      return;
    }

    isLoadingMore.value = true;
    page.value++;

    try {
      const { hasMore } = await loadMoreCallback(tabIndex);
      if (!hasMore) {
        isNoMore.value = true;
      }
    } finally {
      isLoadingMore.value = false;
    }
  }

  // 重置分页状态
  function resetPagination() {
    page.value = 1;
    isNoMore.value = false;
    isLoadingMore.value = false;
  }

  return {
    page,
    pageSize,
    isRefreshing,
    isLoadingMore,
    isNoMore,
    onRefresh,
    onLoadMore,
    resetPagination
  };
}
```

### 5.2 useOrderList.js

职责：
- 管理订单列表数据
- 处理订单相关操作

```js
import { ref } from 'vue';
import { navTo, parseObjToPath } from '@/hooks';
import { cashier } from '@/hooks/cashier.js';
import { getStatusDetail } from '../utils/helpers';

export function useOrderList() {
  const orderList = ref([]);
  const { payResult } = cashier();

  // 刷新订单列表
  async function refreshOrderList(status) {
    console.log('refreshOrderList 被调用', { status });

    uni.showLoading({
      title: '加载中...',
      mask: true
    });

    try {
      const { data } = await uni.http.get(uni.api.orderList, {
        params: {
          status,
          pageNo: 1,
          pageSize: 10
        }
      });

      const records = data.result.records || [];
      console.log('refreshOrderList 获取到数据', {
        count: records.length,
        status
      });

      orderList.value = records;

      return {
        hasMore: records.length >= 10,
        records
      };
    } catch (err) {
      console.error('加载订单数据失败', err);
      orderList.value = [];
      return {
        hasMore: false,
        records: []
      };
    } finally {
      uni.hideLoading();
      uni.stopPullDownRefresh();
    }
  }

  // 加载更多订单
  async function loadMoreOrders(pageNo, status) {
    console.log('loadMoreOrders 被调用', { pageNo, status });

    try {
      const { data } = await uni.http.get(uni.api.orderList, {
        params: {
          status,
          pageNo,
          pageSize: 10
        }
      });

      const newRecords = data.result.records || [];
      console.log('loadMoreOrders 获取到数据', {
        count: newRecords.length,
        pageNo,
        status
      });

      if (newRecords.length > 0) {
        orderList.value = [...orderList.value, ...newRecords];
      }

      return {
        hasMore: newRecords.length >= 10,
        records: newRecords
      };
    } catch (err) {
      console.error('加载更多订单数据失败', err);
      return {
        hasMore: false,
        records: []
      };
    }
  }

  // 订单详情
  function toOrderDetail(item) {
    let info = {
      id: item.id,
      isPlatform: item.isPlatform,
      isEvaluate: item.isEvaluate,
      name: getStatusDetail(item).name,
      state: getStatusDetail(item).type,
      reason: item.reason || 0
    };
    navTo(`/pages/orderDetail/orderDetail${parseObjToPath(info)}`);
  }

  // 确认收货
  function takeGoods(item) {
    uni.showModal({
      title: '提示',
      content: '确认收货后，订单将完成交易',
      success: async (res) => {
        if (res.confirm) {
          uni.showLoading({
            title: '处理中...',
            mask: true
          });

          try {
            await uni.http.post(uni.api.confirmReceipt, {
              id: item.id
            });

            uni.showToast({
              title: '确认收货成功',
              icon: 'success'
            });

            // 刷新订单列表
            refreshOrderList(item.status);
          } catch (err) {
            console.error('确认收货失败', err);
            uni.showToast({
              title: '确认收货失败',
              icon: 'none'
            });
          } finally {
            uni.hideLoading();
          }
        }
      }
    });
  }

  // 去支付
  function toPay(item) {
    let info = {
      id: item.id,
      isPlatform: item.isPlatform,
      payClassic: 4
    };
    payResult(info, true, () => {
      refreshOrderList(item.status);
    });
  }

  // 查看物流
  function toLogistics(item) {
    let info = {
      orderListId: item.id,
      isPlatform: item.isPlatform
    };
    navTo(`/pages/logistics/logistics${parseObjToPath(info)}`);
  }

  return {
    orderList,
    refreshOrderList,
    loadMoreOrders,
    toOrderDetail,
    takeGoods,
    toPay,
    toLogistics
  };
}
```

### 5.3 useAfterSaleList.js

职责：
- 管理售后列表数据
- 处理售后相关操作

```js
import { ref } from 'vue';
import { navTo, parseObjToPath } from '@/hooks';

export function useAfterSaleList() {
  const afterSaleList = ref([]);

  // 刷新售后列表
  async function refreshAfterSaleList() {
    console.log('refreshAfterSaleList 被调用');

    uni.showLoading({
      title: '加载中...',
      mask: true
    });

    try {
      const { data } = await uni.http.get(uni.api.refundList, {
        params: {
          pageNo: 1,
          pageSize: 10
        }
      });

      const records = data.result.records || [];
      console.log('refreshAfterSaleList 获取到数据', {
        count: records.length
      });

      afterSaleList.value = records;

      return {
        hasMore: records.length >= 10,
        records
      };
    } catch (err) {
      console.error('刷新售后数据失败', err);
      afterSaleList.value = [];
      return {
        hasMore: false,
        records: []
      };
    } finally {
      uni.hideLoading();
      uni.stopPullDownRefresh();
    }
  }

  // 加载更多售后
  async function loadMoreAfterSales(pageNo) {
    console.log('loadMoreAfterSales 被调用', { pageNo });

    try {
      const { data } = await uni.http.get(uni.api.refundList, {
        params: {
          pageNo,
          pageSize: 10
        }
      });

      const newRecords = data.result.records || [];
      console.log('loadMoreAfterSales 获取到数据', {
        count: newRecords.length,
        pageNo
      });

      if (newRecords.length > 0) {
        afterSaleList.value = [...afterSaleList.value, ...newRecords];
      }

      return {
        hasMore: newRecords.length >= 10,
        records: newRecords
      };
    } catch (err) {
      console.error('加载更多售后数据失败', err);
      return {
        hasMore: false,
        records: []
      };
    }
  }

  // 售后详情
  function toAfterSaleDetail(item) {
    let info = {
      id: item.id,
      isPlatform: item.isPlatform
    };
    navTo(`/pages/afterSaleDetail/afterSaleDetail${parseObjToPath(info)}`);
  }

  // 删除售后订单
  function deleteAfterSaleOrder(item) {
    uni.showModal({
      title: '提示',
      content: '确认删除该售后订单？',
      success: async (res) => {
        if (res.confirm) {
          uni.showLoading({
            title: '处理中...',
            mask: true
          });

          try {
            await uni.http.post(uni.api.deleteRefund, {
              id: item.id
            });

            uni.showToast({
              title: '删除成功',
              icon: 'success'
            });

            // 刷新售后列表
            refreshAfterSaleList();
          } catch (err) {
            console.error('删除售后订单失败', err);
            uni.showToast({
              title: '删除失败',
              icon: 'none'
            });
          } finally {
            uni.hideLoading();
          }
        }
      }
    });
  }

  // 处理售后订单
  function dealAfterSaleOrder(item) {
    uni.showModal({
      title: '提示',
      content: '确认取消该售后申请？',
      success: async (res) => {
        if (res.confirm) {
          uni.showLoading({
            title: '处理中...',
            mask: true
          });

          try {
            await uni.http.post(uni.api.cancelRefund, {
              id: item.id
            });

            uni.showToast({
              title: '取消成功',
              icon: 'success'
            });

            // 刷新售后列表
            refreshAfterSaleList();
          } catch (err) {
            console.error('取消售后申请失败', err);
            uni.showToast({
              title: '取消失败',
              icon: 'none'
            });
          } finally {
            uni.hideLoading();
          }
        }
      }
    });
  }

  return {
    afterSaleList,
    refreshAfterSaleList,
    loadMoreAfterSales,
    toAfterSaleDetail,
    deleteAfterSaleOrder,
    dealAfterSaleOrder
  };
}
```

### 5.4 useTabControl.js

职责：
- 管理页签状态
- 处理页签切换逻辑

```js
import { ref } from 'vue';

export function useTabControl({ tabs, onTabChange }) {
  const tabSelectIndex = ref(0);

  // 切换页签
  function changeTabSelectIndex(e) {
    const index = e.index;
    console.log('切换页签', index);

    if (tabSelectIndex.value === index) {
      return;
    }

    tabSelectIndex.value = index;

    // 调用外部传入的回调函数
    if (typeof onTabChange === 'function') {
      onTabChange(index);
    }
  }

  return {
    tabs,
    tabSelectIndex,
    changeTabSelectIndex
  };
}
```

### 5.5 usePopupControl.js

职责：
- 管理弹窗状态
- 处理弹窗相关操作

```js
import { ref, reactive } from 'vue';

export function usePopupControl() {
  // 弹窗引用
  const alertDialog = ref(null);
  const cancelOrderPop = ref(null);
  const exchangeGoodsPopup = ref(null);

  // 弹窗状态
  const popupMsg = reactive({
    title: '',
    content: '',
    confirmText: '确定',
    showClose: false
  });

  // 取消订单弹窗类型
  const cancelOrderPopType = ref(1);
  const cancelOrderItem = ref(null);

  // 换货弹窗数据
  const currentOrder = ref(null);
  const currentOrderGoods = ref([]);

  // 显示取消订单弹窗
  function toggleCancelOrderPop(item) {
    cancelOrderItem.value = item;
    cancelOrderPopType.value = 1;
    cancelOrderPop.value.open();
  }

  // 关闭取消订单弹窗
  function closeCancelOrderPop() {
    cancelOrderPop.value.close();
  }

  // 取消订单成功回调
  function cancelSuccess() {
    uni.showToast({
      title: '取消成功',
      icon: 'success'
    });

    // 触发刷新事件
    uni.$emit('refreshMyOrder');
  }

  // 显示换货商品弹窗
  function showExchangeGoodsPopup(item) {
    currentOrder.value = item;
    currentOrderGoods.value = item.goods || [];
    exchangeGoodsPopup.value.open();
  }

  // 处理换货确认
  function handleExchangeGoodsConfirm(selectedGoods) {
    if (!selectedGoods || selectedGoods.length === 0) {
      uni.showToast({
        title: '请选择要换货的商品',
        icon: 'none'
      });
      return;
    }

    const info = {
      orderId: currentOrder.value.id,
      goodsIds: selectedGoods.map(item => item.id).join(',')
    };

    uni.navigateTo({
      url: `/pages/applyExchange/applyExchange?${parseObjToPath(info)}`
    });

    exchangeGoodsPopup.value.close();
  }

  // 关闭换货商品弹窗
  function handleExchangeGoodsClose() {
    exchangeGoodsPopup.value.close();
  }

  // 通用确认回调
  function confirm() {
    alertDialog.value.close();
  }

  return {
    popupMsg,
    alertDialog,
    cancelOrderPop,
    cancelOrderPopType,
    cancelOrderItem,
    currentOrder,
    currentOrderGoods,
    exchangeGoodsPopup,
    showExchangeGoodsPopup,
    toggleCancelOrderPop,
    closeCancelOrderPop,
    cancelSuccess,
    handleExchangeGoodsConfirm,
    handleExchangeGoodsClose,
    confirm
  };
}
```

## 6. 工具函数和常量定义

### 6.1 constants.js

职责：
- 定义常量和配置数据

```js
// 页签配置
export const TABS = [
  { name: '全部订单' },
  { name: '待付款' },
  { name: '待发货' },
  { name: '待收货' },
  { name: '售后' }
];

// 订单状态映射
export const STATUS_LIST = ['', '3', '4', '6', ''];

// 订单状态详情
export const ORDER_STATUS = {
  // 待付款
  WAIT_PAY: {
    type: 3,
    name: '待付款'
  },
  // 待发货
  WAIT_DELIVERY: {
    type: 4,
    name: '待发货'
  },
  // 待收货
  WAIT_RECEIVE: {
    type: 6,
    name: '待收货'
  },
  // 已完成
  COMPLETED: {
    type: 7,
    name: '已完成'
  },
  // 已取消
  CANCELED: {
    type: 8,
    name: '已取消'
  },
  // 已关闭
  CLOSED: {
    type: 9,
    name: '已关闭'
  },
  // 部分发货
  PARTIAL_DELIVERY: {
    type: 1,
    name: '部分发货'
  }
};

// 售后状态
export const AFTER_SALE_STATUS = {
  // 待审核
  WAIT_AUDIT: {
    type: 1,
    name: '待审核'
  },
  // 待退货
  WAIT_RETURN: {
    type: 2,
    name: '待退货'
  },
  // 待收货
  WAIT_RECEIVE: {
    type: 3,
    name: '待收货'
  },
  // 已完成
  COMPLETED: {
    type: 4,
    name: '已完成'
  },
  // 已拒绝
  REJECTED: {
    type: 5,
    name: '已拒绝'
  },
  // 已取消
  CANCELED: {
    type: 6,
    name: '已取消'
  }
};
```

### 6.2 helpers.js

职责：
- 提供辅助函数

```js
import { ORDER_STATUS, AFTER_SALE_STATUS } from './constants';

/**
 * 获取订单状态详情
 * @param {Object} item 订单信息
 * @returns {Object} 状态详情
 */
export function getStatusDetail(item) {
  // 已取消
  if (item.status === '8') {
    return ORDER_STATUS.CANCELED;
  }

  // 已关闭
  if (item.status === '9') {
    return ORDER_STATUS.CLOSED;
  }

  // 待付款
  if (item.status === '3') {
    return ORDER_STATUS.WAIT_PAY;
  }

  // 待发货
  if (item.status === '4') {
    return ORDER_STATUS.WAIT_DELIVERY;
  }

  // 待收货
  if (item.status === '6') {
    return ORDER_STATUS.WAIT_RECEIVE;
  }

  // 已完成
  if (item.status === '7') {
    return ORDER_STATUS.COMPLETED;
  }

  // 部分发货
  if (item.isSender === '1' && item.status === '1') {
    return ORDER_STATUS.PARTIAL_DELIVERY;
  }

  return {
    type: 0,
    name: '未知状态'
  };
}

/**
 * 获取商品状态详情
 * @param {Object} item 商品信息
 * @returns {Object} 状态详情
 */
export function getGoodStatusDetail(item) {
  // 待审核
  if (item.status === '1') {
    return AFTER_SALE_STATUS.WAIT_AUDIT;
  }

  // 待退货
  if (item.status === '2') {
    return AFTER_SALE_STATUS.WAIT_RETURN;
  }

  // 待收货
  if (item.status === '3') {
    return AFTER_SALE_STATUS.WAIT_RECEIVE;
  }

  // 已完成
  if (item.status === '4') {
    return AFTER_SALE_STATUS.COMPLETED;
  }

  // 已拒绝
  if (item.status === '5') {
    return AFTER_SALE_STATUS.REJECTED;
  }

  // 已取消
  if (item.status === '6') {
    return AFTER_SALE_STATUS.CANCELED;
  }

  return {
    type: 0,
    name: '未知状态'
  };
}

/**
 * 格式化价格
 * @param {number|string} price 价格
 * @returns {string} 格式化后的价格
 */
export function formatPrice(price) {
  if (!price && price !== 0) return '0.00';
  return Number(price).toFixed(2);
}
```

### 6.3 formatters.js

职责：
- 提供格式化函数

```js
/**
 * 格式化日期时间
 * @param {string|number|Date} time 时间
 * @param {string} format 格式
 * @returns {string} 格式化后的时间
 */
export function formatDateTime(time, format = 'YYYY-MM-DD HH:mm:ss') {
  if (!time) return '';

  const date = new Date(time);

  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  const seconds = String(date.getSeconds()).padStart(2, '0');

  return format
    .replace('YYYY', year)
    .replace('MM', month)
    .replace('DD', day)
    .replace('HH', hours)
    .replace('mm', minutes)
    .replace('ss', seconds);
}

/**
 * 格式化订单号
 * @param {string} orderNo 订单号
 * @returns {string} 格式化后的订单号
 */
export function formatOrderNo(orderNo) {
  if (!orderNo) return '';

  // 显示前4位和后4位，中间用星号代替
  if (orderNo.length > 8) {
    return `${orderNo.substring(0, 4)}****${orderNo.substring(orderNo.length - 4)}`;
  }

  return orderNo;
}
```

## 7. 实施计划

### 7.1 第一阶段：准备工作（1-2天）

1. 创建新的文件结构
   - 创建 `components`、`composables` 和 `utils` 目录
   - 创建常量和工具函数文件

2. 提取常量和工具函数
   - 将订单状态、页签配置等常量提取到 `constants.js`
   - 将辅助函数提取到 `helpers.js`
   - 将格式化函数提取到 `formatters.js`

3. 编写基础组合式函数
   - 实现 `usePagination.js`
   - 实现 `useTabControl.js`
   - 实现 `usePopupControl.js`

### 7.2 第二阶段：组件拆分（2-3天）

1. 创建子组件
   - 实现 `TabBar.vue`
   - 实现 `OrderItem.vue`
   - 实现 `AfterSaleItem.vue`
   - 实现 `EmptyState.vue`
   - 实现 `LoadingState.vue`
   - 实现弹窗组件

2. 实现组件间通信
   - 定义 props 和 events
   - 确保数据流转清晰

3. 测试子组件功能
   - 单独测试每个子组件
   - 确保子组件功能正常

### 7.3 第三阶段：主组件重构（2-3天）

1. 实现业务逻辑组合式函数
   - 实现 `useOrderList.js`
   - 实现 `useAfterSaleList.js`

2. 重构主组件
   - 使用组合式函数替换原有逻辑
   - 整合子组件
   - 确保数据流转正确

3. 测试整体功能
   - 测试页签切换
   - 测试分页加载
   - 测试各种操作功能

### 7.4 第四阶段：优化和测试（1-2天）

1. 性能优化
   - 优化组件渲染性能
   - 减少不必要的计算和请求
   - 实现懒加载和虚拟列表（如有必要）

2. 全面测试
   - 测试各种边界情况
   - 测试错误处理
   - 测试用户体验

3. 文档完善
   - 完善代码注释
   - 编写组件文档
   - 编写使用指南

## 8. 性能优化策略

### 8.1 减少不必要的渲染

1. 使用 `computed` 属性缓存计算结果
   ```js
   // 计算当前列表数据
   const list = computed(() => {
     return tabSelectIndex.value === 4 ? afterSaleList.value : orderList.value;
   });
   ```

2. 使用 `v-memo` 避免不必要的组件重新渲染
   ```html
   <order-item
     v-for="(item, index) in list"
     :key="item.id"
     v-memo="[item.id, item.status]"
     :item="item"
     :store-type-list="storeTypeList"
   />
   ```

### 8.2 优化数据加载

1. 实现分页加载和虚拟列表
   - 只加载和渲染当前可见的数据
   - 使用 `scroll-view` 的虚拟列表功能

2. 数据预加载
   - 在用户切换页签前预加载数据
   - 使用缓存减少重复请求

3. 优化请求策略
   - 合并请求
   - 使用防抖和节流控制请求频率

### 8.3 减少重复计算

1. 使用 `useMemo` 缓存复杂计算结果
   ```js
   const filteredList = computed(() => {
     // 复杂的过滤逻辑
     return list.value.filter(/* ... */);
   });
   ```

2. 避免在模板中进行复杂计算
   - 将复杂计算移到组合式函数中
   - 使用计算属性缓存结果

### 8.4 优化组件结构

1. 使用异步组件
   ```js
   const ExchangeGoodsPopup = defineAsyncComponent(() =>
     import('./components/popups/ExchangeGoodsPopup.vue')
   );
   ```

2. 按需加载组件
   - 只在需要时才加载弹窗组件
   - 使用 `v-if` 而不是 `v-show` 控制不常用组件的渲染

3. 减少组件嵌套层级
   - 扁平化组件结构
   - 避免不必要的包装组件

## 9. 测试方案

### 9.1 单元测试

1. 测试组合式函数
   - 测试 `usePagination` 的分页逻辑
   - 测试 `useOrderList` 和 `useAfterSaleList` 的数据处理逻辑
   - 测试工具函数的正确性

2. 测试工具函数
   - 测试 `getStatusDetail` 函数对不同状态的处理
   - 测试格式化函数的输出

### 9.2 组件测试

1. 测试子组件渲染
   - 测试 `OrderItem` 在不同状态下的渲染
   - 测试 `TabBar` 的页签切换功能

2. 测试组件交互
   - 测试按钮点击事件
   - 测试弹窗显示和隐藏

### 9.3 集成测试

1. 测试组件间通信
   - 测试父子组件之间的数据传递
   - 测试事件触发和处理

2. 测试数据流转
   - 测试状态管理
   - 测试数据更新后的UI响应

### 9.4 端到端测试

1. 测试完整功能流程
   - 测试页签切换
   - 测试分页加载
   - 测试订单操作

2. 测试用户体验
   - 测试加载状态和空状态
   - 测试错误处理和提示

## 10. 注意事项

1. 保持与现有代码的兼容性
   - 确保API调用方式不变
   - 保持现有功能完整性

2. 确保重构过程中不引入新的bug
   - 增量式重构，逐步替换
   - 每个阶段都进行充分测试

3. 遵循微信小程序的最佳实践
   - 注意小程序的性能限制
   - 遵循小程序的开发规范

4. 注意性能优化
   - 避免不必要的渲染和计算
   - 优化数据加载和处理

5. 保持代码风格一致性
   - 遵循项目的命名规范
   - 保持代码格式统一

6. 添加必要的注释和文档
   - 为复杂逻辑添加注释
   - 编写组件和函数文档

## 11. 总结

通过本次重构，我们将大型组件拆分为多个小型、功能单一的子组件，使用 Composition API 重构，严格分离业务逻辑、UI 展示和数据处理，建立清晰的数据流转模型，提高代码复用性和可测试性，优化性能，提升用户体验。

主要收益包括：

1. **提高可维护性**：代码结构清晰，职责分明，便于理解和维护
2. **提高可扩展性**：模块化设计，便于添加新功能和修改现有功能
3. **提高性能**：优化渲染和数据处理，提升用户体验
4. **提高可测试性**：逻辑分离，便于单元测试和集成测试
5. **提高代码复用性**：组合式函数和子组件可在其他页面复用

这将为后续的功能迭代打下坚实的基础，使代码更易于维护和扩展。
