# 售后数量不一致问题分析

## 问题描述

在小程序中，首页或个人中心页面显示的"换货/售后"数量与实际进入售后列表页面看到的数据条数不一致。具体表现为：首页显示的售后数量为1条，但点击进入售后列表页面后，实际显示了2条售后记录。

## 原因分析

通过对代码的分析，发现问题的根源在于首页和售后列表页面获取售后数据时使用的筛选条件不同。

### 1. 首页/个人中心页面获取售后数量的代码

在个人中心页面(`pages/user/user.vue`)中，通过调用`getAboutCount()`函数获取各种订单数量，包括售后数量：

```javascript
async function getAboutCount() {
  if (users.token) {
    let { data } = await uni.http.get(uni.api.memberGoodAndOrderCount);
    info.value = data.result
  } else {
    info.value = {}
  }
  // ...
}
```

这个函数调用了后端接口`memberGoodAndOrderCount`，该接口在`AfterMemberController.java`中定义：

```java
@RequestMapping("memberGoodAndOrderCount")
@ResponseBody
public Result<Map<String, Object>> memberGoodAndOrderCount(HttpServletRequest request) {
    // ...
    // 退款售后数量
    LambdaQueryWrapper<OrderRefundList> orderRefundListLambdaQueryWrapper = new LambdaQueryWrapper<>();
    orderRefundListLambdaQueryWrapper
            .eq(OrderRefundList::getDelFlag, "0")
            .eq(OrderRefundList::getMemberId, memberId)
            .in(OrderRefundList::getStatus,"0","1","2","3","5");
    long refundCount = orderRefundListService.count(orderRefundListLambdaQueryWrapper);
    map.put("refundCount", refundCount);
    // ...
}
```

**关键点**：首页获取售后数量时，只统计了状态为`0`(待处理)、`1`(待买家退回)、`2`(换货中)、`3`(退款中)和`5`(已拒绝)的售后单。

### 2. 售后列表页面获取售后数据的代码

在售后列表页面(`pages/myPackage/myPackage.vue`)中，通过以下代码获取售后数据：

```javascript
uni.http.get(uni.api.refundList).then(({data}) => {
  afterSaleList.value = data.result.records || []
  // ...
})
```

这个函数调用了后端接口`refundList`，该接口在`AfterOrderRefundController.java`中定义：

```java
@GetMapping(value = "/list")
public Result<IPage<OrderRefundList>> queryPageList(OrderRefundList orderRefundList,
                                                    @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                    @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                                    HttpServletRequest req) {
    String memberId = Convert.toStr(req.getAttribute(JwtConstants.CURRENT_USER_NAME));
    QueryWrapper<OrderRefundList> queryWrapper = QueryGenerator.initQueryWrapper(orderRefundList, req.getParameterMap());
    queryWrapper.eq("member_id", memberId);
    queryWrapper.orderByDesc("apply_time");
    Page<OrderRefundList> page = new Page<OrderRefundList>(pageNo, pageSize);
    IPage<OrderRefundList> pageList = orderRefundListService.page(page, queryWrapper);
    // ...
    return Result.OK(pageList);
}
```

**关键点**：售后列表页面获取售后数据时，没有对售后单状态进行筛选，而是获取了所有状态的售后单。

### 3. OrderRefundList实体类中的status字段含义

根据`OrderRefundList.java`实体类的定义，售后状态(status)字段的含义如下：

```java
/**
 * 售后状态 0=待处理 1=待买家退回 2=换货中（等待店铺确认收货） 3=退款中 4=退款成功 5=已拒绝 6=退款关闭 7=换货关闭 8=换货完成。 关联字典：order_refund_status
 */
@Excel(name = "售后处理进度", width = 15,dicCode = "order_refund_status")
@Dict(dicCode = "order_refund_status")
private java.lang.String status;
```

## 问题确认

通过分析，我们可以确认问题的原因是：

1. **数据统计逻辑不一致**：首页只统计了状态为`0`、`1`、`2`、`3`和`5`的售后单，而售后列表页面显示了所有状态的售后单。

2. **后端接口筛选条件差异**：
   - 首页使用的`memberGoodAndOrderCount`接口明确指定了要统计的售后单状态
   - 售后列表页面使用的`refundList`接口没有对售后单状态进行筛选

3. **前端获取数据方式不一致**：
   - 首页只关心售后单的数量
   - 售后列表页面需要显示所有售后单的详细信息

## 解决方案

根据业务需求，有以下几种可能的解决方案：

### 方案1：统一筛选条件

修改`AfterOrderRefundController.java`中的`list`方法，添加与首页相同的状态筛选条件：

```java
@GetMapping(value = "/list")
public Result<IPage<OrderRefundList>> queryPageList(OrderRefundList orderRefundList,
                                                   @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                   @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                                   HttpServletRequest req) {
    String memberId = Convert.toStr(req.getAttribute(JwtConstants.CURRENT_USER_NAME));
    QueryWrapper<OrderRefundList> queryWrapper = QueryGenerator.initQueryWrapper(orderRefundList, req.getParameterMap());
    queryWrapper.eq("member_id", memberId);
    // 添加与首页相同的状态筛选条件
    queryWrapper.in("status", "0", "1", "2", "3", "5");
    queryWrapper.orderByDesc("apply_time");
    // ...
}
```

### 方案2：修改首页统计逻辑

修改`AfterMemberController.java`中的`memberGoodAndOrderCount`方法，统计所有状态的售后单：

```java
@RequestMapping("memberGoodAndOrderCount")
@ResponseBody
public Result<Map<String, Object>> memberGoodAndOrderCount(HttpServletRequest request) {
    // ...
    // 退款售后数量 - 统计所有状态的售后单
    LambdaQueryWrapper<OrderRefundList> orderRefundListLambdaQueryWrapper = new LambdaQueryWrapper<>();
    orderRefundListLambdaQueryWrapper
            .eq(OrderRefundList::getDelFlag, "0")
            .eq(OrderRefundList::getMemberId, memberId);
    // 移除状态筛选条件
    // .in(OrderRefundList::getStatus,"0","1","2","3","5");
    long refundCount = orderRefundListService.count(orderRefundListLambdaQueryWrapper);
    map.put("refundCount", refundCount);
    // ...
}
```

### 方案3：在UI层面明确说明

如果业务上确实需要区分"进行中的售后"和"所有售后"，可以在UI层面明确说明：

1. 在首页的"换货/售后"图标旁添加说明文字，如"进行中"
2. 在售后列表页面添加状态筛选功能，默认显示所有售后单，但提供筛选选项

## 建议实施方案

根据小程序的业务逻辑，建议采用**方案1**，统一筛选条件。理由如下：

1. 首页显示的售后数量应该反映用户需要关注的售后单，即那些仍在处理中或需要用户操作的售后单
2. 已完成的售后单(状态为4、6、7、8)对用户的即时关注度较低
3. 统一筛选条件可以保持用户体验的一致性，避免用户困惑

## 实施步骤

1. 修改`AfterOrderRefundController.java`中的`list`方法，添加状态筛选条件
2. 在售后列表页面添加状态筛选功能，允许用户查看所有状态的售后单
3. 在UI层面明确说明首页显示的是"进行中的售后"数量
4. 进行充分测试，确保修改不会影响其他功能
