# 店铺补助金功能模块开发实施计划

**项目名称：** 八闽助业集市 - 店铺补助金功能模块
**需求文档版本：** 1.2 (2025-05-22 最终确认版)
**计划制定日期：** 2025-05-22
**负责人：** (待定)
**核心目标：** 根据需求文档，完成店铺补助金功能在平台管理后台、小程序用户端及相关系统逻辑的开发、测试与上线，为平台提供灵活的定向营销工具，激励用户消费，提升用户粘性。

---

### 一、总体计划与阶段划分

| 阶段         | 主要内容                                                                 | 预计周期 | 责任方       | 产出物                                                     |
| :----------- | :----------------------------------------------------------------------- | :------- | :----------- | :--------------------------------------------------------- |
| **阶段零：准备与分析** | 需求分析、文档确认                                                         | -        | 产品经理、开发Leader | 《店铺补助金需求说明文档_v1.2》                            |
| **阶段一：后端核心逻辑与数据模型** | 数据表结构调整、补助金核心状态管理、系统逻辑 (过期、并发)、Admin查询接口开发 | 2-3 周   | 后端开发     | 更新后的数据库Schema、核心业务逻辑代码、Admin查询API文档       |
| **阶段二：平台管理后台功能 (查询)** | 会员补助金明细查询界面开发与联调                                               | 1 周     | 前端(Admin)、后端开发 | Admin后台会员补助金查询功能                                  |
| **阶段三：小程序用户端 - 信息展示** | 个人中心补助金入口、补助金列表页、店铺内补助金提示功能开发与联调                       | 1.5-2 周 | 前端(小程序)、后端开发 | 用户端补助金信息展示相关界面及功能                             |
| **阶段四：小程序用户端 - 补助金核销** | 购物车/结算页补助金抵扣逻辑、支付逻辑整合、订单详情展示补助金使用情况                | 2-2.5 周 | 前端(小程序)、后端开发 | 用户端补助金抵扣、支付集成功能                               |
| **阶段五：小程序用户端 - 退款与并发处理** | 订单取消/售后退款时补助金恢复逻辑、并发消耗控制逻辑完善与测试                        | 1-1.5 周 | 后端开发、前端(小程序) | 补助金退款恢复逻辑、高并发场景下的补助金消耗稳定性                |
| **阶段六：集成测试与UAT** | 功能集成测试、场景测试、用户验收测试                                             | 1-2 周   | 测试团队、产品经理、业务方 | 测试报告、UAT反馈报告                                        |
| **阶段七：上线与监控** | 功能上线、生产环境验证、日志监控与问题跟踪                                         | 1 周     | 开发团队、运维团队   | 上线功能、监控报告                                           |

---

### 二、详细任务分解

#### 阶段零：准备与分析
* **任务0.1：需求文档最终确认**
    * **描述：** 确认《店铺补助金功能模块需求说明文档_v1.2》为最终开发依据。
    * **状态：** ✅ 已完成
    * **责任方：** 产品经理

#### 阶段一：后端核心逻辑与数据模型

* **任务1.1：数据库 `member_account_capital` 表结构调整**
    * **描述：**
        * 确认并新增 `pay_type` 数据字典值：'50' (店铺补助金发放), '51' (店铺补助金消费/抵扣), '52' (店铺补助金退回/恢复)。
        * 确认并新增 `store_subsidy_status` 数据字典值 (仅对 `pay_type`='50' 有效)：'0' (可用), '1' (已过期), '2' (已用尽)。
        * 新增 `version` 字段用于乐观锁并发控制。
        * 确认 `subsidy_store_id`, `expire_time`, `remaining_amount` 字段存在且类型正确。
        * 确保 `balance` 字段在补助金发放流水中的隔离逻辑得到遵循。
    * **责任方：** 后端开发、DBA
    * **预计工时：** 0.5 天
    * **依赖：** 无
    * **状态：** ✅ 已完成

* **任务1.2：店铺补助金发放管理 - 后端逻辑**
    * **描述：** 实现创建 `member_account_capital` 记录作为补助金发放的后端逻辑，确保 `pay_type`, `go_and_come`, `subsidy_store_id`, `expire_time`, `amount`, `remaining_amount`, `store_subsidy_status`, `balance` (隔离原则), `remarks` 字段按需求文档3.1.1正确写入。
    * **责任方：** 后端开发
    * **状态：** ✅ 已完成

* **任务1.3：店铺补助金自动过期处理逻辑开发**
    * **描述：** 开发定时任务，扫描并更新已过期的店铺补助金记录，将其 `store_subsidy_status` 更新为 '1' (已过期)。
    * **责任方：** 后端开发
    * **预计工时：** 1 天
    * **依赖：** 任务1.1
    * **状态：** ✅ 已完成

* **任务1.4：补助金并发消耗控制逻辑实现 (乐观锁)**
    * **描述：** 在更新补助金 `remaining_amount` 的相关操作中，引入基于 `version` 字段的乐观锁机制，处理并发冲突。
    * **责任方：** 后端开发
    * **预计工时：** 1.5 天
    * **依赖：** 任务1.1
    * **状态：** 待办

* **任务1.5：Admin后台 - 会员补助金发放记录查询API**
    * **描述：** 开发API接口，支持管理员查询所有 `pay_type` 为"店铺补助金发放"的记录。
    * **责任方：** 后端开发
    * **预计工时：** 0.5 天
    * **依赖：** 任务1.1
    * **状态：** ✅ 已完成

* **任务1.6：Admin后台 - 会员补助金明细查询API**
    * **描述：** 开发API接口，支持根据会员ID/手机号等筛选 `pay_type` 为"店铺补助金发放"的记录，并返回需求文档3.1.2中指定的列表信息。
    * **责任方：** 后端开发
    * **预计工时：** 1 天
    * **依赖：** 任务1.1, 《八闽助业集市平台操作手册.md》(用于理解会员信息关联)
    * **状态：** ✅ 已完成

#### 阶段二：平台管理后台功能 (查询)

* **任务2.1：Admin后台 - 会员补助金明细查询界面开发**
    * **描述：** 根据需求文档3.1.2，开发管理员查询会员补助金明细的界面，包括筛选条件输入和结果列表展示。
    * **责任方：** 前端(Admin)
    * **预计工时：** 2 天
    * **依赖：** 任务1.6 API完成
    * **状态：** ✅ 已完成

* **任务2.2：Admin后台 - 会员补助金明细查询功能联调**
    * **描述：** 前后端联调会员补助金明细查询功能。
    * **责任方：** 前端(Admin)、后端开发
    * **预计工时：** 1 天
    * **依赖：** 任务2.1
    * **状态：** ✅ 已完成

#### 阶段三：小程序用户端 - 信息展示

* **任务3.1：用户端 - "我的店铺补助金" 相关API开发**
    * **描述：**
        * API 1: 获取用户可用店铺补助金总额 (用于个人中心摘要)。
        * API 2: 获取用户"我的店铺补助金"列表（支持"可用"、"已用尽"、"已过期"状态切换，按有效期排序）。
        * API 3: 查询用户在特定店铺可用的补助金总额及最近过期时间 (用于店铺首页/商品详情页提示)。
    * **责任方：** 后端开发
    * **预计工时：** 2 天
    * **依赖：** 任务1.1
    * **状态：** ✅ 已完成

* **任务3.2：用户端 - 个人中心/我的钱包页面补助金入口开发**
    * **描述：** 根据需求文档3.2.1，在个人中心或我的钱包页面增加"店铺补助金"入口/摘要信息，展示可用总额并可跳转至列表页。 通用账户余额展示需排除补助金金额。
    * **责任方：** 前端(小程序)
    * **预计工时：** 1 天
    * **依赖：** 任务3.1 (API 1)
    * **状态：** ✅ 已完成

* **任务3.3：用户端 - "我的店铺补助金"列表页面开发**
    * **描述：** 根据需求文档3.2.1，开发"我的店铺补助金"列表页面，包括Tab切换、卡片式信息展示和"去店铺使用"按钮。
    * **责任方：** 前端(小程序)
    * **预计工时：** 2 天
    * **依赖：** 任务3.1 (API 2)
    * **状态：** ✅ 已完成

* **任务3.4：用户端 - 特定店铺首页/商品详情页补助金提示开发**
    * **描述：** 根据需求文档3.2.1，在用户进入店铺首页或商品详情页时，调用API检查并显示可用补助金提示。
    * **责任方：** 前端(小程序)
    * **预计工时：** 1 天
    * **依赖：** 任务3.1 (API 3)
    * **状态：** ✅ 已完成

* **任务3.5：用户端 - 补助金信息展示功能联调**
    * **描述：** 前后端联调个人中心、补助金列表、店铺页提示等信息展示功能。
    * **责任方：** 前端(小程序)、后端开发
    * **预计工时：** 1.5 天
    * **依赖：** 任务3.2, 3.3, 3.4
    * **状态：** ✅ 已完成

#### 阶段四：小程序用户端 - 补助金核销

* **任务4.1：用户端 - 订单确认/结算页面补助金抵扣API及逻辑开发**
    * **描述：**
        * 后端：开发API，根据购物车商品和用户，返回各店铺可用的补助金及自动抵扣金额（按最早过期优先）。
        * 后端：实现支付时补助金消耗核心逻辑，包括判断适用补助金、消耗策略、更新原补助金记录的 `remaining_amount` 和 `store_subsidy_status`。
        * 后端：实现生成"店铺补助金消费/抵扣" (`pay_type`='51')的支出型流水记录。
    * **责任方：** 后端开发
    * **预计工时：** 3 天
    * **依赖：** 任务1.1, 1.4, 《八闽助业集市用户操作手册.md》(理解购物车和结算流程)
    * **状态：** ✅ 已完成

* **任务4.2：用户端 - 订单确认/结算页面补助金抵扣界面开发**
    * **描述：** 根据需求文档3.2.2，在订单确认/结算页面按店铺分组展示商品，并显示各店铺的补助金抵扣情况及最终费用汇总。
    * **责任方：** 前端(小程序)
    * **预计工时：** 2.5 天
    * **依赖：** 任务4.1 (API部分)
    * **状态：** ✅ 已完成

* **任务4.3：用户端 - 支付逻辑整合 (补助金、余额、三方支付)**
    * **描述：** 整合支付流程，确保按"店铺补助金 -> 通用账户余额 -> 其他支付方式"的顺序进行扣款。
    * **责任方：** 前端(小程序)、后端开发
    * **预计工时：** 2 天
    * **依赖：** 任务4.1, 任务4.2
    * **状态：** ✅ 已完成

* **任务4.4：用户端 - 订单详情页面展示补助金使用情况**
    * **描述：**
        * 后端：API提供订单使用的补助金信息。
        * 前端：在订单详情页清晰展示实际使用的店铺补助金抵扣金额。
    * **责任方：** 前端(小程序)、后端开发
    * **预计工时：** 1 天
    * **依赖：** 任务4.3
    * **状态：** 待办

* **任务4.5：用户端 - 补助金核销功能联调**
    * **描述：** 前后端联调购物车/结算页的补助金自动抵扣、支付逻辑及订单详情展示。
    * **责任方：** 前端(小程序)、后端开发
    * **预计工时：** 2 天
    * **依赖：** 任务4.3, 4.4
    * **状态：** 待办

#### 阶段五：小程序用户端 - 退款与并发处理完善

* **任务5.1：用户端 - 订单取消/售后退款时补助金恢复API及逻辑开发**
    * **描述：**
        * 后端：实现订单退款时，按规则恢复原补助金记录的 `remaining_amount` 和 `store_subsidy_status`（如果未过期）。
        * 后端：实现生成"店铺补助金退回/恢复" (`pay_type`='52')的收入型流水记录。
    * **责任方：** 后端开发
    * **预计工时：** 2 天
    * **实际工时：** 2 天
    * **依赖：** 任务4.1, 《八闽助业集市用户操作手册.md》(理解退款流程)
    * **完成日期：** 2024年12月19日
    * **状态：** ✅ 已完成

* **任务5.2：用户端 - 订单取消/售后退款流程整合补助金恢复**
    * **描述：** 在用户端发起订单取消或售后退款流程中，前端需配合调用后端补助金恢复逻辑，并正确处理界面展示。
    * **责任方：** 前端(小程序)
    * **预计工时：** 1 天
    * **依赖：** 任务5.1
    * **状态：** 待办

* **任务5.3：补助金并发消耗场景压力测试与优化**
    * **描述：** 针对高并发场景下补助金消耗的逻辑进行压力测试，验证乐观锁机制的有效性并进行必要优化。
    * **责任方：** 后端开发、测试团队
    * **预计工时：** 1.5 天
    * **依赖：** 任务1.4, 4.1
    * **状态：** 待办

* **任务5.4：用户端 - 退款及并发处理功能联调**
    * **描述：** 前后端联调订单退款时补助金恢复的逻辑。
    * **责任方：** 前端(小程序)、后端开发
    * **预计工时：** 1 天
    * **依赖：** 任务5.2, 5.3
    * **状态：** 待办

#### 阶段六：集成测试与UAT

* **任务6.1：功能集成测试用例编写**
    * **描述：** 编写覆盖所有功能点和主要业务流程的集成测试用例。
    * **责任方：** 测试团队、产品经理
    * **预计工时：** 2 天
    * **依赖：** 需求文档V1.2
    * **状态：** 待办

* **任务6.2：第一轮集成测试**
    * **描述：** 执行集成测试用例，记录并跟踪缺陷。
    * **责任方：** 测试团队
    * **预计工时：** 3 天
    * **依赖：** 所有开发任务完成
    * **状态：** 待办

* **任务6.3：缺陷修复与回归测试**
    * **描述：** 开发团队修复测试中发现的缺陷，测试团队进行回归测试。
    * **责任方：** 开发团队、测试团队
    * **预计工时：** (根据缺陷数量而定)
    * **依赖：** 任务6.2
    * **状态：** 待办

* **任务6.4：用户验收测试 (UAT)**
    * **描述：** 组织业务方或真实用户进行UAT。
    * **责任方：** 产品经理、业务方、测试团队
    * **预计工时：** 2 天
    * **依赖：** 主要缺陷修复完成
    * **状态：** 待办

* **任务6.5：UAT缺陷修复与确认**
    * **描述：** 修复UAT中发现的问题并由业务方确认。
    * **责任方：** 开发团队、产品经理、业务方
    * **预计工时：** (根据缺陷数量而定)
    * **依赖：** 任务6.4
    * **状态：** 待办

#### 阶段七：上线与监控

* **任务7.1：上线部署方案制定与演练**
    * **描述：** 制定详细的上线步骤、回滚方案，并进行演练。
    * **责任方：** 开发团队、运维团队
    * **预计工时：** 1 天
    * **依赖：** UAT通过
    * **状态：** 待办

* **任务7.2：生产环境部署**
    * **描述：** 将店铺补助金功能模块部署到生产环境。
    * **责任方：** 运维团队、开发团队
    * **预计工时：** 0.5 天
    * **依赖：** 任务7.1
    * **状态：** 待办

* **任务7.3：上线后功能验证与监控**
    * **描述：** 在生产环境进行基本功能验证，并建立日志监控，跟踪功能运行状态。
    * **责任方：** 开发团队、测试团队、运维团队
    * **预计工时：** 持续监控
    * **依赖：** 任务7.2
    * **状态：** 待办

---

### 三、资源需求 (占位)

* **后端开发工程师：** X人
* **前端(Admin)开发工程师：** X人
* **前端(小程序)开发工程师：** X人
* **测试工程师：** X人
* **DBA：** (按需支持)
* **运维工程师：** (按需支持)

---

### 四、风险与应对

* **风险1：** 数据库 `balance` 字段隔离逻辑理解不一致或实现有偏差，导致用户通用余额计算错误。
    * **应对：** 开发前与产品经理、后端开发充分沟通确认，加强单元测试和集成测试中对余额变动的校验。
* **风险2：** 补助金并发消耗控制不当，导致超额抵扣或数据不一致。
    * **应对：** 严格执行乐观锁方案，进行充分的并发压力测试。
* **风险3：** 多店铺结算页面逻辑复杂，用户体验不佳。
    * **应对：** 开发过程中邀请产品经理和UI/UX设计师提前介入评审，及时调整优化。
* **风险4：** 现有订单退款流程复杂，补助金恢复逻辑嵌入难度高。
    * **应对：** 提前梳理现有退款流程，预留充足时间进行设计和测试。
* **风险5：** 依赖的旧有接口或模块存在未知问题，影响联调进度。
    * **应对：** 尽早开始联调，发现问题及时沟通解决。

---

### 五、沟通与协作

* **每日站会：** 开发团队内部同步进度、问题和风险。
* **每周项目例会：** 产品、开发、测试同步整体进度，评审阶段性成果。
* **即时通讯工具：** 用于日常问题沟通。
* **文档共享平台：** 统一管理需求文档、设计文档、API文档等。

---