# 店铺商品库存优化实施计划

## 一、现状分析

通过对代码的分析，我们发现当前系统在店铺商品库存管理方面存在以下问题：

### 1. 下单操作

在 `OrderStoreListServiceImpl.submitOrderStoreGoods` 方法中，下单时只更新了规格表的库存，没有同步更新商品主表的库存字段：

```java
goodStoreSpecification.setRepertory(goodStoreSpecification.getRepertory().subtract(orderStoreGoodRecord.getAmount()));
iGoodStoreListService.saveOrUpdate(goodStoreList);
iGoodStoreSpecificationService.saveOrUpdate(goodStoreSpecification);
```

### 2. 取消订单操作

在 `OrderStoreListServiceImpl.abrogateOrder` 方法中，取消订单时只恢复了规格表的库存，没有同步更新商品主表的库存字段：

```java
orderStoreGoodRecords.forEach(osgr -> {
    String goodStoreSpecificationId = osgr.getGoodStoreSpecificationId();
    GoodStoreSpecification goodStoreSpecification = iGoodStoreSpecificationService.getById(goodStoreSpecificationId);
    if (goodStoreSpecification != null) {
        goodStoreSpecification.setRepertory(goodStoreSpecification.getRepertory().add(osgr.getAmount()));
        iGoodStoreSpecificationService.saveOrUpdate(goodStoreSpecification);
    }
});
```

### 3. 确认收货操作

在 `OrderStoreListServiceImpl.affirmOrder` 方法中，确认收货操作不涉及库存变更，只是更新订单状态和更新商品销量：

```java
// 更新商品销量
List<Map<String, Object>> orderStoreGoodRecordByOrderId = orderStoreGoodRecordMapper.getOrderStoreGoodRecordByOrderId(orderStoreList.getId());
for (Map<String, Object> stringObjectMap : orderStoreGoodRecordByOrderId) {
    String specificationId = Convert.toStr(stringObjectMap.get("specificationId"));
    BigDecimal amount = Convert.toBigDecimal(stringObjectMap.get("amount"));
    GoodStoreSpecification goodStoreSpecification = iGoodStoreSpecificationService.getById(specificationId);
    if (goodStoreSpecification != null) {
        goodStoreSpecification.setSalesVolume(NumberUtil.add(goodStoreSpecification.getSalesVolume(),amount));
        iGoodStoreSpecificationService.updateById(goodStoreSpecification);
    }
}
```

## 二、优化目标

1. 确保在所有涉及库存变更的操作中，同步更新商品主表的库存字段
2. 提高库存操作的可靠性和一致性
3. 优化库存查询性能

## 三、实施计划

### 第一阶段：同步更新商品主表库存

#### 1. 创建通用的库存更新方法

在 `GoodStoreListServiceImpl` 中添加一个通用方法，用于计算并更新商品主表的总库存：

```java
/**
 * 更新商品主表库存
 * @param goodStoreListId 商品ID
 */
public void updateGoodStoreListRepertory(String goodStoreListId) {
    // 查询所有规格的库存并汇总
    BigDecimal totalRepertory = iGoodStoreSpecificationService.list(
        new LambdaQueryWrapper<GoodStoreSpecification>()
            .eq(GoodStoreSpecification::getGoodStoreListId, goodStoreListId)
    ).stream()
    .map(GoodStoreSpecification::getRepertory)
    .reduce(BigDecimal.ZERO, BigDecimal::add);
    
    // 更新商品主表库存
    GoodStoreList goodStoreList = this.getById(goodStoreListId);
    if (goodStoreList != null) {
        goodStoreList.setRepertory(totalRepertory);
        this.updateById(goodStoreList);
    }
}
```

#### 2. 修改下单操作

在 `OrderStoreListServiceImpl.submitOrderStoreGoods` 方法中，添加同步更新商品主表库存的逻辑：

```java
// 扣减规格库存
goodStoreSpecification.setRepertory(goodStoreSpecification.getRepertory().subtract(orderStoreGoodRecord.getAmount()));
iGoodStoreSpecificationService.saveOrUpdate(goodStoreSpecification);

// 同步更新商品主表库存
iGoodStoreListService.updateGoodStoreListRepertory(goodStoreList.getId());
```

#### 3. 修改取消订单操作

在 `OrderStoreListServiceImpl.abrogateOrder` 方法中，添加同步更新商品主表库存的逻辑：

```java
orderStoreGoodRecords.forEach(osgr -> {
    String goodStoreSpecificationId = osgr.getGoodStoreSpecificationId();
    GoodStoreSpecification goodStoreSpecification = iGoodStoreSpecificationService.getById(goodStoreSpecificationId);
    if (goodStoreSpecification != null) {
        // 恢复规格库存
        goodStoreSpecification.setRepertory(goodStoreSpecification.getRepertory().add(osgr.getAmount()));
        iGoodStoreSpecificationService.saveOrUpdate(goodStoreSpecification);
        
        // 同步更新商品主表库存
        iGoodStoreListService.updateGoodStoreListRepertory(goodStoreSpecification.getGoodStoreListId());
    }
});
```

### 第二阶段：添加库存变更日志

#### 1. 创建库存变更日志表

```sql
CREATE TABLE `good_store_inventory_log` (
  `id` varchar(36) NOT NULL COMMENT '主键ID',
  `good_store_list_id` varchar(36) DEFAULT NULL COMMENT '商品ID',
  `good_store_specification_id` varchar(36) DEFAULT NULL COMMENT '规格ID',
  `before_quantity` decimal(10,2) DEFAULT NULL COMMENT '变更前库存',
  `after_quantity` decimal(10,2) DEFAULT NULL COMMENT '变更后库存',
  `change_quantity` decimal(10,2) DEFAULT NULL COMMENT '变更数量',
  `change_type` varchar(2) DEFAULT NULL COMMENT '变更类型（0：下单，1：取消订单，2：退货，3：手动调整）',
  `related_order_id` varchar(36) DEFAULT NULL COMMENT '关联订单ID',
  `change_time` datetime DEFAULT NULL COMMENT '变更时间',
  `operator` varchar(36) DEFAULT NULL COMMENT '操作人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `del_flag` varchar(1) DEFAULT '0' COMMENT '删除状态（0，正常，1已删除）',
  PRIMARY KEY (`id`),
  KEY `idx_good_store_list_id` (`good_store_list_id`),
  KEY `idx_good_store_specification_id` (`good_store_specification_id`),
  KEY `idx_related_order_id` (`related_order_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商品库存变更日志表';
```

#### 2. 创建库存变更日志实体类和服务

创建 `GoodStoreInventoryLog` 实体类和对应的服务接口及实现类。

#### 3. 在库存变更操作中添加日志记录

在下单、取消订单等操作中，添加库存变更日志记录。

### 第三阶段：优化库存查询性能

#### 1. 添加库存缓存

使用 Redis 缓存热门商品的库存信息，提高查询性能。

#### 2. 实现库存缓存更新策略

在库存变更时，同步更新缓存。

### 第四阶段：库存预警机制

#### 1. 添加库存预警阈值配置

在商品表中添加库存预警阈值字段，用于设置库存预警阈值。

#### 2. 实现库存预警通知功能

当库存低于预警阈值时，发送通知给商家或管理员。

## 四、注意事项

1. 所有涉及库存变更的操作都应该在事务中进行，确保数据一致性
2. 库存不能为负数，在扣减库存前应该先检查库存是否足够
3. 考虑并发情况下的库存操作，可能需要添加乐观锁或悲观锁
4. 定期对商品主表和规格表的库存进行校验和同步，确保数据一致性

## 五、预期效果

1. 商品主表和规格表的库存数据保持一致
2. 库存查询性能提升
3. 库存操作更加可靠和一致
4. 库存变更有完整的日志记录，便于追踪和排查问题
5. 库存预警机制可以及时提醒商家或管理员补充库存
