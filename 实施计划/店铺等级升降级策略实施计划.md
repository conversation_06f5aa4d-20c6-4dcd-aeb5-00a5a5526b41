# 店铺等级升降级策略实施计划

**版本**: 1.0  
**日期**: 2025年1月25日  
**负责人**: 开发团队  
**项目周期**: 预计 15-20 个工作日

## 1. 项目概述

### 1.1 项目背景
基于《产品需求文档：店铺等级升降级策略》，实现一套动态的店铺等级评定与激励机制，通过自动化评估店铺的月度表现，实现等级的动态调整及相应奖励。

### 1.2 核心功能
- 店铺等级配置管理
- 自动化等级评定引擎
- 等级变更日志记录
- 奖励发放机制
- 管理端配置界面
- 商家端等级展示

### 1.3 技术架构
- **后端**: Spring Boot + MyBatis Plus
- **前端管理端**: Vue 2 + Ant Design Vue
- **前端商家端**: UniApp 微信小程序
- **数据库**: MySQL
- **定时任务**: Quartz Job

## 2. 渐进式实施策略

### 阶段划分原则
1. **数据基础优先**: 先建立数据表结构和基础配置
2. **核心逻辑次之**: 实现等级评定的核心算法
3. **管理功能跟进**: 提供管理员配置和监控能力
4. **用户体验最后**: 完善商家端展示和通知功能
5. **测试验证贯穿**: 每个阶段都包含充分的测试

## 3. 详细实施计划

### 阶段1：数据库设计与基础配置 (3-4天)

#### 3.1.1 数据表设计与创建
**目标**: 建立完整的数据表结构

**任务清单**:
- ✅ 创建 `store_level_configurations` 表
- ✅ 创建 `store_level_change_logs` 表
- ✅ 创建相关数据字典 (`store_level_change_type`, `common_active_status`)
- ✅ 验证表结构与现有系统的兼容性
- ✅ 初始化基础配置数据

**SQL脚本**:
```sql
-- store_level_configurations 表
CREATE TABLE `store_level_configurations` (
  `id` varchar(32) NOT NULL COMMENT '主键ID',
  `create_by` varchar(32) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT (now()) NOT NULL COMMENT '创建时间',
  `update_by` varchar(32) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `del_flag` varchar(1) DEFAULT '0' COMMENT '删除状态 0=未删除 1=已删除',
  `level_value` varchar(20) NOT NULL COMMENT '店铺等级值',
  `level_name` varchar(100) NOT NULL COMMENT '店铺等级名称',
  `min_monthly_sales` decimal(20,2) DEFAULT '0.00' COMMENT '最低月业绩要求',
  `min_entrepreneur_count` int DEFAULT '0' COMMENT '最低关联企业家数量要求',
  `monthly_reward_balance_amount` decimal(10,2) DEFAULT '0.00' COMMENT '月度奖励金额',
  `description` text COMMENT '等级说明',
  `sort_order` int DEFAULT '0' COMMENT '排序字段',
  `is_active` tinyint(1) DEFAULT '1' COMMENT '是否启用',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_store_level_value` (`level_value`, `del_flag`),
  KEY `idx_is_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='店铺等级评定配置表';

-- store_level_change_logs 表
CREATE TABLE `store_level_change_logs` (
  `id` varchar(32) NOT NULL COMMENT '主键ID',
  `create_by` varchar(32) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT (now()) NOT NULL COMMENT '创建时间',
  `update_by` varchar(32) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `del_flag` varchar(1) DEFAULT '0' COMMENT '删除状态',
  `store_manage_id` varchar(32) NOT NULL COMMENT '店铺ID',
  `assessment_period` varchar(7) NOT NULL COMMENT '评定业绩周期 YYYY-MM',
  `previous_level` varchar(20) NOT NULL COMMENT '变更前等级',
  `current_level` varchar(20) NOT NULL COMMENT '变更后等级',
  `assessed_sales` decimal(20,2) DEFAULT '0.00' COMMENT '评定使用的上月业绩金额',
  `assessed_entrepreneur_count` int DEFAULT '0' COMMENT '评定使用的合作企业家数量',
  `change_type` varchar(10) NOT NULL COMMENT '变更类型: UPGRADE, DOWNGRADE, MAINTAIN',
  `reward_balance_amount_given` decimal(10,2) DEFAULT '0.00' COMMENT '发放的奖励金额',
  `remarks` varchar(255) DEFAULT NULL COMMENT '备注信息',
  PRIMARY KEY (`id`),
  KEY `idx_store_manage_id` (`store_manage_id`),
  KEY `idx_assessment_period` (`assessment_period`),
  KEY `idx_change_type` (`change_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='店铺等级变更及奖励日志表';
```

**验收标准**:
- ✅ 数据表创建成功，字段类型和约束正确
- ✅ 数据字典配置完成
- ✅ 初始化数据插入成功
- ✅ 与现有 `store_manage` 表的关联验证通过

#### 3.1.2 实体类和基础服务创建
**目标**: 创建对应的Java实体类和基础服务

**任务清单**:
- ✅ 创建 `StoreLevelConfiguration` 实体类
- ✅ 创建 `StoreLevelChangeLog` 实体类
- ✅ 创建对应的 Mapper 接口
- ✅ 创建基础的 Service 接口和实现类
- ✅ 编写基础的 CRUD 操作

**验收标准**:
- ✅ 实体类字段映射正确
- ✅ 基础 CRUD 操作测试通过
- ✅ 代码符合项目规范

### 阶段2：核心评定算法实现 (4-5天)

#### 3.2.1 数据统计服务开发
**目标**: 实现上月业绩和合作企业家数量的统计逻辑

**任务清单**:
- ✅ 实现上月业绩统计方法
  ```java
  /**
   * 统计店铺上月业绩
   * @param storeId 店铺ID
   * @param assessmentMonth 评定月份 (YYYY-MM)
   * @return 上月业绩总额
   */
  BigDecimal calculateMonthlyPerformance(String storeId, String assessmentMonth);
  ```
- ✅ 实现合作企业家数量统计方法
  ```java
  /**
   * 统计店铺合作企业家数量
   * @param storeId 店铺ID
   * @return 合作企业家数量
   */
  Long calculateEntrepreneurCount(String storeId);
  ```
- ✅ 编写单元测试验证统计逻辑
- ✅ 性能优化（添加必要的数据库索引）

**验收标准**:
- ✅ 业绩统计逻辑正确，与PRD描述一致
- ✅ 企业家数量统计准确
- ✅ 单元测试覆盖率达到90%以上
- ✅ 查询性能满足要求（单次查询<500ms）

#### 3.2.2 等级评定核心引擎
**目标**: 实现店铺等级评定的核心算法

**任务清单**:
- ✅ 实现等级评定主流程
  ```java
  /**
   * 执行店铺等级评定
   * @param storeId 店铺ID
   * @param assessmentPeriod 评定周期
   * @return 评定结果
   */
  StoreLevelAssessmentResult assessStoreLevel(String storeId, String assessmentPeriod);
  ```
- ✅ 实现维持当前等级评估逻辑
- ✅ 实现晋升下一等级评估逻辑
- ✅ 实现降级处理逻辑
- ✅ 实现奖励发放逻辑
- ✅ 添加详细的日志记录

**核心算法流程**:
```java
public class StoreLevelAssessmentService {
    
    public StoreLevelAssessmentResult assessStoreLevel(String storeId, String assessmentPeriod) {
        // 1. 获取店铺当前等级
        String currentLevel = getCurrentStoreLevel(storeId);
        
        // 2. 统计上月业绩和企业家数量
        BigDecimal actualSales = calculateMonthlyPerformance(storeId, assessmentPeriod);
        Integer actualEntrepreneurs = calculateEntrepreneurCount(storeId);
        
        // 3. 维持当前等级评估
        if (canMaintainCurrentLevel(currentLevel, actualSales, actualEntrepreneurs)) {
            // 4. 尝试晋升评估
            String nextLevel = getNextLevel(currentLevel);
            if (canUpgradeToNextLevel(nextLevel, actualSales, actualEntrepreneurs)) {
                return upgradeStore(storeId, currentLevel, nextLevel, actualSales, actualEntrepreneurs, assessmentPeriod);
            } else {
                return maintainStore(storeId, currentLevel, actualSales, actualEntrepreneurs, assessmentPeriod);
            }
        } else {
            // 5. 执行降级
            String previousLevel = getPreviousLevel(currentLevel);
            return downgradeStore(storeId, currentLevel, previousLevel, actualSales, actualEntrepreneurs, assessmentPeriod);
        }
    }
}
```

**验收标准**:
- ✅ 评定逻辑完全符合PRD要求
- ✅ 升级、降级、维持三种情况处理正确
- ✅ 奖励发放逻辑准确
- ✅ 异常情况处理完善
- ✅ 集成测试通过

### 阶段3：定时任务与批量处理 (2-3天)

#### 3.3.1 定时任务实现
**目标**: 实现每月自动执行的等级评定任务

**任务清单**:
- ✅ 创建Quartz Job类，参考 `MemberStoreSubsidyStatusJob` 实现
- ✅ 实现批量评定逻辑
- ✅ 实现失败重试机制
- ✅ 添加任务执行监控和日志
- ✅ 在管理后台配置定时任务执行周期

**Quartz Job实现**:
```java
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;

/**
 * 店铺等级评定定时任务
 * 参考: org.jeecg.modules.member.job.MemberStoreSubsidyStatusJob
 */
@Component
@Slf4j
public class StoreLevelAssessmentJob implements Job {
    
    @Autowired
    private StoreLevelAssessmentService assessmentService;
    
    @Override
    public void execute(JobExecutionContext context) throws JobExecutionException {
        log.info("开始执行店铺等级评定任务");
        
        try {
            String assessmentPeriod = getLastMonth(); // 格式: YYYY-MM
            
            // 获取所有符合评估条件的店铺
            List<String> eligibleStores = getEligibleStores();
            log.info("本次评定店铺数量: {}", eligibleStores.size());
            
            // 分批处理，避免长时间锁定
            int batchSize = 50;
            AtomicInteger successCount = new AtomicInteger(0);
            AtomicInteger failCount = new AtomicInteger(0);
            
            for (int i = 0; i < eligibleStores.size(); i += batchSize) {
                int end = Math.min(i + batchSize, eligibleStores.size());
                List<String> batch = eligibleStores.subList(i, end);
                
                try {
                    processBatch(batch, assessmentPeriod, successCount, failCount);
                } catch (Exception e) {
                    log.error("批次处理异常，批次范围[{}-{}]", i, end, e);
                    failCount.addAndGet(batch.size());
                }
            }
            
            log.info("店铺等级评定任务执行完成，成功：{}，失败：{}", successCount.get(), failCount.get());
        } catch (Exception e) {
            log.error("店铺等级评定任务执行异常", e);
            throw new JobExecutionException(e);
        }
    }
}
```

**验收标准**:
- ✅ 定时任务配置正确，能按计划执行
- ✅ 批量处理逻辑正确，支持分批处理
- ✅ 异常处理机制完善
- ✅ 日志记录详细完整
- ✅ 重试机制有效

#### 3.3.2 管理后台配置界面
**目标**: 实现定时任务的管理界面

**任务清单**:
- ✅ 创建定时任务配置页面
- ✅ 实现任务执行状态查看
- ✅ 实现手动触发功能
- ✅ 实现任务执行历史记录
- ✅ 添加任务监控告警功能

**验收标准**:
- ✅ 配置界面功能完整
- ✅ 手动触发功能正常
- ✅ 执行历史记录准确
- ✅ 监控告警功能有效

### 阶段4：管理端配置界面 (3-4天)

#### 3.4.1 等级配置管理页面
**目标**: 提供店铺等级配置的管理界面

**任务清单**:
- ✅ 创建等级配置列表页面
  - 支持分页查询
  - 支持按等级名称、状态筛选
  - 显示等级值、名称、条件、奖励等信息
- ✅ 创建等级配置新增/编辑页面
  - 表单验证（等级值唯一性、数值范围等）
  - 支持富文本编辑等级说明
- ✅ 实现等级配置的启用/禁用功能
- ✅ 添加批量操作功能

**页面结构**:
```
/system/store-level-config/
├── index.vue                 # 列表页面
├── modules/
│   ├── StoreLevelConfigModal.vue  # 新增/编辑弹窗
│   └── StoreLevelConfigForm.vue   # 表单组件
```

**验收标准**:
- ✅ 页面布局美观，操作流畅
- ✅ 表单验证完善，错误提示清晰
- ✅ 数据操作功能正常
- ✅ 权限控制正确

#### 3.4.2 等级变更日志查看页面
**目标**: 提供等级变更历史的查询和监控功能

**任务清单**:
- ✅ 创建等级变更日志列表页面
  - 支持按店铺、时间段、变更类型筛选
  - 显示变更详情、业绩数据、奖励信息
  - 支持导出功能
- ✅ 创建等级变更详情页面
- ✅ 添加统计图表（等级分布、变更趋势等）

**验收标准**:
- ✅ 查询功能完善，性能良好
- ✅ 数据展示清晰，便于分析
- ✅ 导出功能正常

#### 3.4.3 手动调整功能
**目标**: 提供特殊情况下的手动等级调整功能

**任务清单**:
- [ ] 创建手动调整等级页面
  - 店铺选择器
  - 等级选择器
  - 调整原因输入
  - 操作确认机制
- [ ] 实现手动调整的后端逻辑
- [ ] 添加操作审计日志

**验收标准**:
- [ ] 手动调整功能安全可靠
- [ ] 操作日志记录完整
- [ ] 权限控制严格

### 阶段5：商家端展示功能 (2-3天)

#### 3.5.1 等级展示组件
**目标**: 在商家端小程序中展示店铺等级信息

**任务清单**:
- [ ] 创建等级展示组件
  ```vue
  <!-- components/StoreLevelDisplay.vue -->
  <template>
    <view class="store-level-display">
      <view class="level-badge">
        <text class="level-name">{{ levelInfo.levelName }}</text>
        <text class="level-value">{{ levelInfo.levelValue }}级</text>
      </view>
      <view class="level-description">
        {{ levelInfo.description }}
      </view>
    </view>
  </template>
  ```
- [ ] 在"我的店铺"页面集成等级展示
- [ ] 在"个人中心"页面添加等级标识
- [ ] 实现等级图标和样式设计

**验收标准**:
- [ ] 等级信息显示准确
- [ ] 界面设计美观，符合整体风格
- [ ] 在不同设备上显示正常

#### 3.5.2 等级变更通知
**目标**: 当店铺等级发生变化时通知商家

**任务清单**:
- [ ] 实现站内消息通知
- [ ] 实现短信通知（可选）
- [ ] 创建等级变更详情页面
- [ ] 在小程序中添加消息中心入口

**通知内容模板**:
```
恭喜！您的店铺等级已升级至【一级认证店铺】
- 评定周期：2025年04月
- 月度业绩：¥12,580.00
- 合作企业家：15人
- 获得奖励：¥200.00已发放至店铺余额
点击查看详情 >
```

**验收标准**:
- [ ] 通知及时准确
- [ ] 通知内容清晰易懂
- [ ] 通知渠道稳定可靠

### 阶段6：测试与优化 (2-3天)

#### 3.6.1 功能测试
**目标**: 全面测试系统功能

**测试用例**:
- [ ] 等级配置管理功能测试
- [ ] 等级评定算法测试（各种边界情况）
- [ ] 定时任务执行测试
- [ ] 奖励发放测试
- [ ] 管理端界面功能测试
- [ ] 商家端展示功能测试
- [ ] 通知功能测试

#### 3.6.2 性能测试
**目标**: 验证系统性能指标

**测试项目**:
- [ ] 批量评定性能测试（1000+店铺）
- [ ] 数据库查询性能测试
- [ ] 并发访问测试
- [ ] 内存使用情况测试

#### 3.6.3 安全测试
**目标**: 确保系统安全性

**测试项目**:
- [ ] 权限控制测试
- [ ] 数据验证测试
- [ ] SQL注入防护测试
- [ ] 接口安全测试

**验收标准**:
- [ ] 所有功能测试用例通过
- [ ] 性能指标满足要求
- [ ] 安全测试无高危漏洞

## 4. 技术实现要点

### 4.1 数据库设计要点
1. **索引优化**: 为查询频繁的字段添加索引
2. **数据类型选择**: 使用合适的数据类型，避免浪费存储空间
3. **约束设计**: 添加必要的唯一约束和外键约束
4. **分区策略**: 考虑对日志表进行分区（按月份）

### 4.2 性能优化策略
1. **查询优化**: 使用合适的查询语句，避免全表扫描
2. **缓存策略**: 对等级配置等相对稳定的数据进行缓存
3. **批量处理**: 使用批量操作减少数据库交互次数
4. **异步处理**: 对非关键路径使用异步处理

### 4.3 安全性考虑
1. **权限控制**: 严格控制管理端的操作权限
2. **数据验证**: 对所有输入数据进行严格验证
3. **操作审计**: 记录所有关键操作的审计日志
4. **敏感信息保护**: 对敏感信息进行适当的加密或脱敏

## 5. 风险评估与应对

### 5.1 技术风险
| 风险项 | 风险等级 | 影响 | 应对措施 |
|--------|----------|------|----------|
| 数据统计性能问题 | 中 | 定时任务执行时间过长 | 1. 优化查询语句<br>2. 添加数据库索引<br>3. 考虑数据预聚合 |
| 并发执行问题 | 中 | 数据不一致 | 1. 使用分布式锁<br>2. 优化事务边界<br>3. 添加重试机制 |
| 大数据量处理 | 低 | 系统响应慢 | 1. 分批处理<br>2. 异步执行<br>3. 监控资源使用 |

### 5.2 业务风险
| 风险项 | 风险等级 | 影响 | 应对措施 |
|--------|----------|------|----------|
| 评定规则理解偏差 | 高 | 功能不符合预期 | 1. 详细的需求确认<br>2. 原型验证<br>3. 分阶段交付 |
| 数据准确性问题 | 高 | 错误的等级评定 | 1. 充分的单元测试<br>2. 数据校验机制<br>3. 人工复核流程 |
| 用户体验问题 | 中 | 商家满意度下降 | 1. 用户体验测试<br>2. 灰度发布<br>3. 快速响应机制 |

### 5.3 项目风险
| 风险项 | 风险等级 | 影响 | 应对措施 |
|--------|----------|------|----------|
| 开发进度延期 | 中 | 上线时间推迟 | 1. 合理的时间估算<br>2. 关键路径管理<br>3. 资源调配 |
| 需求变更 | 中 | 开发成本增加 | 1. 需求冻结机制<br>2. 变更影响评估<br>3. 版本管理 |

## 6. 质量保证

### 6.1 代码质量
- 遵循项目编码规范
- 代码审查机制
- 单元测试覆盖率 ≥ 90%
- 集成测试覆盖主要业务流程

### 6.2 文档质量
- API文档完整准确
- 数据库设计文档
- 部署运维文档
- 用户操作手册

### 6.3 测试质量
- 功能测试用例覆盖率 100%
- 性能测试达标
- 安全测试通过
- 用户验收测试通过

## 7. 部署与上线

### 7.1 环境准备
- [ ] 开发环境配置
- [ ] 测试环境配置
- [ ] 预生产环境配置
- [ ] 生产环境配置

### 7.2 数据迁移
- [ ] 数据表创建脚本
- [ ] 初始化数据脚本
- [ ] 数据迁移验证

### 7.3 上线计划
- [ ] 灰度发布策略
- [ ] 回滚预案
- [ ] 监控告警配置
- [ ] 应急响应流程

## 8. 后续维护

### 8.1 监控指标
- 定时任务执行状态
- 系统性能指标
- 业务数据指标
- 错误日志监控

### 8.2 优化方向
- 算法优化
- 性能调优
- 用户体验改进
- 功能扩展

## 9. 项目里程碑

| 里程碑 | 预计完成时间 | 交付物 |
|--------|--------------|--------|
| 阶段1完成 | 第4天 | 数据表结构、基础服务 |
| 阶段2完成 | 第9天 | 核心评定算法 |
| 阶段3完成 | 第12天 | 定时任务系统 |
| 阶段4完成 | 第16天 | 管理端界面 |
| 阶段5完成 | 第19天 | 商家端功能 |
| 项目完成 | 第22天 | 完整系统上线 |

## 10. 资源需求

### 10.1 人力资源
- 后端开发工程师：1人，15天
- 前端开发工程师：1人，10天
- 测试工程师：1人，5天
- 产品经理：0.5人，全程参与

### 10.2 技术资源
- 开发环境服务器
- 测试环境服务器
- 数据库资源
- 第三方服务（短信通知等）

## 11. 成功标准

1. **功能完整性**: 所有PRD要求的功能都已实现
2. **性能达标**: 系统性能满足预期指标
3. **质量合格**: 通过所有测试用例
4. **用户满意**: 商家和管理员反馈良好
5. **稳定运行**: 上线后系统稳定运行

---

**备注**: 本实施计划基于当前的技术架构和团队能力制定，如有变化请及时调整计划。 