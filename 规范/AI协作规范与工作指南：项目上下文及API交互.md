# AI协作规范与工作指南：项目上下文、API交互及文档协作

## 引言

本文件定义了您（AI助手）在协助我（用户）处理与我的VS Code工作区内各项目相关的开发任务时，所需遵循的核心规范和工作指南。请严格依据本指南提供信息、建议和执行任务，以确保高效和准确的协作。

**核心目标：**
1.  确保您充分理解我的项目环境与技术栈。
2.  规范在客户端与后端服务接口交互场景中的验证流程。
3.  标准化API设计缺陷或需求变更时的沟通方式与建议格式。
4.  明确项目文档的统一管理与协作方式。
5.  提升整体开发效率和团队协作的顺畅度。

**指南内容分为以下主要部分：**
* 第一部分：项目工作区上下文的详细说明。
* 第二部分：API交互的验证原则与问题处理规范。
* 第三部分：文档协作规范。

---

## 第一部分：项目工作区上下文 (Project Workspace Context)

为使您全面了解我的开发环境，以下是我当前VS Code工作区中核心项目的结构和技术详情：

1.  **项目路径：`heartful-mall`**
    * **项目配置名：** `Backend Services (Java/Spring Boot)`
    * **描述：** 后端核心服务，采用Java和Spring Boot技术栈，支撑整个电商平台的业务逻辑和数据管理。
    * **主要技术：** Java, Spring Boot, [相关的数据库技术如MySQL/PostgreSQL], [相关的中间件如Redis/Kafka等，如有]

2.  **项目路径：`heartful-mall-web`**
    * **项目配置名：** `Admin Portal (Vue Frontend)`
    * **描述：** 系统管理控制台，用于平台管理、数据监控、运营支持等，基于Vue.js框架构建的前端应用，与后端服务通过API交互。
    * **主要技术：** Vue.js, Vuex/Pinia, Vue Router, JavaScript/TypeScript, Element Plus/Ant Design Vue [或其他UI库]

3.  **项目路径：`heartful-mall-app`**
    * **项目配置名：** `User Client (Uniapp MiniProgram)`
    * **描述：** 面向C端（最终用户）的小程序客户端，使用Uniapp框架开发，可编译部署到微信小程序等多个平台，提供用户交互界面和业务功能，与后端服务通过API交互。
    * **主要技术：** Uniapp, Vue.js (Uniapp语法核心), [uView UI等Uniapp生态UI库]

4.  **项目路径：`heartful-mall-merchants-pro`**
    * **项目配置名：** `Merchant Client (Uniapp MiniProgram)`
    * **描述：** 面向B端（商户）的小程序客户端，同样基于Uniapp框架开发，提供商户入驻、商品管理、订单处理等功能，与后端服务通过API交互。
    * **主要技术：** Uniapp, Vue.js (Uniapp语法核心), [uView UI等Uniapp生态UI库]

5.  **项目路径：`share-docs`**
    * **项目配置名：** `Project Shared Documents`
    * **描述：** 项目级共享资源文件夹。根据**第三部分：文档协作规范**，此项目是所有项目共用的官方文档贡献与维护中心，包含API文档、需求文档、架构图、设计稿链接、通用配置等。
    * **主要技术/内容：** Markdown, Confluence/Notion链接, Swagger/OpenAPI规范文件, 图片资源, 配置文件样本等。

---

## 第二部分：API交互验证与问题处理规范

此部分规定了在处理涉及前后端API交互的任务时，您应遵循的验证原则和问题反馈流程。

**1. 主动验证原则：**

在协助处理任何面向用户或管理员的前端/客户端项目（具体包括 `Admin Portal (heartful-mall-web)` - 系统管理控制台，`User Client (heartful-mall-app)` - 用户端小程序，以及 `Merchant Client (heartful-mall-merchants-pro)` - 商户端小程序）的需求时，只要涉及到与后端服务 (`heartful-mall` - Backend Services) 的接口交互，都请务必将**主动查阅并核实后端接口的最新源代码（如果可访问）或最准确的API文档（通常位于`share-docs`项目内，或由我直接提供）**作为一项关键的验证步骤予以强调或执行。这样做是为了确保前后端对接口契约的理解保持一致，预防集成风险，并保障数据交互的准确性与健壮性。

**2. 结构化问题反馈与修改建议：**

在完成上述源码/文档核实后，如果发现后端接口存在缺失（例如，当前前端场景需要一个全新的接口）或不完善（例如，现有接口缺少必要的请求参数或未能返回前端所需的关键数据字段）的情况，请**不要仅简单指出问题，而是应主动以如下结构化的方式向我（用户）提出具体的后端API修改建议**。这些建议需要足够详细，以便我能够清晰、完整地将其转达给后端开发团队进行评估和实现。

请严格遵照以下结构化格式提供后端API修改建议：

---
**后端API修改建议报告**

* **1. 场景描述与问题概要：**
    * （请简述当前正在处理的前端页面、组件或核心功能场景，以及在该场景下所遇到的具体后端API问题，例如：接口缺失、数据不足、参数不符等。）

* **2. 问题分类：** （请指明属于以下哪种主要类型）
    * ☐ 需要新增后端接口
    * ☐ 现有后端接口需要调整/增强

---
**(A) 若为“需要新增后端接口”：**

* **3. 建议新增接口详情：**
    * **接口用途描述：** （清晰说明此新接口应实现的核心业务功能或数据服务能力。）
    * **建议HTTP方法：** （例如：GET, POST, PUT, DELETE）
    * **建议端点路径 (Endpoint Path)：** （例如：`/api/v1/module/...` 或 `/api/module/...`，请遵循项目现有API命名规范）
    * **请求参数 (Request Parameters)：**
        * （对于路径参数、查询参数或请求体中的每个参数，请按以下格式说明）
        * `parameter_name_1` (`数据类型`, `是否必需: 是/否`, `来源: Path/Query/Body`): 参数含义和用途描述，以及任何校验规则建议。
        * `parameter_name_2` (`数据类型`, `是否必需: 是/否`, `来源: Path/Query/Body`): 参数含义和用途描述，以及任何校验规则建议。
        * （如有复杂对象作为请求体，可简述其JSON结构）
    * **响应数据结构 (Success Response Body - `application/json`)：**
        * （描述成功响应时的JSON结构，包括各字段的名称、数据类型、是否可空(nullable)和含义。请尽可能提供贴近实际业务的数据示例或说明。）
        * `field_name_1` (`数据类型`, `是否可空: 是/否`): 字段含义和内容示例。
        * `field_name_2` (`数据类型`, `是否可空: 是/否`): 字段含义和内容示例。
        * `nested_object` (`Object`, `是否可空: 是/否`):
            * `sub_field_1` (`数据类型`, `是否可空: 是/否`): 描述。
        * `array_of_items` (`Array<Object>`, `是否可空: 是/否`):
            * `item_id` (`数据类型`, `是否可空: 是/否`): 描述。
            * `item_name` (`数据类型`, `是否可空: 是/否`): 描述。
    * **关键错误场景与建议响应：** （简述一两个关键的、可预见的错误场景，例如参数校验失败、资源未找到、无权限等，以及建议的HTTP状态码和错误响应体结构。）

---
**(B) 若为“现有后端接口需要调整/增强”：**

* **3. 需调整的现有接口：**
    * **HTTP方法与端点路径：** （明确指出是哪个已存在的后端接口。）

* **4. 具体调整内容与理由：**
    * **(a) 如需新增请求参数：**
        * **建议新增的请求参数：**
            * `parameter_name_1` (`数据类型`, `是否必需: 是/否`, `来源: Path/Query/Body`): 参数含义、为何需要此参数以满足前端场景、任何校验规则建议。
    * **(b) 如响应数据缺少关键字段：**
        * **建议在响应中新增的字段：**
            * `field_name_1` (`数据类型`, `是否可空: 是/否`): 字段含义、此字段应包含何种数据、为何前端场景需要此数据。
    * **(c) 如现有参数或字段定义不满足需求：**
        * **需调整的参数/字段：** `parameter_or_field_name`
        * **当前定义问题：** (描述现有定义不适用的原因。)
        * **建议调整为：** (描述新的定义，如数据类型变更、是否可空调整、枚举值更新等。)
    * **(d) 如接口行为或业务逻辑需变更：**
        * **需调整的逻辑描述：** (详细说明接口的哪些行为或处理逻辑需要修改以适应前端需求，例如排序逻辑、过滤条件、权限判断等。)
    * **整体调整理由：** (综合阐述为何进行这些调整能够解决前端当前面临的问题或满足新的功能需求，并评估潜在影响。)
---

请务必运用此结构化反馈机制，以确保API相关问题的沟通既清晰又具备高度的可操作性，从而促进前后端的高效协同。

---

## 第三部分：文档协作规范 (Document Collaboration Guidelines)

为了确保项目信息的集中管理和高效共享，特制定以下关于`share-docs`项目的文档协作规范。

**1. 核心定位与重要性：**
    * `share-docs` (项目配置名：`Project Shared Documents`, 路径：`share-docs`) 项目是本VS Code工作区内所有其他项目（包括 `heartful-mall` 后端服务、`heartful-mall-web` 管理后台、`heartful-mall-app` 用户端小程序、`heartful-mall-merchants-pro` 商户端小程序）的**唯一官方文档贡献、存储与维护中心**。
    * 所有项目成员都应认识到，及时、准确地在此处维护文档是保障项目顺利进行和知识有效传承的关键环节。

**2. 文档范围与维护责任：**
    * **需求文档：** 所有项目（无论是前端还是后端模块）的需求规格说明书、用户故事、业务流程图、原型链接、会议纪要中与需求相关的决议等，均应在 `share-docs` 中创建、更新和追溯。
    * **设计文档：** 包括但不限于API设计规范 (如Swagger/OpenAPI文件或其导出版本)、系统架构图、模块设计文档、数据库设计ER图、UI/UX设计稿的最终版本链接或关键页面截图索引等，也应集中存放于此。
    * **开发与运维文档：** 重要的开发环境配置指南、编码标准与规范、公共库/组件使用说明、第三方服务集成配置与密钥管理（安全前提下）说明、部署流程文档、故障排查手册（FAQ）、性能测试报告摘要等公共技术文档，亦鼓励在此共享和维护。
    * **维护共识：** 所有团队成员（包括但不限于前端开发者、后端开发者、产品经理、项目经理、UI/UX设计师、测试工程师、运维工程师），在产生或获取到任何需要跨团队共享、或对项目具有长期参考价值的文档或信息时，都有责任将其及时、规范地归档和更新到 `share-docs` 的预定目录结构下。

**3. 协作指引：**
    * **统一目录结构：** `share-docs` 内部应共同维护一个清晰、逻辑化、易于理解和查找的目录结构。建议按文档性质（如 `01_需求文档`, `02_设计文档`, `03_API文档`, `04_开发规范`, `05_运维部署`, `06_会议纪要`, `07_培训材料`等）或按项目模块进行合理组织。
    * **命名规范：** 文件和文件夹的命名应具有描述性，并遵循统一的命名约定（例如，使用日期前缀、版本号、模块标识等）。
    * **版本控制：** 强烈建议对 `share-docs` 项目本身也纳入Git等版本控制系统进行管理，以便追踪文档的每一次重要变更历史、方便回溯和协作。
    * **AI 协助文档工作：** 当您（AI助手）协助我处理与文档相关的任务时（例如，根据讨论内容草拟需求点、整理API变更日志、总结会议纪要、或者查询特定规范），请默认这些文档资料来源于或最终应归档于 `share-docs` 项目。在提供建议时，也请积极引导我（用户）将相关的文档成果或重要信息整合进此共享文档库，并提示可能的存放路径或命名方式。

此规范旨在提升团队知识沉淀效率，减少信息孤岛，确保所有成员在任何时候都能方便地访问到最新、最准确、最全面的项目文档资料。

---

## 结论

本指南中提供的项目工作区结构信息、API交互验证与问题处理规范（特别是接口问题反馈的结构化要求），以及文档协作规范，共同构成了您在处理所有相关技术咨询、任务规划、代码审查、内容生成或故障排查请求时必须遵循的基础框架和操作准则。期望您能严格遵守，以提供最高质量的协助。

---

## 关于本指南的说明 (Meta-Instructions for AI)

* **目的：** 本文档本身是为AI助手设定的工作规范，旨在使AI能够更精准、高效地参与到用户的项目中。
* **“项目配置名”与“项目路径”：** 这些字段直接对应用户VS Code工作区中的实际配置，用于准确定位项目。
* **“主要技术”：** 用于帮助AI理解各项目的技术环境，从而给出更相关的建议（例如，代码片段、库推荐等）。
* **“API交互验证与问题处理规范”与“文档协作规范”：** 这些是核心行为准则。特别是结构化API修改建议的格式和文档归档的指引，是AI在特定场景下生成响应时应严格遵循的模板和原则。
* **动态性：** 虽然本指南提供了当前的项目结构和规范，但实际项目中技术选型、API细节及文档实践可能会演进。在接收到用户关于此类变更的更新信息时，请优先采纳最新信息，并理解本指南的精神灵活应用。