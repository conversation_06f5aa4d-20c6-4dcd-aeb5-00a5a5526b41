# 微信小程序邀请海报设计规范

## 目录

1. [概述](#概述)
2. [海报尺寸规范](#海报尺寸规范)
3. [二维码区域设计](#二维码区域设计)
4. [安全区域](#安全区域)
5. [色彩规范](#色彩规范)
6. [文件格式要求](#文件格式要求)
7. [设计注意事项](#设计注意事项)
8. [更新与维护](#更新与维护)

## 概述

本文档为微信小程序邀请海报的设计规范，旨在确保海报在不同设备上的显示效果一致，并提高用户体验。设计师在制作海报时应严格遵循本规范。

## 海报尺寸规范

### 基本尺寸

- **设计尺寸**：660rpx × 1002rpx（微信小程序响应式单位）
- **实际像素**：1320px × 2004px（2倍设计尺寸，确保高清显示）
- **宽高比例**：约 2:3（竖向海报）

### 分辨率要求

- **最低分辨率**：300DPI
- **推荐分辨率**：350DPI 或更高

### 适配方案

- 海报设计采用微信小程序的 rpx 单位，可自动适配不同屏幕尺寸
- 关键视觉元素应放置在安全区域内，避免在不同设备上被裁剪

## 二维码区域设计

### 位置与尺寸

- **位置**：海报底部居中
- **具体参数**：
  - 距离底部：24rpx（48px）
  - 水平居中（通过 CSS `left: 50%; transform: translateX(-50%);` 实现）
- **二维码尺寸**：135rpx × 135rpx（270px × 270px）
- **用户信息条**：位于二维码下方，高度为 35rpx（70px）

### 二维码规格

- **尺寸比例**：二维码宽度应为海报宽度的约 1/5
- **边距要求**：二维码四周应有足够的空白边距（至少二维码宽度的 10%）
- **圆角设置**：8rpx（16px）的圆角边框

### 用户信息条设计

- **高度**：35rpx（70px）
- **背景色**：rgba(0, 0, 0, 0.55)（半透明黑色）
- **圆角**：15rpx（30px）
- **内容**：用户头像（25rpx × 25rpx）+ 用户昵称（单行截断）
- **字体大小**：24rpx（48px）
- **字体颜色**：#FFFFFF（白色）

## 安全区域

为确保海报内容在不同设备上正常显示，设计时应遵循以下安全区域规范：

### 边距要求

- **上边距**：至少 60rpx（120px）
- **左右边距**：至少 60rpx（120px）
- **底部边距**：至少 200rpx（400px），为二维码和用户信息预留足够空间

### 关键内容区域

- **主视觉区域**：上部 60% 区域（约 600rpx 高度）
- **品牌/标识区域**：上部 20% 区域（约 200rpx 高度）
- **营销文案区域**：中部 40% 区域（约 400rpx 高度）
- **二维码预留区域**：底部 20% 区域（约 200rpx 高度）

## 色彩规范

### 色彩模式

- **设计色彩模式**：RGB
- **色彩深度**：8位/通道

### 推荐配色

- **背景色**：建议使用品牌主色或渐变色
- **文字颜色**：确保与背景有足够对比度（至少 4.5:1）
- **二维码区域**：
  - 二维码本身应为黑白色，确保可扫描性
  - 二维码背景应为白色或浅色，提高对比度
  - 用户信息条使用半透明黑色背景（rgba(0, 0, 0, 0.55)）

## 文件格式要求

### 设计源文件

- **格式**：PSD 或 AI
- **图层要求**：分层设计，便于后期修改
- **命名规范**：`poster_YYYYMMDD_主题名称`

### 输出格式

- **格式**：PNG 或 JPG
- **质量**：JPG 格式质量不低于 90%
- **文件大小**：建议控制在 500KB 以内，最大不超过 1MB
- **透明度**：如需透明效果，请使用 PNG 格式

## 设计注意事项

### 品牌一致性

- 海报设计应符合品牌视觉识别系统
- 使用正确的品牌标识、字体和色彩

### 文案要求

- 主标题字体大小不小于 36rpx（72px）
- 副标题/正文字体大小不小于 28rpx（56px）
- 文案简洁明了，突出核心价值主张

### 图片素材

- 使用高质量图片，避免模糊或像素化
- 人物图片应积极正面，符合品牌调性
- 产品图片应清晰展示产品特点

### 二维码区域

- 二维码区域背景应简洁，避免复杂图案干扰扫码
- 确保二维码清晰可辨，不要使用过度装饰的二维码
- 用户信息条应清晰显示用户头像和昵称

## 更新与维护

### 更新周期

- 建议每季度更新一次海报背景设计
- 重大活动或节日可设计专属海报

### 版本控制

- 每次更新应记录版本号和更新日期
- 保留历史版本，便于回溯

### 测试要求

- 每次更新后应在不同机型上测试海报显示效果
- 确保二维码可正常扫描识别

---

*最后更新日期：2024年6月1日*
