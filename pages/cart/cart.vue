<template>
	<view class="cart">
		<template v-if="count > 0">
			<view class="cart-header">
				<view class="cart-title">
					全部商品 <text class="cart-count">{{count}}</text>
				</view>
				<view class="cart-edit" @click="changeEditStatus">
					{{isEdit?'完成': '编辑'}}
				</view>
			</view>

			<view class="cart-content">
				<!-- 平台商品,免单商品,无效商品 -->
				<template v-if="info.others && info.others.length > 0">
					<view v-for="(item,index) in info.others" :key="index">
						<view class="cart-section">
							<view class="cart-section-header">
								<view class="cart-checkbox" @click="togglePtStoreGx(item.key)">
									<image class="checkbox-icon"
										:src="'/static/cart/' + (ptStoreGxAllSelected(item.key) ? 'selected' : 'noselect') + '.png'">
									</image>
								</view>

								<view class="section-title">
									{{keyForTitle(item.key)}}
								</view>
							</view>

							<view class="cart-item" v-for="i in item.list" :key="i.id" hover-class="item-hover">
								<view class="cart-checkbox" @click="toggleStoreGx(i.id)">
									<image class="checkbox-icon"
										:src="'/static/cart/' + (selectCartId.indexOf(i.id) != -1 ? 'selected' : 'noselect') + '.png'">
									</image>
								</view>

								<view class="cart-item-content">
									<commodityWrap rightWrapHeight='140rpx' :infos='i'></commodityWrap>
									<view class="cart-item-quantity">
										<numberInput v-model="i.quantity" :min="1" :max="i.repertory"
											@change="quantityChange($event,i)"></numberInput>
									</view>
								</view>
							</view>
						</view>
					</view>
				</template>

				<!-- 店铺商品 -->
				<template v-if="info.storeGoods && info.storeGoods.length > 0">
					<view v-for="(item,index) in info.storeGoods" :key="index">
						<view class="cart-section">
							<!-- 店铺标题和全选功能 -->
							<view class="cart-section-header">
								<view class="cart-checkbox" @click="toggleDpStoreGx(item.id)">
									<image class="checkbox-icon"
										:src="'/static/cart/' + (dpStoreGxAllSelected(item.id) ? 'selected' : 'noselect') + '.png'">
									</image>
								</view>
								<view class="section-title">
									{{item.storeName}}
								</view>
							</view>

							<!-- 店铺商品列表 -->
							<view class="cart-item" v-for="i in item.myStoreGoods" :key="i.id" hover-class="item-hover">
								<view class="cart-checkbox" @click="toggleStoreGx(i.id)">
									<image class="checkbox-icon"
										:src="'/static/cart/' + (selectCartId.indexOf(i.id) != -1 ? 'selected' : 'noselect') + '.png'">
									</image>
								</view>

								<view class="cart-item-content">
									<commodityWrap rightWrapHeight='140rpx' :infos='i'></commodityWrap>
									<view class="cart-item-quantity">
										<numberInput v-model="i.quantity" :min="1" :max="i.repertory"
											@change="quantityChange($event,i)"></numberInput>
									</view>
								</view>
							</view>
						</view>
					</view>
				</template>
			</view>

			<view class="cart-footer-spacer"></view>

			<view class="cart-footer">
				<view class="cart-footer-left">
					<view class="cart-select-all" @click="toggleSelectAll">
						<image class="checkbox-icon"
							:src="'/static/cart/' + (isAllSelected ? 'selected' : 'noselect') + '.png'">
						</image>
						<text>全选</text>
					</view>

					<view class="cart-total" v-if="!isEdit">
						<text>合计：</text>
						<image src="@/static/index/i_1.png" mode="aspectFit"></image>
						<text class="total-price">{{selectedStoreResCom.price.toFixed(2)}}</text>
					</view>
				</view>

				<view class="cart-footer-right">
					<view v-if="isEdit" class="cart-actions">
						<view class="cart-action-btn favorite" hover-class="btn-hover" @click="collection">
							收藏
						</view>
						<view class="cart-action-btn delete" hover-class="btn-hover" @click="deleteSelect">
							删除
						</view>
					</view>
					<view v-else>
						<view class="cart-checkout-btn" hover-class="btn-hover" @click="goBuy">
							去兑换
						</view>
					</view>
				</view>
			</view>
		</template>

		<template v-else>
			<view class="cart-empty">
				<empty></empty>
				<view class="cart-empty-btn" hover-class="btn-hover" @click="switchTo('/pages/index/index')">
					先去逛逛
				</view>
			</view>
		</template>
	</view>
</template>

<script setup>
	import commodityWrap from './components/commodityWrap/commodityWrap.vue'
	import {
		onShow,
		onLoad
	} from "@dcloudio/uni-app"
	import {
		computed,
		ref
	} from "vue";
	import {
		navTo,
		switchTo
	} from "@/hooks";
	import {
		parseObjToPath
	} from "@/utils";
	import {
		userStore
	} from "@/store";
	let isEdit = ref(false);
	const users = userStore()
	//页面信息
	let info = ref({});
	//选中的购物车id(数组形式)
	let selectCartId = ref([]);
	//购物车商品总数(商品总数中的购买数量不算入内)
	let count = ref(0)
	// 计算是否全选
	const isAllSelected = computed(() => {
		// 计算所有可选商品的总数
		let totalSelectableItems = 0;

		// 计算店铺商品数量
		if (info.value.storeGoods && info.value.storeGoods.length > 0) {
			for (let store of info.value.storeGoods) {
				totalSelectableItems += store.myStoreGoods.length;
			}
		}

		// 计算其他商品数量
		if (info.value.others && info.value.others.length > 0) {
			for (let category of info.value.others) {
				totalSelectableItems += category.list.length;
			}
		}

		// 判断是否全选
		return selectCartId.value.length == totalSelectableItems && totalSelectableItems > 0;
	});

	//返回合计价格和选中的商品信息
	const selectedStoreResCom = computed(() => {
		let price = 0;
		let selectStoreInfoRes = []
		if (info.value.storeGoods && info.value.storeGoods.length > 0) {
			info.value.storeGoods.forEach((item, index) => {
				item.myStoreGoods.forEach((i, d) => {
					let has = selectCartId.value.indexOf(i.id) != -1;
					if (has) {
						price += i.price * i.quantity
						selectStoreInfoRes.push(i)
					}
				})
			})
		}
		if (info.value.others && info.value.others.length > 0) {
			info.value.others.forEach((item, index) => {
				item.list.forEach((i, d) => {
					let has = selectCartId.value.indexOf(i.id) != -1;
					if (has) {
						price += i.price * i.quantity
						selectStoreInfoRes.push(i)
					}
				})
			})
		}

		return {
			price,
			selectStoreInfoRes
		}
	})

	//改变购物车商品数量
	async function quantityChange(count, item) {
		try {
			uni.showLoading({
				mask: true
			})
			await uni.http.post(uni.api.updateCarGood, {
				id: item.id,
				quantity: count,
				// item.quantity
			})
			uni.hideLoading()
		} catch (e) {
			refresh()
			//TODO handle the exception
		}

	}

	//删除
	async function deleteSelect() {
		if (selectCartId.value.length <= 0) {
			uni.showToast({
				title: '请先选择商品~',
				icon: "none"
			})
			return;
		}
		uni.showModal({
			title: '删除商品',
			content: '确认要从购物车删除这些商品吗',
			success: async function(res) {
				if (res.confirm) {
					let data = await uni.http.post(uni.api.delCarGood, {
						ids: selectCartId.value.join(',')
					})
					uni.showToast({
						title: '删除成功~',
						icon: "none"
					})
					setTimeout(() => {
						refresh()
					}, 500)

				} else if (res.cancel) {

				}
			}
		})
	}
	//收藏
	async function collection() {
		if (selectCartId.value.length <= 0) {
			uni.showToast({
				title: '请先选择商品~',
				icon: "none"
			})
			return;
		}
		uni.showLoading({
			mask: true
		})
		let requestInfo = {
			ids: selectCartId.value.join(','),
			goodIds: selectedStoreResCom.value.selectStoreInfoRes.map(item => item.goodId).join(','),
			isPlatforms: selectedStoreResCom.value.selectStoreInfoRes.map(item => item.isPlatform).join(','),
		}
		let data = await uni.http.post(uni.api.addMemberGoodsCollectionByGoodIds, requestInfo)
		uni.showToast({
			title: '收藏成功~',
			icon: "none"
		})
	}
	//编辑
	function changeEditStatus() {
		isEdit.value = !isEdit.value
	}
	//去结算
	function goBuy() {
		if (selectCartId.value.length <= 0) {
			uni.showToast({
				title: '请选择商品~',
				icon: "none"
			})
			return;
		}
		let obj = {
			ids: selectCartId.value.join(','),
			type: 2
		}
		navTo(`/pages/confirmOrder/confirmOrder${parseObjToPath(obj)}`)
	}
	//点击商品旁的勾选
	function toggleStoreGx(id) {
		let nowIdx = selectCartId.value.indexOf(id);
		if (nowIdx != -1) {
			selectCartId.value.splice(nowIdx, 1)
		} else {
			selectCartId.value.push(id);
		}
	}
	//平台/免单/无效商品是否全选 true 全选 false 未全选  key对应商品类型的键名
	function ptStoreGxAllSelected(key) {
		let list = info.value.others && info.value.others.find(item => item.key == key).list || []
		return list.every(item => selectCartId.value.indexOf(item.id) != -1)
	}
	//对应店铺的店铺商品是否全选   true 全选 false 未全选
	function dpStoreGxAllSelected(storeId) {
		const store = info.value.storeGoods.find(item => item.id === storeId);
		if (!store || !store.myStoreGoods || store.myStoreGoods.length === 0) return false;
		return store.myStoreGoods.every(item => selectCartId.value.indexOf(item.id) !== -1);
	}
	//点击平台/免单/无效商品旁的勾选 key对应商品类型的键名 forceGX:点击是否一定全选
	function togglePtStoreGx(key, forceGX = false) {
		let result = ptStoreGxAllSelected(key);
		let list = info.value.others && info.value.others.find(item => item.key == key).list || []
		for (let item of list) {
			let nowIndex = selectCartId.value.indexOf(item.id)
			if (result && !forceGX) {
				selectCartId.value.splice(nowIndex, 1)
			} else {
				if (nowIndex == -1) {
					selectCartId.value.push(item.id)
				}
			}
		}
	}
	//点击对应店铺旁的勾选  storeId:店铺ID forceGX:点击是否一定全选
	function toggleDpStoreGx(storeId, forceGX = false) {
		const store = info.value.storeGoods.find(item => item.id === storeId);
		if (!store || !store.myStoreGoods || store.myStoreGoods.length === 0) return;

		let result = dpStoreGxAllSelected(storeId);
		let nowDpStoreGoods = store.myStoreGoods;

		for (let item of nowDpStoreGoods) {
			let nowIndex = selectCartId.value.indexOf(item.id)
			if (result && !forceGX) {
				selectCartId.value.splice(nowIndex, 1)
			} else {
				if (nowIndex == -1) {
					selectCartId.value.push(item.id)
				}
			}
		}
	}
	//点击全选
	function toggleSelectAll() {
		if (isAllSelected.value) {
			// 如果已全选，则取消全选
			selectCartId.value = []
		} else {
			// 否则全选所有商品
			if (info.value.storeGoods && info.value.storeGoods.length > 0) {
				for (let item of info.value.storeGoods) {
					toggleDpStoreGx(item.id, true)
				}
			}
			if (info.value.others && info.value.others.length > 0) {
				for (let item of info.value.others) {
					togglePtStoreGx(item.key, true)
				}
			}
		}
	}
	//键名对应的标题名
	function keyForTitle(key) {
		let title = ''
		switch (key) {
			case 'goods':
				title = '平台商品'
				break;
			case 'disableGoods':
				title = '失效商品'
				break;
			case 'marketingFreeGoods':
				title = '免单商品'
				break;
			default:
				title = ''
				break;
		}
		return title
	}


	onLoad((options) => {
		// 处理快捷结算跳转
		if (options.fromQuickCheckout === '1') {
			handleQuickCheckoutSelection()
		}
	})

	onShow(() => {
		refresh()
	})

	// 处理快捷结算的商品选择
	const handleQuickCheckoutSelection = () => {
		try {
			const checkoutData = uni.getStorageSync('quickCheckoutData')
			if (checkoutData && checkoutData.fromQuickCheckout) {
				// 检查数据是否过期（5分钟内有效）
				const now = Date.now()
				if (now - checkoutData.timestamp < 5 * 60 * 1000) {
					// 延迟执行，等待购物车数据加载完成
					setTimeout(() => {
						selectQuickCheckoutItems(checkoutData.selectedItems)
						// 清除存储的数据
						uni.removeStorageSync('quickCheckoutData')
					}, 500)
				} else {
					// 数据过期，清除
					uni.removeStorageSync('quickCheckoutData')
				}
			}
		} catch (error) {
			console.error('处理快捷结算选择失败:', error)
		}
	}

	// 选中快捷结算的商品
	const selectQuickCheckoutItems = (selectedItems) => {
		try {
			if (!selectedItems || selectedItems.length === 0) return
			
			// 清空当前选择
			selectCartId.value = []
			
			// 根据购物车项ID选中商品
			selectedItems.forEach(item => {
				if (item.cartItemId) {
					// 在购物车数据中查找对应的商品
					let found = false
					
					// 在店铺商品中查找
					if (info.value.storeGoods && info.value.storeGoods.length > 0) {
						for (let store of info.value.storeGoods) {
							for (let cartItem of store.myStoreGoods) {
								if (cartItem.id === item.cartItemId) {
									selectCartId.value.push(cartItem.id)
									found = true
									break
								}
							}
							if (found) break
						}
					}
					
					// 在其他商品中查找
					if (!found && info.value.others && info.value.others.length > 0) {
						for (let category of info.value.others) {
							for (let cartItem of category.list) {
								if (cartItem.id === item.cartItemId) {
									selectCartId.value.push(cartItem.id)
									found = true
									break
								}
							}
							if (found) break
						}
					}
				}
			})
			
			// 显示选中结果提示
			if (selectCartId.value.length > 0) {
				uni.showToast({
					title: `已选中 ${selectCartId.value.length} 件商品`,
					icon: 'none',
					duration: 2000
				})
			}
			
		} catch (error) {
			console.error('选中快捷结算商品失败:', error)
		}
	}

	async function refresh() {
		if (!users.token) {
			count.value = 0
			return;
		}
		let {
			data
		} = await uni.http.get(uni.api.getCarGoodByMemberId);
		let results = {
			storeGoods: [], //店铺列表
			others: [] //其他商品 包括平台,免单专区,无效商品
		}
		for (let key of Object.keys(data.result)) {
			if (key == 'storeGoods') {
				// 根据 storeManageId 对店铺商品进行分组
				const storeGroupMap = {};

				// 遍历原始店铺数据，按 storeManageId 分组
				data.result.storeGoods.forEach(store => {
					if (!store.storeManageId) return;

					if (!storeGroupMap[store.storeManageId]) {
						// 创建新的店铺分组
						storeGroupMap[store.storeManageId] = {
							id: store.storeManageId,
							storeName: store.storeName,
							storeManageId: store.storeManageId,
							myStoreGoods: [...store.myStoreGoods]
						};
					} else {
						// 将商品添加到已存在的分组中
						storeGroupMap[store.storeManageId].myStoreGoods = [
							...storeGroupMap[store.storeManageId].myStoreGoods,
							...store.myStoreGoods
						];
					}
				});

				// 将分组后的数据转换为数组
				results.storeGoods = Object.values(storeGroupMap);
			} else {
				if (data.result[key].length > 0) {
					results.others.push({
						key,
						list: data.result[key]
					})
				}
			}
		}
		info.value = results;
		selectCartId.value = []
		let {
			data: countRes
		} = await uni.http.get(uni.api.findCarGoods);
		count.value = countRes.result
	}
</script>

<style lang="scss">
	page {
		background-color: #f8f8f8;
	}

	.cart {
		width: 100%;
		padding-bottom: 120rpx;
	}

	/* 头部样式 */
	.cart-header {
		width: 100%;
		height: 88rpx;
		background: #FFFFFF;
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 0 30rpx;
		margin-bottom: 20rpx;
		box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
	}

	.cart-title {
		font-size: 28rpx;
		color: #222222;
		font-weight: 500;
		display: flex;
		align-items: center;
	}

	.cart-count {
		color: #22A3FF;
		margin-left: 8rpx;
		font-weight: 600;
	}

	.cart-edit {
		font-size: 28rpx;
		color: #666666;
		padding: 10rpx 20rpx;
	}

	/* 内容区域样式 */
	.cart-content {
		padding: 0 20rpx;
	}

	.cart-section {
		background: #FFFFFF;
		border-radius: 16rpx;
		margin-bottom: 20rpx;
		overflow: hidden;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
	}

	.cart-section-header {
		display: flex;
		align-items: center;
		padding: 20rpx;
		border-bottom: 1rpx solid #F5F5F5;
	}

	.section-title {
		font-size: 26rpx;
		color: #333333;
		font-weight: 600;
	}

	.cart-checkbox {
		padding: 10rpx;
		margin-right: 10rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.checkbox-icon {
		width: 36rpx;
		height: 36rpx;
		transition: all 0.2s ease;
	}

	.cart-item {
		display: flex;
		align-items: flex-start;
		padding: 20rpx;
		border-bottom: 1rpx solid #F5F5F5;
		transition: all 0.2s ease;
	}

	.cart-item:last-child {
		border-bottom: none;
	}

	.item-hover {
		background-color: #F9F9F9;
	}

	.cart-item-content {
		flex: 1;
		position: relative;
	}

	.cart-item-quantity {
		position: absolute;
		right: 0;
		bottom: 14rpx;
	}

	/* 底部结算栏样式 */
	.cart-footer-spacer {
		height: 100rpx;
	}

	.cart-footer {
		position: fixed;
		bottom: 0;
		left: 0;
		width: 100%;
		height: 100rpx;
		background: #FFFFFF;
		box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 0 30rpx;
		z-index: 10;
	}

	.cart-footer-left {
		display: flex;
		align-items: center;
	}

	.cart-select-all {
		display: flex;
		align-items: center;
		font-size: 28rpx;
		color: #333333;
		margin-right: 30rpx;

		.checkbox-icon {
			margin-right: 10rpx;
		}
	}

	.cart-total {
		display: flex;
		align-items: center;
		font-size: 28rpx;
		color: #333333;

		image {
			width: 30rpx;
			height: 30rpx;
			margin: 0 4rpx;
		}

		.total-price {
			color: #E6A600;
			font-size: 36rpx;
			font-weight: 600;
		}
	}

	.cart-footer-right {
		display: flex;
		align-items: center;
	}

	.cart-actions {
		display: flex;
		align-items: center;
	}

	.cart-action-btn {
		width: 140rpx;
		height: 70rpx;
		border-radius: 35rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 28rpx;
		transition: all 0.2s ease;
		margin-left: 20rpx;
	}

	.cart-action-btn.favorite {
		background: #FFFFFF;
		border: 1rpx solid #999999;
		color: #333333;
	}

	.cart-action-btn.delete {
		background: #FF4D4F;
		color: #FFFFFF;
	}

	.cart-checkout-btn {
		width: 180rpx;
		height: 70rpx;
		background: linear-gradient(135deg, #22A3FF, #1A69D1);
		border-radius: 35rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 28rpx;
		color: #FFFFFF;
		transition: all 0.2s ease;
		box-shadow: 0 4rpx 8rpx rgba(34, 163, 255, 0.2);
	}

	.btn-hover {
		transform: scale(0.95);
		opacity: 0.9;
	}

	/* 空购物车样式 */
	.cart-empty {
		padding: 50rpx 30rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
	}

	.cart-empty-btn {
		width: 200rpx;
		height: 70rpx;
		background: #FFFFFF;
		border: 1rpx solid #333333;
		border-radius: 35rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 28rpx;
		color: #333333;
		margin-top: 30rpx;
		transition: all 0.2s ease;
	}
</style>