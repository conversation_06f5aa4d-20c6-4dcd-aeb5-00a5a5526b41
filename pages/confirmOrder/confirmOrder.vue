<template>
	<view class="confirmOrder" v-if="resultList.length">
		<view class="confirmOrder-commonWrap confirmOrder-address" @click="toAddAddress">
			<view class="confirmOrder-address-left" v-if="info.memberShippingAddress.linkman">
				<view class="confirmOrder-commonWrap-title">
					{{ info.memberShippingAddress.linkman }} {{ info.memberShippingAddress.phone }}
				</view>
				<view class="confirmOrder-address-left-ad">
					{{ info.memberShippingAddress.areaExplan }}{{ info.memberShippingAddress.areaAddress }}
				</view>
			</view>
			<view class="confirmOrder-address-left" v-else>
				请添加收货地址
			</view>
			<image class="confirmOrder-address-arrow" src="@/static/right-arrow-black.png" mode=""></image>
		</view>
		<view style="margin-bottom: 20rpx;" v-for="(item, index) in resultList" :key="index">
			<view class="confirmOrder-commonWrap confirmOrder-store" style="margin-bottom: 0; box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);">
				<view class="confirmOrder-store-top">
					<view>
						{{ item.title }}
					</view>
					<view v-if="storeTypeList?.length">
						{{storeTypeList.find(i=>item.storeType === i.value)?.title}}
					</view>
				</view>
				<view class="confirmOrder-store-cnt" v-for="it in item.list" :key="it.id">
					<image class="confirmOrder-store-cnt-img" :src="imgUrl + parseImgurl(it.mainPicture)?.[0]" mode="aspectFill">
					</image>
					<view class="confirmOrder-store-cnt-right">
						<view class="confirmOrder-store-cnt-right-name">
							{{it.goodName}}
						</view>
						<view class="confirmOrder-store-cnt-right-spec">
							规格: {{it.specification || it.goodSpecification}}
						</view>
						<view class="confirmOrder-store-cnt-right-spc">
							<view class="confirmOrder-store-cnt-right-spc-qty">
								数量: {{ it.quantity }}
							</view>
							<view class="confirmOrder-store-cnt-right-spc-lv">
								<image src="@/static/index/i_1.png" mode="aspectFit"></image>
								{{ it.price }}
							</view>
						</view>
					</view>
				</view>

				<!-- 店铺补助金抵扣信息 -->
				<view class="confirmOrder-store-subsidy" v-if="item.availableStoreSubsidyAmount > 0 || item.storeSubsidyDeduction > 0">
					<view class="confirmOrder-store-subsidy-row">
						<view class="confirmOrder-store-subsidy-label">商品小计：</view>
						<view class="confirmOrder-store-subsidy-amount">¥{{item.originalPrice || item.totalPrice}}</view>
					</view>
					<view class="confirmOrder-store-subsidy-row" v-if="item.shareDiscountAmount > 0">
						<view class="confirmOrder-store-subsidy-label">推荐人优惠：</view>
						<view class="confirmOrder-store-subsidy-deduction">-¥{{item.shareDiscountAmount}}</view>
					</view>
					<view class="confirmOrder-store-subsidy-row" v-if="item.storeSubsidyDeduction > 0">
						<view class="confirmOrder-store-subsidy-label">店铺补助金抵扣：</view>
						<view class="confirmOrder-store-subsidy-deduction">-¥{{item.storeSubsidyDeduction}}</view>
					</view>
					<view class="confirmOrder-store-subsidy-row final">
						<view class="confirmOrder-store-subsidy-label">实际支付：</view>
						<view class="confirmOrder-store-subsidy-final">¥{{calculateStoreFinalPrice(item)}}</view>
					</view>
					<view class="confirmOrder-store-subsidy-tip" v-if="item.availableStoreSubsidyAmount > 0 && item.nearestExpireTime">
						<text class="tip-text">您在本店有 ¥{{item.availableStoreSubsidyAmount}} 补助金可用</text>
						<text class="tip-expire" v-if="item.nearestExpireTime">，有效期至 {{formatExpireTime(item.nearestExpireTime)}}</text>
					</view>
				</view>

				<view class="confirmOrder-store-bz">
					<view class="confirmOrder-store-bz-left">
						订单备注
					</view>
					<view class="confirmOrder-store-bz-right">
						<input style="text-align: right; height: 60rpx;" type="text" v-model="item.message"
							placeholder="选填，填写内容和卖家协商确认" />
					</view>
				</view>

			</view>
			<view class="confirmOrder-reference" v-if="item.commissionAmount > 0">
				<view class="confirmOrder-reference-top">
					<view class="confirmOrder-reference-top-label">
						推荐人
					</view>
					<view class="confirmOrder-reference-top-left">
						<view class="confirmOrder-reference-top-left-avatar">
							<image :src="item.promoterInfo?.avatar" mode="aspectFill"></image>
						</view>
						<view class="confirmOrder-reference-top-left-info">
							<text class="nickname">{{item.promoterInfo?.nickname}}</text>
						</view>
					</view>
					<view class="confirmOrder-reference-top-right" @click="toPromoterDetail(item.promoterInfo)">
						<text>参与推荐</text>
						<image src="@/static/right-arrow-black.png" mode="aspectFit"></image>
					</view>
				</view>
				<view class="confirmOrder-reference-bottom">
					<view class="confirmOrder-reference-bottom-left">
						推荐预计可得
					</view>
					<view class="confirmOrder-reference-bottom-right">
						<text class="amount">{{item.commissionAmount}}</text>
						<text class="unit">助力</text>
					</view>
				</view>
			</view>

		</view>


		<view class="confirmOrder-operation">
			<view class="confirmOrder-operation-left">
				<view class="confirmOrder-operation-left-top">
					<view class="total-label">应付</view>
					<image src="@/static/index/i_1.png" mode="aspectFit"></image>
					<text class="total-amount">{{ canFreight ? allTotalPrice : '-.--' }}</text>
				</view>
				<view v-if="info.totalShareDiscountAmount > 0" class="confirmOrder-operation-left-discount">
					<view class="discount-label">推荐人优惠</view>
					<view class="discount-amount">-{{info.totalShareDiscountAmount}}</view>
				</view>
				<view v-if="info.totalStoreSubsidyDeduction > 0" class="confirmOrder-operation-left-subsidy">
					<view class="subsidy-label">店铺补助金抵扣</view>
					<view class="subsidy-amount">-{{info.totalStoreSubsidyDeduction}}</view>
				</view>
			</view>
			<view class="confirmOrder-operation-right" @click='showPayAboutModalRef'>
				立即兑换
			</view>
		</view>

		<payAboutModal ref='payAboutModalRef' :info='info' @toBuy="toOrder"></payAboutModal>
	</view>
</template>

<script setup>
	import {
		clone,
		getSafeBottom,
		parseObjToPath,
		parseImgurl
	} from '@/utils';
	import {
		navTo,
		redTo,
		toUpPage
	} from '@/hooks';
	import {
		cashier
	} from '@/hooks/cashier.js'
	import {
		computed,
		nextTick,
		reactive,
		ref,
		watch
	} from 'vue';
	import {
		frontSetting,
		userStore
	} from '@/store';
	import {
		onLoad,
		onUnload,
		onShow
	} from '@dcloudio/uni-app';
	const tabList = ['快递寄送', '到店自取'];
	const users = userStore()
	const payAboutModalRef = ref()
	let options = ref({});
	let info = ref({});
	let storeTypeList = ref([]);
	const imgUrl = uni.env.IMAGE_URL;
	let tabSelectIndex = ref(0);
	
	// 获取安全区域高度
	const safeBottom = getSafeBottom();
	
	const {
		payResult
	} = cashier()
	//记录点击选择了在resultList中的哪个索引的优惠券
	let selectCouponIndex = ref('');
	//记录同城自提的状态(暂时只记录id)
	let recordLocalPickUpList = ref([]);
	//记录同城配送的状态(暂时只记录id)
	let recordLocalDeliveryList = ref([]);
	//获取店铺类型字典
	async function getStoreTypeDicts() {
		let {
			data
		} = await uni.http.get(`${uni.api.getDicts}?code=store_type`);
		storeTypeList.value = data.result || []
	}
	const integralValue = frontSetting().setting.integralValue;
	//整合平台商品和店铺商品到同一个list中 方便渲染
	const resultList = computed(() => {
		let list = [];
		let infoRes = info.value;
		if (infoRes.goods && infoRes.goods.goods) {
			list.push(
				reactive({
					...infoRes.goods,
					title: '平台商品',
					list: infoRes.goods.goods,
				})
			);
		}
		if (infoRes.storeGoods && infoRes.storeGoods.length > 0) {
			for (let item of infoRes.storeGoods) {
				list.push(
					reactive({
						...item,
						title: item.storeName,
						list: item.myStoreGoods,
					})
				);
			}
		}
		return list;
	});
	//跳转收银台的所需参数
	let toCashierCs = ref({});
	//确认订单接口和立即购买到确认订单接口所需参数(当有店铺折扣券时带入此参数)
	let rebateOrder = computed(() => {
		let jsons = {
			storeGoods: [],
		};
		if (!toCashierCs.value.orderJson) {
			return '';
		}
		//从 toCashierCs 中找出isNomal = 2的列表并返回
		jsons.storeGoods = toCashierCs.value.orderJson.storeGoods;
		return JSON.stringify(jsons);
	});
	//选择订单方式
	function changeTabSelectIndex(i) {
		tabSelectIndex.value = i;
	}
	//判断当前是否可配送
	const canFreight = computed(() => {
		if (resultList.value.length <= 0) return false;
		return resultList.value.every(item => item.freight != -1 && item.list.every(i => i.opinion != '0'));
	});

	//订单总价格
	const allTotalPrice = computed(() => {
		//将所有的商品类型价格相加
		let allTotalPrice = 0;
		resultList.value.forEach(item => {
			allTotalPrice += resultTotalPrice(item);
		});
		return allTotalPrice.toFixed(2);
	});



	//判断是否存在优惠券（平台和店铺合在一起）
	function returnCouponConFromItem(item) {
		return ('goods' in item && findCouponFromPt(item)?.[0]) || ('myStoreGoods' in item && findCouponFromStore(item)?.[
			0
		]) || '';
	}

	//返回券数量
	function returnCouponCountsConFromItem(item) {
		let res = ('goods' in item && findCouponFromPt(item)) || ('myStoreGoods' in item && findCouponFromStore(item));
		return (res && res.length) || 0;
	}

	//改变同城自提状态
	function changeZtStatus(item, itemIndex) {
		//将使用同城自提加入/删除到数组中
		let nowIdx = recordLocalPickUpList.value.indexOf(item.id);
		if (nowIdx == -1) {
			recordLocalPickUpList.value.push(item.id);
			toCashierCs.value.orderJson.storeGoods[itemIndex].distribution = 1;
			if (recordLocalDeliveryList.value.indexOf(item.id) > -1) {
				recordLocalDeliveryList.value.splice(recordLocalDeliveryList.value.indexOf(item.id), 1);
			}
		} else {
			recordLocalPickUpList.value.splice(nowIdx, 1);
			toCashierCs.value.orderJson.storeGoods[itemIndex].distribution = 0;
		}
		refresh();
	}

	// 改变同城配送状态
	function changePsStatus(item, itemIndex) {
		let nowIdx = recordLocalDeliveryList.value.indexOf(item.id);
		if (nowIdx == -1) {
			recordLocalDeliveryList.value.push(item.id);
			toCashierCs.value.orderJson.storeGoods[itemIndex].distribution = 2;
			if (recordLocalPickUpList.value.indexOf(item.id) > -1) {
				recordLocalPickUpList.value.splice(recordLocalPickUpList.value.indexOf(item.id), 1);
			}
		} else {
			recordLocalDeliveryList.value.splice(nowIdx, 1);
			toCashierCs.value.orderJson.storeGoods[itemIndex].distribution = 0;
		}
		refresh();
	}

	// 判断当前对应的店铺订单是否可以进行同城配送
	function getDeliveryStatus(item) {
		if (!item.myStoreGoods) {
			return false;
		}
		let ableDelivery = item.myStoreGoods.filter(it => it.distribution.indexOf('2') > -1);
		return ableDelivery.length == item.myStoreGoods.length && item.myStoreGoods.length > 0 && item
			.distributionStatus == 1;
	}

	//去选择优惠券
	function toSelectCoupon(index) {
		//当前点击对应的item（根据resultList）
		let nowItem = resultList.value[index];
		if (nowItem.discounts.length > 0) {
			//从toCashierCs中找到对应券id在找到对应券
			let selectCoupon = {};
			if ('goods' in nowItem) {
				//如果是平台
				selectCoupon = findCouponFromPt(nowItem);
			}
			if ('myStoreGoods' in nowItem) {
				//如果是店铺
				selectCoupon = findCouponFromStore(nowItem);
			}
			if (!selectCoupon || selectCoupon.length <= 0) {
				selectCoupon = {};
			}
			selectCouponIndex.value = index;
			navTo('/pages/availableCoupon/availableCoupon', () => {
				setTimeout(() => {
					//延迟500ms 窗口动画时间
					//触发监听事件,将优惠券列表传给availableCoupon页面
					uni.$emit('getSelectCouponList', {
						list: nowItem.discounts,
						selectCoupon,
					});
				}, 500);
			});
		}
	}
	//店铺中找到对应的券
	function findCouponFromStore(nowItem) {
		if (!toCashierCs.value.orderJson) {
			return {};
		}
		let results = nowItem.discounts?.filter(i => {
			let resDisId = toCashierCs.value.orderJson.storeGoods.find(item => item.id == nowItem.id)?.discountId;
			if (!resDisId) return false;
			if (resDisId.split(',').indexOf(i.id) != -1) return true;
		});
		return results;
	}
	//平台中找到对应的券
	function findCouponFromPt(nowItem) {
		if (!toCashierCs.value.orderJson) {
			return {};
		}
		return nowItem.discounts.find(i => toCashierCs.value.orderJson.goods.discountId.split(',').indexOf(i.id) != -1);
	}

	//计算当前店铺中的商品或平台中的商品的最后合计
	function resultTotalPrice(item) {
		//相减计算
		function toSubtract(num1, num2 = '') {
			if (num2 === '') return num1 * 1;
			if (num1 - num2 >= 0) {
				return (num1 - num2).toFixed(2) * 1;
			} else {
				return 0;
			}
		}
		return toSubtract(item.totalPrice, returnCouponConFromItem(item) ? returnCouponConFromItem(item).price : 0);
	}

	function showPayAboutModalRef() {
		// 获取用户余额，使用balance字段
		const userBalance = Number(users.userInfo.balance || 0)
		// 获取订单总价
		const totalPrice = Number(allTotalPrice.value)

		// 打印调试信息
		console.log('用户余额(balance):', userBalance, '订单总价:', totalPrice)

		// 比较数字类型的金额
		if (userBalance < totalPrice) {
			// 余额不足，显示提示弹窗
			payAboutModalRef.value.open(info.value, 3)
		} else {
			// 余额充足，显示确认弹窗
			payAboutModalRef.value.open(info.value, 1)
		}
	}

	//提交订单跳转收银台
	function toOrder() {
		function toDo() {
			let infoRes = clone(toCashierCs.value);
			//处理留言
			for (let item of resultList.value) {
				if ('goods' in item) {
					//如果是平台
					infoRes.orderJson.goods.message = item.message || '';
				}
				if ('myStoreGoods' in item) {
					//如果是店铺
					for (let i of infoRes.orderJson.storeGoods) {
						if (i.id == item.id) {
							i.message = item.message || '';
						}
					}
				}
			}
			//处理同城自提
			infoRes.orderJson.storeGoods.forEach((i, k) => {
				i.distribution = recordLocalPickUpList.value.indexOf(i.id) > -1 ? 1 : recordLocalDeliveryList.value
					.indexOf(i.id) > -1 ? 2 : 0;
				if (i.distribution === 2) {
					i.freight = resultList.value[k].freight || 0;
					i.distance = resultList.value[k].distance || 0;
				}
				// if (recordLocalPickUpList.value.indexOf(i.id) != -1 || recordLocalDeliveryList.value.indexOf(i.id) != -1) {
				// 	i.distribution = 1 //自提
				// } else {
				// 	i.distribution = 0
				// }
			});
			// infoRes.orderJson = encodeURIComponent(JSON.stringify(infoRes.orderJson));
			infoRes.orderJson = JSON.stringify(infoRes.orderJson)
			//将订单支付类型传递到购物车中（单品，多品，兑换券兑换）
			infoRes.buyType = options.value.type * 1;
			infoRes.payClassic = 0
			// 调用支付函数
			payResult(infoRes, false, (e) => {
				// 支付成功回调，显示支付成功弹窗
				payAboutModalRef.value.open(e, 2)
			}, () => {
				// 支付失败回调，显示余额不足弹窗
				setTimeout(() => {
					payAboutModalRef.value.open(info.value, 3)
				}, 100)
			})
			//跳转到收银台
			// redTo(`/pages/cashier/cashier${parseObjToPath(infoRes)}`);
		}
		//如果所有都是同城配送,则不做限制  反之做限制
		if (recordLocalPickUpList.value.length !== resultList.value.length) {
			if (!info.value.memberShippingAddress || !info.value.memberShippingAddress.linkman) {
				uni.showToast({
					icon: 'none',
					title: '您还未设置收货地址~',
				});
				return;
			}
			if (!canFreight.value) {
				uni.showToast({
					icon: 'none',
					title: '配送地区暂不支持配送,非常抱歉~',
				});
				return;
			}
		}

		//判断是否有福利金支付 有则弹窗
		if (resultList.value.some(item => item.welfarePayments)) {
			let allWelf = 0;
			for (let item of resultList.value) {
				if (item.welfarePayments) {
					allWelf += ((item.welfarePayments || 0) * integralValue * 1).toFixed(2) * 1;
				}
			}
			uni.showModal({
				title: '温馨提示',
				content: `确定使用${allWelf}福利金进行支付?`,
				success(res) {
					if (res.confirm) {
						toDo();
					}
				},
			});
			return;
		}
		toDo();
	}
	//去选择(添加)地址
	function toAddAddress() {
		navTo('/pages/myAddress/myAddress?type=select');
	}

	// 跳转到推荐人详情
	function toPromoterDetail(promoterInfo) {
		console.log('点击参与推荐', promoterInfo);
		// 这里可以添加跳转逻辑，如果需要的话
		// navTo('/pages/promoter/detail?id=' + promoterInfo.id);
	}

	// 格式化过期时间
	function formatExpireTime(expireTime) {
		if (!expireTime) return '';
		try {
			const date = new Date(expireTime);
			const year = date.getFullYear();
			const month = String(date.getMonth() + 1).padStart(2, '0');
			const day = String(date.getDate()).padStart(2, '0');
			return `${year}-${month}-${day}`;
		} catch (error) {
			console.error('格式化过期时间失败:', error);
			return expireTime;
		}
	}

	// 计算店铺最终支付金额
	function calculateStoreFinalPrice(item) {
		try {
			// 后端已经计算好了补助金抵扣后的支付金额，直接使用
			if (item.storeSubsidyAfterPrice !== undefined) {
				return Number(item.storeSubsidyAfterPrice).toFixed(2);
			}
			
			// 如果没有storeSubsidyAfterPrice字段，则使用totalPrice作为兜底
			return Number(item.totalPrice || 0).toFixed(2);
		} catch (error) {
			console.error('获取店铺最终支付金额失败:', error);
			return (Number(item.totalPrice || 0)).toFixed(2);
		}
	}

	// 确保页面滚动正常
	function ensurePageScrollable() {
		nextTick(() => {
			// 获取页面高度信息
			const query = uni.createSelectorQuery();
			query.select('.confirmOrder').boundingClientRect((data) => {
				if (data) {
					console.log('页面内容高度:', data.height);
					// 如果内容高度超过屏幕高度，确保可以滚动
					const systemInfo = uni.getSystemInfoSync();
					const screenHeight = systemInfo.screenHeight;
					if (data.height > screenHeight) {
						console.log('页面内容超出屏幕，已启用滚动');
					}
				}
			}).exec();
		});
	}

	// 检查地址是否有更新
	async function checkAddressUpdate() {
		try {
			// 获取当前选中地址的最新信息
			let { data } = await uni.http.get(uni.api.getAddress);

			if (data.result && data.result.records && data.result.records.length > 0) {
				// 找到当前选中的地址
				const currentAddress = data.result.records.find(addr => addr.id === options.value.memberShippingAddressId);

				// 如果找到地址且与当前显示的地址信息不同，则刷新页面
				if (currentAddress && (
					currentAddress.linkman !== info.value.memberShippingAddress.linkman ||
					currentAddress.phone !== info.value.memberShippingAddress.phone ||
					currentAddress.areaExplan !== info.value.memberShippingAddress.areaExplan ||
					currentAddress.areaAddress !== info.value.memberShippingAddress.areaAddress
				)) {
					console.log('地址信息已更新，刷新页面');
					refresh();
				}
			}
		} catch (error) {
			console.error('检查地址更新失败', error);
		}
	}

	//选择地址回调
	function selectAddressCallback(data) {
		options.value.memberShippingAddressId = data.id;
		refresh();
	}
	//选择优惠券回调
	function selectedCouponCallback(data) {
		//如果确认已经点击选择了某个索引的优惠券 赋值
		if (selectCouponIndex.value !== '') {
			//点击选择优惠券所对应的item(基于resultList)
			const nowSelectedItem = resultList.value[selectCouponIndex.value];
			if ('goods' in nowSelectedItem) {
				//如果是平台
				toCashierCs.value.orderJson.goods.discountId = data.id || '';
			}
			if ('myStoreGoods' in nowSelectedItem) {
				//如果是店铺
				let storeGoods = toCashierCs.value.orderJson.storeGoods;
				for (let it of storeGoods) {
					if (it.id == nowSelectedItem.id) {
						//在storeGoods根据id找到对应的item  修改对应item的券id
						it.discountId = data.id || '';
						it.isNomal = data.isNomal || 0;
						break;
					}
				}
			}
			selectCouponIndex.value = '';
			setTimeout(() => {
				//选择优惠券后刷新页面
				refresh();
			}, 200);
		}
	}

	onLoad(op => {
		options.value = op;
		//监听地址选择
		uni.$on('selectAddresss', selectAddressCallback);
		//监听选择优惠券
		uni.$on('selectedCoupon', selectedCouponCallback);
		getStoreTypeDicts()
		refresh();
	});

onShow(() => {
	// 如果已经有地址ID，检查地址是否有更新
	if (options.value.memberShippingAddressId && info.value.memberShippingAddress) {
		checkAddressUpdate();
	}
});
onUnload(() => {
	//页面销毁 销毁监听
	uni.$off('selectAddresss', selectAddressCallback);
	uni.$off('selectedCoupon', selectedCouponCallback);
});
async function refresh() {
	uni.showLoading({
		mask: true,
	});
	//默认接口为单品订单
	let apiRes = uni.api.promptlyAffirmOrder;
	switch (options.value.type * 1) {
		case 1:
			//单品订单
			apiRes = uni.api.promptlyAffirmOrder;
			break;
		case 2:
			//购物车多品订单
			apiRes = uni.api.affirmOrder;
			break;
		case 3:
			//兑换券免费兑换
			apiRes = uni.api.orderSubmitCertificate;
			break;
		default:
			break;
	}
try {
	let {
		data
	} = await uni.http.post(apiRes, {
		...options.value,
		orderJson: rebateOrder.value,
	});
	//原始数据保存
	info.value = data.result;
	//初始化跳转到收银台所需要的数据
	let infoRes = {
		memberShippingAddressId: data.result.memberShippingAddress.id,
		ids: [],
		orderJson: {
			//店铺商品
			storeGoods: [],
			//平台商品
			goods: {
				distribution: 0,
				discountId: '',
				message: '',
			},
		},
	};
	nextTick(() => {
		if ('memberShippingAddressId' in toCashierCs.value) {
			//如果已经初始化过数据,则不改变其中orderJson中的数据
			toCashierCs.value.memberShippingAddressId = infoRes.memberShippingAddressId;
			for (let item of resultList.value) {
				item.list.forEach(i => {
					infoRes.ids.push(i.id);
				});
			}
			toCashierCs.value.ids = infoRes.ids;
		} else {
			//初始化数据
			for (let item of resultList.value) {
				item.list.forEach(i => {
					infoRes.ids.push(i.id);
				});
				if ('myStoreGoods' in item) {
					//如果是店铺
					infoRes.orderJson.storeGoods.push({
						id: item.id,
						distribution: 0,
						discountId: '',
						message: '',
						isNomal: 0, //仅用作判断是否是使用了折扣券
					});
				}
			}
			//如果没有店铺,则放入默认值
			if (infoRes.orderJson.storeGoods.length == 0) {
				infoRes.orderJson.storeGoods.push({
					id: '',
					distribution: 0,
					discountId: '',
					message: '',
					isNomal: 0, //仅用作判断是否是使用了折扣券
				});
			}
			toCashierCs.value = infoRes;
		}
	});
	uni.hideLoading();
	
	// 确保页面滚动正常
	ensurePageScrollable();
} catch (e) {
	uni.hideLoading();
	uni.hideToast();
	uni.showModal({
		title: '温馨提示',
		content: (e.data && e.data.message) || '网络状况差，请稍后再试~',
		showCancel: false,
		confirmText: '知道了',
		success(res) {
			// 如果当前位于登录页(login)则点击确认后不需要返回首页(index)
			const pages = getCurrentPages();
			const currentPage = pages[pages.length - 1].route;
			if (currentPage.includes('pages/login/login')) return;
			toUpPage();
			// if (res.confirm)
		},
	});
	//TODO handle the exception
}
	}
</script>




<style lang="scss">
	page {
		background-color: #F5F5F5;
		height: 100%;
		overflow-x: hidden;
		overflow-y: auto;
	}

	.confirmOrder {
		padding: 30rpx;
		padding-bottom: calc(180rpx + env(safe-area-inset-bottom));
		min-height: 100vh;
		box-sizing: border-box;

		&-operation {
			position: fixed;
			bottom: 0;
			left: 0;
			height: calc(180rpx + env(safe-area-inset-bottom));
			width: 100%;
			background: #ffffff;
			box-shadow: 0px -5px 30px 0px rgba(194, 193, 193, 0.25);
			padding: 30rpx;
			padding-bottom: calc(30rpx + env(safe-area-inset-bottom));
			display: flex;
			align-items: center;
			justify-content: space-between;
			z-index: 999;

			&-left {
				display: flex;
				flex-direction: column;
				justify-content: space-between;
				font-size: 26rpx;
				color: #666666;

				>view {
					display: flex;
					align-items: center;
				}

				&-top {
					font-weight: 500;
					margin-bottom: 10rpx;
					display: flex;
					align-items: center;

					.total-label {
						color: #333333;
						font-size: 26rpx;
					}

					>image {
						width: 36rpx;
						height: 36rpx;
						margin: 0 16rpx;
					}

					.total-amount {
						color: #333333;
						font-size: 46rpx;
						font-weight: bold;
					}
				}

				&-discount {
					display: flex;
					align-items: center;
					justify-content: flex-start;
					margin-top: 4rpx;

					.discount-label {
						color: #FF6B6B;
						font-size: 24rpx;
						display: flex;
						align-items: center;
					}

					.discount-amount {
						color: #FF6B6B;
						font-size: 28rpx;
						font-weight: bold;
						margin-left: 10rpx;
					}
				}

				&-subsidy {
					display: flex;
					align-items: center;
					justify-content: flex-start;
					margin-top: 4rpx;

					.subsidy-label {
						color: #FF6B6B;
						font-size: 24rpx;
						display: flex;
						align-items: center;
					}

					.subsidy-amount {
						color: #FF6B6B;
						font-size: 28rpx;
						font-weight: bold;
						margin-left: 10rpx;
					}
				}
			}

			&-right {
				width: 300rpx;
				height: 80rpx;
				line-height: 80rpx;
				background: #22a3ff;
				border-radius: 100rpx;
				text-align: center;
				font-size: 30rpx;
				color: white;
				font-weight: 500;
			}
		}

		&-commonWrap {
			background-color: #fefefe;
			border-radius: 16rpx;
			margin-bottom: 20rpx;
			padding: 30rpx;

			&-title {
				color: #333333;
				font-size: 28rpx;
			}
		}

		&-reference {
			margin: 0 auto;
			width: 650rpx;
			height: 180rpx;
			padding: 0 30rpx;
			display: flex;
			flex-direction: column;
			background-image: url(@/static/confirmOrder/tj_bg.png);
			background-size: 100% 100%;
			box-sizing: border-box;

			>view {
				width: 100%;
				flex: 1;
				display: flex;
				align-items: center;
			}

			&-top {
				display: flex;
				align-items: center;
				justify-content: space-between;
				font-size: 26rpx;
				color: #333333;
				width: 100%;

				&-label {
					font-size: 28rpx;
					color: #333333;
					margin-right: 16rpx;
					flex-shrink: 0;
				}

				&-left {
					display: flex;
					align-items: center;
					flex: 1;
					overflow: hidden;

					&-avatar {
						width: 50rpx;
						height: 50rpx;
						border-radius: 50%;
						overflow: hidden;
						margin-right: 16rpx;
						flex-shrink: 0;

						>image {
							width: 100%;
							height: 100%;
						}
					}

					&-info {
						flex: 1;
						overflow: hidden;
						white-space: nowrap;
						text-overflow: ellipsis;

						.nickname {
							color: #333333;
						}
					}
				}

				&-right {
					display: flex;
					align-items: center;
					justify-content: flex-end;
					padding-left: 16rpx;
					flex-shrink: 0;

					>text {
						font-size: 24rpx;
						color: #666666;
					}

					>image {
						width: 22rpx;
						height: 22rpx;
						margin-left: 8rpx;
					}
				}
			}

			&-bottom {
				justify-content: space-between;
				color: #333333;

				&-left {
					font-size: 26rpx;
					color: #666666;
				}

				&-right {
					display: flex;
					align-items: center;

					.amount {
						color: #E6A600;
						margin-right: 10rpx;
						font-size: 30rpx;
					}

					.unit {
						font-size: 24rpx;
						color: #666666;
					}
				}
			}
		}

		&-store {
			&-bz {
				display: flex;
				align-items: center;
				justify-content: space-between;
				font-size: 28rpx;
				margin-top: 10rpx;
				padding-top: 20rpx;
				border-top: 1rpx solid #F5F5F5;

				&-right {
					flex: 1;
					display: flex;
					align-items: center;
					justify-content: flex-end;

					input {
						color: #999999;
						font-size: 24rpx;
						width: 100%;
					}

					>image {
						margin-left: 10rpx;
						width: 20rpx;
						height: 20rpx;
					}
				}

				&-left {
					color: #333333;
					font-weight: 500;
				}
			}

			&-subsidy {
				margin-top: 20rpx;
				padding: 20rpx;
				background-color: #FFF9E6;
				border-radius: 12rpx;
				border-left: 4rpx solid #FFB800;

				&-row {
					display: flex;
					align-items: center;
					justify-content: space-between;
					margin-bottom: 8rpx;
					font-size: 26rpx;

					&.final {
						margin-top: 8rpx;
						padding-top: 8rpx;
						border-top: 1rpx dashed #FFB800;
						font-weight: 500;
					}

					&:last-child {
						margin-bottom: 0;
					}
				}

				&-label {
					color: #333333;
					flex: 1;
				}

				&-amount {
					color: #333333;
					font-weight: 500;
				}

				&-deduction {
					color: #FF6B6B;
					font-weight: 500;
				}

				&-final {
					color: #FFB800;
					font-weight: bold;
					font-size: 28rpx;
				}

				&-tip {
					margin-top: 12rpx;
					padding-top: 12rpx;
					border-top: 1rpx dashed #FFB800;
					font-size: 24rpx;
					line-height: 1.4;

					.tip-text {
						color: #FF6B6B;
						font-weight: 500;
					}

					.tip-expire {
						color: #999999;
					}
				}
			}

			&-payInfo {
				display: flex;
				align-items: center;
				justify-content: flex-end;
				height: 100rpx;
				border-top: 2rpx solid #F5F5F5;
				border-bottom: 2rpx solid #F5F5F5;
				margin-bottom: 30rpx;

				>view {
					display: flex;
					align-items: center;
				}

				&-tj {
					font-size: 24rpx;
					color: #999999;
				}

				&-yf {
					font-size: 24rpx;
					color: #666666;
					margin-left: 20rpx;
				}
			}

			&-cnt {
				display: flex;
				align-items: center;
				margin-bottom: 24rpx;
				padding: 20rpx 0;
				border-radius: 12rpx;
				background-color: #FAFAFA;

				&-img {
					width: 160rpx;
					height: 160rpx;
					margin: 0 24rpx 0 20rpx;
					border-radius: 8rpx;
					overflow: hidden;
				}

				&-right {
					flex: 1;
					height: 160rpx;
					display: flex;
					flex-direction: column;
					justify-content: space-between;
					font-size: 26rpx;
					color: #999999;
					padding-right: 20rpx;

					&-name {
						color: #333333;
						font-size: 28rpx;
						font-weight: 500;
						line-height: 1.4;
						overflow: hidden;
						text-overflow: ellipsis;
						display: -webkit-box;
						-webkit-line-clamp: 2;
						line-clamp: 2;
						-webkit-box-orient: vertical;
					}

					&-spec {
						margin: 8rpx 0;
						color: #666666;
					}

					&-spc {
						display: flex;
						align-items: center;
						justify-content: space-between;

						&-qty {
							color: #666666;
						}

						&-lv {
							display: flex;
							align-items: center;
							color: #E6A600;
							font-weight: 500;
							font-size: 28rpx;

							>image {
								width: 36rpx;
								height: 36rpx;
								margin-right: 6rpx;
							}
						}
					}

				}
			}

			&-top {
				display: flex;
				align-items: center;
				justify-content: space-between;
				margin-bottom: 20rpx;
				padding-bottom: 16rpx;
				border-bottom: 1rpx solid #F5F5F5;

				>view:nth-child(1) {
					font-size: 30rpx;
					color: #333333;
					font-weight: bold;
				}

				>view:nth-child(2) {
					font-size: 26rpx;
					width: 98rpx;
					height: 40rpx;
					line-height: 40rpx;
					background: #d5eeff;
					color: #22A3FF;
					border-radius: 100rpx;
					text-align: center;
				}
			}
		}

		&-address {
			display: flex;
			align-items: center;

			&-left {
				flex: 1;
				margin-right: 20rpx;

				&-ad {
					margin-top: 20rpx;
					font-size: 24rpx;
					color: #999999;
					line-height: 35rpx;
				}
			}

			&-arrow {
				width: 20rpx;
				height: 20rpx;
			}
		}
	}
</style>