<template>
	<view class="user">

		<image class="user-bg" :src="imgUrl + '/static/user/bg.png'" mode=""></image>
		<view class="user-ct">
			<view class="user-ct-info">
				<image class="user-ct-info-avatar" :src="users.userInfo.avatarUrl || '/static/missing-face.png'"
					mode=""></image>
				<view class="user-ct-info-right" @click="navToBeforeLogin('/packageUser/pages/userProfile/userProfile')">
					<template v-if="users.token">
						<view class="user-ct-info-right-top">
							{{users.userInfo.nickName}}
							<image src="@/static/right-arrow-white.png" mode=""></image>
						</view>
						<view class="user-ct-info-right-bottom">
							<image v-if="users.userInfo.isLoveAmbassador == 1" class="user-ct-info-right-bottom-img"
								src="@/static/user/vip.png" mode=""></image>
							创业业绩:{{users.userInfo.totalPerformance || 0}}
						</view>
					</template>
					<view class="user-ct-info-right-top" v-else>
						请登录
						<image src="@/static/right-arrow-white.png" mode=""></image>
					</view>
				</view>

				<view class="user-ct-info-progress" v-if="users.token"> <!-- 暂时注释掉 isLoveAmbassador != 1 条件 -->
					<view class="user-ct-info-progress-txt">
						满值{{loveAmbassador}}可成助梦家
					</view>
					<view class="user-ct-info-progress-image">
						<!-- /static/user/star_1.png -->
						<image class="user-ct-info-progress-image-ig" :src="progressIgSrc" mode=""></image>
						<text class="user-ct-info-progress-image-number">{{users.userInfo.totalRechargeBalance}}</text>
					</view>

				</view>

			</view>
			<view class="user-ct-myPkg">
				<view class="user-ct-myPkg-title">
					我的包裹
				</view>
				<view class="user-ct-myPkg-detail">
					<uni-badge size="small" :text="0" absolute="rightTop" :offset="[18,10]" type="error">
						<view class="user-ct-myPkg-detail-view" @click="navToBeforeLogin('/packageOrder/pages/myPackage/myPackage')">
							<image src="@/static/user/p_1.png" mode=""></image>
							<view>
								全部包裹
							</view>
						</view>
					</uni-badge>

					<uni-badge size="small" :text="info.dealsAreDoneCount" absolute="rightTop" :offset="[18,10]"
						type="error">
						<view class="user-ct-myPkg-detail-view"
							@click="navToBeforeLogin('/packageOrder/pages/myPackage/myPackage?status=3')">
							<image src="@/static/user/p_2.png" mode=""></image>
							<view>
								已收货
							</view>
						</view>
					</uni-badge>

					<uni-badge size="small" :text="info.waitDeliveryCount" absolute="rightTop" :offset="[18,10]"
						type="error">
						<view class="user-ct-myPkg-detail-view"
							@click="navToBeforeLogin('/packageOrder/pages/myPackage/myPackage?status=1')">
							<image src="@/static/user/p_3.png" mode=""></image>
							<view>
								待发货
							</view>
						</view>
					</uni-badge>

					<uni-badge size="small" :text="info.waitForReceivingCount" absolute="rightTop" :offset="[18,10]"
						type="error">
						<view class="user-ct-myPkg-detail-view"
							@click="navToBeforeLogin('/packageOrder/pages/myPackage/myPackage?status=2')">
							<image src="@/static/user/p_4.png" mode=""></image>
							<view>
								待收货
							</view>
						</view>
					</uni-badge>


					<uni-badge size="small" :text="info.refundCount" absolute="rightTop" :offset="[18,10]" type="error">
						<view class="user-ct-myPkg-detail-view"
							@click="goToAfterSale()">
							<image src="@/static/user/p_5.png" mode=""></image>
							<view>
								换货/售后
							</view>
						</view>
					</uni-badge>

				</view>
			</view>


			<view style="margin-bottom: 30rpx;">
				<activityBanner></activityBanner>
			</view>

			<view class="user-ct-myLove">
				<view class="user-ct-myLove-title" @click="navToBeforeLogin('/packageUser/pages/heartMeter/heartMeter')">
					我的助力值
					<image src="@/static/right-arrow-black.png" mode=""></image>
				</view>
				<view class="user-ct-myLove-det">
					<view class="user-ct-myLove-det-left">
						<view class="user-ct-myLove-det-left-count">
							{{users?.userInfo?.balance || 0}}
						</view>
						<view class="user-ct-myLove-det-left-desc">
							助力值
						</view>
					</view>
					<view class="user-ct-myLove-det-right" @click="navToBeforeLogin('/packageRecharge/pages/recharge/recharge')">
						立即助力
					</view>
				</view>
				<view class="user-ct-myLove-line">

				</view>
				<view class="user-ct-myLove-other">
					<view class="user-ct-myLove-other-view clickable-item"
						@click="navToBeforeLogin('/pages/commissionDetails/commissionDetails')">
						<view class="item-header">
							已获得
							<image src="@/static/right-arrow-black.png" mode="" class="arrow-icon"></image>
						</view>
						<view class="item-value">
							{{users?.userInfo?.totalCommission || 0}}
						</view>
						<view class="item-unit">
							助力值
						</view>
					</view>
					<view class="user-ct-myLove-other-view clickable-item" @click="navToBeforeLogin('/pages/withDrawLs/withDrawLs')">
						<view class="item-header">
							已结算
							<image src="@/static/right-arrow-black.png" mode="" class="arrow-icon"></image>
						</view>
						<view class="item-value">
							{{users?.userInfo?.withdrawnAmount || 0}}
						</view>
						<view class="item-unit">
							助力值
						</view>
					</view>
				</view>
			</view>

			<!-- 店铺补助金模块 -->
			<view class="user-ct-storeSubsidy">
				<view class="user-ct-storeSubsidy-title" @click="navToBeforeLogin('/packageUser/pages/storeSubsidy/storeSubsidyList')">
					我的店铺补助金
					<image src="@/static/right-arrow-black.png" mode=""></image>
				</view>
				<view class="user-ct-storeSubsidy-det">
					<view class="user-ct-storeSubsidy-det-left">
						<view class="user-ct-storeSubsidy-det-left-count">
							{{storeSubsidyInfo?.totalAmount || 0}}
						</view>
						<view class="user-ct-storeSubsidy-det-left-desc">
							可用补助金
						</view>
					</view>
					<view class="user-ct-storeSubsidy-det-right" @click="navToBeforeLogin('/packageUser/pages/storeSubsidy/storeSubsidyList')">
						查看明细
					</view>
				</view>
				<view class="user-ct-storeSubsidy-line">

				</view>
				<view class="user-ct-storeSubsidy-other">
					<view class="user-ct-storeSubsidy-other-view clickable-item"
						@click="navToBeforeLogin('/packageUser/pages/storeSubsidy/storeSubsidyList?status=0')">
						<view class="item-header">
							可用
							<image src="@/static/right-arrow-black.png" mode="" class="arrow-icon"></image>
						</view>
						<view class="item-value">
							{{storeSubsidyInfo?.availableAmount || 0}}
						</view>
						<view class="item-unit">
							元
						</view>
					</view>
					<view class="user-ct-storeSubsidy-other-view clickable-item" @click="navToBeforeLogin('/packageUser/pages/storeSubsidy/storeSubsidyList?status=1')">
						<view class="item-header">
							已过期
							<image src="@/static/right-arrow-black.png" mode="" class="arrow-icon"></image>
						</view>
						<view class="item-value">
							{{storeSubsidyInfo?.expiredAmount || 0}}
						</view>
						<view class="item-unit">
							元
						</view>
					</view>
				</view>
			</view>

			<view class="user-ct-marketing">
				<view class="user-ct-marketing-title">
					营销中心
				</view>
				<view class="user-ct-marketing-detail">
					<view @click="navToBeforeLogin('/packageUser/pages/myTeam/myTeam')">
						<image src="@/static/user/tab_8.png" mode=""></image>
						<view>
							我的团队
						</view>
					</view>
					<view @click="navToBeforeLogin('/packageUser/pages/myPoster/myPoster')">
						<image src="@/static/user/tab_4.png" mode=""></image>
						<view>
							邀请好友
						</view>
					</view>
					<view @click="navToBeforeLogin('/packageUser/pages/myBankCard/myBankCard')">
						<image src="@/static/user/tab_6.png" mode=""></image>
						<view>
							银行卡管理
						</view>
					</view>
				</view>
			</view>

			<view class="user-ct-fc">
				<view class="user-ct-fc-title">
					其他功能
				</view>
				<view class="user-ct-fc-detail">
					<view @click="navToBeforeLogin('/packageGoods/pages/productCollection/productCollection')">
						<image src="@/static/user/tab_1.png" mode=""></image>
						<view>
							产品收藏
						</view>
					</view>
					<!-- 浏览记录功能已隐藏 -->
					<view style="display: none;" @click="navToBeforeLogin('/packageTest/browseHistory/browseHistory')">
						<image src="@/static/user/tab_2.png" mode=""></image>
						<view>
							浏览记录
						</view>
					</view>
					<view @click="navToBeforeLogin('/pages/storeCollection/storeCollection')">
						<image src="@/static/user/tab_3.png" mode=""></image>
						<view>
							店铺收藏
						</view>
					</view>
					<view @click="navToBeforeLogin('/pages/myAddress/myAddress')">
						<image src="@/static/user/tab_7.png" mode=""></image>
						<view>
							我的地址
						</view>
					</view>
					<!-- 银行卡管理功能已移至营销中心 -->
					<view @click="navTo('/packageUser/pages/setting/setting')">
						<image src="@/static/user/tab_5.png" mode=""></image>
						<view>
							设置
						</view>
					</view>

				</view>
			</view>
		</view>
		<cartAction></cartAction>
	</view>
</template>

<script setup>
	import {
		onPullDownRefresh,
		onShow
	} from '@dcloudio/uni-app';
	import {
		navTo,
		navToBeforeLogin,
		getUserInfos
	} from '@/hooks';
	import {
		cartCountHooks
	} from '@/hooks/getCartCount.js'
	import {
		frontSetting,
		userStore
	} from '@/store/index.js'
	import {
		computed,
		ref
	} from 'vue';
	const imgUrl = uni.env.IMAGE_URL
	const loveAmbassador = computed(() => frontSetting().setting.loveAmbassador);
	let info = ref({})
	let storeSubsidyInfo = ref({
		totalAmount: 0,
		availableAmount: 0,
		expiredAmount: 0
	})
	
	//获取店铺补助金概要信息
	async function getStoreSubsidySummary() {
		if (users.token) {
			try {
				// 直接从用户信息中获取店铺补助金汇总数据
				storeSubsidyInfo.value = {
					totalAmount: parseFloat(users.userInfo.storeSubsidyAmount || 0).toFixed(2),
					availableAmount: parseFloat(users.userInfo.availableStoreSubsidyAmount || 0).toFixed(2),
					expiredAmount: parseFloat(users.userInfo.expiredStoreSubsidyAmount || 0).toFixed(2)
				};
			} catch (error) {
				console.log('获取店铺补助金信息失败：', error);
				storeSubsidyInfo.value = {
					totalAmount: '0.00',
					availableAmount: '0.00',
					expiredAmount: '0.00'
				};
			}
		} else {
			storeSubsidyInfo.value = {
				totalAmount: '0.00',
				availableAmount: '0.00',
				expiredAmount: '0.00'
			};
		}
	}

	//获取数量显示
	async function getAboutCount() {
		if (users.token) {
			let {
				data
			} = await uni.http.get(uni.api.memberGoodAndOrderCount);
			info.value = data.result
		} else {
			info.value = {}
		}

		//程序设置相关
		let {
			data: fts
		} = await uni.http.get(uni.api.findMarketingDistributionSetting);
		frontSetting().setSetting(fts.result)

	}
	const users = userStore()
	//百分比
	const progressBfb = computed(() => {
		if (users.token && loveAmbassador.value) {
			return parseFloat((users.userInfo.totalRechargeBalance / loveAmbassador.value * 100).toFixed(0))
		}
		return 0
	})
	//百分比图片
	const progressIgSrc = computed(() => {
		let num = 1
		if (progressBfb.value >= 0 && progressBfb.value < 25) {
			num = 1
		}
		if (progressBfb.value >= 25 && progressBfb.value < 50) {
			num = 2
		}
		if (progressBfb.value >= 50 && progressBfb.value < 75) {
			num = 3
		}
		if (progressBfb.value >= 75 && progressBfb.value < 100) {
			num = 4
		}
		return `/static/user/star_${num}.png`
	})
	onShow(() => {
		getUserInfos();
		getAboutCount()
		getStoreSubsidySummary()
		const {
			cartCountRefresh
		} = cartCountHooks()
		cartCountRefresh()
	})
	onPullDownRefresh(() => {
		getUserInfos();
		getAboutCount()
		getStoreSubsidySummary()
		setTimeout(() => {
			uni.stopPullDownRefresh()
		}, 1500)
	})

	// 跳转到售后页面
	function goToAfterSale() {
		if (!users.token) {
			// 如果未登录，先跳转到登录页面
			uni.reLaunch({
				url: '/pages/login/login',
			});
			return;
		}

		// 已登录，跳转到包裹列表页面并选中售后页签
		uni.navigateTo({
			url: '/packageOrder/pages/myPackage/myPackage?tabIndex=4',
			fail() {
				uni.switchTab({
					url: '/packageOrder/pages/myPackage/myPackage?tabIndex=4',
				});
			}
		});
	}
</script>

<style lang="scss">
	page {
		background-color: #f2f2f2;
	}

	.user {
		position: relative;
		width: 750rpx;
		overflow: hidden;

		&-bg {
			position: absolute;
			top: 0;
			z-index: 1;
			left: 0;
			width: 750rpx;
			height: 692rpx;
		}

		&-ct {
			padding: 30rpx;
			padding-top: 190rpx;
			position: relative;
			z-index: 3;

			&-ad {
				width: 100%;
				height: 96rpx;
				margin-bottom: 20rpx;
			}

			&-marketing {
				padding: 30rpx;
				background-color: white;
				border-radius: 16rpx;
				box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
				margin-bottom: 30rpx;

				&-title {
					color: #333333;
					font-size: 30rpx;
					font-weight: 600;
					margin-bottom: 24rpx;
				}

				&-detail {
					display: grid;
					grid-template-columns: repeat(4, 1fr);
					gap: 24rpx;

					>view {
						display: flex;
						flex-direction: column;
						align-items: center;
						justify-content: center;
						color: #000000;
						font-size: 26rpx;

						>image {
							width: 48rpx;
							height: 48rpx;
							margin-bottom: 15rpx;
						}
					}
				}
			}

			&-fc {
				padding: 30rpx;
				background-color: white;
				border-radius: 16rpx;
				box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);

				&-title {
					color: #333333;
					font-size: 30rpx;
					font-weight: 600;
					margin-bottom: 24rpx;
				}

				&-detail {
					display: grid;
					/* 四列，均分容器宽度 */
					grid-template-columns: repeat(4, 1fr);
					gap: 24rpx;

					>view {
						display: flex;
						flex-direction: column;
						align-items: center;
						justify-content: center;
						color: #000000;
						font-size: 26rpx;

						>image {
							width: 48rpx;
							height: 48rpx;
							margin-bottom: 15rpx;
						}
					}
				}
			}

			&-myLove {
				padding: 25rpx;
				background: #ffffff;
				border-radius: 16rpx;
				margin-bottom: 30rpx;
				box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);

				&-title {
					font-size: 30rpx;
					font-weight: 600;
					color: #000000;
					display: flex;
					align-items: center;
					margin-bottom: 24rpx;

					>image {
						width: 20rpx;
						height: 20rpx;
						margin-left: 6rpx;
					}
				}

				&-line {
					width: 100%;
					height: 2rpx;
					background: #f5f5f5;
					margin: 20rpx 0;
				}

				&-other {
					display: flex;
					align-items: center;
					margin-bottom: 20rpx;
					background-color: #f9f9f9;
					border-radius: 12rpx;
					padding: 20rpx 0;

					&-view {
						flex: 1;
						display: flex;
						flex-direction: column;
						justify-content: center;
						align-items: center;
						border-right: 1rpx solid #e5e5e5;
						padding: 10rpx 0;

						.item-header {
							color: #666666;
							font-size: 26rpx;
							display: flex;
							align-items: center;
							justify-content: center;
							margin-bottom: 8rpx;

							.arrow-icon {
								width: 16rpx;
								height: 16rpx;
								margin-left: 6rpx;
								opacity: 0.6;
							}
						}

						.item-value {
							color: #488FF3;
							font-size: 36rpx;
							font-weight: 500;
							margin-bottom: 4rpx;
						}

						.item-unit {
							color: #999999;
							font-size: 24rpx;
						}

						&.clickable-item {
							position: relative;

							&:active {
								background-color: rgba(0, 0, 0, 0.05);
								border-radius: 8rpx;
							}
						}
					}

					&-view:last-child {
						border-right: none;
					}
				}

				&-det {
					display: flex;
					align-items: center;
					justify-content: space-between;

					&-left {
						display: flex;
						align-items: center;

						&-count {
							font-size: 56rpx;
							line-height: 66rpx;
							color: #488FF3;
							margin-right: 20rpx;
						}

						&-desc {
							color: #999999;
							font-size: 24rpx;
						}
					}

					&-right {
						width: 203rpx;
						height: 61rpx;
						background: #22a3ff;
						border-radius: 80rpx;
						font-size: 28rpx;
						color: white;
						display: flex;
						align-items: center;
						justify-content: center;
					}
				}
			}

			&-storeSubsidy {
				padding: 25rpx;
				background: #ffffff;
				border-radius: 16rpx;
				margin-bottom: 30rpx;
				box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);

				&-title {
					font-size: 30rpx;
					font-weight: 600;
					color: #000000;
					display: flex;
					align-items: center;
					margin-bottom: 24rpx;

					>image {
						width: 20rpx;
						height: 20rpx;
						margin-left: 6rpx;
					}
				}

				&-line {
					width: 100%;
					height: 2rpx;
					background: #f5f5f5;
					margin: 20rpx 0;
				}

				&-other {
					display: flex;
					align-items: center;
					margin-bottom: 20rpx;
					background-color: #f9f9f9;
					border-radius: 12rpx;
					padding: 20rpx 0;

					&-view {
						flex: 1;
						display: flex;
						flex-direction: column;
						justify-content: center;
						align-items: center;
						border-right: 1rpx solid #e5e5e5;
						padding: 10rpx 0;

						.item-header {
							color: #666666;
							font-size: 26rpx;
							display: flex;
							align-items: center;
							justify-content: center;
							margin-bottom: 8rpx;

							.arrow-icon {
								width: 16rpx;
								height: 16rpx;
								margin-left: 6rpx;
								opacity: 0.6;
							}
						}

						.item-value {
							color: #FF6B35;
							font-size: 36rpx;
							font-weight: 500;
							margin-bottom: 4rpx;
						}

						.item-unit {
							color: #999999;
							font-size: 24rpx;
						}

						&.clickable-item {
							position: relative;

							&:active {
								background-color: rgba(0, 0, 0, 0.05);
								border-radius: 8rpx;
							}
						}
					}

					&-view:last-child {
						border-right: none;
					}
				}

				&-det {
					display: flex;
					align-items: center;
					justify-content: space-between;

					&-left {
						display: flex;
						align-items: center;

						&-count {
							font-size: 56rpx;
							line-height: 66rpx;
							color: #FF6B35;
							margin-right: 20rpx;
						}

						&-desc {
							color: #999999;
							font-size: 24rpx;
						}
					}

					&-right {
						width: 203rpx;
						height: 61rpx;
						background: #FF6B35;
						border-radius: 80rpx;
						font-size: 28rpx;
						color: white;
						display: flex;
						align-items: center;
						justify-content: center;
					}
				}
			}

			&-myPkg {
				padding: 30rpx;
				// height: 230rpx;
				background: #ffffff;
				border-radius: 16px;
				margin-bottom: 30rpx;
				box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);

				&-title {
					font-size: 30rpx;
					font-weight: 600;
					color: #333333;
					margin-bottom: 24rpx;
				}

				&-detail {
					display: flex;
					align-items: center;
					justify-content: space-between;

					&-view {
						flex: 1;
						display: flex;
						flex-direction: column;
						align-items: center;
						justify-content: center;
						font-size: 26rpx;
						color: #333333;
						text-align: center;


						>image {
							width: 100rpx;
							height: 100rpx;
							// margin-bottom: 18rpx;
						}
					}
				}
			}

			&-info {
				height: 110rpx;
				display: flex;
				align-items: center;
				margin-bottom: 50rpx;
				position: relative;

				&-avatar {
					width: 110rpx;
					height: 110rpx;
					border: 2rpx solid #ffffff;
					margin-right: 20rpx;
					border-radius: 50%;
				}

				&-right {
					height: 90rpx;
					display: flex;
					flex-direction: column;
					justify-content: space-around;
					color: white;

					&-top {
						display: flex;
						align-items: center;
						font-size: 30rpx;

						image {
							width: 20rpx;
							height: 20rpx;
							margin-left: 30rpx;
						}
					}

					&-bottom {
						font-size: 24rpx;
						display: flex;
						align-items: center;

						&-img {
							width: 36rpx;
							height: 36rpx;
							margin-right: 2rpx;
						}
					}
				}

				&-progress {
					width: 400rpx;
					height: 300rpx;
					position: absolute;
					right: -60rpx;
					top: 0;
					z-index: 2;
					display: flex;
					flex-direction: column;
					align-items: center;

					&-txt {
						height: 40rpx;
						line-height: 40rpx;
						text-align: center;
						background: #2ea8fb;
						border: 2rpx solid #ffffff;
						border-radius: 100rpx;
						position: relative;
						z-index: 2;
						font-size: 20rpx;
						color: white;
						padding: 0 20rpx;
					}

					&-image {
						width: 180rpx;
						height: 180rpx;
						position: relative;
						z-index: 1;
						top: -30rpx;
						display: flex;
						align-items: center;
						justify-content: center;

						&-ig {
							width: 100%;
							height: 100%;
						}

						&-number {
							color: #ffffff;
							font-size: 56rpx;
							position: absolute;
							z-index: 1;
						}
					}
				}

			}
		}
	}
</style>