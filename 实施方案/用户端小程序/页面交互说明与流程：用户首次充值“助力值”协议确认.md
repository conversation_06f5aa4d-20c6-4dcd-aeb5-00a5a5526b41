# 页面交互说明与流程：用户首次充值“助力值”协议确认

**目标：** 确保用户在首次充值“助力值”前，已明确阅读并同意平台的相关充值协议与用户服务协议，保障用户权益，符合平台合规要求，并对协议的易读性、版本管理及用户同意记录的存档有明确要求。

**涉及主要页面/组件：**

1.  **助力充值页面 (参考 `image_ac1d42.png`)**
2.  **协议展示弹窗/页面**
3.  **微信支付确认界面 (外部)**

---

## 一、页面元素与状态说明

### 1. 助力充值页面 (参考 `image_ac1d42.png`)

* **导航栏：**
    * 左侧：返回按钮（`<`）。
    * 中间：标题“助力”。
    * 右侧：更多操作按钮（`...`）、小程序胶囊按钮。
* **助力金额选择区：**
    * 提示文字：“助力金额 选择或输入您想要助力的金额”。
    * 预设金额按钮：¥50, ¥100, ¥300, ¥500, ¥1500, 其他。
        * **交互：** 点击预设金额按钮，该按钮高亮（如截图中 ¥50 被选中），下方“助力金额”实时显示选中金额。点击“其他”按钮，允许用户在下方的“助力金额”输入框（若设计为可输入）或弹出的输入框中自定义输入金额。
* **助力金额展示区：**
    * 标签：“助力金额”。
    * 金额显示：实时根据用户选择或输入的金额更新，例如 “¥ 50”。
* **【新增】协议同意区 (位于“确认助力”按钮上方)：**
    * **复选框 (Checkbox)：**
        * **初始状态：** 默认未勾选。
    * **文本说明：**
        * 文字：“我已阅读并同意 [《八闽助业集市助力值充值协议 (版本号 X.X)》](链接至协议详情) 及 [《用户服务协议 (版本号 Y.Y)》](链接至协议详情)”。
        * **交互：** 协议名称应为可点击链接，点击后弹出/跳转至对应的协议详情页面。建议在链接文本中包含当前协议的版本号。
* **操作按钮区：**
    * 按钮：“确认助力”。
        * **初始/未同意协议状态：** 置灰，不可点击。
        * **用户勾选同意协议后状态：** 激活（如截图中蓝色高亮），可点击。

### 2. 协议展示弹窗/页面

* **标题：** 显示对应协议的名称及版本号，如“八闽助业集市助力值充值协议 (版本 X.X)”。
* **内容区：** 展示协议的详细条款。
    * **【补充】协议文本的易读性：**
        * **语言清晰简洁：** 协议文本应使用通俗易懂的语言，避免过多复杂的法律术语和行业黑话，确保普通用户能够理解。
        * **结构化展示：** 采用清晰的段落、标题和小标题，方便用户快速定位和阅读关键信息。
        * **重点条款突出：** 对于核心条款，如“充值后不可退款”、“大额充值需实名认证”、“禁止行为”等，应使用加粗、下划线或不同颜色等方式进行显著提示，确保用户充分注意到。
        * **提供摘要或FAQ（可选）：** 对于较长的协议，可考虑在开头提供一个简明扼要的摘要或常见问题解答（FAQ），帮助用户快速了解核心内容。
    * **《八闽助业集市助力值充值协议》应包含：**
        * “助力值”的定义、性质。
        * “助力值”的获取途径。
        * “助力值”的用途（仅限平台内兑换商品）。
        * **充值后不可退款规则的明确声明**。
        * 可能的充值限额说明。
        * 关于大额充值可能需要实名认证的提示。
        * 禁止利用充值进行洗钱、套现等非法活动的声明。
        * 平台在风控方面的权利。
        * 解释权归属等。
    * **《用户服务协议》及《隐私政策》** (根据用户手册1.1提及) 也应遵循易读性原则。
* **操作按钮：**
    * “已阅读并同意”按钮（或类似肯定性按钮，用于关闭弹窗/页面并返回充值页）。
    * “关闭”或“返回”按钮。

---

## 二、交互流程

### 场景：用户首次充值 / 或之前未同意最新版协议

1.  **进入充值页面：**
    * 用户从任意入口进入“助力”充值页面。
    * 页面加载完成，显示各金额选项，【协议同意区】复选框默认未勾选，“确认助力”按钮为置灰不可点击状态。协议链接文本显示当前最新版本号。

2.  **用户选择/输入充值金额。**

3.  **用户尝试直接点击“确认助力”（按钮置灰时）：**
    * 系统无响应或toast提示：“请先阅读并同意相关协议”。

4.  **用户阅读协议：**
    * 用户点击【协议同意区】中的《八闽助业集市助力值充值协议 (版本号 X.X)》链接。
    * **交互：** 触发新弹窗或跳转至新页面，展示该版本协议的详细内容。
    * 用户阅读完毕，点击弹窗/页面中的“已阅读并同意”或“关闭”按钮，返回“助力”充值页面。
    * 用户可同样方式阅读《用户服务协议 (版本号 Y.Y)》。

5.  **用户同意协议：**
    * 用户在“助力”充值页面的【协议同意区】勾选复选框。
    * **交互：**
        * 复选框状态变为“已勾选”。
        * “确认助力”按钮从置灰状态变为激活状态，变为可点击。
        * **【补充】记录存档：** 平台后台应安全、准确地记录用户同意协议的动作。记录内容应包括：
            * **用户唯一标识 (UserID)。**
            * **所同意协议的名称及版本号 (例如：《八闽助业集市助力值充值协议 V1.2》)。**
            * **用户同意操作的时间戳 (精确到秒)。**
            * **用户同意时的IP地址及设备信息 (可选，用于辅助证明)。**
            * 这些记录应妥善保存，不可篡改，以备后续查证或作为解决纠纷的依据。

6.  **用户提交充值：**
    * 用户点击已激活的“确认助力”按钮。
    * 后续流程同前述。

7.  **完成微信支付。**

8.  **充值成功反馈。**

### 场景：用户已同意过最新版协议，再次充值

1.  **进入充值页面。**
2.  **系统校验：** 后台判断用户已同意过最新版协议 (通过对比用户已同意的协议版本号与当前最新协议版本号)。
3.  **若已同意最新版：**
    * 【协议同意区】的复选框可以设计为默认已勾选状态，或不强制显示，但仍需提供协议查阅入口。
    * “确认助力”按钮直接为激活状态。
    * 用户选择金额后，可直接点击“确认助力”进行支付。

### 场景：协议更新后，已同意旧版协议的用户首次充值

1.  **进入充值页面。**
2.  **系统校验：** 后台判断用户已同意的是旧版协议，而当前已有新版协议。
3.  **【补充】版本管理与用户知晓机制：**
    * **强制重新同意：** 按照【场景：用户首次充值 / 或之前未同意最新版协议】的流程执行。协议链接文本显示新版本号。
    * **更新提示（可选但建议）：** 在【协议同意区】或通过弹窗/站内信等方式，明确提示用户“充值协议已更新，请阅读并同意新版协议后再继续操作”。可以简要说明主要更新点。
    * **后台记录：** 用户同意新版协议后，后台更新其同意记录为新版本。

---

## 三、异常流程与提示

(与前述一致)

1.  **未勾选协议点击“确认助力”：**
    * 提示：“请先阅读并同意《八闽助业集市助力值充值协议》及《用户服务协议》”。
2.  **选择“其他”但未输入有效金额：**
    * 点击“确认助力”时提示：“请输入有效的助力金额”。
3.  **微信支付失败/取消：**
    * 微信支付界面关闭，返回小程序。
    * 小程序内提示：“支付失败”或“支付已取消”。用户可重新发起支付。
4.  **网络异常导致协议加载失败：**
    * 点击协议链接时，若无法加载协议内容，应提示：“网络不给力，协议加载失败，请稍后重试”。
5.  **网络异常导致充值请求失败：**
    * 点击“确认助力”后，若请求无法发送或无响应，应提示：“网络连接异常，请检查网络后重试”。

---

**备注：**

* 本文档基于通用设计原则和所提供的截图信息。具体UI细节可由UI/UX设计师进一步细化。
* **后台系统支持：** 后台必须能够管理协议文本（包括版本号），并准确记录和查询用户对各版本协议的同意状态和时间。
* **法律合规：** 建议平台法务团队审核协议文本内容及用户同意流程，确保符合相关法律法规要求。